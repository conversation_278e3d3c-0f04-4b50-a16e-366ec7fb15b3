import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'app-ddos-mitigation-messages',
    templateUrl: './ddos-mitigation-messages.component.html',
    styleUrl: './ddos-mitigation-messages.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DdosMitigationMessagesComponent {
    protected readonly activeModal = inject(NgbActiveModal);
    readonly messages = signal<string[]>(null);
}
