﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class ReportsAuthorizeFilter(
        IUserContextService userContextService,
        IIdentityService identityService,
        IEntityAuthorizationService entityAuthorizationService,
        Perms[] perms,
        int distance,
        string name) : BaseAsyncAuthorizationFilter(perms, distance, name)
    {
        private readonly IUserContextService _userContextService = userContextService;
        private readonly IIdentityService _identityService = identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService = entityAuthorizationService;

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            int? organizationId = await GetOrganizationId(context);
            if (!organizationId.HasValue)
            {
                context.Result = new BadRequestResult();
                return;
            }

            if (!HasPermission(userId, organizationId.Value))
            {
                context.Result = new ForbidResult();
                return;
            }

            AuthorizeFilterHelpers.SetOrganizationId(context, organizationId.Value);
        }

        private async Task<int?> GetOrganizationId(AuthorizationFilterContext context)
        {
            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            if (int.TryParse(val, out int reportRequestId))
            {
                return await _entityAuthorizationService.GetReportRequestOrganizationId(reportRequestId);
            }

            string orgIdString = context.HttpContext.Request.RouteValues["organizationId"]?.ToString();
            if (int.TryParse(orgIdString, out int orgId))
            {
                return orgId;
            }

            return null;
        }

        private bool HasPermission(int userId, int organizationId)
        {
            return _perms == null || _userContextService.HasPermission(userId, organizationId, _distance, _perms);
        }
    }

    public class ReportsAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public ReportsAuthorizeAttribute(params Perms[] perms) : base(typeof(ReportsAuthorizeFilter), perms)
        {
            Name = "reportRequestId";
        }
    }
}