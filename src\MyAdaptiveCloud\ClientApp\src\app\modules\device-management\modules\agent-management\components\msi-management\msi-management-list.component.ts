import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, map, Observable, switchMap, take, tap } from 'rxjs';
import { Msi } from '../../models/msi.model';
import { ServiceTypeEnum } from '../../models/service-type.enum';
import { MsiManagementService } from '../../services/msi-management.service';
import { UploadMsiModalComponent } from './upload-msi-modal/upload-msi-modal.component';
import { AgentApiHealthService } from '../../services/agent-api-health.service';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
    selector: 'app-msi-management-list',
    imports: [NgxDatatableModule, TableActionComponent, DatePipe],
    templateUrl: './msi-management-list.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class MsiManagementListComponent extends BaseListClientComponent<Msi> implements OnInit {
    private readonly modalService = inject(ModalService);
    private readonly msiManagementService = inject(MsiManagementService);
    private readonly agentApiHealthService = inject(AgentApiHealthService);

    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');
    private readonly dateCellTemplate = viewChild<TemplateRef<never>>('dateCellTemplate');
    private readonly serviceTypeCellTemplate = viewChild<TemplateRef<never>>('serviceTypeCellTemplate');
    private readonly checksumCellTemplate = viewChild<TemplateRef<never>>('checksumTemplate');
    private readonly obsoleteCellTemplate = viewChild<TemplateRef<never>>('obsoleteTemplate');

    private columns: TableColumn[] = [];

    protected readonly serviceType = ServiceTypeEnum;
    protected readonly isAgentAPIConnected = toSignal(this.agentApiHealthService.getHealthCheck().pipe(map(res => res.data)), { initialValue: false });

    ngOnInit(): void {

        this.columns = [
            {
                name: 'Service Type',
                prop: 'serviceType',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.serviceTypeCellTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 50,
            },
            {
                name: 'Name',
                prop: 'name',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 75
            },
            {
                name: 'Version',
                prop: 'version',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Release Tag',
                prop: 'releaseTag',
                sortable: false,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Release Date',
                prop: 'releaseDate',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.dateCellTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Agent Count',
                prop: 'agentCount',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: false,
                canAutoResize: true
            },
            {
                name: 'Checksum',
                cellTemplate: this.checksumCellTemplate(),
                sortable: false,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Status',
                prop: 'isObsolete',
                cellTemplate: this.obsoleteCellTemplate(),
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                width: 100,
                sortable: false,
                resizeable: false,
                canAutoResize: false
            },
        ];

        super.initialize(this.getServices$, this.columns);
        this.table().sorts = [{ prop: 'releaseDate', dir: 'desc' }];
    }

    private getServices$(): Observable<ApiDataResult<Msi[]>> {
        return this.msiManagementService.getList();
    }

    protected uploadMsi() {
        if (this.isAgentAPIConnected()) {
            const modal = this.modalService.openModalComponent(UploadMsiModalComponent);
            modal.closed
                .pipe(filter(res => !!res), take(1))
                .subscribe(() => {
                    super.initialize(this.getServices$, this.columns);
                });
        }
    }

    protected deleteMsi(serviceId: number) {
        if (this.isAgentAPIConnected()) {
            this.modalService.openDeleteConfirmationDialog('Delete MSI', 'Are you sure you want to delete this MSI?').closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                    switchMap(() => this.msiManagementService.delete(serviceId)),
                    tap(() => {
                        super.initialize(this.getServices$, this.columns);
                    })
                ).subscribe();
        }
    }

    protected downloadMsi(serviceId: number) {
        if (this.isAgentAPIConnected()) {
            this.msiManagementService.download(serviceId);
        }
    }

    protected markAsActive(row: Msi) {
        if (row.isObsolete) {
            this.msiManagementService.markAsActive(row.serviceId)
                .subscribe(() => {
                    row.isObsolete = false;
                    this.table().recalculate();
                });
        }
    }

    protected markAsObsolete(row: Msi) {
        if (!row.isObsolete && row.canMarkObsolete) {
            this.msiManagementService.markAsObsolete(row.serviceId)
                .subscribe(() => {
                    row.isObsolete = true;
                    this.table().recalculate();
                });
        }
    }

}
