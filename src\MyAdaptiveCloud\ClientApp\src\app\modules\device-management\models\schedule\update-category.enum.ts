export enum UpdateCategoryEnum {
    Critical = 'critical',
    Security = 'security',
    Definition = 'definition',
    Rollup = 'rollup',
    Others = 'others',
}

export const UpdateCategoryFullNames: Record<UpdateCategoryEnum, string> = {
    [UpdateCategoryEnum.Security]: 'Security',
    [UpdateCategoryEnum.Critical]: 'Critical',
    [UpdateCategoryEnum.Definition]: 'Definition Updates',
    [UpdateCategoryEnum.Rollup]: 'Rollup Updates',
    [UpdateCategoryEnum.Others]: 'Others',
};
