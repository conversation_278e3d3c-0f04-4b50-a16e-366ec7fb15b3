import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DeviceFolderTreeNode } from '../../models/device-folder-tree-node';
import { DeviceTreeNode } from '../../models/device-tree-node';
import { DevicesDetailsModalComponent } from './devices-details-modal.component';

describe('DevicesDetailsModalComponent', () => {
    let component: DevicesDetailsModalComponent;
    let fixture: ComponentFixture<DevicesDetailsModalComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DevicesDetailsModalComponent],
            providers: [NgbActiveModal], // Mocking NgbActiveModal dependency
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DevicesDetailsModalComponent);
        component = fixture.componentInstance;
        component.devices = [];
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should display "No devices match" message when devices array is empty', () => {
        fixture.detectChanges();
        const noDevicesMsgElement = fixture.nativeElement.querySelector('.empty-row');
        expect(noDevicesMsgElement.textContent).toContain('No data to display');
    });

    it('should display the list of devices with their hostnames and paths', () => {

        const device = new DeviceTreeNode();
        device.agentId = 1;
        device.hostname = 'Device 1';
        device.path = 'Root > Subfolder1 > Subfolder2';

        const device2 = new DeviceTreeNode();
        device2.agentId = 2;
        device2.hostname = 'Device 2';
        device2.path = 'Root > Subfolder1';

        const devices = [
            device,
            device2,
        ];
        component.devices = devices;
        fixture.detectChanges();

        const hostnameCells = fixture.nativeElement.querySelectorAll('.hostname-cell');
        const pathCells = fixture.nativeElement.querySelectorAll('.path-cell');
        expect(hostnameCells.length).toBe(devices.length);
        expect(pathCells.length).toBe(devices.length);

        hostnameCells.forEach((item, index) => expect(item.textContent).toContain(devices[index].hostname));
        pathCells.forEach((item, index) => expect(item.textContent).toContain(devices[index].path));
    });

    it('should display the parent path using the pathFromRootString method', () => {
        const device = new DeviceTreeNode();
        device.agentId = 1;
        device.hostname = 'Device 1';
        device.path = 'Root > Subfolder1 > Subfolder2';

        const parent = new DeviceFolderTreeNode({
            folderId: 1,
            name: 'Root',
            description: 'A desc',
            deviceCount: 1,
            hasSubfolders: true,
            parentFolderId: 0,
            deviceCountCurrentFolder: 0
        }, 0, undefined, []);
        device.parent.set(parent);
        const devices = [
            device,
        ];
        component.devices = devices;
        fixture.detectChanges();

        const devicePathElement = fixture.nativeElement.querySelector('.path-cell');
        expect(devicePathElement.textContent).toContain('Root');
    });
});
