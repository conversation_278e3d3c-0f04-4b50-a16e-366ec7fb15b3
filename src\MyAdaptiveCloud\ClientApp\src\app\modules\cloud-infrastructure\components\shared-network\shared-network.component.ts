import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { SharedNetworkForm } from '@app/modules/cloud-infrastructure/forms/shared-network.form';
import { NetworkOfferingViewModel } from '@app/modules/cloud-infrastructure/models/network-offering.view-model';
import { noWhitespaceValidator } from '@app/shared/validators/no-white-space-validator';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { networkDomainValidator, validateCidr } from '../../validators/network-validators';

@Component({
    selector: 'app-add-network-shared',
    imports: [ReactiveFormsModule, NgSelectComponent, NgbPopover],
    templateUrl: './shared-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SharedNetworkComponent {

    private readonly formBuilder = inject(FormBuilder);
    readonly networkOfferings = input.required<NetworkOfferingViewModel[]>();

    public form = this.formBuilder.group<SharedNetworkForm>(
        {description: this.formBuilder.control<string | null>(null, Validators.required),
            name: this.formBuilder.control<string | null>(null, [Validators.required, noWhitespaceValidator()]),
            networkOffering: this.formBuilder.control<NetworkOfferingViewModel | null>(null, Validators.required),
            vlanVni: this.formBuilder.control<string | null>(null, [Validators.required, noWhitespaceValidator(), Validators.pattern(/^\d+$/)]),
            hideIpAddressUsage: this.formBuilder.control(false),
            ipv4EndIp: this.formBuilder.control(null),
            ipv4Gateway: this.formBuilder.control(null),
            ipv4Netmask: this.formBuilder.control(null),
            ipv4StartIp: this.formBuilder.control(null),
            ipv6CIDR: this.formBuilder.control(null, [validateCidrIfFilled()]),
            ipv6EndIp: this.formBuilder.control(null),
            ipv6Gateway: this.formBuilder.control(null),
            ipv6StartIp: this.formBuilder.control(null),
            networkDomain: this.formBuilder.control<string | null>(null, [networkDomainValidator()])},
        {
            validators: ipv4OrIpv6CompleteValidator
        }
    );

}

export const ipv4OrIpv6CompleteValidator: ValidatorFn = (control: AbstractControl): ValidationErrors | null => {
    if (!(control instanceof FormGroup)) {
        return null;
    }

    const group = control as FormGroup & { controls: { [K in keyof SharedNetworkForm]: AbstractControl } };

    const isFilled = (val: unknown) => val !== null && val !== undefined && val.toString()?.trim() !== '';

    const ipv4Complete = [
        group.controls.ipv4Gateway.value,
        group.controls.ipv4Netmask.value,
        group.controls.ipv4StartIp.value,
        group.controls.ipv4EndIp.value
    ].every(isFilled);

    const ipv6Complete = [
        group.controls.ipv6Gateway.value,
        group.controls.ipv6CIDR.value,
        group.controls.ipv6StartIp.value,
        group.controls.ipv6EndIp.value
    ].every(isFilled);

    return ipv4Complete || ipv6Complete ? null : { ipv4OrIpv6Incomplete: true };
};

export function validateCidrIfFilled(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value;
        if (!value || value.toString().trim() === '') {
            return null;
        }
        return validateCidr()(control);
    };
}
