<div class="modal-header">
    <h4 class="modal-title">Create Virtual Private Cloud</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form()">
        <div class="row mb-3">
            <label for="name" class="col-3 col-form-label">Name<span class="required-asterisk">*</span></label>
            <div class="col">
                <input class="form-control" data-testid="name-input" formControlName="name" id="name"
                    [class]="{ 'is-invalid': form().controls.name.invalid && form().controls.name.dirty }"
                    autocomplete="off" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="description" for="description" class="col-3 col-form-label">Description</label>
            <div class="col">
                <input class="form-control" data-testid="description-input" formControlName="description" id="description"
                    [class]="{ 'is-invalid': form().controls.description.invalid && form().controls.description.dirty }" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-3 col-form-label">Zone<span class="required-asterisk">*</span></label>
            </div>
            <div class="col-9">
                <ng-select [items]="zones()" id="zone" bindLabel="name" bindValue="id" formControlName="zone"
                    data-testid="zone-select" [clearable]="false" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="cidr" class="col-3 col-form-label">CIDR<span class="required-asterisk">*</span>
                <i class="ms-1 fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'The CIDR of the VPC. All VPC guest networks cidrs should be within this CIDR.'"
                    triggers="hover" container="body"></i>
            </label>
            <div class="col">
                <input class="form-control" data-testid="cidr-input" id="cidr" formControlName="cidr"
                    [class]="{ 'is-invalid': form().controls.cidr.invalid && form().controls.cidr.dirty }"
                    autocomplete="off" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="networkDomain" class="col-3 col-form-label">Network Domain
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'VPC network domain. All networks inside the VPC will belong to this domain.'"
                    triggers="hover" container="body"></i>
            </label>
            <div class="col">
                <input class="form-control" data-testid="network-domain-input" formControlName="networkDomain"
                    id="networkDomain"
                    [class]="{ 'is-invalid': form().controls.networkDomain.invalid && form().controls.networkDomain.dirty }">
            </div>
        </div>
        <div class="row mb-3">
            <label class="col-3 col-form-label">VPC Offering<span class="required-asterisk">*</span>
            </label>
            <div class="col">
                <ng-select [items]="vpcOfferings()" bindLabel="description" bindValue="id"
                    formControlName="vpcOffering" data-testid="vpc-offering-select"
                    [class]="{ 'is-invalid': form().controls.vpcOffering.invalid && form().controls.vpcOffering.dirty }" />
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!form()?.valid" (submitClickEvent)="submit()">OK
    </app-btn-submit>
</div>
