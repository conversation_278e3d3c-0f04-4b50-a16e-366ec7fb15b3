import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, OnInit, inject, input } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideComponentStore } from '@ngrx/component-store';
import { take } from 'rxjs';
import { FoldersTreeStore } from '../../store/folders-tree.store';
import { DeviceFolderTreeComponent } from '../folder-tree-panel/folder-tree.component';

@Component({
    selector: 'app-browse-folders-devices-modal',
    imports: [DeviceFolderTreeComponent, BtnSubmitComponent, AsyncPipe],
    templateUrl: './browse-folders-devices-modal.component.html',
    styleUrl: './browse-folders-devices-modal.component.scss',
    providers: [
        provideComponentStore(FoldersTreeStore),
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class BrowseFoldersDevicesModalComponent implements OnInit {
    protected readonly activeModal = inject(NgbActiveModal);
    protected readonly foldersTreeStore = inject(FoldersTreeStore);
    private readonly destroyRef = inject(DestroyRef);

    readonly allowMultiSelect = input(true);
    readonly showDevices = input(true);

    ngOnInit() {
        // This is just a trick to guarantee foldersTreeStore is initialized before calling its methods
        this.foldersTreeStore.foldersRoot$.pipe(take(1)).subscribe(() => {
            this.foldersTreeStore.setAllowMultiSelect(this.allowMultiSelect());
            this.foldersTreeStore.removeUnassignedDevicesFolder();
        });
    }

    protected save() {
        this.foldersTreeStore.selectedFoldersAndDevices$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(selectedFoldersAndDevices => {
            this.activeModal.close(selectedFoldersAndDevices);
        });
    }
}
