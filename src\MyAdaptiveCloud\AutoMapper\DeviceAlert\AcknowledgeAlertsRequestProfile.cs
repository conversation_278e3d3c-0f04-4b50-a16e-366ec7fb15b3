using AutoMapper;
using MyAdaptiveCloud.Api.Requests.DeviceAlerts;
using MyAdaptiveCloud.Services.Requests.DeviceAlerts;

namespace MyAdaptiveCloud.Api.AutoMapper.DeviceAlert
{
    public class AcknowledgeAlertsRequestProfile : Profile
    {
        public AcknowledgeAlertsRequestProfile()
        {
            CreateMap<Requests.DeviceAlerts.AcknowledgeAlertsRequest, Services.Requests.DeviceAlerts.AcknowledgeAlertsRequest>();
            CreateMap<AgentsAndDeviceAlertsRequest, AgentsAndDeviceAlertsDTO>();
        }
    }
}