import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { forkJoin } from 'rxjs';
import { CreateAffinityGroupForm, ExistingAffinityGroupForm } from '../../forms/create-affinity-group.form';
import { VmAffinityGroupsService } from '../../services/vm-affinity-groups.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';

@Component({
    selector: 'app-add-to-affinity-group',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgSelectComponent],
    templateUrl: './add-to-affinity-group.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AddToAffinityGroupComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmAffinityGroupsService = inject(VmAffinityGroupsService);
    protected readonly vmManagementPermissionService = inject(VmManagementPermissionService);

    readonly domainId = signal<string>(null);
    readonly account = signal<string>(null);
    readonly virtualMachineId = signal<string>(null);

    protected readonly isSubmitting = signal<boolean>(false);
    protected readonly affinityGroupTypes = toSignal(this.vmAffinityGroupsService.getAffinityGroupTypes());
    protected readonly availableAffinityGroups = signal<{ id: string; name: string }[]>([]);
    protected readonly alreadyAssignedAffinityGroups = signal<string[]>([]);

    protected form: FormGroup<{ useExisting: FormControl<boolean> }>;

    protected readonly createNewForm = this.formBuilder.group<CreateAffinityGroupForm>({
        name: this.formBuilder.control(null, [Validators.maxLength(255), Validators.required]),
        description: this.formBuilder.control(null, Validators.maxLength(255)),
        type: this.formBuilder.control(null)
    });

    protected readonly useExistingForm = this.formBuilder.group<ExistingAffinityGroupForm>({
        id: this.formBuilder.control(null, Validators.required)
    });

    ngOnInit(): void {

        this.form = this.formBuilder.group<{ useExisting: FormControl<boolean> }>({
            useExisting: this.formBuilder.control(!this.vmManagementPermissionService.canCreateAffinityGroup()),
        });

        forkJoin([this.vmAffinityGroupsService.getAffinityGroupsByVirtualMachine(this.virtualMachineId()), this.vmAffinityGroupsService.getAffinityGroups(this.domainId(), this.account())])
            .subscribe(([assignedAffinityGroups, affinityGroups]) => {
                this.alreadyAssignedAffinityGroups.set(assignedAffinityGroups.map(group => group.id));
                this.availableAffinityGroups.set(affinityGroups.filter(affinityGroup => !this.alreadyAssignedAffinityGroups().includes(affinityGroup.id)));
            });
    }

    protected submit() {
        if (this.form.controls.useExisting.value && this.useExistingForm.valid) {
            this.isSubmitting.set(true);
            const updatedAffinityGroupIds = [...this.alreadyAssignedAffinityGroups(), this.useExistingForm.controls.id.value];
            this.updateAvailableAffinityGroups(updatedAffinityGroupIds);
        } else if (!this.form.controls.useExisting.value && this.createNewForm.valid) {
            this.isSubmitting.set(true);
            const newAffinityGroup = this.createNewForm.value;
            this.vmAffinityGroupsService.createAffinityGroup(newAffinityGroup.name, newAffinityGroup.description, newAffinityGroup.type, this.domainId(), this.account())
                .subscribe(newAffinityGroupId => {
                    const updatedAffinityGroupIds = [...this.alreadyAssignedAffinityGroups(), newAffinityGroupId];
                    this.updateAvailableAffinityGroups(updatedAffinityGroupIds);
                });
        }
    }

    private updateAvailableAffinityGroups(updatedAffinityGroupIds: string[]) {
        this.vmAffinityGroupsService.updateAffinityGroupsForVirtualMachine(this.virtualMachineId(), updatedAffinityGroupIds).subscribe(() => {
            this.isSubmitting.set(false);
            this.activeModal.close();
        });
    }

}
