import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { Ngb<PERSON><PERSON>rdionBody, NgbAccordionButton, NgbAccordionCollapse, NgbAccordionDirective, Ngb<PERSON><PERSON>rdionHeader, NgbAccordionItem } from '@ng-bootstrap/ng-bootstrap';
import { VmDetailsStateService } from '../../services/vm-details.state.service';

@Component({
    selector: 'app-vm-details-nic',
    imports: [NgbAccordionBody, NgbAccordionButton, NgbAccordionItem, NgbAccordionDirective, NgbA<PERSON>rdionHeader, NgbAccordionCollapse],
    templateUrl: './vm-details-nic.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmDetailsNicComponent {

    protected readonly vmDetailsStateService = inject(VmDetailsStateService);
    protected readonly nics = computed(() => this.vmDetailsStateService.selectedVM()?.nic ?? []);

}
