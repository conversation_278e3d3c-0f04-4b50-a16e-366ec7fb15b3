﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using MyAdaptiveCloud.Services.Extensions;
using MyAdaptiveCloud.Services.Services;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace MyAdaptiveCloud.Api.Authorization.Basic
{
    public class BasicAuthOptions : AuthenticationSchemeOptions
    {
        public string Key { get; set; }
    }

    public static class BasicAuthExtensions
    {
        public static AuthenticationBuilder AddBasicAuthAuthentication(this AuthenticationBuilder builder, string authScheme,
            Action<BasicAuthOptions> configureOptions)
        {
            return builder.AddScheme<BasicAuthOptions, BasicAuthHandler>(authScheme, configureOptions);
        }
    }

    public class BasicAuthHandler : AuthenticationHandler<BasicAuthOptions>
    {
        private readonly IAuthenticationKeyService _authenticationKeyService;

        public BasicAuthHandler(IOptionsMonitor<BasicAuthOptions> options, ILoggerFactory logger, UrlEncoder encoder,
            IAuthenticationKeyService authenticationKeyService)
            : base(options, logger, encoder)
        {
            _authenticationKeyService = authenticationKeyService;
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            if (!Request.Headers.Authorization.Any())
            {
                return AuthenticateResult.Fail("Authentication failed");
            }

            string authorizationHeader = Request.Headers.Authorization[0];
            if (string.IsNullOrEmpty(authorizationHeader))
            {
                return AuthenticateResult.NoResult();
            }

            if (!authorizationHeader.StartsWith("basic", StringComparison.OrdinalIgnoreCase))
            {
                return AuthenticateResult.Fail("Authentication failed");
            }

            string token = authorizationHeader.Substring("basic".Length).Trim();

            if (string.IsNullOrEmpty(token))
            {
                return AuthenticateResult.Fail("Authentication failed");
            }

            try
            {
                return await ValidateToken(token);
            }
            catch (Exception ex)
            {
                return AuthenticateResult.Fail(ex.Message);
            }
        }

        private async Task<AuthenticateResult> ValidateToken(string token)
        {
            var decoded = token.FromBase64String();
            var credArray = decoded.Split(':');
            if (credArray.Length != 2)
            {
                return AuthenticateResult.Fail("Invalid Basic Auth value");
            }

            var apikey = credArray[0];
            var apisecret = credArray[1];

            var personId = await _authenticationKeyService.GetBasicAuthenticatedPersonId(apikey, apisecret);
            if (personId == null)
            {
                return AuthenticateResult.Fail("Authentication failed");
            }

            Claim[] claims = new[] { new Claim(ClaimTypes.Name, personId.ToString()) };

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return AuthenticateResult.Success(ticket);
        }
    }
}
