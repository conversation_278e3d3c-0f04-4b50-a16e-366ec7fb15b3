import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-remote-vpngateways-container',
    imports: [RouterOutlet],
    templateUrl: './remote-vpngateways-container.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class RemoteVpngatewaysContainerComponent {

}
