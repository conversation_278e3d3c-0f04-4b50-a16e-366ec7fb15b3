@import "custom-variables.scss";

.wrapper {
  position: relative;
  height: 100vh;
  background-color: $content-bg;
  display: flex;
  flex-direction: column;

  .topnavbar-wrapper {
    height: $navbar-height;
  }

  .main-section-wrapper {
    flex-grow: 1;
    display: flex;
    height: calc(100vh - $navbar-height);
    overflow: hidden;

    .aside-container {
        height: 100%;
    }

    .section-container {
      background-color: $content-bg;
      flex-grow: 1;
      height: 100%;
      overflow: auto;
    }
  }
}
