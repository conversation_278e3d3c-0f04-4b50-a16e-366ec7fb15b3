# Development Workflow Guide

## Table of Contents
- [Local Development Setup](#local-development-setup)
- [Development Environment](#development-environment)
- [Coding Standards](#coding-standards)
- [Testing Strategies](#testing-strategies)
- [Debugging Approaches](#debugging-approaches)
- [Git Workflow](#git-workflow)
- [Code Review Process](#code-review-process)
- [Contribution Guidelines](#contribution-guidelines)

## Local Development Setup

### Prerequisites
Before starting development, ensure you have the following installed:

```bash
# Required Software
- .NET 9.0 SDK
- Node.js 18+ (for Angular)
- MySQL/MariaDB (for local database)
- Docker Desktop (optional, for containerized development)
- Poetry (for Python test automation)
- Git
- Visual Studio Code or Visual Studio 2022
```

### Initial Setup Steps

1. **Clone the Repository**
```bash
git clone https://github.com/your-org/client-center.git
cd client-center
```

2. **Backend Setup**
```bash
# Restore .NET packages
dotnet restore

# Build the solution
dotnet build
```

3. **Frontend Setup**
```bash
# Navigate to Angular app
cd src/MyAdaptiveCloud/ClientApp

# Install dependencies
npm install

# Build Angular app
npm run build
```

4. **Database Setup**
```bash
# Create local databases
mysql -u root -p
CREATE DATABASE myadaptivecloud_dev;
CREATE DATABASE acagent_dev;
CREATE DATABASE myadaptivecloudlogs_dev;

# Update connection strings in appsettings.Development.json
```

5. **Configuration Files**
```json
// src/MyAdaptiveCloud/appsettings.Development.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;User Id=root;Database=myadaptivecloud_dev;",
    "ACAgentConnection": "Server=localhost;User Id=root;Database=acagent_dev;",
    "LogsConnection": "Server=localhost;User Id=root;Database=myadaptivecloudlogs_dev;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Information",
      "MYAC": "Debug"
    }
  }
}
```

### Running the Application

1. **Start Backend**
```bash
# From project root
cd src/MyAdaptiveCloud
dotnet run

# Or with hot reload
dotnet watch run
```

2. **Start Frontend (Development Mode)**
```bash
# From ClientApp directory
cd src/MyAdaptiveCloud/ClientApp
npm start

# This starts Angular dev server with SSL on https://localhost:4200
```

3. **Start Worker Services (Optional)**
```bash
cd src/MyAdaptiveCloud.WorkerServices
dotnet run
```

## Development Environment

### IDE Configuration

#### Visual Studio Code
```json
// .vscode/settings.json
{
    "dotnet.defaultSolution": "MyAdaptiveCloud.sln",
    "typescript.preferences.importModuleSpecifier": "relative",
    "eslint.workingDirectories": ["src/MyAdaptiveCloud/ClientApp"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
    }
}
```

#### Visual Studio 2022
- Install Angular project template
- Configure IIS Express for HTTPS
- Set multiple startup projects (API + Angular)

### Environment Variables
```bash
# Development environment variables
export ASPNETCORE_ENVIRONMENT=Development
export DOTNET_CLI_HOME=/tmp/DOTNET_CLI_HOME
export NODE_OPTIONS="--max_old_space_size=8192"
```

### Hot Reload Configuration
```json
// src/MyAdaptiveCloud/Properties/launchSettings.json
{
  "profiles": {
    "MyAdaptiveCloud": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "applicationUrl": "https://localhost:7001;http://localhost:5001",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

## Coding Standards

### C# Coding Standards

#### Naming Conventions
```csharp
// Classes: PascalCase
public class UserService : IUserService

// Methods: PascalCase
public async Task<User> GetUserByIdAsync(int userId)

// Properties: PascalCase
public string FirstName { get; set; }

// Private fields: camelCase with underscore
private readonly IUserRepository _userRepository;

// Constants: PascalCase
public const string DefaultConnectionString = "...";

// Local variables: camelCase
var userName = user.FirstName;
```

#### Code Organization
```csharp
// File structure order
using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Services;

namespace MyAdaptiveCloud.Api.Controllers
{
    [Route("api/[controller]")]
    public class UserController : AuthenticatedControllerBase
    {
        // 1. Private fields
        private readonly IUserService _userService;

        // 2. Constructor
        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        // 3. Public methods
        [HttpGet("{id}")]
        public async Task<ActionResult<UserDTO>> GetUser(int id)
        {
            // Implementation
        }

        // 4. Private methods
        private void ValidateUser(User user)
        {
            // Implementation
        }
    }
}
```

### TypeScript/Angular Standards

#### Component Structure
```typescript
// Component file organization
import { Component, OnInit, inject } from '@angular/core';
import { Observable } from 'rxjs';

@Component({
    selector: 'app-user-list',
    templateUrl: './user-list.component.html',
    styleUrls: ['./user-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserListComponent implements OnInit {
    // 1. Injected services
    private readonly userService = inject(UserService);
    
    // 2. Public properties
    public users$: Observable<User[]>;
    
    // 3. Lifecycle methods
    ngOnInit(): void {
        this.loadUsers();
    }
    
    // 4. Public methods
    public onUserSelected(user: User): void {
        // Implementation
    }
    
    // 5. Private methods
    private loadUsers(): void {
        this.users$ = this.userService.getUsers();
    }
}
```

#### Naming Conventions
```typescript
// Interfaces: PascalCase with 'I' prefix (optional)
interface UserModel {
    id: number;
    firstName: string;
}

// Classes: PascalCase
class UserService {
    // Methods: camelCase
    public getUserById(id: number): Observable<User> { }
    
    // Properties: camelCase
    public currentUser: User;
    
    // Private members: camelCase with underscore
    private readonly _httpClient = inject(HttpClient);
}

// Constants: UPPER_SNAKE_CASE
const API_ENDPOINTS = {
    USERS: '/api/users',
    ORGANIZATIONS: '/api/organizations'
};
```

### Code Quality Tools

#### .NET Code Analysis
```xml
<!-- Directory.Build.props -->
<Project>
  <PropertyGroup>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
  </PropertyGroup>
</Project>
```

#### Angular ESLint Configuration
```json
// .eslintrc.json
{
  "extends": [
    "@angular-eslint/recommended",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@angular-eslint/component-class-suffix": "error",
    "@angular-eslint/directive-class-suffix": "error"
  }
}
```

## Testing Strategies

### Unit Testing (.NET)

#### Test Structure
```csharp
[TestClass]
public class UserServiceTests
{
    private Mock<IUserRepository> _mockUserRepository;
    private Mock<IMapper> _mockMapper;
    private UserService _userService;

    [TestInitialize]
    public void Setup()
    {
        _mockUserRepository = new Mock<IUserRepository>();
        _mockMapper = new Mock<IMapper>();
        _userService = new UserService(_mockUserRepository.Object, _mockMapper.Object);
    }

    [TestMethod]
    public async Task GetUserByIdAsync_ValidId_ReturnsUser()
    {
        // Arrange
        var userId = 1;
        var expectedUser = new User { Id = userId, FirstName = "John" };
        _mockUserRepository.Setup(r => r.GetByIdAsync(userId))
                          .ReturnsAsync(expectedUser);

        // Act
        var result = await _userService.GetUserByIdAsync(userId);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(expectedUser.Id, result.Id);
        _mockUserRepository.Verify(r => r.GetByIdAsync(userId), Times.Once);
    }
}
```

#### Running Tests
```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test project
dotnet test tests/MyAdaptiveCloud.Services.Tests/
```

### Frontend Testing (Angular)

#### Component Testing
```typescript
describe('UserListComponent', () => {
    let component: UserListComponent;
    let fixture: ComponentFixture<UserListComponent>;
    let mockUserService: jasmine.SpyObj<UserService>;

    beforeEach(async () => {
        const spy = jasmine.createSpyObj('UserService', ['getUsers']);

        await TestBed.configureTestingModule({
            imports: [UserListComponent],
            providers: [
                { provide: UserService, useValue: spy }
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(UserListComponent);
        component = fixture.componentInstance;
        mockUserService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should load users on init', () => {
        const mockUsers = [{ id: 1, firstName: 'John' }];
        mockUserService.getUsers.and.returnValue(of(mockUsers));

        component.ngOnInit();

        expect(mockUserService.getUsers).toHaveBeenCalled();
    });
});
```

#### Running Angular Tests
```bash
# Run unit tests
npm run test

# Run tests in CI mode
npm run test-ci

# Run with coverage
ng test --code-coverage
```

### End-to-End Testing (Playwright)

#### Test Structure
```python
# tests/ui/test_user_management.py
import pytest
from playwright.sync_api import Page, expect

class TestUserManagement:
    def test_user_list_displays_correctly(self, page: Page):
        # Navigate to user management page
        page.goto("/administration/users")
        
        # Wait for page to load
        page.wait_for_selector("[data-testid='user-list']")
        
        # Verify user list is visible
        user_list = page.locator("[data-testid='user-list']")
        expect(user_list).to_be_visible()
        
        # Verify at least one user is displayed
        user_rows = page.locator("[data-testid='user-row']")
        expect(user_rows).to_have_count_greater_than(0)

    def test_create_new_user(self, page: Page):
        page.goto("/administration/users")
        
        # Click create user button
        page.click("[data-testid='create-user-btn']")
        
        # Fill user form
        page.fill("[data-testid='first-name']", "John")
        page.fill("[data-testid='last-name']", "Doe")
        page.fill("[data-testid='email']", "<EMAIL>")
        
        # Submit form
        page.click("[data-testid='save-user-btn']")
        
        # Verify success message
        expect(page.locator("[data-testid='success-message']")).to_be_visible()
```

#### Running E2E Tests
```bash
# Install dependencies
cd testAutomation/MyAdaptiveCloud.Tests
poetry install

# Run all tests
poetry run pytest tests/ui/

# Run specific browser
poetry run pytest tests/ui/ --browser chrome

# Run in parallel
poetry run pytest tests/ui/ -n 4
```

## Debugging Approaches

### Backend Debugging

#### Visual Studio/VS Code
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": ".NET Core Launch (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/MyAdaptiveCloud/bin/Debug/net9.0/MyAdaptiveCloud.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/MyAdaptiveCloud",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            }
        }
    ]
}
```

#### Logging Configuration
```csharp
// Program.cs - Enhanced logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

if (builder.Environment.IsDevelopment())
{
    builder.Logging.AddFilter("Microsoft.EntityFrameworkCore.Database.Command", LogLevel.Information);
}
```

### Frontend Debugging

#### Browser DevTools
- **Angular DevTools**: Install browser extension
- **Redux DevTools**: For NgRx state debugging
- **Network Tab**: Monitor API calls
- **Console**: Check for JavaScript errors

#### Angular Debugging
```typescript
// Enable debug mode in development
import { enableProdMode } from '@angular/platform-browser';
import { environment } from './environments/environment';

if (environment.production) {
    enableProdMode();
} else {
    // Development debugging
    console.log('Running in development mode');
}
```

### Database Debugging

#### Entity Framework Logging
```csharp
// Enable SQL logging in development
services.AddDbContext<MyAdaptiveCloudContext>(options =>
{
    options.UseMySql(connectionString, serverVersion)
           .EnableSensitiveDataLogging(isDevelopment)
           .LogTo(Console.WriteLine, LogLevel.Information);
});
```

#### SQL Profiling
```sql
-- Enable MySQL general log
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = '/var/log/mysql/general.log';

-- Monitor queries
SHOW PROCESSLIST;

## Git Workflow

### Branch Strategy
The project follows a feature branch workflow:

```
main (production)
├── develop (integration)
│   ├── feature/MYAC-123-user-management
│   ├── feature/MYAC-124-device-monitoring
│   └── hotfix/MYAC-125-critical-bug
└── release/v1.2.0
```

### Branch Naming Conventions
- **Feature branches**: `feature/MYAC-{ticket-number}-{short-description}`
- **Bug fixes**: `bugfix/MYAC-{ticket-number}-{short-description}`
- **Hotfixes**: `hotfix/MYAC-{ticket-number}-{short-description}`
- **Release branches**: `release/v{major}.{minor}.{patch}`

### Commit Message Format
```
type(scope): short description

Longer description if needed

Fixes #123
```

**Types**: feat, fix, docs, style, refactor, test, chore

**Examples**:
```bash
feat(auth): add OAuth2 integration
fix(api): resolve null reference in user service
docs(readme): update installation instructions
test(user): add unit tests for user validation
```

### Git Commands Workflow
```bash
# Create feature branch
git checkout -b feature/MYAC-123-user-management

# Make changes and commit
git add .
git commit -m "feat(user): add user creation endpoint"

# Push branch
git push origin feature/MYAC-123-user-management

# Create pull request (via GitHub/GitLab)

# After review, merge to develop
git checkout develop
git pull origin develop
git merge feature/MYAC-123-user-management
git push origin develop

# Clean up
git branch -d feature/MYAC-123-user-management
git push origin --delete feature/MYAC-123-user-management
```

## Code Review Process

### Pre-Review Checklist
Before submitting a pull request:

- [ ] Code compiles without warnings
- [ ] All tests pass locally
- [ ] Code follows established conventions
- [ ] Documentation is updated
- [ ] No sensitive data in commits
- [ ] Branch is up to date with target branch

### Pull Request Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No merge conflicts
```

### Review Guidelines

#### For Reviewers
- **Functionality**: Does the code work as intended?
- **Readability**: Is the code easy to understand?
- **Performance**: Are there any performance concerns?
- **Security**: Are there any security vulnerabilities?
- **Testing**: Is the code adequately tested?

#### Review Comments
```markdown
# Constructive feedback examples

## Suggestion
Consider using a more descriptive variable name here:
```csharp
// Instead of 'data'
var userData = await _userService.GetUserAsync(id);
```

## Question
Why did you choose this approach over using the existing UserValidator?

## Nitpick
Minor: Missing space after comma in parameter list.

## Blocking
This could cause a null reference exception. Please add null checking.
```

## Contribution Guidelines

### Getting Started
1. **Fork the repository** (for external contributors)
2. **Create a feature branch** from develop
3. **Make your changes** following coding standards
4. **Write tests** for new functionality
5. **Update documentation** as needed
6. **Submit a pull request**

### Development Workflow
```bash
# 1. Sync with upstream
git checkout develop
git pull origin develop

# 2. Create feature branch
git checkout -b feature/your-feature-name

# 3. Make changes
# ... code changes ...

# 4. Test changes
dotnet test
npm run test-ci

# 5. Commit changes
git add .
git commit -m "feat: add new feature"

# 6. Push and create PR
git push origin feature/your-feature-name
```

### Code Quality Gates
All contributions must pass:

- **Build**: Code compiles successfully
- **Tests**: All unit and integration tests pass
- **Linting**: Code passes ESLint and .NET analyzers
- **Security**: No security vulnerabilities detected
- **Coverage**: Maintain or improve test coverage

### Documentation Requirements
- **API Changes**: Update OpenAPI documentation
- **New Features**: Add user documentation
- **Breaking Changes**: Update migration guides
- **Configuration**: Document new settings

### Release Process
1. **Feature Freeze**: No new features in release branch
2. **Testing**: Comprehensive testing of release candidate
3. **Documentation**: Update release notes and documentation
4. **Deployment**: Deploy to staging for final validation
5. **Release**: Tag and deploy to production
6. **Post-Release**: Monitor for issues and hotfixes

### Support and Communication
- **Issues**: Use GitHub Issues for bug reports and feature requests
- **Discussions**: Use GitHub Discussions for questions
- **Slack**: Internal team communication
- **Documentation**: Keep README and wiki updated
```
