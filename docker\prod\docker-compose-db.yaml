services:
  db:
    user: "${MY_UID}:${MY_GID}"
    container_name: "${COMPOSE_PROJECT_NAME}-db"
    image: mariadb:10-focal
    command: '--default-authentication-plugin=mysql_native_password'
    restart: always
    ports:
      - 3306:3306/tcp
    logging:
      driver: syslog
      options:
        tag: "{{.Name}}"
    healthcheck:
      test: ['CMD-SHELL', 'mysqladmin ping -h 127.0.0.1 --password="$$(cat /run/secrets/db-password)" --silent']
      interval: 30s
      retries: 5
      start_period: 30s
    secrets:
      - db-password
    volumes:
      - ./db/init:/docker-entrypoint-initdb.d
      - ./db/data:/var/lib/mysql
      - ./docker_passwd:/etc/passwd
      - ./docker_group:/etc/group
    environment:
      - MYSQL_DATABASE=myac
      - MYSQL_ROOT_PASSWORD_FILE=/run/secrets/db-password
secrets:
  db-password:
    file: ./db/password.txt
