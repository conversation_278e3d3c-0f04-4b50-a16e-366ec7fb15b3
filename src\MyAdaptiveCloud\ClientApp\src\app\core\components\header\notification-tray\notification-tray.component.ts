import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NotificationTrayItem } from '@app/core/models/notification-tray-item';
import { JobQueueEvent, JobQueueEventDescription } from '@app/shared/models/job-queue/job-queue-event.enum';
import { JobQueueStatus } from '@app/shared/models/job-queue/job-queue-status.enum';
import { JobQueueType } from '@app/shared/models/job-queue/job-queue-type.enum';
import { JobQueue } from '@app/shared/models/job-queue/job-queue.model';
import { JobQueueService } from '@app/shared/services/job-queue.service';
import { defer, filter, interval, startWith, Subject, takeUntil, tap } from 'rxjs';

@Component({
    selector: 'app-notification-tray',
    templateUrl: './notification-tray.component.html',
    styleUrl: './notification-tray.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class NotificationTrayComponent implements OnInit, OnDestroy {

    private readonly jobQueueService = inject(JobQueueService);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly notifications = signal<NotificationTrayItem[]>([]);
    protected readonly unreadNotificationCount = computed(() => this.notifications().filter(item => !item.isRead).length);
    protected readonly showTray = signal<boolean>(false);
    protected readonly jobQueueStatus = JobQueueStatus;

    readonly interval = 10000;

    private cancelRefreshElapsedTime$ = new Subject<void>();

    /*
    *   This observable is used to refresh the elapsed time of each notification item every 10 seconds.
    *   It runs immediately upon subscription and then every 10 seconds.
    */
    private refreshElapsedTime$ = defer(() => interval(this.interval)
        .pipe(
            // Emit immediately, then every 10s
            startWith(0),
            filter(() => !!this.notifications().length),
            tap(() => {
                const currentNotifications = [...this.notifications()];
                currentNotifications.forEach(item => {
                    item.elapsedTime = this.getElapsedTime(item.createdDateUtcIsoString);
                });
                this.notifications.update(() => [...currentNotifications]);
            }),
            takeUntil(this.cancelRefreshElapsedTime$),
            takeUntilDestroyed(this.destroyRef)
        ));

    ngOnInit(): void {
        this.jobQueueService.jobQueue$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(jobQueue => {
                this.mapItems(jobQueue);
            });
    }

    /*
    *   Removes an item from the list and from the job queue. The item will no longer be updated
    */
    protected dismiss(notification: NotificationTrayItem): void {
        this.notifications.update(items => items.filter(item => item !== notification));
        this.jobQueueService.removeFromQueue(notification.id, notification.type);
    }

    /*
    *   Toggle the tray visibility and mark all notifications that are not in progress as read when closing.
    *   It also cancels the refreshElapsedTime$ observable when the tray is closed and starts it when the tray is opened.
    */
    protected toggleTray() {
        this.showTray.update(show => !show);

        if (!this.showTray()) {
            this.markAllNotInProgressAsRead();
            this.cancelRefreshElapsedTime$.next();
        } else {
            this.refreshElapsedTime$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe();
        }
    }

    private mapItems(jobQueue: JobQueue[]) {
        const currentNotifications = [...this.notifications()];
        jobQueue.forEach(queuedJob => {
            // Add a new notification item if one of the same type and id does not exist.
            // Existing notifications matching these criteria are considered the same notification and get updated with the new status
            const currentItem = currentNotifications.find(item => item.id === queuedJob.id && item.type === queuedJob.type);
            if (!currentItem) {
                currentNotifications.push({
                    id: queuedJob.id,
                    event: queuedJob.event,
                    eventDescription: this.getEventDescription(queuedJob),
                    title: queuedJob.title,
                    isRead: false,
                    status: queuedJob.status,
                    type: queuedJob.type,
                    elapsedTime: this.getElapsedTime(queuedJob.dateUtcIsoString),
                    createdDateUtcIsoString: queuedJob.dateUtcIsoString
                });
            } else {
                currentItem.elapsedTime = this.getElapsedTime(queuedJob.dateUtcIsoString);
                currentItem.status = queuedJob.status;
                currentItem.eventDescription = this.getEventDescription(queuedJob);
            }
        });
        this.notifications.update(() => [...currentNotifications]);
    }

    /*
    *   Mark all notifications that are not in progress as read.
    *   Called when the tray is closed.
    */
    private markAllNotInProgressAsRead(): void {
        this.notifications.update(items => items.map(item => ({ ...item, isRead: item.status !== JobQueueStatus.InProgress })));
    }

    private getElapsedTime(dateUtcIsoString: string): string {
        const now = new Date().getTime();

        const elapsed = now - Date.parse(dateUtcIsoString);
        const ONE_MIN = 60_000;
        const ONE_HOUR = 60 * ONE_MIN;

        if (elapsed < ONE_MIN) {
            return '<1 min ago';
        } else if (elapsed < 2 * ONE_MIN) {
            return '1 min ago';
        } else if (elapsed < ONE_HOUR) {
            return `${Math.floor(elapsed / ONE_MIN)} mins ago`;
        } else if (elapsed < 2 * ONE_HOUR) {
            return '1 hour ago';
        }

        return `${Math.floor(elapsed / ONE_HOUR)} hours ago`;
    }

    /*
    * This method is used to customize notification event descriptions based on the type of job queue result.
    */
    private getEventDescription(queuedJob: JobQueue): string {
        // Display the new virtual machine password when a virtual machine reset password job is completed.
        if (queuedJob.event === JobQueueEventDescription[JobQueueEvent.ResetVirtualMachinePassword] && queuedJob.status === JobQueueStatus.Completed) {
            return `New password: ${queuedJob.result['virtualmachine']['password']}`;
        }

        // If the job type is CloudInfra VM, display the error code and error text when a job fails.
        if (queuedJob.type === JobQueueType.CloudInfrastructureVm && queuedJob.status === JobQueueStatus.Failed) {
            return `${queuedJob.result['errorcode']} -  ${queuedJob.result['errortext']}`;
        }

        return '';
    }

    ngOnDestroy(): void {
        this.cancelRefreshElapsedTime$.complete();
    }

}
