import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { AdaptiveCloudUsageDetails } from '../../models/adaptive-cloud-usage-details.model';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AdaptiveCloudUsageDetailComponent } from './adaptivecloud-usage-detail.component';

describe('AdaptiveCloudUsageDetailComponent', () => {
    let fixture: ComponentFixture<AdaptiveCloudUsageDetailComponent>;
    let component: AdaptiveCloudUsageDetailComponent;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockAdaptiveCloudUsageService: jasmine.SpyObj<AdaptiveCloudUsageService>;

    const virtualMachineServices = [
        {
            name: 'VM',
            services: []
        }
    ];

    const storageServices = [
        {
            quantity: 4,
            cost: 36,
            unitPrice: 6,
            unitDescription: 'storage',
            type: 'CPU',
            additionalDescription: 'new'
        },
    ];

    const networkServices = [
        {
            quantity: 5,
            cost: 45,
            unitPrice: 9,
            unitDescription: 'network',
            type: 'WIFI',
            additionalDescription: 'expert'
        },
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [AdaptiveCloudUsageDetailComponent],
            providers: [
                provideMock(UserContextService),
                provideMock(AdaptiveCloudUsageService)
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockAdaptiveCloudUsageService = TestBed.inject(AdaptiveCloudUsageService) as jasmine.SpyObj<AdaptiveCloudUsageService>;
        mockAdaptiveCloudUsageService.getUsageDetails.and.returnValue(of({
            data: {
                virtualMachineServices,
                storageServices,
                networkServices
            } as AdaptiveCloudUsageDetails,
            message: 'success'
        }));
        mockUserContextService.currentUser = { organizationId: 15 } as UserContext;

        fixture = TestBed.createComponent(AdaptiveCloudUsageDetailComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should load the data', () => {
            component.selectedAccountId = '1';
            component.selectedPeriod = '202211';
            expect(mockAdaptiveCloudUsageService.getUsageDetails).toHaveBeenCalledTimes(1);
        });

    });

});
