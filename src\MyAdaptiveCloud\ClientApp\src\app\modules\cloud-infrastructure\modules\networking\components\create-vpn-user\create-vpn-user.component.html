<div class="modal-header">
    <h4 class="modal-title">Create VPN User</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form class="form-horizontal" [formGroup]="form">
        <div class="row mb-3">
            <label for="name" class="col-3 col-form-label">User Name
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'User name has to begin with an alphabet have 3-64 characters including alphanumeric and the set @+=._-'" triggers="hover" placement="right"
                    container="body">
                </i>
                <span class="required-asterisk">*</span>
            </label>
            <div class="col">
                <input class="form-control" formControlName="userName" data-testid="vpn-user-name-input"
                    [class]="{ 'is-invalid': form.controls.userName.invalid && form.controls.userName.dirty }"
                    autocomplete="off" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="description" class="col-3 col-form-label">Password
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Password has to be 3-32 characters including alphanumeric and the set @+=._-'"
                    triggers="hover" placement="right" container="body">
                </i>
                <span
                    class="required-asterisk">*</span></label>
            <div class="col">
                <input class="form-control" formControlName="password" data-testid="vpn-password-input"
                    [class]="{ 'is-invalid': form.controls.password.invalid && form.controls.password.dirty }"
                    autocomplete="off" />
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="form.invalid" (submitClickEvent)="submitForm()">OK
    </app-btn-submit>
</div>
