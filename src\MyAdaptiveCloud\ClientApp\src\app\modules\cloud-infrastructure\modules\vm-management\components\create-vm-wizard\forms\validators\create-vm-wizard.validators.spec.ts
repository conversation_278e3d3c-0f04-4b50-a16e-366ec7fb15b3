import { FormControl, FormGroup } from '@angular/forms';
import { VmNetwork } from '../../../../models/vm-network.model';
import { macAddressValidator, vmIpCidrValidator } from './create-vm-wizard.validators';

export const mockVmNetwork: VmNetwork = {
    id: '1',
    name: 'Test Network',
    cidr: '***********/24',
    type: 'Private',
    vpcname: 'Test VPC',
    ipaddress: '***********2',
    macaddress: '00:11:22:33:44:55',
    gateway: '***********'
};

describe('CreateVmWizardValidators', () => {
    describe('ipAddressValidator', () => {
        it('should return null if IP address is valid and different from gateway', () => {

            const validator = vmIpCidrValidator([mockVmNetwork]);

            const selectNetworkForm = new FormGroup({
                networkSelectedId: new FormControl<string | null>(mockVmNetwork.id),
                ipAddress: new FormControl<string | null>(mockVmNetwork.ipaddress),
                macAddress: new FormControl<string | null>(mockVmNetwork.macaddress)
            });

            const result = validator(selectNetworkForm);
            expect(result).toBeNull();
        });

        it('should return { invalidIpAddress: true } if IP address is invalid', () => {
            const selectedNetwork = mockVmNetwork;
            const validator = vmIpCidrValidator([selectedNetwork]);

            const selectNetworkForm = new FormGroup({
                networkSelectedId: new FormControl<string | null>(mockVmNetwork.id),
                ipAddress: new FormControl<string | null>('***********'),
                macAddress: new FormControl<string | null>(mockVmNetwork.macaddress)
            });

            const result = validator(selectNetworkForm);
            expect(result).toEqual({ invalidIpAddress: true });
        });

        it('should return { invalidIpAddress: true } if IP address is the same as gateway', () => {
            const selectedNetwork = mockVmNetwork;
            const validator = vmIpCidrValidator([selectedNetwork]);

            const selectNetworkForm = new FormGroup({
                networkSelectedId: new FormControl<string | null>(mockVmNetwork.id),
                ipAddress: new FormControl<string | null>('***********'),
                macAddress: new FormControl<string | null>(mockVmNetwork.macaddress)
            });

            const result = validator(selectNetworkForm);
            expect(result).toEqual({ invalidIpAddress: true });
        });

        it('should return null if IP address is empty', () => {
            const selectedNetwork = mockVmNetwork;
            const validator = vmIpCidrValidator([selectedNetwork]);

            const selectNetworkForm = new FormGroup({
                networkSelectedId: new FormControl<string | null>(mockVmNetwork.id),
                ipAddress: new FormControl<string | null>(''),
                macAddress: new FormControl<string | null>(mockVmNetwork.macaddress)
            });

            const result = validator(selectNetworkForm);
            expect(result).toBeNull();
        });
    });

    describe('macAddressValidator', () => {
        it('should return null if MAC address is valid', () => {
            const validator = macAddressValidator();
            const control = new FormControl('00:11:22:33:44:55');

            const result = validator(control);
            expect(result).toBeNull();
        });

        it('should return { invalidMacAddress: true } if MAC address is invalid', () => {
            const validator = macAddressValidator();
            const control = new FormControl('00:11:22:33:44:ZZ');

            const result = validator(control);
            expect(result).toEqual({ invalidMacAddress: true });
        });

        it('should return null if MAC address is empty', () => {
            const validator = macAddressValidator();
            const control = new FormControl('');

            const result = validator(control);
            expect(result).toBeNull();
        });
    });
});
