import { TestBed } from '@angular/core/testing';
import { Organization } from '@app/shared/models/organization.model';
import { Device } from './device';
import { DeviceFolder } from './device-folder';
import { DeviceFolderTreeNode } from './device-folder-tree-node';
import { DeviceTreeNode } from './device-tree-node';
import { DraggingItemsSelection } from './dragging-items-selection';
import { OrganizationFolderTreeNode } from './organization-folder-tree-node';
import { UnassignedDevicesFolderTreeNode } from './unassigned-devices-folder-tree-node';

const ROOT_FOLDER_ID = 0;
describe('DeviceFolderTreeNode', () => {
    let rootOrganization: Partial<Organization>;
    beforeEach(() => {

        TestBed.configureTestingModule({
            providers: [
                DeviceFolderTreeNode
            ]
        });

        rootOrganization = { name: 'Root Folder', organizationId: ROOT_FOLDER_ID };
    });

    describe('ids', () => {
        it('Folder and Organization unique string id should be different even when ids are equal', () => {
            const sameId = 1;
            const folder = new DeviceFolderTreeNode(({ folderId: sameId, name: 'Folder1' }));
            const unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(sameId);
            const organizationFolder = new OrganizationFolderTreeNode({ name: 'Root Folder', organizationId: sameId });

            expect(folder.getUniqueStringId()).not.toEqual(organizationFolder.getUniqueStringId());
            expect(unassignedDevicesFolder.getUniqueStringId()).not.toEqual(organizationFolder.getUniqueStringId());
            expect(folder.getUniqueStringId()).not.toEqual(unassignedDevicesFolder.getUniqueStringId());
        });
    });

    describe('findFolderById', () => {
        let rootFolder: OrganizationFolderTreeNode;

        beforeEach(() => {
            const subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Sub Folder 1' }));
            const subSubFolder = new DeviceFolderTreeNode(({ folderId: 4, name: 'Sub Sub Folder' }));
            const subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Sub Folder 2' }), null, null, [subSubFolder]);

            rootFolder = new OrganizationFolderTreeNode(rootOrganization, [subFolder1, subFolder2]);
        });

        it('should return root folder when folderId matches rootFolder.folderId', () => {
            const folderId = 0;
            const result = rootFolder.findFolderById(folderId);
            expect(result).toBe(rootFolder);
        });

        it('should return sub folder when folderId matches subFolder.folderId', () => {
            const folderId = 2;
            const result = rootFolder.findFolderById(folderId);
            expect(result.folderId).toBe(folderId);
        });

        it('should return sub sub folder when folderId matches subSubFolder.folderId', () => {
            const folderId = 4;
            const result = rootFolder.findFolderById(folderId);
            expect(result.folderId).toBe(folderId);
        });

        it('should return null when folderId does not exist in the folder structure', () => {
            const folderId = 5;
            const result = rootFolder.findFolderById(folderId);
            expect(result).toBeNull();
        });
    });

    describe('findRootFolder', () => {

        let subFolder1: DeviceFolderTreeNode,
            subFolder2: DeviceFolderTreeNode,
            subFolder21: DeviceFolderTreeNode,
            subFolder211: DeviceFolderTreeNode,
            rootFolders: DeviceFolderTreeNode;

        beforeEach(() => {
            subFolder211 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 211' }));
            subFolder21 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 21' }), null, null, [subFolder211]);
            subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }));
            subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }), null, null, [subFolder21]);

            rootFolders = new OrganizationFolderTreeNode(rootOrganization, [subFolder1, subFolder2]);
        });

        it('should return root folder when folder level is 1', () => {
            const result = subFolder1.findRootFolder();
            expect(result.folderId).toBe(rootFolders.folderId);
        });

        it('should return root folder when folder is at level 2', () => {
            const result = subFolder21.findRootFolder();
            expect(result.folderId).toBe(rootFolders.folderId);
        });

        it('should return root folder when folder is at level 3', () => {
            const result = subFolder211.findRootFolder();
            expect(result.folderId).toBe(rootFolders.folderId);
        });

    });

    describe('removeSubfolder', () => {
        let rootFolder: OrganizationFolderTreeNode,
            subFolder1: DeviceFolderTreeNode,
            subFolder2: DeviceFolderTreeNode,
            subFolder21: DeviceFolderTreeNode,
            subFolder211: DeviceFolderTreeNode;

        beforeEach(() => {
            subFolder211 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 211' }));
            subFolder21 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 21' }), null, null, [subFolder211]);
            subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }));
            subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }), null, null, [subFolder21]);

            rootFolder = new OrganizationFolderTreeNode(rootOrganization, [subFolder1, subFolder2]);
        });

        it('should remove the specified subfolder from the subFolders array', () => {
            rootFolder.removeSubfolder(subFolder1);
            expect(rootFolder.getSubFolders().includes(subFolder1)).toBe(false);
        });

        it('should update the "hasSubfolders" and "isExpanded" property if the subFolders array becomes empty', () => {
            subFolder2.removeSubfolder(subFolder21);
            expect(subFolder2.hasSubfolders()).toBe(false);
            expect(subFolder2.isExpanded()).toBe(false);
        });

        it('should not update the "hasSubfolders" property if the subFolders array still has elements', () => {
            rootFolder.removeSubfolder(subFolder1);
            const isExpanded = rootFolder.isExpanded();
            expect(rootFolder.getSubFolders().includes(subFolder1)).toBe(false);
            expect(rootFolder.hasSubfolders()).toBe(true);
            expect(rootFolder.isExpanded()).toBe(isExpanded);
        });
        it('should not modify the subFolders array if the specified folder is not found', () => {
            const folderToRemove = new DeviceFolderTreeNode();
            folderToRemove.folderId = 5;
            folderToRemove.name.set('Non-existent Folder');
            rootFolder.removeSubfolder(folderToRemove);
            expect(rootFolder.getSubFolders().length).toBe(2);
        });

    });

    describe('addSuborganization', () => {
        let rootFolder: OrganizationFolderTreeNode;

        beforeEach(() => {
            rootFolder = new OrganizationFolderTreeNode(rootOrganization);
        });

        it('should add the suborganization', () => {
            const subOrg1 = new OrganizationFolderTreeNode({ organizationId: 2, name: 'Org 1' });
            rootFolder.addSubfolder(subOrg1);

            expect(rootFolder.getSubFolders().includes(subOrg1)).toBeTrue();
            expect(rootFolder.hasSubfolders()).toBeTrue();
            expect(subOrg1.parent()).toBe(rootFolder);
            expect(subOrg1.level()).toBe(rootFolder.level() + 1);
        });

        it('should add and sort suborganization', () => {
            const subOrgA = new OrganizationFolderTreeNode({ organizationId: 2, name: 'A' });
            rootFolder.addSubfolders([subOrgA]);

            expect(rootFolder.getSubFolders().includes(subOrgA)).toBeTrue();
            expect(rootFolder.getSubFolders().length).toBe(1);
            expect(rootFolder.hasSubfolders()).toBeTrue();
            expect(subOrgA.parent()).toBe(rootFolder);
            expect(subOrgA.level()).toBe(rootFolder.level() + 1);
            expect(rootFolder.getSubFolders()[0]).toBe(subOrgA);
        });

        it('should add and sort suborganizations and folders', () => {
            const subFolderA = new DeviceFolderTreeNode(({ folderId: 2, name: 'A' }));
            const subFolderZ = new DeviceFolderTreeNode(({ folderId: 3, name: 'Z' }));
            const subOrgA = new OrganizationFolderTreeNode({ organizationId: 4, name: 'A' });
            const subOrgZ = new OrganizationFolderTreeNode({ organizationId: 5, name: 'Z' });
            rootFolder.addSubfolders([subFolderA, subFolderZ, subOrgA]);

            rootFolder.addSubfolder(subOrgZ);
            expect(rootFolder.getSubFolders().includes(subOrgZ)).toBeTrue();
            expect(rootFolder.getSubFolders().length).toBe(4);
            expect(rootFolder.hasSubfolders()).toBeTrue();
            expect(subOrgZ.parent()).toBe(rootFolder);
            expect(subOrgZ.level()).toBe(rootFolder.level() + 1);
            expect(rootFolder.getSubFolders()[0]).toBe(subOrgA);
            expect(rootFolder.getSubFolders()[1]).toBe(subOrgZ);
            expect(rootFolder.getSubFolders()[2]).toBe(subFolderA);
            expect(rootFolder.getSubFolders()[3]).toBe(subFolderZ);
        });
    });

    describe('addSubfolder', () => {
        let rootFolder: OrganizationFolderTreeNode;

        beforeEach(() => {
            rootFolder = new OrganizationFolderTreeNode(rootOrganization);
        });

        it('should add the subfolder', () => {
            const subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }));
            rootFolder.addSubfolder(subFolder1);

            expect(rootFolder.getSubFolders().includes(subFolder1)).toBe(true);
            expect(rootFolder.hasSubfolders()).toBe(true);
            expect(subFolder1.parent()).toBe(rootFolder);
            expect(subFolder1.getParentId()).toBe(rootFolder.folderId);
            expect(subFolder1.level()).toBe(rootFolder.level() + 1);
        });

        it('should add and sort the subfolders when DD Folder is present', () => {
            const subFolderA = new DeviceFolderTreeNode(({ folderId: 2, name: 'A' }));
            const subFolderZ = new DeviceFolderTreeNode(({ folderId: 3, name: 'Z' }));
            const newDevicesFolder = new UnassignedDevicesFolderTreeNode(ROOT_FOLDER_ID);
            rootFolder.addSubfolders([subFolderA, newDevicesFolder]);

            rootFolder.addSubfolder(subFolderZ);
            expect(rootFolder.getSubFolders().includes(subFolderZ)).toBeTrue();
            expect(rootFolder.getSubFolders().length).toBe(3);
            expect(rootFolder.hasSubfolders()).toBeTrue();
            expect(subFolderZ.parent()).toBe(rootFolder);
            expect(subFolderZ.getParentId()).toBe(rootFolder.folderId);
            expect(subFolderZ.level()).toBe(rootFolder.level() + 1);
            expect(rootFolder.getSubFolders()[0]).toBe(newDevicesFolder);
            expect(rootFolder.getSubFolders()[1]).toBe(subFolderA);
            expect(rootFolder.getSubFolders()[2]).toBe(subFolderZ);
        });

        it('should add and sort the subfolders when DD Folder is not present', () => {
            const subFolderA = new DeviceFolderTreeNode(({ folderId: 2, name: 'A' }));
            const subFolderZ = new DeviceFolderTreeNode(({ folderId: 3, name: 'Z' }));
            const subFolderC = new DeviceFolderTreeNode(({ folderId: 4, name: 'C' }));
            rootFolder.addSubfolders([subFolderA, subFolderZ]);

            rootFolder.addSubfolder(subFolderC);
            expect(rootFolder.getSubFolders().includes(subFolderC)).toBeTrue();
            expect(rootFolder.getSubFolders().length).toBe(3);
            expect(rootFolder.hasSubfolders()).toBeTrue();
            expect(subFolderC.parent()).toBe(rootFolder);
            expect(subFolderC.getParentId()).toBe(rootFolder.folderId);
            expect(subFolderC.level()).toBe(rootFolder.level() + 1);
            expect(rootFolder.getSubFolders()[0]).toBe(subFolderA);
            expect(rootFolder.getSubFolders()[1]).toBe(subFolderC);
            expect(rootFolder.getSubFolders()[2]).toBe(subFolderZ);
        });

        it('should add and update subfolders level', () => {
            const folderA = new DeviceFolderTreeNode(({ folderId: 2, name: 'A' }));
            const folderC = new DeviceFolderTreeNode(({ folderId: 4, name: 'C' }));
            const folderB = new DeviceFolderTreeNode(({ folderId: 3, name: 'B' }), null, null, [folderC]);
            rootFolder.addSubfolders([folderA, folderB]);

            folderA.addSubfolder(folderB);
            expect(folderA.getSubFolders().includes(folderB)).toBeTrue();
            expect(folderA.getSubFolders().length).toBe(1);
            expect(folderA.hasSubfolders()).toBeTrue();

            expect(folderB.parent()).toBe(folderA);
            expect(folderB.getParentId()).toBe(folderA.folderId);
            expect(folderB.level()).toBe(folderA.level() + 1);

            expect(folderC.level()).toBe(folderA.level() + 2);

        });
    });

    describe('setSubfolders', () => {
        let rootFolder: OrganizationFolderTreeNode;

        beforeEach(() => {
            rootFolder = new OrganizationFolderTreeNode(rootOrganization);
        });

        it('should set the subfolders', () => {
            const subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }));
            const subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }));

            rootFolder.setSubfolders([subFolder1, subFolder2]);

            expect(rootFolder.getSubFolders().includes(subFolder1)).toBe(true);
            expect(rootFolder.getSubFolders().includes(subFolder2)).toBe(true);
            expect(rootFolder.hasSubfolders()).toBe(true);
            expect(rootFolder.getSubFolders().length).toBe(2);
            expect(subFolder1.parent()).toBe(rootFolder);
            expect(subFolder1.getParentId()).toBe(rootFolder.folderId);
            expect(subFolder1.level()).toBe(rootFolder.level() + 1);
            expect(subFolder2.parent()).toBe(rootFolder);
            expect(subFolder2.getParentId()).toBe(rootFolder.folderId);
            expect(subFolder2.level()).toBe(rootFolder.level() + 1);
        });

        it('should set the subfolders when there are other subfolders present', () => {
            const subFolder3 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 3' }));
            const subFolder4 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 4' }));

            rootFolder.addSubfolders([subFolder3, subFolder4]);

            const subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }));
            const subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }));

            rootFolder.setSubfolders([subFolder1, subFolder2]);

            expect(rootFolder.getSubFolders().includes(subFolder1)).toBe(true);
            expect(rootFolder.getSubFolders().includes(subFolder2)).toBe(true);
            expect(rootFolder.hasSubfolders()).toBe(true);
            expect(rootFolder.getSubFolders().length).toBe(2);
            expect(subFolder1.parent()).toBe(rootFolder);
            expect(subFolder1.getParentId()).toBe(rootFolder.folderId);
            expect(subFolder1.level()).toBe(rootFolder.level() + 1);
            expect(subFolder2.parent()).toBe(rootFolder);
            expect(subFolder2.getParentId()).toBe(rootFolder.folderId);
            expect(subFolder2.level()).toBe(rootFolder.level() + 1);
        });
    });

    describe('addDevice', () => {
        let folder: DeviceFolderTreeNode;

        beforeEach(() => {
            folder = new DeviceFolderTreeNode(({ folderId: 1, name: 'Folder' }));
        });

        it('should add the device', () => {
            const device = new DeviceTreeNode({ agentId: 2, hostname: 'Device 2' } as Device);

            const devicesCount = folder.folderDevices()?.length ?? 0;
            folder.addDevice(device);
            expect(folder.folderDevices().includes(device)).toBe(true);
            expect(folder.folderDevices().length).toBe(devicesCount + 1);
            expect(device.parent()).toBe(folder);
        });
    });

    describe('sortSubfolders', () => {
        let rootFolder: OrganizationFolderTreeNode,
            subFolderA: DeviceFolderTreeNode,
            subFolderZ: DeviceFolderTreeNode,
            subOrganizationA: OrganizationFolderTreeNode,
            subOrganizationZ: OrganizationFolderTreeNode,
            unassignedDevicesFolder: UnassignedDevicesFolderTreeNode;

        beforeEach(() => {
            subFolderA = new DeviceFolderTreeNode(({ folderId: 2, name: 'A' }));
            subFolderZ = new DeviceFolderTreeNode(({ folderId: 3, name: 'Z' }));
            unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(ROOT_FOLDER_ID);
            subOrganizationA = new OrganizationFolderTreeNode({ name: 'A', organizationId: 4 });
            subOrganizationZ = new OrganizationFolderTreeNode({ name: 'Z', organizationId: 5 });
            rootFolder = new OrganizationFolderTreeNode(rootOrganization);
        });

        it('should sort unordered suborganizations', () => {
            rootFolder.addSubfolders([subOrganizationZ, subOrganizationA]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].getId()).toBe(subOrganizationA.getId());
            expect(rootFolder.getSubFolders()[1].getId()).toBe(subOrganizationZ.getId());
            expect(rootFolder.getSubFolders().length).toBe(2);
        });

        it('should sort unordered suborganizations with ordered folders', () => {
            rootFolder.addSubfolders([subOrganizationZ, subOrganizationA, unassignedDevicesFolder, subFolderA, subFolderZ]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].getId()).toBe(subOrganizationA.getId());
            expect(rootFolder.getSubFolders()[1].getId()).toBe(subOrganizationZ.getId());
            expect(rootFolder.getSubFolders()[2].getId()).toBe(unassignedDevicesFolder.getId());
            expect(rootFolder.getSubFolders()[3].getId()).toBe(subFolderA.getId());
            expect(rootFolder.getSubFolders()[4].getId()).toBe(subFolderZ.getId());
            expect(rootFolder.getSubFolders().length).toBe(5);
        });

        it('should sort unordered suborganizations and folders', () => {
            rootFolder.addSubfolders([subFolderZ, subOrganizationZ, subFolderA, unassignedDevicesFolder, subOrganizationA]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].getId()).toBe(subOrganizationA.getId());
            expect(rootFolder.getSubFolders()[1].getId()).toBe(subOrganizationZ.getId());
            expect(rootFolder.getSubFolders()[2].getId()).toBe(unassignedDevicesFolder.getId());
            expect(rootFolder.getSubFolders()[3].getId()).toBe(subFolderA.getId());
            expect(rootFolder.getSubFolders()[4].getId()).toBe(subFolderZ.getId());
            expect(rootFolder.getSubFolders().length).toBe(5);
        });

        it('should sort unordered subfolders with devices folder', () => {
            rootFolder.addSubfolders([subFolderZ, subFolderA, unassignedDevicesFolder]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].folderId).toBe(unassignedDevicesFolder.folderId);
            expect(rootFolder.getSubFolders()[1].folderId).toBe(subFolderA.folderId);
            expect(rootFolder.getSubFolders()[2].folderId).toBe(subFolderZ.folderId);
            expect(rootFolder.getSubFolders().length).toBe(3);
        });

        it('should sort orderer devices folder', () => {
            rootFolder.addSubfolders([unassignedDevicesFolder, subFolderZ, subFolderA]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].folderId).toBe(unassignedDevicesFolder.folderId);
            expect(rootFolder.getSubFolders()[1].folderId).toBe(subFolderA.folderId);
            expect(rootFolder.getSubFolders()[2].folderId).toBe(subFolderZ.folderId);
            expect(rootFolder.getSubFolders().length).toBe(3);
        });

        it('should sort orderer subfolders with devices folder', () => {
            rootFolder.addSubfolders([unassignedDevicesFolder, subFolderA, subFolderZ]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].folderId).toBe(unassignedDevicesFolder.folderId);
            expect(rootFolder.getSubFolders()[1].folderId).toBe(subFolderA.folderId);
            expect(rootFolder.getSubFolders()[2].folderId).toBe(subFolderZ.folderId);
            expect(rootFolder.getSubFolders().length).toBe(3);
        });

        it('should sort unordered subfolders without devices folder', () => {
            rootFolder.addSubfolders([subFolderZ, subFolderA]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].folderId).toBe(subFolderA.folderId);
            expect(rootFolder.getSubFolders()[1].folderId).toBe(subFolderZ.folderId);
            expect(rootFolder.getSubFolders().length).toBe(2);
        });

        it('should sort orderer subfolders without devices folder', () => {
            rootFolder.addSubfolders([subFolderA, subFolderZ]);

            rootFolder.sortSubfolders();

            expect(rootFolder.getSubFolders()[0].folderId).toBe(subFolderA.folderId);
            expect(rootFolder.getSubFolders()[1].folderId).toBe(subFolderZ.folderId);
            expect(rootFolder.getSubFolders().length).toBe(2);
        });
    });

    describe('removeDevice', () => {
        let folder: DeviceFolderTreeNode;
        let device: DeviceTreeNode;
        let device2: DeviceTreeNode;

        beforeEach(() => {
            folder = new DeviceFolderTreeNode(({ folderId: 1, name: 'Folder' } as DeviceFolder));
            device = new DeviceTreeNode({ agentId: 2, hostname: 'Device 1' } as Device, folder);
            device2 = new DeviceTreeNode({ agentId: 3, hostname: 'Device 2' } as Device, folder);
            folder.addDevices([device, device2]);
        });

        it('should remove the specified device from the devices array', () => {
            folder.removeDevice(device);
            expect(folder.folderDevices().length).toBe(1);
            expect(folder.folderDevices().includes(device)).toBeFalse();
            expect(folder.deviceCountOnlySelf()).toBe(1);
            expect(device.parent()).toBeNull();
        });

        it('should remove all devices', () => {
            folder.removeDevice(device2);
            folder.removeDevice(device);
            expect(folder.folderDevices().length).toBe(0);
        });

        it('should not modify the devices array if the specified device is not found', () => {
            const deviceToRemove = new DeviceTreeNode();
            deviceToRemove.agentId = 15;
            deviceToRemove.description.set('Non-existent Folder');
            folder.removeDevice(deviceToRemove);
            expect(folder.folderDevices().length).toBe(2);
        });

    });

    describe('pathFromRoot and findRootFolder with suborganizations', () => {
        let rootFolder: OrganizationFolderTreeNode,
            subOrganization1: OrganizationFolderTreeNode,
            subOrganization2: OrganizationFolderTreeNode,
            subFolder21: DeviceFolderTreeNode,
            subFolder211: DeviceFolderTreeNode;

        beforeEach(() => {
            subFolder211 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 211' }));
            subFolder21 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 21' }), null, null, [subFolder211]);
            subOrganization1 = new OrganizationFolderTreeNode({ organizationId: 2, name: 'Org 1' });
            subOrganization2 = new OrganizationFolderTreeNode({ organizationId: 3, name: 'Org 2' }, [subFolder21]);

            rootFolder = new OrganizationFolderTreeNode(rootOrganization, [subOrganization1, subOrganization2]);
        });

        it('pathFromRoot should return 4 nodes', () => {
            const path = subFolder211.pathFromRoot();

            expect(path.length).toBe(4);

            expect(path[0]).toBe(rootFolder);
            expect(path[1]).toBe(subOrganization2);
            expect(path[2]).toBe(subFolder21);
            expect(path[3]).toBe(subFolder211);
        });

        it('pathFromRoot should return 2 nodes', () => {
            const path = subOrganization2.pathFromRoot();

            expect(path.length).toBe(2);
            expect(path[0]).toBe(rootFolder);
            expect(path[1]).toBe(subOrganization2);
        });

        it('pathFromRoot should return rootFolder', () => {
            const path = rootFolder.pathFromRoot();

            expect(path.length).toBe(1);
            expect(path[0]).toBe(rootFolder);
        });

        it('findRootFolder should return rootFolder', () => {
            const root = subFolder211.findRootFolder();
            expect(root).toBe(rootFolder);
        });

        it('findRootFolder should return rootFolder', () => {
            const root = subOrganization1.findRootFolder();
            expect(root).toBe(rootFolder);
        });
    });

    describe('pathFromRoot and findRootFolder', () => {
        let rootFolder: OrganizationFolderTreeNode,
            subFolder1: DeviceFolderTreeNode,
            subFolder2: DeviceFolderTreeNode,
            subFolder21: DeviceFolderTreeNode,
            subFolder211: DeviceFolderTreeNode;

        beforeEach(() => {
            subFolder211 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 211' }));
            subFolder21 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 21' }), null, null, [subFolder211]);
            subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }));
            subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }), null, null, [subFolder21]);

            rootFolder = new OrganizationFolderTreeNode(rootOrganization, [subFolder1, subFolder2]);
        });

        it('pathFromRoot should return 4 nodes', () => {
            const path = subFolder211.pathFromRoot();

            expect(path.length).toBe(4);

            expect(path[0]).toBe(rootFolder);
            expect(path[1]).toBe(subFolder2);
            expect(path[2]).toBe(subFolder21);
            expect(path[3]).toBe(subFolder211);
        });

        it('pathFromRoot should return 2 nodes', () => {
            const path = subFolder2.pathFromRoot();

            expect(path.length).toBe(2);
            expect(path[0]).toBe(rootFolder);
            expect(path[1]).toBe(subFolder2);
        });

        it('pathFromRoot should return rootFolder', () => {
            const path = rootFolder.pathFromRoot();

            expect(path.length).toBe(1);
            expect(path[0]).toBe(rootFolder);
        });

        it('findRootFolder should return rootFolder', () => {
            const root = subFolder211.findRootFolder();
            expect(root).toBe(rootFolder);
        });

        it('findRootFolder should return rootFolder', () => {
            const root = subFolder1.findRootFolder();
            expect(root).toBe(rootFolder);
        });
    });

    describe('canReceiveItems', () => {
        let rootFolder: OrganizationFolderTreeNode,
            unassignedDevicesFolder: UnassignedDevicesFolderTreeNode,
            subFolder2: DeviceFolderTreeNode,
            subFolder21: DeviceFolderTreeNode,
            subFolder22: DeviceFolderTreeNode,
            subFolder211: DeviceFolderTreeNode,
            device: DeviceTreeNode,
            device2: DeviceTreeNode;

        beforeEach(() => {
            unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(ROOT_FOLDER_ID);
            subFolder211 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 211' }));
            subFolder21 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 21' }), null, null, [subFolder211]);
            subFolder22 = new DeviceFolderTreeNode(({ folderId: 32, name: 'Folder 22' }));
            subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }), null, null, [subFolder22, subFolder21]);

            device = new DeviceTreeNode({ agentId: 2, hostname: 'Device 1', orgId: ROOT_FOLDER_ID } as Device);
            device2 = new DeviceTreeNode({ agentId: 3, hostname: 'Device 2', orgId: ROOT_FOLDER_ID } as Device);

            subFolder21.addDevices([device, device2]);

            rootFolder = new OrganizationFolderTreeNode(rootOrganization, [unassignedDevicesFolder, subFolder2]);
        });

        it('new device folder can not receive items', () => {
            const selectedItems = new DraggingItemsSelection([], [subFolder2], subFolder2);
            const canReceiveItems = unassignedDevicesFolder.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('device can not be moved to root', () => {
            const selectedItems = new DraggingItemsSelection([device], [], device);
            const canReceiveItems = rootFolder.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('device can not be moved to the same folder it is now', () => {
            const selectedItems = new DraggingItemsSelection([device], [], device);
            const canReceiveItems = subFolder21.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('folder can not be moved to the same folder it is now', () => {
            const selectedItems = new DraggingItemsSelection([], [subFolder21], subFolder21);
            const canReceiveItems = subFolder2.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('folder can not be moved to the same root folder it is now', () => {
            const selectedItems = new DraggingItemsSelection([], [subFolder2], subFolder2);
            const canReceiveItems = rootFolder.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('folder can not be moved to a child', () => {
            const selectedItems = new DraggingItemsSelection([], [subFolder2], subFolder2);
            const canReceiveItems = subFolder211.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('devices can be moved', () => {
            const selectedItems = new DraggingItemsSelection([device, device2], [], device2);
            const canReceiveItems = subFolder211.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeTrue();
        });

        it('folders and devices can be moved', () => {
            const selectedItems = new DraggingItemsSelection([device, device2], [subFolder21], device2);
            const canReceiveItems = subFolder22.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeTrue();
        });

        it('devices from another organization can not be moved', () => {
            device.orgId = 3;
            const selectedItems = new DraggingItemsSelection([device, device2], [], device2);
            const canReceiveItems = subFolder211.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('folders from another organization can not be moved', () => {
            const folderFromAnotherOrg = new DeviceFolderTreeNode(({ folderId: 31, name: 'Folder' }), null, null);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const anotherOrg = new OrganizationFolderTreeNode({ name: 'Org', organizationId: 13 }, [folderFromAnotherOrg]);

            const selectedItems = new DraggingItemsSelection([], [folderFromAnotherOrg], folderFromAnotherOrg);
            const canReceiveItems = subFolder22.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

        it('An organization can not receive items', () => {
            const anOrg = new OrganizationFolderTreeNode({ name: 'Org', organizationId: 13 });

            const selectedItems = new DraggingItemsSelection([device, device2], [], device2);
            const canReceiveItems = anOrg.canReceiveItems(selectedItems);
            expect(canReceiveItems).toBeFalse();
        });

    });

    describe('selection', () => {
        let rootFolder: OrganizationFolderTreeNode,
            subFolder1: DeviceFolderTreeNode,
            subFolder2: DeviceFolderTreeNode,
            subFolder21: DeviceFolderTreeNode,
            subFolder211: DeviceFolderTreeNode,
            device: DeviceTreeNode,
            device2: DeviceTreeNode;

        beforeEach(() => {
            subFolder211 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 211' }));
            subFolder21 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 21' }), null, null, [subFolder211]);
            subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }));
            subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }), null, null, [subFolder21]);

            device = new DeviceTreeNode();
            device.agentId = 2;
            device.hostname = 'Device 1';
            device.parent.set(subFolder21);

            device2 = new DeviceTreeNode();
            device2.agentId = 3;
            device2.hostname = 'Device 2';
            device2.parent.set(subFolder21);

            subFolder21.addDevices([device, device2]);

            rootFolder = new OrganizationFolderTreeNode(rootOrganization, [subFolder1, subFolder2]);
        });

        it('deselectSubfolders should deselect', () => {
            subFolder1.isSelected.set(true);
            subFolder2.isSelected.set(true);
            subFolder211.isSelected.set(true);
            rootFolder.deselectSubfolders();

            expect(subFolder1.isSelected()).toBeFalse();
            expect(subFolder2.isSelected()).toBeFalse();
            expect(subFolder211.isSelected()).toBeTrue();
        });

        it('deselectDevices should deselect', () => {
            device.isSelected.set(true);
            device2.isSelected.set(true);
            subFolder21.deselectDevices();

            expect(device.isSelected()).toBeFalse();
            expect(device2.isSelected()).toBeFalse();
        });

        it('toggleSelected should select/deselect', () => {
            subFolder21.isSelected.set(false);
            subFolder21.toggleSelected();

            expect(subFolder21.isSelected()).toBeTrue();
            subFolder21.toggleSelected();
            expect(subFolder21.isSelected()).toBeFalse();
        });

    });

    describe('forEach', () => {
        let rootFolder: OrganizationFolderTreeNode,
            subFolder1: DeviceFolderTreeNode,
            subFolder11: DeviceFolderTreeNode,
            subFolder12: DeviceFolderTreeNode,
            subFolder2: DeviceFolderTreeNode,
            subFolder21: DeviceFolderTreeNode,
            subFolder211: DeviceFolderTreeNode;

        beforeEach(() => {
            subFolder11 = new DeviceFolderTreeNode(({ folderId: 7, name: 'Folder 11' }));
            subFolder12 = new DeviceFolderTreeNode(({ folderId: 6, name: 'Folder 12' }));

            subFolder211 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 211' }));
            subFolder21 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 21' }), null, null, [subFolder211]);
            subFolder1 = new DeviceFolderTreeNode(({ folderId: 2, name: 'Folder 1' }), null, null, [subFolder11, subFolder12]);
            subFolder2 = new DeviceFolderTreeNode(({ folderId: 3, name: 'Folder 2' }), null, null, [subFolder21]);

            rootFolder = new OrganizationFolderTreeNode(rootOrganization, [subFolder1, subFolder2]);
        });

        it('should cover all nodes', () => {
            const coveredNodesId = [];
            rootFolder.forEach(node => coveredNodesId.push(node.getId()));

            expect(coveredNodesId.length).toBe(7);
            expect(coveredNodesId.includes(rootFolder.getId())).toBeTrue();
            expect(coveredNodesId.includes(subFolder1.getId())).toBeTrue();
            expect(coveredNodesId.includes(subFolder2.getId())).toBeTrue();
            expect(coveredNodesId.includes(subFolder11.getId())).toBeTrue();
            expect(coveredNodesId.includes(subFolder12.getId())).toBeTrue();
            expect(coveredNodesId.includes(subFolder211.getId())).toBeTrue();
            expect(coveredNodesId.includes(subFolder21.getId())).toBeTrue();
        });

        it('should be called with the correct node level', () => {
            const nodeIdLevelDic = new Map<number, number>();
            rootFolder.forEach((node, level) => nodeIdLevelDic.set(node.getId(), level));

            expect(nodeIdLevelDic.get(rootFolder.getId())).toBe(0);
            expect(nodeIdLevelDic.get(subFolder1.getId())).toBe(1);
            expect(nodeIdLevelDic.get(subFolder2.getId())).toBe(1);
            expect(nodeIdLevelDic.get(subFolder11.getId())).toBe(2);
            expect(nodeIdLevelDic.get(subFolder12.getId())).toBe(2);
            expect(nodeIdLevelDic.get(subFolder211.getId())).toBe(3);
            expect(nodeIdLevelDic.get(subFolder21.getId())).toBe(2);
        });
    });

});
