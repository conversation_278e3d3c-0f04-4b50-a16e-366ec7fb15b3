import { Injectable, inject } from '@angular/core';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';
import { LoginRequest } from '../requests/login.request';

@Injectable({
    providedIn: 'root'
})
export class LocalAuthenticationService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'localauthentication';

    login(request: LoginRequest): Observable<void> {
        return this.apiService.post<null, LoginRequest>(`${this.endpoint}/authenticate`, request);
    }

}
