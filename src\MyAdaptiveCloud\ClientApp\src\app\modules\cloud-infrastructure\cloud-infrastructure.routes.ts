import { Routes } from '@angular/router';
import { AllowIfMenuItemIsPresentGuard } from '@app/shared/guards/allow-if-menu-item-is-present.guard';
import { cloudInfrastructureCredentialsGuard } from './guards/cloud-infrastructure-credentials.guard';
import { ROUTE_SEGMENTS } from './models/route-segments';

export const routes: Routes = [
    {
        path: ROUTE_SEGMENTS.VM_MANAGEMENT,
        loadChildren: () => import('./modules/vm-management/vm-management.routes').then(m => m.vmRoutes),
        canActivate: [AllowIfMenuItemIsPresentGuard, cloudInfrastructureCredentialsGuard]
    },
    {
        path: ROUTE_SEGMENTS.NETWORKING,
        loadChildren: () => import('./modules/networking/networking.routes').then(m => m.networkingRoutes),
        canActivate: [AllowIfMenuItemIsPresentGuard, cloudInfrastructureCredentialsGuard]
    },
    {
        path: ROUTE_SEGMENTS.STORAGE,
        loadChildren: () => import('./modules/storage/storage.routes').then(m => m.storageRoutes),
        canActivate: [AllowIfMenuItemIsPresentGuard, cloudInfrastructureCredentialsGuard]
    },
];
