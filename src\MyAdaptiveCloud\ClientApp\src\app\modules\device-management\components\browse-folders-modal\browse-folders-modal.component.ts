import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DEVICES_ROUTES_SEGMENTS } from '@app/shared/constants/routes-segments';
import { ModalService } from '@app/shared/services/modal.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { filter, take, tap } from 'rxjs';
import { DeviceFolderTreeNode } from '../../models/device-folder-tree-node';
import { DeviceTreeNode } from '../../models/device-tree-node';
import { CreateDeviceFolderComponent } from '../../modules/device-folders/components/create-folder/create-device-folder.component';
import { DeleteFolderModalComponent } from '../../modules/device-folders/components/delete-folder-modal/delete-folder-modal.component';
import { DeviceFolderDetailPanelComponent } from '../../modules/device-folders/components/folder-details-panel/folder-details-panel.component';
import { FoldersTreeStore } from '../../store/folders-tree.store';
import { DeviceFoldersBreadcrumbComponent } from '../breadcrumb/breadcrumb.component';
import { DeviceFolderTreeComponent } from '../folder-tree-panel/folder-tree.component';

@Component({
    selector: 'app-browse-folders-modal',
    imports: [
        DeviceFoldersBreadcrumbComponent,
        DeviceFolderDetailPanelComponent,
        DeviceFolderTreeComponent,
        AsyncPipe
    ],
    templateUrl: './browse-folders-modal.component.html',
    styleUrl: './browse-folders-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class BrowseFoldersModalComponent implements OnInit {

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) foldersRoot: DeviceFolderTreeNode = null;

    public readonly foldersTreeStore = inject(FoldersTreeStore);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly permissionService = inject(PermissionService);
    private readonly modalService = inject(ModalService);
    private readonly router = inject(Router);

    protected readonly canEditFolders: boolean = this.permissionService.canManageDevices() || this.permissionService.canManageDeviceFolders();

    ngOnInit(): void {
        this.foldersTreeStore.toggleBrowseModalOpen(true);
        this.foldersTreeStore.setRootFolder(this.foldersRoot);
    }

    selectFolder($event: DeviceFolderTreeNode | DeviceTreeNode) {
        this.foldersTreeStore.selectFolder($event as DeviceFolderTreeNode);
    }

    protected openCreateFolderModal(folder: DeviceFolderTreeNode) {
        const modalRef = this.modalService.openModalComponent(CreateDeviceFolderComponent, { size: 'xl' });
        const modalComponentInstance = modalRef.componentInstance as CreateDeviceFolderComponent;
        modalComponentInstance.selectedFolder = folder;
        modalRef.closed.pipe(
            filter(modalResult => modalResult instanceof DeviceFolderTreeNode),
            tap(createdFolder => {
                this.foldersTreeStore.insertFolderInFolderTree({ createdFolder, parentFolder: folder });
                this.foldersTreeStore.selectFolder(createdFolder);
            }),
            take(1)
        ).subscribe();
    }

    protected openEditFolderModal(folder: DeviceFolderTreeNode): void {
        this.foldersTreeStore.selectFolder(folder);
        const modalRef = this.modalService.openModalComponent(CreateDeviceFolderComponent, { size: 'xl' });
        const modalComponentInstance = modalRef.componentInstance as CreateDeviceFolderComponent;
        modalComponentInstance.deviceFolder = folder;
        modalComponentInstance.selectedFolder = folder;
        modalComponentInstance.isCreate = false;
        modalRef.closed
            .pipe(
                filter(modalResult => !!modalResult),
                tap(updatedFolder => this.foldersTreeStore.updateFolderInFolderTree({ uniqueFolderId: folder.getUniqueStringId(), name: updatedFolder.name, description: updatedFolder.description })),
                take(1)
            )
            .subscribe();
    }

    protected openDeleteFolderModal(folder: DeviceFolderTreeNode) {
        const modalRef = this.modalService.openModalComponent(DeleteFolderModalComponent, { size: 'l' });
        (modalRef.componentInstance as DeleteFolderModalComponent).folder = folder;
        modalRef.closed
            .pipe(
                filter(res => !!res),
                take(1)
            )
            .subscribe(() => {
                this.foldersTreeStore.removeFolderFromFolderTree(folder);
                this.foldersTreeStore.selectFolder(folder.parent());
            });
    }

    protected close() {
        this.activeModal.close();
        this.router.navigate([`/${DEVICES_ROUTES_SEGMENTS.BASE_DEVICES_MANAGEMENT}/`]);
    }
}
