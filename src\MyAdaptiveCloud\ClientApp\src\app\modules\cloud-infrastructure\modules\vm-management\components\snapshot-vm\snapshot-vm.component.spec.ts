import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { SnapshotVmComponent } from './snapshot-vm.component';

describe('SnapshotVmComponent', () => {

    let component: SnapshotVmComponent;
    let fixture: ComponentFixture<SnapshotVmComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [SnapshotVmComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(SnapshotVmComponent);
        component = fixture.componentInstance;
        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.snapshotVirtualMachine.and.returnValue(of('jobId1'));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.virtualMachineId.set('test-id');
    });

    describe('Submit', () => {

        it('should close modal on cancel', () => {

            fixture.detectChanges();
            const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit the form with snapshot memory set to true when the vm is running', () => {

            fixture.detectChanges();

            const nameControl = fixture.debugElement.query(By.css('#name')).nativeElement as HTMLInputElement;
            nameControl.value = 'VM Name';
            nameControl.dispatchEvent(new Event('input'));

            const descriptionControl = fixture.debugElement.query(By.css('#description')).nativeElement as HTMLInputElement;
            descriptionControl.value = 'VM Description';
            descriptionControl.dispatchEvent(new Event('input'));

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.snapshotVirtualMachine).toHaveBeenCalledOnceWith('test-id', 'VM Name', 'VM Description', true);
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

    });

});

