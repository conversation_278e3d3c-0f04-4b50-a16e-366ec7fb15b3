import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'ddos-mitigation-value'
})
export class DDosMitigationValuePipe implements PipeTransform {
    transform(value: string | string[] | number | number[] | null | undefined): string | string[] | number | number[] {

        if ((Array.isArray(value) && value.length === 0) || value === null || value === undefined || value === '') {
            return '-';
        }

        return value;
    }
}
