<div class="modal-header">
    <h4 class="modal-title">Create Network</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <nav ngbNav #nav="ngbNav" class="nav-tabs" [(activeId)]="activeTab" [destroyOnHide]="false">
        <ng-container [ngbNavItem]="addNetworkTabs.Isolated">
            <a ngbNavLink>Isolated</a>
            <ng-template [ngbNavContent]>
                <app-add-network-isolated [networkOfferings]="store.networkStep.isolatedNetworkOfferings()"
                    [vpcs]="store.networkStep.vpcs()" />
            </ng-template>
        </ng-container>
        <ng-container [ngbNavItem]="addNetworkTabs.Layer2">
            <a ngbNavLink>Layer-2</a>
            <ng-template [ngbNavContent]>
                <app-add-network-layer2 [networkOfferings]="store.networkStep.layer2NetworkOfferings()" />
            </ng-template>
        </ng-container>
        @if (cloudInfraPermissionService.isRootAdmin()) {
            <ng-container [ngbNavItem]="addNetworkTabs.Shared">
                <a ngbNavLink>Shared</a>
                <ng-template [ngbNavContent]>
                    <app-add-network-shared [networkOfferings]="store.networkStep.sharedNetworkOfferings()" />
                </ng-template>
            </ng-container>
        }
    </nav>
    <div [ngbNavOutlet]="nav"></div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="store.networkStep.requestStatus() === createVmRequestStatus.Pending ||
            ((activeTab === addNetworkTabs.Isolated && isolatedNetworkComponent()?.form.invalid) ||
             (activeTab === addNetworkTabs.Layer2 && layer2NetworkComponent()?.form.invalid) ||
             (activeTab === addNetworkTabs.Shared && sharedNetworkComponent()?.form.invalid))"
        (submitClickEvent)="submitForm()">OK
    </app-btn-submit>
</div>
