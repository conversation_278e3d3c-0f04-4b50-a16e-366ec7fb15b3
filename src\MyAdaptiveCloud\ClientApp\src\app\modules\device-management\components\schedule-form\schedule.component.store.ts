import { Injectable } from '@angular/core';
import { ComponentStore, OnStoreInit } from '@ngrx/component-store';
import { ScheduleType } from '@app/shared/models/schedule-type.enum';
import { AutoApprovalUpdateCategoryInfo } from '../../models/schedule/auto-approval-update-category-info.model';
import { UpdateCategoryEnum } from '../../models/schedule/update-category.enum';
import { ScheduleMonthsType } from '../../models/schedule/schedule-months-type.enum';

export interface DailyScheduleState {
    freqRecurrenceFactor: number | null;
    isValid: boolean;
}

export interface WeeklyScheduleState {
    freqRecurrenceFactor: number | null;
    weeklyDays: number[];
    isValid: boolean;
}

export interface MonthlyScheduleState {
    scheduleMonthsType: number;
    months: number[];
    monthDays: number[];
    monthWeeks: number[];
    monthWeeksDays: number[];
    isValid: boolean;
}

export interface ScheduleInfoStore {
    name: string;
    description: string;
    isScheduleInfoFormValid: boolean;
    isReadOnly: boolean;
    hasMicrosoftUpdates: boolean;
    releaseTagName: string;
    minAgentVersionImmediateUpdateInstall: string;
}

export interface ScheduleTypeStore {
    startDateLocalized: Date;
    scheduleType: ScheduleType;
    dailySchedule: DailyScheduleState;
    weeklySchedule: WeeklyScheduleState;
    monthlySchedule: MonthlyScheduleState;
    autoApprovalUpdateCategoryInfo: AutoApprovalUpdateCategoryInfoState;
    isReadOnly: boolean;
}

export interface ScheduleState {
    scheduleInfoForm: ScheduleInfoStore;
    scheduleTypeForm: ScheduleTypeStore;
    isEditing: boolean;
    autoApprovalState: AutoApprovalUpdateCategoryInfoState;
}

export interface AutoApprovalUpdateCategoryInfoState {
    isReadOnly: boolean;
    autoApprovalUpdateCategories: AutoApprovalUpdateCategoryInfo[]
}

// eslint-disable-next-line @angular-eslint/use-injectable-provided-in
@Injectable()
export class ScheduleStore extends ComponentStore<ScheduleState> implements OnStoreInit {

    private readonly scheduleInitialState: ScheduleState = {
        scheduleInfoForm: {
            name: '',
            description: '',
            isScheduleInfoFormValid: false,
            isReadOnly: false,
            hasMicrosoftUpdates: false,
            minAgentVersionImmediateUpdateInstall: 'v1.27.115',
            releaseTagName: 'Recommended'
        },
        scheduleTypeForm: {
            startDateLocalized: new Date(),
            scheduleType: ScheduleType.Weekly,
            dailySchedule: {
                freqRecurrenceFactor: null,
                isValid: false
            },
            weeklySchedule: {
                freqRecurrenceFactor: null,
                weeklyDays: [],
                isValid: false
            },
            monthlySchedule: {
                scheduleMonthsType: ScheduleMonthsType.On,
                months: [],
                monthDays: [],
                monthWeeks: [],
                monthWeeksDays: [],
                isValid: false
            },
            autoApprovalUpdateCategoryInfo: {
                autoApprovalUpdateCategories: Object.values(UpdateCategoryEnum).map(categoryKey => ({
                    categoryKey,
                    autoApprove: false,
                    installImmediately: false,
                })),
                isReadOnly: false,
            },
            isReadOnly: false
        },
        autoApprovalState: {
            autoApprovalUpdateCategories: Object.values(UpdateCategoryEnum).map(categoryKey => ({
                categoryKey,
                autoApprove: false,
                installImmediately: false,
            })),
            isReadOnly: false,
        },
        isEditing: false,
    };

    ngrxOnStoreInit() {
        this.setState(this.scheduleInitialState);
    }

    // ****************** Updaters ******************
    readonly updateScheduleState = this.updater((state, value: ScheduleState): ScheduleState => ({
        ...state,
        ...value
    }));
    readonly updateIsEditingState = this.updater((state, value: boolean): ScheduleState => ({
        ...state,
        isEditing: value
    }));
    readonly updateScheduleTypeFormState = this.updater((state, value: Partial<ScheduleTypeStore>): ScheduleState => ({
        ...state,
        scheduleTypeForm: {...state.scheduleTypeForm, ...value}
    }));
    readonly updateSelectedScheduleTypeState = this.updater((state, value: ScheduleType): ScheduleState => ({
        ...state,
        scheduleTypeForm: { ...state.scheduleTypeForm, scheduleType: value }
    }));
    readonly updateSelectedStartDateState = this.updater((state, value: Date): ScheduleState => ({
        ...state,
        scheduleTypeForm: { ...state.scheduleTypeForm, startDateLocalized: value }
    }));
    readonly updateScheduleTypeDailyForm = this.updater((state, value: DailyScheduleState): ScheduleState => ({
        ...state,
        scheduleTypeForm: { ...state.scheduleTypeForm, dailySchedule: value }
    }));
    readonly updateScheduleTypeWeeklyFormState = this.updater((state, value: WeeklyScheduleState): ScheduleState => ({
        ...state,
        scheduleTypeForm: { ...state.scheduleTypeForm, weeklySchedule: value }
    }));
    readonly updateScheduleTypeMonthlyFormState = this.updater((state, value: MonthlyScheduleState): ScheduleState => ({
        ...state,
        scheduleTypeForm: { ...state.scheduleTypeForm, monthlySchedule: value }
    }));
    readonly updateScheduleInfoFormState = this.updater((state, value: ScheduleInfoStore): ScheduleState => ({
        ...state,
        scheduleInfoForm: value
    }));
    readonly updateScheduleInfoFormValid = this.updater((state, value: boolean): ScheduleState => ({
        ...state,
        scheduleInfoForm: { ...state.scheduleInfoForm, isScheduleInfoFormValid: value }
    }));
    readonly updateUpdateCategoriesFormState = this.updater((state, value: AutoApprovalUpdateCategoryInfoState): ScheduleState => ({
        ...state,
        autoApprovalState: { ...state.autoApprovalState, autoApprovalUpdateCategories: value.autoApprovalUpdateCategories, isReadOnly: state.scheduleTypeForm.isReadOnly }
    }));

    // ****************** Selectors ******************
    readonly getScheduleState$ = this.select(state => state);
    readonly getIsEditingState$ = this.select(state => state.isEditing);
    readonly getScheduleInfoFormState$ = this.select(state => state.scheduleInfoForm);
    readonly getScheduleTypeFormState$ = this.select(state => state.scheduleTypeForm);
    readonly getScheduleTypeTypeState$ = this.select(state => state.scheduleTypeForm.scheduleType);
    readonly getScheduleTypeDailyFormState$ = this.select(state => state.scheduleTypeForm.dailySchedule);
    readonly getScheduleTypeWeeklyFormState$ = this.select(state => state.scheduleTypeForm.weeklySchedule);
    readonly getScheduleTypeMonthlyFormState$ = this.select(state => state.scheduleTypeForm.monthlySchedule);
    readonly getScheduleInfoFormValid$ = this.select(state => state.scheduleInfoForm.isScheduleInfoFormValid);
    readonly getScheduleHasMicrosoftUpdates$ = this.select(state => state.scheduleInfoForm.hasMicrosoftUpdates);
    readonly getUpdateCategoriesFormState$ = this.select(state => state.autoApprovalState);

    // ****************** Utility Methods ******************
    readonly resetScheduleState = this.updater((): ScheduleState => this.scheduleInitialState);
}
