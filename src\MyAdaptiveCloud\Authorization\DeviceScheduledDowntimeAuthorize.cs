﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class DeviceScheduledDowntimeAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public DeviceScheduledDowntimeAuthorizeFilter(
            IIdentityService identityService,
            IUserContextService userContextService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int scheduledDowntimeId);

            var scheduledDowntimeOrganizationId = await _entityAuthorizationService.GetScheduleDowntimeOrganizationId(scheduledDowntimeId);

            if (scheduledDowntimeOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, scheduledDowntimeOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, scheduledDowntimeOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class DeviceScheduledDowntimeAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public DeviceScheduledDowntimeAuthorizeAttribute(params Perms[] perms) : base(typeof(DeviceScheduledDowntimeAuthorizeFilter), perms)
        {
            Name = "scheduledDowntimeId";
        }
    }
}