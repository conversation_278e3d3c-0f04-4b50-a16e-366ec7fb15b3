import { DatePipe } from '@angular/common';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ChangeDetectorRef, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { DeviceFolderTreeNode } from '../../models/device-folder-tree-node';
import { DeviceTreeNode } from '../../models/device-tree-node';
import { InstalledSoftwareModel } from '../../modules/devices/models/installed-software-model';
import { SoftwareInventoryStatus } from '../../modules/devices/models/software-inventory-status.enum';
import { SoftwareInventoryService } from '../../modules/devices/services/software-inventory-service';
import { DevicesService } from '../../services/devices.service';
import { FoldersTreeStore } from '../../store/folders-tree.store';
import { ListInstalledSoftwareComponent } from './list-installed-software.component';

describe('ListInstalledSoftwareComponent', () => {
    let component: ListInstalledSoftwareComponent;
    let fixture: ComponentFixture<ListInstalledSoftwareComponent>;
    let mockDevicesService: jasmine.SpyObj<DevicesService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    const data: InstalledSoftwareModel[] = [
        {
            application: 'first application',
            version: 'first',
            installedOn: new Date(2023, 1, 1),
            softwareInventoryStatus: SoftwareInventoryStatus.Active,
            hasUninstall: false,
            canViewUninstallSoftwareInventory: false
        },
        {
            application: 'second application',
            version: 'second',
            installedOn: new Date(2023, 2, 2),
            softwareInventoryStatus: SoftwareInventoryStatus.Active,
            hasUninstall: false,
            canViewUninstallSoftwareInventory: false
        },
        {
            application: 'third application',
            version: 'third',
            installedOn: new Date(Date.UTC(2023, 3, 3)),
            softwareInventoryStatus: SoftwareInventoryStatus.Active,
            hasUninstall: false,
            canViewUninstallSoftwareInventory: false
        }
    ];

    const mockedDevice = new DeviceTreeNode();

    const mockActivatedRoute = jasmine.createSpyObj('ActivatedRoute', [], {
        parent: { data: of({ selectedDevice: { name: 'Mock Device' } }), params: of({ deviceId: 12345 }) }
    });

    const apiDataResult: ApiDataResult<string> = {
        data: 'My Host Name',
        message: 'success'
    };

    class MockFoldersTreeStore {
        selectedDevice$ = of(mockedDevice);
    }

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                ListInstalledSoftwareComponent
            ],
            providers: [
                provideHttpClientTesting(),
                provideMock(UserContextService),
                provideMock(DevicesService),
                provideMock(PermissionService),
                provideMock(NotificationService),
                provideMock(SoftwareInventoryService),
                provideMock(ModalService),
                { provide: ActivatedRoute, useValue: mockActivatedRoute },
                { provide: FoldersTreeStore, useClass: MockFoldersTreeStore },
                ChangeDetectorRef,
            ]
        }).compileComponents();

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            isRegistered: true,
            isApproved: true,
            organizationId: 300,
            featureFlags: [FeatureFlag.FeatureFlagUninstallSoftwareInventory]
        } as UserContext;

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        mockDevicesService = TestBed.inject(DevicesService) as jasmine.SpyObj<DevicesService>;
        mockDevicesService.getInstalledSoftware.and.returnValue(of({ data, message: 'success', totalCount: data.length }));
        mockDevicesService.getHostname.and.returnValue(of(apiDataResult));

        fixture = TestBed.createComponent(ListInstalledSoftwareComponent);

        component = fixture.componentInstance;
        mockedDevice.agentId = 2;
        mockedDevice.hostname = 'Device 1';
        mockedDevice.parent.set(new DeviceFolderTreeNode());
        mockedDevice.orgId = 0;
        component.agentId.set(1);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should call getInstalledSoftware', () => {
            expect(component['hostName']()).toBe('Mock Device'); // Accessing signal value
            expect(mockDevicesService.getInstalledSoftware).toHaveBeenCalledOnceWith(12345, component.pagination);
        });

        it('should have the right amount of data', () => {
            expect(component.table().count).toEqual(3);
            expect(component.table().rows).toEqual(data);
        });
    });

    describe('Component Interaction', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
            fixture.detectChanges();
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });

        it('should display the correct data in the application column', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const thirdRow = rows[2];
            const rowWrapper = thirdRow.querySelectorAll('datatable-body-cell span');
            const thirdColumn = rowWrapper[0].textContent;
            expect(thirdColumn).toEqual(data[2].application);
        });

        it('should display the correct data in the version column', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const thirdRow = rows[2];
            const rowWrapper = thirdRow.querySelectorAll('datatable-body-cell span');
            const thirdColumn = rowWrapper[1].textContent;
            expect(thirdColumn).toEqual(data[2].version);
        });

        it('should display the correct data in the installedOn column', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const thirdRow = rows[2];
            const rowWrapper = thirdRow.querySelectorAll('datatable-body-cell span');
            const thirdColumn = rowWrapper[2].textContent.trim();
            const expectedDate = new DatePipe('en-US').transform(data[2].installedOn, 'MM/dd/yy');
            expect(thirdColumn).toEqual(`${expectedDate}`);
        });
    });
});
