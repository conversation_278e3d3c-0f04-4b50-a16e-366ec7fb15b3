import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { HubService } from '@app/shared/hubs/services/hub.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { NgbAccordionBody, NgbAccordionButton, NgbAccordionCollapse, NgbAccordionDirective, NgbAccordionHeader, NgbAccordionItem } from '@ng-bootstrap/ng-bootstrap';
import { take } from 'rxjs';
import { ConfigurationValueInputType } from '../../models/configuration-value-input-type.enum';
import { Configuration } from '../../models/configuration.model';
import { EditConfigurationRequest } from '../../requests/edit-configuration.request';
import { ConfigurationService } from '../../services/configuration.service';

@Component({
    selector: 'app-configuration',
    imports: [
        ReactiveFormsModule,
        NgbAccordionDirective,
        NgbAccordionItem,
        NgbAccordionButton,
        NgbAccordionCollapse,
        NgbAccordionBody,
        NgbAccordionHeader
    ],
    templateUrl: './configuration.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfigurationComponent implements OnInit {
    private readonly configurationService = inject(ConfigurationService);
    private readonly formBuilder = inject(FormBuilder);
    private readonly notificationService = inject(NotificationService);
    protected readonly permissionService = inject(PermissionService);
    private readonly hubService = inject(HubService);

    protected readonly configurationList = signal<Configuration[]>([]);
    protected readonly form = signal<FormGroup | null>(null);
    protected readonly inputType = ConfigurationValueInputType;

    private featureFlagsIds: number[] = [];

    ngOnInit(): void {
        const hasManageConfigurationPermission = this.permissionService.canManageConfiguration();
        this.configurationService.getList()
            .pipe(take(1))
            .subscribe(res => {
                // Find the configuration for feature flags and store its, so if at least one changes we can trigger the signalr event
                this.featureFlagsIds = res.data.find(c => c.category === 'Feature Flags')?.configurationValues.map(v => v.id) ?? [];

                const form = this.formBuilder.group({});
                res.data.forEach(configuration => {
                    configuration.configurationValues.forEach(configurationValue => {
                        switch (configurationValue.inputType) {
                            case ConfigurationValueInputType.Input:
                                form.addControl(
                                    configurationValue.id.toString(),
                                    new FormControl<string>(
                                        { value: configurationValue.value, disabled: !hasManageConfigurationPermission },
                                        [Validators.maxLength(255)]
                                    )
                                );
                                break;
                            case ConfigurationValueInputType.TextArea:
                                form.addControl(
                                    configurationValue.id.toString(),
                                    new FormControl<string>(
                                        { value: configurationValue.value, disabled: !hasManageConfigurationPermission },
                                        [Validators.maxLength(65000)]
                                    )
                                );
                                break;
                            case ConfigurationValueInputType.Toggle:
                                form.addControl(
                                    configurationValue.id.toString(),
                                    new FormControl<boolean>({ value: configurationValue.value === 'true', disabled: !hasManageConfigurationPermission })
                                );
                                break;
                        }
                    });
                });
                this.configurationList.set([...res.data]);
                this.form.set(form);
            });
    }

    protected submitForm(): void {
        if (this.form().valid && this.permissionService.canManageConfiguration()) {
            const request = new EditConfigurationRequest();
            Object.keys(this.form().controls).forEach(key => {
                const control = this.form().controls[key];
                if (control.dirty) {
                    request.values.push({ id: parseInt(key, 10), value: control.value.toString() });
                }
            });
            if (request.values.length > 0) {
                const updateValueIds = request.values.map(v => v.id);
                if (updateValueIds.some(id => this.featureFlagsIds.includes(id))) {
                    this.hubService.updateFeatureFlags();
                }

                this.configurationService.editConfigurationValue(request)
                    .pipe(take(1))
                    .subscribe(res => {
                        if (res) {
                            this.form().markAsPristine();
                            this.notificationService.notify(res.message);
                        }
                    });
            }
        }
    }

}
