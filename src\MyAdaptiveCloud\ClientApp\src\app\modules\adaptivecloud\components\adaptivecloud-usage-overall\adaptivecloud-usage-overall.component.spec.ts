import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { AdaptiveCloudUsage, AdaptiveCloudUsageOverall } from '../../models/adaptivecloud-usage-overall.model';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AdaptiveCloudUsageOverallComponent } from './adaptivecloud-usage-overall.component';

describe('AdaptiveCloudUsageOverallComponent', () => {

    let mockAdaptiveCloudUsageService: jasmine.SpyObj<AdaptiveCloudUsageService>;
    let fixture: ComponentFixture<AdaptiveCloudUsageOverallComponent>;

    const usageData: AdaptiveCloudUsageOverall[] = [
        {
            acId: '14',
            acName: 'test',
            acType: 'CPU',
            vCPUs: {
                unitPrice: 1,
                quantity: 1
            } as AdaptiveCloudUsage,
            ram: {
                unitPrice: 1,
                quantity: 1
            } as AdaptiveCloudUsage,
            ipAddresses: {
                unitPrice: 1,
                quantity: 1
            } as AdaptiveCloudUsage,
            networkBytes: {
                unitPrice: 1,
                quantity: 1
            } as AdaptiveCloudUsage,
            primaryStorage: {
                unitPrice: 1,
                quantity: 1
            } as AdaptiveCloudUsage,
            secondaryStorage: {
                unitPrice: 1,
                quantity: 1
            } as AdaptiveCloudUsage,
            licensing: []
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AdaptiveCloudUsageOverallComponent
            ],
            providers: [
                provideMock(AdaptiveCloudUsageService),
                FormBuilder,
                ChangeDetectorRef
            ]
        });

        mockAdaptiveCloudUsageService = TestBed.inject(AdaptiveCloudUsageService) as jasmine.SpyObj<AdaptiveCloudUsageService>;
        mockAdaptiveCloudUsageService.getUsagePeriodsOverall.and.returnValue(of({
            data: ['202211', '202210'],
            message: 'success'
        }));
        mockAdaptiveCloudUsageService.getUsageOverall.and.returnValue(of({
            data: usageData,
            message: 'success'
        }));

        fixture = TestBed.createComponent(AdaptiveCloudUsageOverallComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should load default data', () => {
            expect(mockAdaptiveCloudUsageService.getUsagePeriodsOverall).toHaveBeenCalledTimes(1);
            expect(mockAdaptiveCloudUsageService.getUsageOverall).toHaveBeenCalledOnceWith('202211');
        });

    });

});
