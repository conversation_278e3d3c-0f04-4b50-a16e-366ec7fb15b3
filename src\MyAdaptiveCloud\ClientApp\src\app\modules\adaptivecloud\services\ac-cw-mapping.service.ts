import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { IPagination } from '@app/shared/models/datatable/pagination.model';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AcCwProductMap } from '../models/ac-cw-product-map.model';
import { AcCwVmMap } from '../models/ac-cw-vm-map.model';
import { AcMapping } from '../models/ac-mapping.model';
import { AcUnmapped } from '../models/ac-unmapped.model';
import { CwAgreement } from '../models/cw-agreement.model';
import { CwCompany } from '../models/cw-company.model';
import { AcCwMappingRequest } from '../requests/ac-cw-mapping.request';
import { AcCwVmMappingListRequest } from '../requests/ac-cw-vm-mapping-list.request';
import { AcCwVmMappingRequest } from '../requests/ac-cw-vm.mapping.request';
import { AgreementRequest } from '../requests/agreement.request';
import { CwCompaniesRequest } from '../requests/cw-companies.request';

@Injectable({
    providedIn: 'root'
})
export class AcToCwMappingService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'acmapping';

    getUnmappedAccountsDomains(): Observable<ApiDataResult<AcUnmapped[]>> {
        return this.apiService.get<ApiDataResult<AcUnmapped[]>>(`${this.endpoint}/acUnmappedAccountsDomains`);
    }

    getMappings(request: IPagination): Observable<ApiDatasetResult<AcMapping[]>> {
        return this.apiService
            .get<ApiDatasetResult<AcMapping[]>>(`${this.endpoint}/mappinglist`, request);
    }

    searchCompanies(request: CwCompaniesRequest): Observable<ApiDataResult<CwCompany[]>> {
        return this.apiService.get<ApiDataResult<CwCompany[]>>(`${this.endpoint}/cwCompanies`, request);
    }

    getAgreements(request: AgreementRequest): Observable<ApiDataResult<CwAgreement[]>> {
        return this.apiService.get<ApiDataResult<CwAgreement[]>>(`${this.endpoint}/cwAgreements`, request);
    }

    createMapping(mapping: AcCwMappingRequest): Observable<ApiResult> {
        return this.apiService.post<ApiResult, AcCwMappingRequest>(`${this.endpoint}/mapping`, mapping);
    }

    editMapping(id: number, mapping: AcCwMappingRequest): Observable<ApiResult> {
        return this.apiService.put<ApiResult, AcCwMappingRequest>(`${this.endpoint}/mapping`, id, mapping);
    }

    deleteMapping(mappingId: number): Observable<void> {
        return this.apiService.delete(`${this.endpoint}/mapping`, mappingId);
    }

    getProductMappings(): Observable<ApiDataResult<AcCwProductMap[]>> {
        return this.apiService.get<ApiDataResult<AcCwProductMap[]>>(`${this.endpoint}/productMapping`);
    }

    getVmMappingPeriods(): Observable<ApiDataResult<Date[]>> {
        return this.apiService.get<ApiDataResult<Date[]>>(`${this.endpoint}/vmMapping/periods`);
    }

    getVmMappings(request: AcCwVmMappingListRequest): Observable<ApiDatasetResult<AcCwVmMap[]>> {
        return this.apiService.get<ApiDataResult<AcCwVmMap[]>>(`${this.endpoint}/vmMapping`, request).pipe(map(result => {
            result.data.forEach(vmMap => {
                vmMap.startDate = new Date(vmMap.startDate);
                vmMap.endDate = vmMap.endDate ? new Date(vmMap.endDate) : undefined;
            });
            return {
                data: result.data,
                message: result.message,
                totalCount: result.data.length
            };
        }));
    }

    createVmMapping(mapping: AcCwVmMappingRequest): Observable<ApiResult> {
        return this.apiService.post<ApiResult, AcCwVmMappingRequest>(`${this.endpoint}/vmMapping`, mapping);
    }

    updateVmMapping(id: number, mapping: AcCwVmMappingRequest): Observable<ApiResult> {
        return this.apiService.put<ApiResult, AcCwVmMappingRequest>(`${this.endpoint}/vmMapping`, id, mapping);
    }

    deleteVmMapping(mappingId: number): Observable<void> {
        return this.apiService.delete(`${this.endpoint}/vmMapping`, mappingId);
    }

}
