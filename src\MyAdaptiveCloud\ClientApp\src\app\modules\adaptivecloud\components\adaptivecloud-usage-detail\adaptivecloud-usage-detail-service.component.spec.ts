import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AdaptiveCloudUsageDetailsService } from '../../models/adaptive-cloud-usage-details-service.model';
import { AdaptiveCloudUsageDetailServiceComponent } from './adaptivecloud-usage-detail-service.component';

describe('AdaptiveCloudUsageNetworkdDetailComponent', () => {
    let component: AdaptiveCloudUsageDetailServiceComponent;
    let fixture: ComponentFixture<AdaptiveCloudUsageDetailServiceComponent>;

    const data: AdaptiveCloudUsageDetailsService[] = [
        {
            quantity: 5,
            cost: 75,
            unitPrice: 15,
            unitDescription: 'first',
            type: 'pizza',
            additionalDescription: 'large'
        },
        {
            quantity: 2,
            cost: 14,
            unitPrice: 7,
            unitDescription: 'second',
            type: 'sandwich',
            additionalDescription: 'large'
        },
        {
            quantity: 5,
            cost: 45,
            unitPrice: 9,
            unitDescription: 'third',
            type: 'pizza',
            additionalDescription: 'small'
        },
        {
            quantity: 1,
            cost: 12,
            unitPrice: 12,
            unitDescription: 'fourth',
            type: 'pizza',
            additionalDescription: 'medium'
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AdaptiveCloudUsageDetailServiceComponent
            ]
        });

        fixture = TestBed.createComponent(AdaptiveCloudUsageDetailServiceComponent);
        component = fixture.componentInstance;
        fixture.componentRef.setInput('data', data);
        fixture.componentRef.setInput('descriptionLabel', 'This is the description');
        fixture.detectChanges();
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(component).toBeTruthy();
        });
    });

    describe('Methods', () => {
        it('getTotal should calculate te cost total', () => {
            fixture.detectChanges();
            const dataTotal = data[0].cost + data[1].cost + data[2].cost + data[3].cost;
            expect(component.getTotal()).toEqual(dataTotal);
        });
    });
});
