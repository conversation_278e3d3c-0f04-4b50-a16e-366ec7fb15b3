import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { UserContext } from '@app/shared/models/user-context.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { Bgp } from '../../models/bgp.model';
import { DDoSMitigationBgpService } from '../../services/ddos-mitigation-bgp.service';
import { DdosMitigationBgpTableDetailsComponent } from '../ddos-mitigation-bgp-table-details/ddos-mitigation-bgp-table-details.component';
import { DdosMitigationBgpTableComponent } from './ddos-mitigation-bgp-table.component';

describe('DdosMitigationBgpTableComponent', () => {
    let fixture: ComponentFixture<DdosMitigationBgpTableComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockDDoSMitigationBgpService: jasmine.SpyObj<DDoSMitigationBgpService>;
    let mockModalService: jasmine.SpyObj<ModalService>;
    let modalRef: NgbModalRef;

    const data: Bgp[] = [
        {
            neighborIp: '**************',
            neighborRemoteAs: 393775,
            prefix: '************/29',
            nextHop: '**************',
            asPath: [],
            communities: [],
            med: 0,
            localPref: 100,
            origin: 0,
            aggregator: null,
            atomicAggregate: false,
            extendedCommunities: [],
            largeCommunities: [],
            clusterList: [
                '**************',
                '**************'
            ],
            originatorId: '**************',
            simulate: false
        },
        {
            neighborIp: '**************',
            neighborRemoteAs: 393775,
            prefix: '************/29',
            nextHop: '**************',
            asPath: [],
            communities: [],
            med: 0,
            localPref: 100,
            origin: 0,
            aggregator: null,
            atomicAggregate: false,
            extendedCommunities: [],
            largeCommunities: [],
            clusterList: [
                '**************',
                '**************'
            ],
            originatorId: '**************',
            simulate: false
        },
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationBgpTableComponent],
            providers: [
                provideMock(DDoSMitigationBgpService),
                provideMock(UserContextService),
                provideMock(ModalService),
                DdosMitigationBgpTableDetailsComponent,
                provideMock(NgbActiveModal)
            ]
        })
            .compileComponents();

        modalRef = {
            closed: of(true),
            componentInstance: TestBed.inject(DdosMitigationBgpTableDetailsComponent)
        } as NgbModalRef;

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1,
            name: 'Test User',
            email: ''
        } as UserContext;

        mockDDoSMitigationBgpService = TestBed.inject(DDoSMitigationBgpService) as jasmine.SpyObj<DDoSMitigationBgpService>;
        mockDDoSMitigationBgpService.getList.and.returnValue(of({ data, message: '' }));

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;
        mockModalService.openModalComponent.and.returnValue(modalRef);

        fixture = TestBed.createComponent(DdosMitigationBgpTableComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
        });

        it('should call getMitigationStatusList', () => {
            expect(mockDDoSMitigationBgpService.getList).toHaveBeenCalledTimes(1);
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });

    });

    describe('Actions', () => {

        let dataTableDebugElement: DebugElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
        });

        it('should call getMitigationStatusList when refresh is called', () => {
            const action = dataTableDebugElement.queryAll(By.directive(TableActionComponent))[0];
            action.query(By.css('.table-action-container')).nativeElement.click();
            fixture.detectChanges();

            expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            expect((modalRef.componentInstance as DdosMitigationBgpTableDetailsComponent).bgpDetails()).toEqual(data[0]);

        });

    });

});
