name: .NET Core

on:
  workflow_dispatch
#  push:
#    branches:
#    - master
#  pull_request:

jobs:
  build-dotnet:

    runs-on: ubuntu-latest
    strategy:
          matrix:
            dotnet-version: ['7.0.x' ]
    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET Core
      uses: actions/setup-dotnet@v2
      with:
        dotnet-version: ${{ matrix.dotnet-version }}
    - name: Display dotnet version
      run: dotnet --version
    - name: Install dependencies
      run: dotnet restore
    - name: Build
      run: dotnet build --configuration Release --no-restore
    - name: Test
      run: dotnet test --no-restore --verbosity normal

  build-angular:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x]

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: src/MyAdaptiveCloud/ClientApp/package-lock.json

    - name: Install Dependencies
      working-directory: src/MyAdaptiveCloud/ClientApp
      run: |
        npm install

    - name: Build and Test
      working-directory: src/MyAdaptiveCloud/ClientApp
      run: |
        npm run build-prod

  lab-deploy:
    env:
      JSON_BRANCH: "${{ toJSON('BRANCH') }}:"
      JSON_PARAMS: ${{ toJSON(github.head_ref || github.ref_name) }}
    needs: [ build-dotnet, build-angular ]
    name: Deploy to the lab
    runs-on: ubuntu-latest
    steps:
      - name: Trigger jenkins deploy job
        uses: joshlk/jenkins-githubaction@master
        with:
          url: https://jenkins.ippathways.com/jenkins
          job_name: myadaptivecloud-dynamiclab-deploy
          username: ggoodrich
          api_token: ${{ secrets.JENKINSAPITOKEN }}
          wait: false
          parameters: ${{ format('{{ {0} {1} }}', env.JSON_BRANCH, env.JSON_PARAMS) }}
