<div class="modal-header">
  <h4 class="modal-title">Matched Devices</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <div class="p-2">
        <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" [placeholder]="'Search devices'" [dataItemName]="'hostname'"/>
    </div>
    <ngx-datatable #table class="table bootstrap scroll-vertical-250" />
</div>
<div class="footer">
  <div class="m-2 card-footer d-flex justify-content-end">
    <button type="button" class="m-2 btn btn-primary" (click)="activeModal.dismiss()">Close</button>
  </div>
</div>

<ng-template #hostNameTemplate let-row="row">
    <span class="hostname-cell"><i class="mx-1 fa-solid fa-desktop"></i>{{row.hostname}}</span>
</ng-template>

<ng-template #pathTemplate let-row="row">
    @if (toItem(row); as row) {
        <span class="path-cell">{{row.path || row?.parent()?.pathFromRootString("\\")}}</span>
    }
</ng-template>
