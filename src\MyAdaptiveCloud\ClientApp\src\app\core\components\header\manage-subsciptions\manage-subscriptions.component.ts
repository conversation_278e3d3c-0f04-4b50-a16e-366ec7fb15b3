import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BtnSwitchComponent } from '@app/shared/components/btn-switch/btn-switch.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { NotificationType } from '@app/shared/models/notification-type';
import { SubscriptionModel } from '@app/shared/models/profile/subscription';
import { NotificationService } from '@app/shared/services/notification.service';
import { ProfileService } from '@app/shared/services/profile.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';

@Component({
    selector: 'app-profile-manage-subscriptions',
    imports: [BtnSwitchComponent, NgxDatatableModule],
    templateUrl: './manage-subscriptions.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})

export class ManageSubscriptionsComponent extends BaseListClientComponent<SubscriptionModel> implements OnInit {
    private readonly profileService = inject(ProfileService);
    private readonly notificationService = inject(NotificationService);

    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');
    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Organization',
                prop: 'organizationName',
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                headerTemplate: this.headerTemplate()
            },
            {
                name: 'Service',
                prop: 'serviceName',
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                headerTemplate: this.headerTemplate()
            },
            {
                name: 'Subscribed',
                cellTemplate: this.actionsTemplate(),
                width: 120,
                sortable: false,
                resizeable: false,
                canAutoResize: false
            }
        ];

        super.initialize(this.profileService.getSubscriptions.bind(this.profileService), columns);
    }

    protected toggleSubscription(row: SubscriptionModel) {
        const service = row.isSubscribed ?
            this.profileService.cancelSubscription(row.organizationId, row.serviceId) :
            this.profileService.createSubscription(row.organizationId, row.serviceId);

        service
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
                row.isSubscribed = !row.isSubscribed;
                this.notificationService.notify(res.message, NotificationType.Success);
            });
    }
}

