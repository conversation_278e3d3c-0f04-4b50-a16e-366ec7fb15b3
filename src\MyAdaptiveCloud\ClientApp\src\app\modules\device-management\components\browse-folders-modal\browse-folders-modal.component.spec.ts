import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { OrganizationFoldersService } from '@app/modules/device-management/services/organization-folders.service';
import { UNASSIGNED_DEVICES_FOLDER_ID } from '@app/shared/constants/shared-folder-devices-constants';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { Organization } from '@app/shared/models/organization.model';
import { UserContext } from '@app/shared/models/user-context.model';
import { DeviceManagementRoutingService } from '@app/shared/services/device-management-routing.service';
import { ModalService } from '@app/shared/services/modal.service';
import { OrganizationSharedService } from '@app/shared/services/organization-shared.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { provideComponentStore } from '@ngrx/component-store';
import { of } from 'rxjs';
import { Device } from '../../models/device';
import { DeviceFolderTreeNode } from '../../models/device-folder-tree-node';
import { DeviceFolderTreeNodeDto } from '../../models/device-folder-tree-node-dto';
import { DeviceTreeNode } from '../../models/device-tree-node';
import { OrganizationFolderTreeNode } from '../../models/organization-folder-tree-node';
import { OrganizationFolder } from '../../models/organization-folder.model';
import { UnassignedDevicesFolderTreeNode } from '../../models/unassigned-devices-folder-tree-node';
import { CreateDeviceFolderComponent } from '../../modules/device-folders/components/create-folder/create-device-folder.component';
import { DeviceFolderDetailPanelComponent } from '../../modules/device-folders/components/folder-details-panel/folder-details-panel.component';
import { DeviceFolderService } from '../../services/device-folder.service';
import { DevicesService } from '../../services/devices.service';
import { FoldersTreeStore } from '../../store/folders-tree.store';
import { DeviceFoldersBreadcrumbComponent } from '../breadcrumb/breadcrumb.component';
import { OrganizationThresholdsService } from '../device-thresholds/services/device-thresholds.service';
import { DeviceFolderTreeComponent } from '../folder-tree-panel/folder-tree.component';
import { BrowseFoldersModalComponent } from './browse-folders-modal.component';

describe('BrowseFoldersModalComponent', () => {
    let fixture: ComponentFixture<BrowseFoldersModalComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockDeviceFolderService: jasmine.SpyObj<DeviceFolderService>;
    let mockModalService: jasmine.SpyObj<ModalService>;
    let mockDevicesService: jasmine.SpyObj<DevicesService>;
    let mockNgbActiveModal: jasmine.SpyObj<NgbActiveModal>;
    let component: BrowseFoldersModalComponent;
    let mockOrganizationService: jasmine.SpyObj<OrganizationSharedService>;
    let mockOrganizationFoldersService: jasmine.SpyObj<OrganizationFoldersService>;
    let mockPermissionService: jasmine.SpyObj<PermissionService>;
    let mockFoldersTreeStore: jasmine.SpyObj<FoldersTreeStore>;

    let folderCCC: DeviceFolderTreeNode;
    let folderBBB: DeviceFolderTreeNode;
    let folderAAA: DeviceFolderTreeNode;

    let folderCC: DeviceFolderTreeNode;
    let folderBB2: DeviceFolderTreeNode;
    let folderBB: DeviceFolderTreeNode;
    let folderAA: DeviceFolderTreeNode;

    let folderC: DeviceFolderTreeNode;
    let folderB: DeviceFolderTreeNode;
    let folderA: DeviceFolderTreeNode;
    let rootFolder: OrganizationFolderTreeNode;
    let devicesTreeNodes: DeviceTreeNode[];

    const organization: Organization = {
        organizationId: 0,
        name: 'Root',
        allowSubOrg: true,
        parentOrganizationId: null,
        parentOrganizationName: null,
        allowWhiteLabel: false,
        isPartner: false,
        organizationParentFullPath: 'Root'
    };

    const getOrganizationResult: ApiDataResult<Organization> = {
        data: organization,
        message: 'success'
    };

    const getOrganizationFolderResult: ApiDataResult<OrganizationFolder> = {
        data: {
            organizationId: 0,
            name: 'Root',
            parentOrganizationId: null,
            isPartner: false,
            deviceCount: 0,
            deviceCountCurrentFolder: 0,
            hasSubfolders: true
        },
        message: 'success'
    };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateDeviceFolderComponent, DeviceFoldersBreadcrumbComponent, DeviceFolderDetailPanelComponent, DeviceFolderTreeComponent],
            providers: [
                provideHttpClientTesting(),
                provideMock(UserContextService),
                provideMock(DeviceFolderService),
                provideMock(OrganizationSharedService),
                provideMock(OrganizationFoldersService),
                provideMock(DevicesService),
                provideMock(NgbActiveModal),
                provideMock(ModalService),
                provideMock(OrganizationThresholdsService),
                provideComponentStore(FoldersTreeStore),
                provideMock(PermissionService),
                provideMock(DeviceManagementRoutingService),
                provideMock(ActivatedRoute),
                provideMock(Router),
                ChangeDetectorRef
            ]
        })
            .compileComponents();

        // initial data for moving tests
        const deviceFolderCCC: DeviceFolderTreeNodeDto = { folderId: 10, name: 'CCC', hasSubfolders: false, parentFolderId: 7 } as DeviceFolderTreeNodeDto;
        folderCCC = new DeviceFolderTreeNode(deviceFolderCCC, 3, null, []);

        const deviceFolderBBB: DeviceFolderTreeNodeDto = { folderId: 9, name: 'BBB', hasSubfolders: false, parentFolderId: 6 } as DeviceFolderTreeNodeDto;
        folderBBB = new DeviceFolderTreeNode(deviceFolderBBB, 3, null, []);

        const deviceFolderAAA: DeviceFolderTreeNodeDto = { folderId: 8, name: 'AAA', hasSubfolders: false, parentFolderId: 12 } as DeviceFolderTreeNodeDto;
        folderAAA = new DeviceFolderTreeNode(deviceFolderAAA, 3, null, []);

        const deviceFolderCC: DeviceFolderTreeNodeDto = { folderId: 7, name: 'CC', hasSubfolders: true, parentFolderId: 4 } as DeviceFolderTreeNodeDto;
        folderCC = new DeviceFolderTreeNode(deviceFolderCC, 2, null, [folderCCC]);

        const deviceFolderBB2: DeviceFolderTreeNodeDto = { folderId: 11, name: 'BB2', hasSubfolders: false, parentFolderId: 5 } as DeviceFolderTreeNodeDto;
        folderBB2 = new DeviceFolderTreeNode(deviceFolderBB2, 2, null, []);

        const deviceFolderBB: DeviceFolderTreeNodeDto = { folderId: 6, name: 'BB', hasSubfolders: true, parentFolderId: 5 } as DeviceFolderTreeNodeDto;
        folderBB = new DeviceFolderTreeNode(deviceFolderBB, 2, null, [folderBBB]);

        const deviceFolderAA: DeviceFolderTreeNodeDto = { folderId: 12, name: 'AA', hasSubfolders: true, parentFolderId: 2 } as DeviceFolderTreeNodeDto;
        folderAA = new DeviceFolderTreeNode(deviceFolderAA, 2, null, [folderAAA]);

        const deviceFolderC: DeviceFolderTreeNodeDto = { folderId: 4, name: 'C', hasSubfolders: true, parentFolderId: 0 } as DeviceFolderTreeNodeDto;
        folderC = new DeviceFolderTreeNode(deviceFolderC, 1, null, [folderCC]);

        const deviceFolderB: DeviceFolderTreeNodeDto = { folderId: 5, name: 'B', hasSubfolders: true, parentFolderId: 0 } as DeviceFolderTreeNodeDto;
        folderB = new DeviceFolderTreeNode(deviceFolderB, 1, null, [folderBB, folderBB2]);

        const deviceFolderA: DeviceFolderTreeNodeDto = { folderId: 2, name: 'A', hasSubfolders: true, parentFolderId: 0 } as DeviceFolderTreeNodeDto;
        folderA = new DeviceFolderTreeNode(deviceFolderA, 1, null, [folderAA]);

        const devices: Device[] = [];
        Array.from(Array(20)).forEach((v, index) => devices.push(({
            agentId: index + 1,
            orgId: 0,
            hostname: `device${index + 1}`,
            description: `device ${index + 1}`,
            name: `device ${index + 1}`,
            folderId: UNASSIGNED_DEVICES_FOLDER_ID
        } as Device)));

        devicesTreeNodes = [];

        const discoveredFolder = new UnassignedDevicesFolderTreeNode(organization.organizationId);
        devices.forEach(device => devicesTreeNodes.push(new DeviceTreeNode(device, discoveredFolder)));

        rootFolder = new OrganizationFolderTreeNode(organization, [folderA, folderB, folderC], 0);
        discoveredFolder.addDevices(devicesTreeNodes);

        // end initial data
        mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
        mockPermissionService.canManageDevices.and.returnValue(true);
        mockOrganizationService = TestBed.inject(OrganizationSharedService) as jasmine.SpyObj<OrganizationSharedService>;
        mockOrganizationService.getOrganizationById.and.returnValue(of(getOrganizationResult));

        mockOrganizationFoldersService = TestBed.inject(OrganizationFoldersService) as jasmine.SpyObj<OrganizationFoldersService>;
        mockOrganizationFoldersService.getOrganizationFolderById.and.returnValue(of(getOrganizationFolderResult));
        mockOrganizationFoldersService.getChildrenOrganizationFolders.and.returnValue(of({ data: [], totalCount: 0, message: 'OK' }));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1
        } as UserContext;
        mockDevicesService = TestBed.inject(DevicesService) as jasmine.SpyObj<DevicesService>;
        mockDevicesService.getDevicesWithoutFolder.and.returnValue(of({ data: devices, totalCount: devicesTreeNodes.length, message: 'OK' }));

        mockDeviceFolderService = TestBed.inject(DeviceFolderService) as jasmine.SpyObj<DeviceFolderService>;
        mockDeviceFolderService.getOrganizationFolders.and.returnValue(of({ data: [deviceFolderA, deviceFolderB, deviceFolderC], message: 'OK' }));

        mockDeviceFolderService.getChildrenFolders.withArgs(folderA.getId()).and.returnValue(of({ data: [deviceFolderAA], message: 'OK' }));
        mockDeviceFolderService.getChildrenFolders.withArgs(folderAA.getId()).and.returnValue(of({ data: [deviceFolderAAA], message: 'OK' }));

        mockDeviceFolderService.getChildrenFolders.withArgs(folderB.getId()).and.returnValue(of({ data: [deviceFolderBB], message: 'OK' }));
        mockDeviceFolderService.getChildrenFolders.withArgs(folderBB.getId()).and.returnValue(of({ data: [deviceFolderBBB], message: 'OK' }));

        mockDeviceFolderService.getChildrenFolders.withArgs(folderC.getId()).and.returnValue(of({ data: [deviceFolderCC], message: 'OK' }));
        mockDeviceFolderService.getChildrenFolders.withArgs(folderCC.getId()).and.returnValue(of({ data: [deviceFolderCCC], message: 'OK' }));

        mockDeviceFolderService.getDevicesByFolder.and.returnValue(of({ data: [], message: 'OK', totalCount: 0 }));
        mockDeviceFolderService.deleteDeviceFolder.and.returnValue(of());
        mockDeviceFolderService.setNoParentFolder.and.returnValue(of({ message: 'OK' }));
        mockDeviceFolderService.moveDevicesAndFolders.and.returnValue(of({ message: 'OK' }));
        mockNgbActiveModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;
        mockModalService.openModalComponent.and.returnValue({ componentInstance: {}, closed: of(true) } as NgbModalRef);

        mockFoldersTreeStore = TestBed.inject(FoldersTreeStore) as jasmine.SpyObj<FoldersTreeStore>;
        mockFoldersTreeStore.setRootFolder(rootFolder);

        fixture = TestBed.createComponent(BrowseFoldersModalComponent);
        component = fixture.componentInstance;
        component.foldersRoot = rootFolder;

        fixture.detectChanges();
    });

    describe('component interaction', () => {

        it('should dismiss the modal when clicking the close icon', () => {
            fixture.debugElement.query(By.css('.btn-close')).nativeElement.click();
            fixture.detectChanges();
            expect(mockNgbActiveModal.close).toHaveBeenCalledTimes(1);
        });

        it('should initialize the create new folder modal', () => {
            const selectFolderSpy = spyOn(component.foldersTreeStore, 'selectFolder').and.callThrough();

            component.foldersTreeStore.selectFolder(folderA);
            fixture.detectChanges();

            const createNewFolderButton = fixture.debugElement.query(By.css('i[title="Add new folder"]'));
            createNewFolderButton.nativeElement.click();
            fixture.detectChanges();

            expect(selectFolderSpy).toHaveBeenCalledWith(folderA);

            mockModalService.openModalComponent.and.returnValue({ componentInstance: { selectedFolder: folderA }, closed: of(folderA) } as NgbModalRef);

            expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);

        });

        it('should open the edit folder modal', () => {
            const selectFolderSpy = spyOn(component.foldersTreeStore, 'selectFolder').and.callThrough();

            component.foldersTreeStore.selectFolder(folderA);
            fixture.detectChanges();

            const editFolderButton = fixture.debugElement.query(By.css('i[title="Edit folder A"]'));
            editFolderButton.nativeElement.click();
            fixture.detectChanges();

            expect(selectFolderSpy).toHaveBeenCalledWith(folderA);

            const modalResult = { name: 'Folder A', description: 'Folder A description' };

            mockModalService.openModalComponent.and.returnValue({ componentInstance: { selectedFolder: folderA }, closed: of(modalResult) } as NgbModalRef);

            expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
        });
    });

    describe('Moving items', () => {

        it('Moving to a folder', () => {
            expandTreeNodes(fixture);

            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderA);
            // get store modified objects and do the expects
            component.foldersTreeStore.foldersRoot$.subscribe(folderTree => {
                const folderUpdated = folderTree.findFolderByUniqueId(folderA.getUniqueStringId());

                expect(folderUpdated.deviceCountSelfAndChildren()).toBe(1);
                expect(folderUpdated.deviceCountOnlySelf()).toBe(1);
                expect(folderUpdated.deviceCountOnlySelf()).toEqual(folderUpdated.folderDevices().length);
            });
        });

        it('Moving to a subfolder', () => {
            expandTreeNodes(fixture);

            const folderA_addDevices_spy = spyOn(folderA, 'addDevices').and.callThrough();
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const folderA_updateDeviceCount_spy = spyOn(folderA as any, 'updateDeviceCount').and.callThrough();
            const folderAA_addDevices_spy = spyOn(folderAA, 'addDevices').and.callThrough();
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const folderAA_updateDeviceCount_spy = spyOn(folderAA as any, 'updateDeviceCount').and.callThrough();

            const device1 = devicesTreeNodes.shift();
            moveDeviceToTheFolder(fixture, device1, folderA);
            const device2 = devicesTreeNodes.shift();
            moveDeviceToTheFolder(fixture, device2, folderAA);
            const device3 = devicesTreeNodes.shift();
            moveDeviceToTheFolder(fixture, device3, folderAA);

            expect(folderA.deviceCountSelfAndChildren()).toBe(3);
            expect(folderA.deviceCountOnlySelf()).toBe(1);
            expect(folderA.deviceCountOnlySelf()).toEqual(folderA.folderDevices().length);
            expect(folderA_addDevices_spy).toHaveBeenCalledOnceWith([device1]);
            expect(folderA_updateDeviceCount_spy).toHaveBeenCalledOnceWith(1);

            expect(folderAA.deviceCountSelfAndChildren()).toBe(2);
            expect(folderAA.deviceCountOnlySelf()).toBe(2);
            expect(folderAA.deviceCountOnlySelf()).toEqual(folderAA.folderDevices().length);
            expect(folderAA_addDevices_spy).toHaveBeenCalledTimes(2);
            expect(folderAA_addDevices_spy).toHaveBeenCalledWith([device2]);
            expect(folderAA_addDevices_spy).toHaveBeenCalledWith([device3]);
            expect(folderAA_updateDeviceCount_spy).toHaveBeenCalledTimes(2);

        });

        it('Moving to a third level subfolder', () => {
            expandTreeNodes(fixture);

            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB2);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBBB);

            component.foldersTreeStore.foldersRoot$.subscribe(folderTree => {
                const folderB_updated = folderTree.findFolderByUniqueId(folderB.getUniqueStringId());
                const folderBB_updated = folderTree.findFolderByUniqueId(folderBB.getUniqueStringId());
                const folderBB2_updated = folderTree.findFolderByUniqueId(folderBB2.getUniqueStringId());
                const folderBBB_updated = folderTree.findFolderByUniqueId(folderBBB.getUniqueStringId());

                expect(folderB_updated.deviceCountSelfAndChildren()).toBe(5);
                expect(folderB_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderB_updated.deviceCountOnlySelf()).toEqual(folderB_updated.folderDevices().length);

                expect(folderBB_updated.deviceCountSelfAndChildren()).toBe(3);
                expect(folderBB_updated.deviceCountOnlySelf()).toBe(2);
                expect(folderBB_updated.deviceCountOnlySelf()).toEqual(folderBB_updated.folderDevices().length);

                expect(folderBB2_updated.deviceCountSelfAndChildren()).toBe(1);
                expect(folderBB2_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderBB2_updated.deviceCountOnlySelf()).toEqual(folderBB2_updated.folderDevices().length);

                expect(folderBBB_updated.deviceCountSelfAndChildren()).toBe(1);
                expect(folderBBB_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderBBB_updated.deviceCountOnlySelf()).toEqual(folderBBB_updated.folderDevices().length);
            });
        });

        it('Moving to a sibling subfolder', () => {
            expandTreeNodes(fixture);

            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB2);
            moveDeviceToTheFolder(fixture, folderBB2.folderDevices()[0], folderBB);

            component.foldersTreeStore.foldersRoot$.subscribe(folderTree => {
                const folderB_updated = folderTree.findFolderByUniqueId(folderB.getUniqueStringId());
                const folderBB_updated = folderTree.findFolderByUniqueId(folderBB.getUniqueStringId());
                const folderBB2_updated = folderTree.findFolderByUniqueId(folderBB2.getUniqueStringId());

                expect(folderB_updated.deviceCountSelfAndChildren()).toBe(4);
                expect(folderB_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderB_updated.deviceCountOnlySelf()).toEqual(folderB_updated.folderDevices().length);

                expect(folderBB_updated.deviceCountSelfAndChildren()).toBe(3);
                expect(folderBB_updated.deviceCountOnlySelf()).toBe(3);
                expect(folderBB_updated.deviceCountOnlySelf()).toEqual(folderBB_updated.folderDevices().length);

                expect(folderBB2_updated.deviceCountSelfAndChildren()).toBe(0);
                expect(folderBB2_updated.deviceCountOnlySelf()).toBe(0);
                expect(folderBB2_updated.deviceCountOnlySelf()).toEqual(folderBB2_updated.folderDevices().length);
            });
        });

        it('Moving to another folder', () => {
            expandTreeNodes(fixture);

            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderA);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderC);
            moveDeviceToTheFolder(fixture, folderC.folderDevices()[0], folderA);

            component.foldersTreeStore.foldersRoot$.subscribe(folderTree => {
                const folderA_updated = folderTree.findFolderByUniqueId(folderA.getUniqueStringId());
                const folderB_updated = folderTree.findFolderByUniqueId(folderB.getUniqueStringId());
                const folderC_updated = folderTree.findFolderByUniqueId(folderC.getUniqueStringId());

                expect(folderA_updated.deviceCountSelfAndChildren()).toBe(2);
                expect(folderA_updated.deviceCountOnlySelf()).toBe(2);
                expect(folderA_updated.deviceCountOnlySelf()).toEqual(folderA_updated.folderDevices().length);

                expect(folderB_updated.deviceCountSelfAndChildren()).toBe(1);
                expect(folderB_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderB_updated.deviceCountOnlySelf()).toEqual(folderB_updated.folderDevices().length);

                expect(folderC_updated.deviceCountSelfAndChildren()).toBe(0);
                expect(folderC_updated.deviceCountOnlySelf()).toBe(0);
                expect(folderC_updated.deviceCountOnlySelf()).toEqual(folderC_updated.folderDevices().length);
            });
        });

        it('Moving to another subfolder', () => {
            expandTreeNodes(fixture);

            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderA);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderAA);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderBB);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderC);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderCC);
            moveDeviceToTheFolder(fixture, folderBB.folderDevices()[0], folderAA);

            component.foldersTreeStore.foldersRoot$.subscribe(folderTree => {
                const folderA_updated = folderTree.findFolderByUniqueId(folderA.getUniqueStringId());
                const folderAA_updated = folderTree.findFolderByUniqueId(folderAA.getUniqueStringId());
                const folderB_updated = folderTree.findFolderByUniqueId(folderB.getUniqueStringId());
                const folderBB_updated = folderTree.findFolderByUniqueId(folderBB.getUniqueStringId());
                const folderC_updated = folderTree.findFolderByUniqueId(folderC.getUniqueStringId());
                const folderCC_updated = folderTree.findFolderByUniqueId(folderCC.getUniqueStringId());

                expect(folderA_updated.deviceCountSelfAndChildren()).toBe(3);
                expect(folderA_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderA_updated.deviceCountOnlySelf()).toEqual(folderA_updated.folderDevices().length);

                expect(folderAA_updated.deviceCountSelfAndChildren()).toBe(2);
                expect(folderAA_updated.deviceCountOnlySelf()).toBe(2);
                expect(folderAA_updated.deviceCountOnlySelf()).toEqual(folderAA_updated.folderDevices().length);

                expect(folderB_updated.deviceCountSelfAndChildren()).toBe(2);
                expect(folderB_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderB_updated.deviceCountOnlySelf()).toEqual(folderB_updated.folderDevices().length);

                expect(folderBB_updated.deviceCountSelfAndChildren()).toBe(1);
                expect(folderBB_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderBB_updated.deviceCountOnlySelf()).toEqual(folderBB_updated.folderDevices().length);

                expect(folderC_updated.deviceCountSelfAndChildren()).toBe(2);
                expect(folderC_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderC_updated.deviceCountOnlySelf()).toEqual(folderC_updated.folderDevices().length);

                expect(folderCC_updated.deviceCountSelfAndChildren()).toBe(1);
                expect(folderCC_updated.deviceCountOnlySelf()).toBe(1);
                expect(folderCC_updated.deviceCountOnlySelf()).toEqual(folderCC_updated.folderDevices().length);
            });
        });

        it('Moving from a subfolder to its parent folder', () => {
            expandTreeNodes(fixture);

            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderA);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderAA);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderAA);
            moveDeviceToTheFolder(fixture, devicesTreeNodes.shift(), folderAA);
            moveDeviceToTheFolder(fixture, folderAA.folderDevices()[0], folderA);

            component.foldersTreeStore.foldersRoot$.subscribe(folderTree => {
                const folderA_updated = folderTree.findFolderByUniqueId(folderA.getUniqueStringId());
                const folderAA_updated = folderTree.findFolderByUniqueId(folderAA.getUniqueStringId());

                expect(folderA_updated.deviceCountSelfAndChildren()).toBe(4);
                expect(folderA_updated.deviceCountOnlySelf()).toBe(2);
                expect(folderA_updated.deviceCountOnlySelf()).toEqual(folderA_updated.folderDevices().length);

                expect(folderAA_updated.deviceCountSelfAndChildren()).toBe(2);
                expect(folderAA_updated.deviceCountOnlySelf()).toBe(2);
                expect(folderAA_updated.deviceCountOnlySelf()).toEqual(folderAA_updated.folderDevices().length);
            });
        });
    });

    describe('Selecting items', () => {
        it('Selecting a folder', () => {
            const selectFolderSpy = spyOn(component.foldersTreeStore, 'selectFolder').and.callThrough();

            const folder = new DeviceFolderTreeNode(({ folderId: 1, name: 'Folder', hasSubfolders: false } as DeviceFolderTreeNodeDto), 1, null, []);
            component.selectFolder(folder);
            fixture.detectChanges();

            expect(selectFolderSpy).toHaveBeenCalledWith(folder);
        });

        it('Selecting a folder should have a folder selected in the store', () => {
            const folder = new DeviceFolderTreeNode(({ folderId: 1, name: 'Folder', hasSubfolders: false } as DeviceFolderTreeNodeDto), 1, null, []);
            component.selectFolder(folder);
            fixture.detectChanges();

            mockFoldersTreeStore.selectedFolder$.subscribe(a => {
                expect(a).toEqual(folder);
            });
        });

        it('Selecting a folder should not select any device', () => {
            expandTreeNodes(fixture);

            const folder = new DeviceFolderTreeNode(({ folderId: 1, name: 'Folder', hasSubfolders: false } as DeviceFolderTreeNodeDto), 1, null, []);
            folder.addDevices([
                new DeviceTreeNode(({ agentId: 1, orgId: 1, hostname: 'device1', description: 'device 1', name: 'device 1', folderId: 1 }), folder),
                new DeviceTreeNode(({ agentId: 2, orgId: 1, hostname: 'device2', description: 'device 2', name: 'device 2', folderId: 1 }), folder)
            ]);
            component.selectFolder(folder);
            fixture.detectChanges();
            mockFoldersTreeStore.selectedDevice$.subscribe(a => {
                expect(a).toBeNull();
            });
        });

        it('Selecting a folder should not select any devices', () => {
            expandTreeNodes(fixture);

            const folder = new DeviceFolderTreeNode(({ folderId: 1, name: 'Folder', hasSubfolders: false } as DeviceFolderTreeNodeDto), 1, null, []);
            folder.addDevices([
                new DeviceTreeNode(({ agentId: 1, orgId: 1, hostname: 'device1', description: 'device 1', name: 'device 1', folderId: 1 }), folder),
                new DeviceTreeNode(({ agentId: 2, orgId: 1, hostname: 'device2', description: 'device 2', name: 'device 2', folderId: 1 }), folder)
            ]);
            component.selectFolder(folder);
            fixture.detectChanges();
            mockFoldersTreeStore.selectedDevices$.subscribe(a => {
                expect(a).toEqual([]);
            });
        });
    });
});

function moveDeviceToTheFolder(fixture: ComponentFixture<BrowseFoldersModalComponent>, device: DeviceTreeNode, folder: DeviceFolderTreeNode): void {
    fixture.componentInstance.foldersTreeStore.setMovingItems(device);
    fixture.componentInstance.foldersTreeStore.moveItemsTo(folder);
    fixture.detectChanges();
}

function expandTreeNodes(fixture: ComponentFixture<BrowseFoldersModalComponent>): void {
    // first level nodes
    let arrows = fixture.debugElement.queryAll(By.css('.toggle-arrow'));
    arrows.forEach(arrow => (arrow.nativeElement as HTMLElement).click());
    fixture.detectChanges();

    // second level nodes
    arrows = fixture.debugElement.queryAll(By.css('.toggle-arrow'));
    arrows.forEach(arrow => (arrow.nativeElement as HTMLElement).click());
    fixture.detectChanges();

    // third level nodes
    arrows = fixture.debugElement.queryAll(By.css('.toggle-arrow'));
    arrows.forEach(arrow => (arrow.nativeElement as HTMLElement).click());
    fixture.detectChanges();
}
