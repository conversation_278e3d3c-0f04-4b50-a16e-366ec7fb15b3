import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { VmMediaService } from '../../services/vm-media-service';
import { MediaSelectorComponent } from '../media-selector/media-selector.component';
import { ReinstallVmComponent } from './reinstall-vm.component';

describe('ReinstallVmComponent', () => {

    let component: ReinstallVmComponent;
    let fixture: ComponentFixture<ReinstallVmComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ReinstallVmComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                provideMock(VmMediaService),
                FormBuilder
            ]
        })
            .compileComponents();

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.reinstallVirtualMachine.and.returnValue(of('jobId1'));

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.getFeaturedISOsByZoneId.and.returnValue(of([{ name: 'Featured ISO', id: 'iso1', size: 10000, description: 'Featured ISO' }]));
        mockVmMediaService.getPublicISOsByZoneId.and.returnValue(of([{ name: 'Public ISO', id: 'iso2', size: 10000, description: 'Public ISO' }]));
        mockVmMediaService.getMyISOsByZoneId.and.returnValue(of([{ name: 'My ISO', id: 'iso3', size: 10000, description: 'My ISO' }]));

        mockVmMediaService.getFeaturedTemplatesByZoneId.and.returnValue(of([{ name: 'Featured Template', id: 'tmp1', size: 10000, description: 'Featured Template' }]));
        mockVmMediaService.getPublicTemplatesByZoneId.and.returnValue(of([{ name: 'Public Template', id: 'tmp2', size: 10000, description: 'Public Template' }]));
        mockVmMediaService.getMyTemplatesByZoneId.and.returnValue(of([{ name: 'My Template', id: 'tmp3', size: 10000, description: 'My Template' }]));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        fixture = TestBed.createComponent(ReinstallVmComponent);
        component = fixture.componentInstance;

    });

    it('should close modal on cancel', () => {

        component.inputData.set({
            virtualMachineId: 'test-id',
            virtualMachineZoneId: 'zone-id',
            domainId: 'domain-id',
            account: 'account',
            virtualMachineHasIso: true
        });

        const cancelButton = fixture.debugElement.query(By.css('.cancel')).nativeElement as HTMLButtonElement;
        cancelButton.click();
        fixture.detectChanges();

        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    describe('ISO media', () => {

        beforeEach(() => {

            component.inputData.set({
                virtualMachineId: 'test-id',
                virtualMachineZoneId: 'zone-id',
                domainId: 'domain-id',
                account: 'account',
                virtualMachineHasIso: true
            });

            fixture.detectChanges();
        });

        it('should reinstall VM using the existing media when no ISO is selected', () => {

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.reinstallVirtualMachine).toHaveBeenCalledWith('test-id', undefined);
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should reinstall VM using the selected ISO', () => {

            const mediaSelector = fixture.debugElement.query(By.directive(MediaSelectorComponent));
            const publicIso = mediaSelector.queryAll(By.css('.btn-check'))[1].nativeElement as HTMLInputElement;
            publicIso.checked = true;
            publicIso.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const firstItem = mediaSelector.queryAll(By.css('.form-check-input'))[0].nativeElement as HTMLInputElement;
            firstItem.checked = true;
            firstItem.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmMediaService.getPublicISOsByZoneId).toHaveBeenCalledWith('zone-id', 'domain-id', 'account');
            expect(mockVmManagementService.reinstallVirtualMachine).toHaveBeenCalledWith('test-id', 'iso2');
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

    });

    describe('Template media', () => {

        beforeEach(() => {

            component.inputData.set({
                virtualMachineId: 'test-id',
                virtualMachineZoneId: 'zone-id',
                domainId: 'domain-id',
                account: 'account',
                virtualMachineHasIso: false
            });
            fixture.detectChanges();
        });

        it('should reinstall VM using the existing media when no template is selected', () => {

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.reinstallVirtualMachine).toHaveBeenCalledWith('test-id', undefined);
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should reinstall VM using the selected template', () => {

            const mediaSelector = fixture.debugElement.query(By.directive(MediaSelectorComponent));
            const publicIso = mediaSelector.queryAll(By.css('.btn-check'))[2].nativeElement as HTMLInputElement;
            publicIso.checked = true;
            publicIso.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const firstItem = mediaSelector.queryAll(By.css('.form-check-input'))[0].nativeElement as HTMLInputElement;
            firstItem.checked = true;
            firstItem.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmMediaService.getMyTemplatesByZoneId).toHaveBeenCalledWith('zone-id', 'domain-id', 'account');
            expect(mockVmManagementService.reinstallVirtualMachine).toHaveBeenCalledWith('test-id', 'tmp3');
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

    });

});

