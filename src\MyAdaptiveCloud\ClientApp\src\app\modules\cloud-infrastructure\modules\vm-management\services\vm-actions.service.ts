import { inject, Injectable } from '@angular/core';
import { VmStatusEnum } from '@app/shared/models/cloud-infra/vm-status.enum';
import { JobQueueEvent, JobQueueEventDescription } from '@app/shared/models/job-queue/job-queue-event.enum';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { filter, Subject, take } from 'rxjs';
import { AttachIsoComponent } from '../components/attach-iso/attach-iso.component';
import { CreateSnapshotFromVmSnapshotComponent } from '../components/create-snapshot-from-vm-snapshot/create-snapshot-from-vm-snapshot.component';
import { DestroyVmComponent } from '../components/destroy-vm/destroy-vm.component';
import { MigrateHostComponent } from '../components/migrate-host/migrate-host.component';
import { RebootVmComponent } from '../components/reboot-vm/reboot-vm.component';
import { ReinstallVmComponent } from '../components/reinstall-vm/reinstall-vm.component';
import { ResetPasswordComponent } from '../components/reset-password/reset-password.component';
import { ResetSSHKeyPairComponent } from '../components/reset-ssh-key-pair/reset-ssh-key-pair.component';
import { SnapshotVmComponent } from '../components/snapshot-vm/snapshot-vm.component';
import { SnapshotVolumeComponent } from '../components/snapshot-volume/snapshot-volume.component';
import { StartVmComponent } from '../components/start-vm/start-vm.component';
import { StopVmComponent } from '../components/stop-vm/stop-vm.component';
import { VmManagementService } from './vm-management.service';
import { VmMediaService } from './vm-media-service';

@Injectable({
    providedIn: 'root'
})
export class VmActionsService {

    private readonly modalService = inject(ModalService);
    private readonly notificationService = inject(NotificationService);
    private readonly cloudInfrastructureJobQueueService = inject(CloudInfrastructureJobQueueService);
    private readonly vmMediaService = inject(VmMediaService);
    private readonly vmManagementService = inject(VmManagementService);

    readonly actionExecuted$ = new Subject<{ virtualMachineId: string, state: VmStatusEnum }>();

    openStartVmModal(virtualMachineId: string, virtualMachineName: string, zoneId: string) {
        const modalRef = this.modalService.openModalComponent(StartVmComponent);
        (modalRef.componentInstance as StartVmComponent).virtualMachineId = virtualMachineId;
        (modalRef.componentInstance as StartVmComponent).zoneId = zoneId;

        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.StartVirtualMachine]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.StartVirtualMachine], virtualMachineId);
        });
    }

    openStopVmModal(virtualMachineId: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(StopVmComponent);
        (modalRef.componentInstance as StopVmComponent).virtualMachineId = virtualMachineId;
        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.StopVirtualMachine]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.StopVirtualMachine], virtualMachineId);
        });
    }

    openRebootVmModal(virtualMachineId: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(RebootVmComponent);
        (modalRef.componentInstance as RebootVmComponent).virtualMachineId = virtualMachineId;
        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.RebootVirtualMachine]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.RebootVirtualMachine], virtualMachineId);
        });
    }

    openAttachIsoModal(virtualMachineId: string, virtualMachineZoneId: string, virtualMachineDomainId: string, virtualMachineAccount: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(AttachIsoComponent);
        (modalRef.componentInstance as AttachIsoComponent).inputData.set({
            virtualMachineId,
            virtualMachineZoneId,
            domainId: virtualMachineDomainId,
            account: virtualMachineAccount
        });

        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.AttachISO]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.AttachISO], virtualMachineId);
        });
    }

    openConsole(virtualMachineId: string) {
        this.vmManagementService.getConsoleUrl(virtualMachineId)
            .pipe(
                take(1),
                filter(requestUrl => !!requestUrl)
            )
            .subscribe(requestUrl => {
                const newTab = window.open(requestUrl);
                if (newTab) {
                    newTab.focus();
                }
            });
    }

    openEjectIsoModal(virtualMachineId: string, virtualMachineName: string) {
        const modalRef = this.modalService.openConfirmationDialog({ content: 'Please confirm that you want to detach the ISO from this virtual Machine.', title: 'Eject ISO', showCancelButton: true });
        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(() => {
            this.vmMediaService.detachIso(virtualMachineId).subscribe(jobId => {
                if (jobId) {
                    this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
                    this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.EjectISO]);
                    this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.EjectISO], virtualMachineId);
                }
            });
        });
    }

    openVolumeSnapshotModal(virtualMachineId: string, virtualMachineDomainId: string, virtualMachineAccount: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(SnapshotVolumeComponent);
        (modalRef.componentInstance as SnapshotVolumeComponent).inputData.set({
            virtualMachineId,
            domainId: virtualMachineDomainId,
            account: virtualMachineAccount
        });

        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.TakeVolumeSnapshot]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.TakeVolumeSnapshot], virtualMachineId);
        });
    }

    openSnapshotVmModal(virtualMachineId: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(SnapshotVmComponent);
        (modalRef.componentInstance as SnapshotVmComponent).virtualMachineId.set(virtualMachineId);

        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.TakeVirtualMachineSnapshot]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.TakeVirtualMachineSnapshot], virtualMachineId);
        });
    }

    openResetSSHKeyPairModal(virtualMachineId: string, virtualMachineDomainId: string, virtualMachineAccount: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(ResetSSHKeyPairComponent);
        (modalRef.componentInstance as ResetSSHKeyPairComponent).inputData.set({
            virtualMachineId,
            domainId: virtualMachineDomainId,
            account: virtualMachineAccount
        });

        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.ResetSSHKeyPair]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.ResetSSHKeyPair], virtualMachineId);
        });
    }

    openReinstallVirtualMachineModal(virtualMachineId: string, virtualMachineZoneId: string, virtualMachineDomainId: string, virtualMachineAccount: string, virtualMachineName: string, virtualMachineIsoId: string) {
        const modalRef = this.modalService.openModalComponent(ReinstallVmComponent);
        (modalRef.componentInstance as ReinstallVmComponent).inputData.set({
            virtualMachineId,
            virtualMachineZoneId,
            domainId: virtualMachineDomainId,
            account: virtualMachineAccount,
            virtualMachineHasIso: !!virtualMachineIsoId
        });

        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.ReinstallVirtualMachine]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.ReinstallVirtualMachine], virtualMachineId);
        });
    }

    openResetPasswordModal(virtualMachineId: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(ResetPasswordComponent);
        (modalRef.componentInstance as ResetPasswordComponent).virtualMachineId = virtualMachineId;
        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.ResetVirtualMachinePassword]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.ResetVirtualMachinePassword], virtualMachineId);
        });
    }

    openDestroyVmModal(virtualMachineId: string, virtualMachineDomainId: string, virtualMachineAccount: string, virtualMachineName: string) {
        const modalRef = this.modalService.openModalComponent(DestroyVmComponent);
        (modalRef.componentInstance as DestroyVmComponent).inputData.set({
            virtualMachineId,
            domainId: virtualMachineDomainId,
            account: virtualMachineAccount
        });
        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.DestroyVirtualMachine]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.DestroyVirtualMachine], virtualMachineId);
        });
    }

    openExpungeDestroyedVirtualMachineModal(virtualMachineId: string, virtualMachineName: string) {
        const modalRef = this.modalService.openConfirmationDialog({ content: 'Please confirm that you want to expunge this VM', title: 'Expunge VM', showCancelButton: true });
        modalRef.closed.pipe(take(1), filter(res => !!res)).subscribe(() => {
            this.vmManagementService.expungeVirtualMachine(virtualMachineId).subscribe(jobId => {
                this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Expunging });
                this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.ExpungeVirtualMachine]);
                this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.ExpungeVirtualMachine], virtualMachineId);
            });
        });
    }

    openRecoverVirtualMachineModal(virtualMachineId: string, virtualMachineName: string) {
        const modalRef = this.modalService.openConfirmationDialog({ content: 'Please confirm that you want to recover this VM', title: 'Recover VM', showCancelButton: true });
        modalRef.closed.pipe(take(1), filter(res => !!res)).subscribe(() => {
            this.vmManagementService.recoverVirtualMachine(virtualMachineId).subscribe(jobId => {
                this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
                this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.RecoverVirtualMachine]);
                this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.RecoverVirtualMachine], virtualMachineId);
            });
        });
    }

    openMigrateHostModal(virtualMachineId: string, virtualMachineName: string) {

        const modalRef = this.modalService.openModalComponent(MigrateHostComponent, { size: 'xl' });
        (modalRef.componentInstance as MigrateHostComponent).virtualMachineId = virtualMachineId;

        modalRef.closed.pipe(
            take(1),
            filter(res => !!res)
        ).subscribe(jobId => {
            this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Migrating });
            this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.MigrateHost]);
            this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.MigrateHost], virtualMachineId);
        });
    }

    openDeleteSnapshotModal(snapshotId: string): void {
        this.modalService.openDeleteConfirmationDialog('Are you sure you want to delete this snapshot?', 'Delete', 'Delete')
            .closed.pipe(
                filter(res => !!res),
                take(1)
            )
            .subscribe(() => {
                this.vmManagementService.deleteVirtualMachineSnapshot(snapshotId).subscribe();
            });
    }

    openRevertToVirtualMachineSnapshotModal(snapshotId: string, virtualMachineId: string, virtualMachineName: string) {
        this.modalService.openConfirmationDialog({ content: 'Are you sure you want to revert to this snapshot?', title: 'Revert to Snapshot' })
            .closed.pipe(
                filter(res => !!res),
                take(1)
            )
            .subscribe(() => {
                this.vmManagementService.revertToVirtualMachineSnapshot(snapshotId).subscribe(jobId => {
                    this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
                    this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.RevertToVirtualMachineSnapshot]);
                    this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.RevertToVirtualMachineSnapshot], virtualMachineId);

                });
            });
    }

    openCreateSnapshotFromVirtualMachineSnapshotModal(snapshotId: string, virtualMachineId: string, virtualMachineName: string, domainId: string, account: string, snapshotName: string, snapshotCreatedDate: Date) {
        const modal = this.modalService.openModalComponent(CreateSnapshotFromVmSnapshotComponent);
        const componentInstance = modal.componentInstance as CreateSnapshotFromVmSnapshotComponent;
        componentInstance.virtualMachineId.set(virtualMachineId);
        componentInstance.domainId.set(domainId);
        componentInstance.account.set(account);
        componentInstance.snapshotName.set(snapshotName);
        componentInstance.snapshotCreatedDate.set(snapshotCreatedDate);
        componentInstance.virtualMachineSnapshotId.set(snapshotId);

        modal.closed.pipe(
            filter(res => !!res),
            take(1)
        )
            .subscribe(jobId => {
                this.actionExecuted$.next({ virtualMachineId, state: VmStatusEnum.Loading });
                this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.CreateSnapshotFromVirtualMachineSnapshot]);
                this.cloudInfrastructureJobQueueService.addToQueue(jobId, virtualMachineName, JobQueueEventDescription[JobQueueEvent.CreateSnapshotFromVirtualMachineSnapshot], virtualMachineId);
            });
    }

}
