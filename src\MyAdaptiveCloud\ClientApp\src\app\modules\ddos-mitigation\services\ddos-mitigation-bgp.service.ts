import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiService } from '@app/shared/services/api.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { Observable } from 'rxjs';
import { Bgp } from '../models/bgp.model';
import { DDOS_MITIGATION_ENDPOINT_SEGMENTS } from './ddos-mitigation-endpoint-segments';

@Injectable({
    providedIn: 'root'
})
export class DDoSMitigationBgpService {

    private readonly apiService = inject(ApiService);
    private readonly userContext = inject(UserContextService);

    getAdvertisedList(): Observable<ApiDataResult<Bgp[]>> {
        return this.apiService.get(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/${DDOS_MITIGATION_ENDPOINT_SEGMENTS.BGP}/advertised/${this.userContext.currentUser.organizationId}`);
    }

    getList(): Observable<ApiDataResult<Bgp[]>> {
        return this.apiService.get(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/${DDOS_MITIGATION_ENDPOINT_SEGMENTS.BGP}/${this.userContext.currentUser.organizationId}`);
    }

}

