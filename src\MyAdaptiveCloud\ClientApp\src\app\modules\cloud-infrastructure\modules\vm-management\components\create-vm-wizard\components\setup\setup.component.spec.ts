import { DebugElement, signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { VmManagementPermissionService } from '@app/modules/cloud-infrastructure/modules/vm-management/services/vm-management-permission.service';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { CreateNetworkService } from '../../../../../../services/create-network.service';
import { TemplateViewModel } from '../../../../models/template.view.model';
import { VmNetwork } from '../../../../models/vm-network.model';
import { VmAffinityGroupsService } from '../../../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../../../services/vm-management.service';
import { VmMediaService } from '../../../../services/vm-media-service';
import { MediaSelectorComponent } from '../../../media-selector/media-selector.component';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { DiskOffering } from '../../models/disk-offering.model';
import { ServiceOfferingViewModel } from '../../models/service-offering.view-model';
import { CreateVmComputeService } from '../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../services/create-vm-network.service';
import { CreateVmService } from '../../services/create-vm-service';
import { CreateVmStorageService } from '../../services/create-vm-storage.service';
import { CreateVmWizardSetupComponent } from './setup.component';

describe('CreateVmWizardSetupComponent', () => {

    let component: CreateVmWizardSetupComponent;
    let fixture: ComponentFixture<CreateVmWizardSetupComponent>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateVmComputeService: jasmine.SpyObj<CreateVmComputeService>;
    let mockCreateVmNetworkService: jasmine.SpyObj<CreateVmNetworkService>;
    let mockCreateNetworkService: jasmine.SpyObj<CreateNetworkService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;

    const zones: ZoneViewModel[] = [
        {
            id: '1',
            name: 'Zone 1'
        },
        {
            id: '2',
            name: 'Zone 2'
        },
        {
            id: '3',
            name: 'Zone 3'
        }
    ];

    const zone1FeaturedTemplates: TemplateViewModel[] = [
        {
            name: 'CentOS 7.8',
            id: '11',
            size: 123456,
            description: 'CentOS 7.8'
        }, {
            name: 'Ubuntu 20.04',
            id: '12',
            size: 123456,
            description: 'Ubuntu 20.04'
        }
    ];

    const zone2FeaturedTemplates: TemplateViewModel[] = [
        {
            name: 'CentOS 8',
            id: '31',
            size: 123456,
            description: 'CentOS 8'
        }
    ];

    const zone1FeaturedISOs: TemplateViewModel[] = [
        {
            name: 'CentOS 7.8',
            id: '21',
            size: 123456,
            description: 'CentOS 7.8'
        },
        {
            name: 'Ubuntu 20.04',
            id: '22',
            size: 123456,
            description: 'Ubuntu 20.04'
        },
        {
            name: 'Debian 10',
            id: '23',
            size: 123456,
            description: 'Debian 10'
        }, {
            name: 'Fedora 32',
            id: '24',
            size: 123456,
            description: 'Fedora 32'
        }
    ];

    const zone2FeaturedISOs: TemplateViewModel[] = [
        {
            name: 'CentOS 7.8',
            id: '21',
            size: 123456,
            description: 'CentOS 7.8'
        },
    ];

    const zone1PublicTemplates: TemplateViewModel[] = [
        {
            name: 'Debian 10',
            id: '41',
            size: 123456,
            description: 'Debian 10'
        }, {
            name: 'Fedora 32',
            id: '42',
            size: 123456,
            description: 'Fedora 32'
        }
    ];

    const zone2PublicTemplates: TemplateViewModel[] = [
        {
            name: 'Debian 16',
            id: '53',
            size: 123456,
            description: 'Debian 16'
        }
    ];

    const zone1PublicISOs: TemplateViewModel[] = [
        {
            name: 'Debian 16',
            id: '61',
            size: 123456,
            description: 'Debian 16'
        },
        {
            name: 'Windows 10',
            id: '62',
            size: 123456,
            description: 'Windows 10'
        }, {
            name: 'Windows Server 2019',
            id: '63',
            size: 123456,
            description: 'Windows Server 2019'
        },
        {
            name: 'Windows 10',
            id: '64',
            size: 123456,
            description: 'Windows 10'
        }, {
            name: 'Windows Server 2019',
            id: '65',
            size: 123456,
            description: 'Windows Server 2019'
        }
    ];

    const zone2PublicISOs: TemplateViewModel[] = [
        {
            name: 'Debian 16',
            id: '61',
            size: 123456,
            description: 'Debian 16'
        },
        {
            name: 'Windows 10',
            id: '64',
            size: 123456,
            description: 'Windows 10'
        }, {
            name: 'Windows Server 2019',
            id: '65',
            size: 123456,
            description: 'Windows Server 2019'
        }
    ];

    const zone1MyTemplates: TemplateViewModel[] = [
        {
            name: 'Windows 10',
            id: '71',
            size: 123456,
            description: 'Windows 10'
        }, {
            name: 'Windows Server 2019',
            id: '72',
            size: 123456,
            description: 'Windows Server 2019'
        }
    ];

    const zone1MyISOs: TemplateViewModel[] = [
        {
            name: 'Windows 10',
            id: '71',
            size: 123456,
            description: 'Windows 10'
        }, {
            name: 'Windows Server 2019',
            id: '72',
            size: 123456,
            description: 'Windows Server 2019'
        }
    ];

    const zone2MyTemplates: TemplateViewModel[] = [
        {
            name: 'Windows XP',
            id: '81',
            size: 123456,
            description: 'Windows XP'
        }
    ];

    const zone2MyISOs: TemplateViewModel[] = [
        {
            name: 'Windows XP',
            id: '81',
            size: 123456,
            description: 'Windows XP'
        },
        {
            name: 'Windows 7',
            id: '82',
            size: 123456,
            description: 'Windows 7'
        },
        {
            name: 'Windows 8',
            id: '83',
            size: 123456,
            description: 'Windows 8'
        }
    ];

    const mockServiceOfferings: ServiceOfferingViewModel[] = [
        {
            name: 'name 1',
            id: '91',
            cpuNumber: 4,
            memory: 4096,
            isCustom: false
        }, {
            name: 'name 2',
            id: '92',
            cpuNumber: 2,
            memory: 2048,
            isCustom: false
        },
        {
            name: 'name 3',
            id: '93',
            cpuNumber: null,
            memory: null,
            isCustom: true
        }
    ];

    const mockVmNetwork: VmNetwork = {
        id: '1',
        name: 'Test Network',
        cidr: '***********/24',
        type: 'Private',
        vpcname: 'Test VPC',
        ipaddress: '***********',
        macaddress: '00:11:22:33:44:55',
        gateway: '*************'
    };

    const mockVmNetwork2: VmNetwork = {
        id: '2',
        name: 'Test Network 2',
        cidr: '***********/24',
        type: 'Private',
        vpcname: 'Test VPC 2',
        ipaddress: '***********',
        macaddress: '00:11:22:33:44:56',
        gateway: '*************'
    };

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    beforeEach(() => {

        const mockZoneDomainAccountStore = { ...getMockZoneDomainAccountStore(), zones: signal(zones) };

        TestBed.configureTestingModule({
            imports: [CreateVmWizardSetupComponent],
            providers: [
                CreateVMWizardStore,
                provideMock(CreateVmComputeService),
                provideMock(VmMediaService),
                provideMock(CreateNetworkService),
                provideMock(CreateVmNetworkService),
                provideMock(VmManagementPermissionService),
                provideMock(CreateVmService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(CreateVmStorageService),
                provideMock(CloudInfraPermissionService),
                provideMock(UserContextService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                }
            ]
        });

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isAdmin.and.returnValue(false);

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;

        mockVmMediaService.getFeaturedTemplatesByZoneId.withArgs(zones[0].id).and.returnValue(of(zone1FeaturedTemplates));
        mockVmMediaService.getPublicTemplatesByZoneId.withArgs(zones[0].id, 'domain-id', 'account').and.returnValue(of(zone1PublicTemplates));
        mockVmMediaService.getMyTemplatesByZoneId.withArgs(zones[0].id, 'domain-id', 'account').and.returnValue(of(zone1MyTemplates));
        mockVmMediaService.getFeaturedTemplatesByZoneId.withArgs(zones[1].id).and.returnValue(of(zone2FeaturedTemplates));
        mockVmMediaService.getPublicTemplatesByZoneId.withArgs(zones[1].id, 'domain-id', 'account').and.returnValue(of(zone2PublicTemplates));
        mockVmMediaService.getMyTemplatesByZoneId.withArgs(zones[1].id, 'domain-id', 'account').and.returnValue(of(zone2MyTemplates));

        mockVmMediaService.getFeaturedISOsByZoneId.withArgs(zones[0].id).and.returnValue(of(zone1FeaturedISOs));
        mockVmMediaService.getPublicISOsByZoneId.withArgs(zones[0].id, 'domain-id', 'account').and.returnValue(of(zone1PublicISOs));
        mockVmMediaService.getMyISOsByZoneId.withArgs(zones[0].id, 'domain-id', 'account').and.returnValue(of(zone1MyISOs));
        mockVmMediaService.getFeaturedISOsByZoneId.withArgs(zones[1].id).and.returnValue(of(zone2FeaturedISOs));
        mockVmMediaService.getPublicISOsByZoneId.withArgs(zones[1].id, 'domain-id', 'account').and.returnValue(of(zone2PublicISOs));
        mockVmMediaService.getMyISOsByZoneId.withArgs(zones[1].id, 'domain-id', 'account').and.returnValue(of(zone2MyISOs));

        mockCreateVmComputeService = TestBed.inject(CreateVmComputeService) as jasmine.SpyObj<CreateVmComputeService>;
        mockCreateVmComputeService.getServiceOfferings.and.returnValue(of(mockServiceOfferings));

        mockCreateVmNetworkService = TestBed.inject(CreateVmNetworkService) as jasmine.SpyObj<CreateVmNetworkService>;
        mockCreateVmNetworkService.getNetworks.and.returnValue(of([mockVmNetwork, mockVmNetwork2]));

        mockCreateNetworkService = TestBed.inject(CreateNetworkService) as jasmine.SpyObj<CreateNetworkService>;
        mockCreateNetworkService.getLayer2NetworkOfferings.and.returnValue(of([{ id: '1', name: 'L2 Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getIsolatedNetworkOfferings.and.returnValue(of([{ id: '10', name: 'Isolated Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getSharedNetworkOfferings.and.returnValue(of([{ id: '100', name: 'Shared Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getVpcOfferings.and.returnValue(of([{ id: '1000', name: 'VPC Offering', cidr: '' }]));

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of([]));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of([]));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'test-account',
                domainId: 'test-domain-id',
                cpuCustomOfferingMaxValue: 4,
                memoryCustomOfferingMaxValue: 8192
            }
        } as UserContext;

        fixture = TestBed.createComponent(CreateVmWizardSetupComponent);
        component = fixture.componentInstance;

        fixture.detectChanges();
    });

    describe('virtual machine name', () => {

        it('should set virtual machine name', () => {
            const virtualMachineNameInput = fixture.debugElement.query(By.css('#virtualMachineName')).nativeElement as HTMLInputElement;
            virtualMachineNameInput.value = 'Test VM New Name';
            virtualMachineNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            expect(component.store.setupStep.form.virtualMachineName()).toBe('Test VM New Name');
        });

    });

    describe('zones', () => {

        let zonesContainer: DebugElement;
        let zoneDetails: DebugElement[];

        beforeEach(() => {
            zonesContainer = fixture.debugElement.query(By.css('.list'));
            zoneDetails = zonesContainer.queryAll(By.css('.details'));
        });

        it('should have zones', () => {
            expect(zoneDetails.length).toBe(zoneDetails.length);
        });

        it('should select zone', () => {
            // Select the first zone, should populate the templates
            const setZoneSpy = spyOn(component.store, 'setZone').and.callThrough();

            const zone1Input = zoneDetails[0].query(By.css('input')).nativeElement as HTMLInputElement;
            zone1Input.dispatchEvent(new Event('change'));
            fixture.detectChanges();
            expect(component.store.setupStep.form.zone().id).toBe(zones[0].id);
            expect(setZoneSpy).toHaveBeenCalledTimes(1);

            expect(component.store.setupStep.templates.featuredISOs()).toEqual(zone1FeaturedISOs);
            expect(component.store.setupStep.templates.featuredTemplates()).toEqual(zone1FeaturedTemplates);
            expect(component.store.setupStep.templates.publicISOs()).toEqual(zone1PublicISOs);
            expect(component.store.setupStep.templates.publicTemplates()).toEqual(zone1PublicTemplates);
            expect(component.store.setupStep.templates.myISOs()).toEqual(zone1MyISOs);
            expect(component.store.setupStep.templates.myTemplates()).toEqual(zone1MyTemplates);

            const templateContainer = fixture.debugElement.query(By.directive(MediaSelectorComponent));
            const templateDetails = templateContainer.queryAll(By.css('.details'));
            expect(templateDetails.length).toBe(zone1FeaturedTemplates.length);

            // Select the second zone, should update the templates
            const zone2Input = zoneDetails[1].query(By.css('input')).nativeElement as HTMLInputElement;
            zone2Input.dispatchEvent(new Event('change'));
            fixture.detectChanges();
            expect(component.store.setupStep.form.zone().id).toBe(zones[1].id);
            expect(component.store.setupStep.templates.featuredISOs()).toEqual(zone2FeaturedISOs);
            expect(component.store.setupStep.templates.featuredTemplates()).toEqual(zone2FeaturedTemplates);
            expect(component.store.setupStep.templates.publicISOs()).toEqual(zone2PublicISOs);
            expect(component.store.setupStep.templates.publicTemplates()).toEqual(zone2PublicTemplates);
            expect(component.store.setupStep.templates.myISOs()).toEqual(zone2MyISOs);
            expect(component.store.setupStep.templates.myTemplates()).toEqual(zone2MyTemplates);
        });

    });

    describe('templates', () => {

        let zonesContainer: DebugElement;
        let zoneDetails: DebugElement[];
        let templateContainer: DebugElement;
        let filters: DebugElement[];

        beforeEach(() => {
            zonesContainer = fixture.debugElement.query(By.css('.list'));
            zoneDetails = zonesContainer.queryAll(By.css('.details'));
            const zone1Input = zoneDetails[0].query(By.css('input')).nativeElement as HTMLInputElement;
            zone1Input.dispatchEvent(new Event('change'));
            fixture.detectChanges();
            templateContainer = fixture.debugElement.query(By.directive(MediaSelectorComponent));
            filters = fixture.debugElement.queryAll(By.css('.btn-check'));
        });

        it('should display featured templates by default', () => {
            fixture.detectChanges();

            expect(templateContainer.query(By.css('#media_11_name')).nativeElement.innerText).toBe(zone1FeaturedTemplates[0].name);
            expect(templateContainer.query(By.css('#media_12_name')).nativeElement.innerText).toBe(zone1FeaturedTemplates[1].name);

            expect(templateContainer.query(By.css('#media_11_description')).nativeElement.innerText).toBe(zone1FeaturedTemplates[0].description);
            expect(templateContainer.query(By.css('#media_12_description')).nativeElement.innerText).toBe(zone1FeaturedTemplates[1].description);
        });

        it('should update template list when changing os type', () => {
            // Select ISO filter
            (filters[1].nativeElement as HTMLInputElement).dispatchEvent(new Event('change'));

            fixture.detectChanges();

            // template list should show ISOs
            expect(templateContainer.query(By.css('#media_21_name')).nativeElement.innerText).toBe(zone1FeaturedISOs[0].name);
            expect(templateContainer.query(By.css('#media_22_name')).nativeElement.innerText).toBe(zone1FeaturedISOs[1].name);
            expect(templateContainer.query(By.css('#media_23_name')).nativeElement.innerText).toBe(zone1FeaturedISOs[2].name);
            expect(templateContainer.query(By.css('#media_24_name')).nativeElement.innerText).toBe(zone1FeaturedISOs[3].name);
            expect(templateContainer.query(By.css('#media_21_description')).nativeElement.innerText).toBe(zone1FeaturedISOs[0].description);
            expect(templateContainer.query(By.css('#media_22_description')).nativeElement.innerText).toBe(zone1FeaturedISOs[1].description);
            expect(templateContainer.query(By.css('#media_23_description')).nativeElement.innerText).toBe(zone1FeaturedISOs[2].description);
            expect(templateContainer.query(By.css('#media_24_description')).nativeElement.innerText).toBe(zone1FeaturedISOs[3].description);
        });

        it('should update template list when changing os type', () => {
            // Select ISO filter
            (filters[1].nativeElement as HTMLInputElement).dispatchEvent(new Event('change'));

            fixture.detectChanges();

            // Select Public filter
            const mediaFilters = templateContainer.queryAll(By.css('.btn-check'));
            mediaFilters[1].nativeElement.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            // template list should show ISOs
            expect(templateContainer.query(By.css('#media_61_name')).nativeElement.innerText).toBe(zone1PublicISOs[0].name);
            expect(templateContainer.query(By.css('#media_62_name')).nativeElement.innerText).toBe(zone1PublicISOs[1].name);
            expect(templateContainer.query(By.css('#media_63_name')).nativeElement.innerText).toBe(zone1PublicISOs[2].name);
            expect(templateContainer.query(By.css('#media_64_name')).nativeElement.innerText).toBe(zone1PublicISOs[3].name);
            expect(templateContainer.query(By.css('#media_65_name')).nativeElement.innerText).toBe(zone1PublicISOs[4].name);

            expect(templateContainer.query(By.css('#media_61_description')).nativeElement.innerText).toBe(zone1PublicISOs[0].description);
            expect(templateContainer.query(By.css('#media_62_description')).nativeElement.innerText).toBe(zone1PublicISOs[1].description);
            expect(templateContainer.query(By.css('#media_63_description')).nativeElement.innerText).toBe(zone1PublicISOs[2].description);
            expect(templateContainer.query(By.css('#media_64_description')).nativeElement.innerText).toBe(zone1PublicISOs[3].description);
            expect(templateContainer.query(By.css('#media_65_description')).nativeElement.innerText).toBe(zone1PublicISOs[4].description);

        });

    });

    describe('virtual machine name', () => {

        let virtualMachineNameInput: HTMLInputElement;

        beforeEach(() => {
            const zone1Input = fixture.debugElement.query(By.css('#zone_1')).nativeElement as HTMLInputElement;
            zone1Input.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const filters = fixture.debugElement.query(By.css('#ostype_iso')).nativeElement as HTMLInputElement;
            filters.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const templateContainer = fixture.debugElement.query(By.directive(MediaSelectorComponent));
            const mediaFilters = templateContainer.query(By.css('#media_21')).nativeElement as HTMLInputElement;
            mediaFilters.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            virtualMachineNameInput = fixture.debugElement.query(By.css('[data-testid="virtual-machine-name-input"]')).nativeElement as HTMLInputElement;
        });

        it('should set virtual machine name and form validity as valid', () => {
            virtualMachineNameInput.value = 'ValidVMName';
            virtualMachineNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            expect(component.store.setupStep.form.virtualMachineName()).toBe('ValidVMName');
            expect(component.store.setupStep.isValid()).toBeTrue();
        });

        it('should set virtual machine name and form validity as invalid', () => {
            virtualMachineNameInput.value = 'Invalid VM Name';
            virtualMachineNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            expect(component.store.setupStep.form.virtualMachineName()).toBe('Invalid VM Name');
            expect(component.store.setupStep.isValid()).toBeFalse();
        });

        it('should set virtual machine name and form validity as invalid when it starts with a digit', () => {
            virtualMachineNameInput.value = '1 Invalid VM Name';
            virtualMachineNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(component.store.setupStep.form.virtualMachineName()).toBe('1 Invalid VM Name');
            expect(component.store.setupStep.isValid()).toBeFalse();
        });

        it('should set virtual machine name and form validity as invalid when it starts with a hyphen', () => {
            virtualMachineNameInput.value = '- Invalid VM Name';
            virtualMachineNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(component.store.setupStep.form.virtualMachineName()).toBe('- Invalid VM Name');
            expect(component.store.setupStep.isValid()).toBeFalse();
        });

        it('should set virtual machine name and form validity as invalid when it ends with a hyphen', () => {
            virtualMachineNameInput.value = 'Invalid VM Name -';
            virtualMachineNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(component.store.setupStep.form.virtualMachineName()).toBe('Invalid VM Name -');
            expect(component.store.setupStep.isValid()).toBeFalse();
        });

        it('should set virtual machine name and form validity as invalid when it contains invalid characters', () => {
            virtualMachineNameInput.value = 'Invalid VM %# Name';
            virtualMachineNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(component.store.setupStep.form.virtualMachineName()).toBe('Invalid VM %# Name');
            expect(component.store.setupStep.isValid()).toBeFalse();
        });

    });

});
