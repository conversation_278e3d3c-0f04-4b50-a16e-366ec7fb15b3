import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { RemoteVpnGatewayService } from '../../services/remote-vpn-gateway.service';
import { CreateRemoteVpnGatewayComponent } from './create-remote-vpn-gateway.component';

describe('CreateRemoteVpnGatewayComponent', () => {

    let fixture: ComponentFixture<CreateRemoteVpnGatewayComponent>;
    let component: CreateRemoteVpnGatewayComponent;

    let mockRemoteVpnGatewayService: jasmine.SpyObj<RemoteVpnGatewayService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateRemoteVpnGatewayComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(RemoteVpnGatewayService)
            ]
        });

        mockRemoteVpnGatewayService = TestBed.inject(RemoteVpnGatewayService) as jasmine.SpyObj<RemoteVpnGatewayService>;
        mockRemoteVpnGatewayService.createRemoteVpnGateway.and.returnValue(of('job-id'));

        fixture = TestBed.createComponent(CreateRemoteVpnGatewayComponent);
        component = fixture.componentInstance;
        component.domainId.set('domain-id');
        component.account.set('account');
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

});
