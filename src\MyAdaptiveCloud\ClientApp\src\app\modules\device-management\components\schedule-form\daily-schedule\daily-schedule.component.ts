import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { debounceTime, distinctUntilChanged, take } from 'rxjs';
import { ScheduleDailyForm } from '@app/modules/device-management/models/schedule/schedule-daily.form';
import { ScheduleStore } from '@app/modules/device-management/components/schedule-form/schedule.component.store';

@Component({
    selector: 'app-daily-schedule',
    imports: [ReactiveFormsModule],
    templateUrl: './daily-schedule.component.html',
    styleUrl: './daily-schedule.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DailyScheduleComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    private readonly scheduleStore = inject(ScheduleStore);
    private readonly destroyRef = inject(DestroyRef);

    protected form: FormGroup<ScheduleDailyForm>;

    public enable() {
        this.form.enable();
    }

    public disable() {
        this.form.disable();
    }

    ngOnInit(): void {
        this.form = this.formBuilder.group<ScheduleDailyForm>({
            freqRecurrenceFactor: new FormControl(null, [
                Validators.min(1),
                Validators.max(999),
                Validators.pattern('[0-9]+'),
                Validators.required
            ])
        });
        this.initializeTypeDailyForm();
        this.scheduleStore.getScheduleTypeFormState$.pipe(take(1))
            .subscribe(scheduleTypeDailyForm => {
                if (scheduleTypeDailyForm.isReadOnly) {
                    this.form.disable();
                } else {
                    this.subscribeToTypeDailyFormChanges();
                }
            });
    }

    private initializeTypeDailyForm() {
        this.scheduleStore.getScheduleTypeDailyFormState$
            .pipe(take(1))
            .subscribe(scheduleTypeDailyForm => {
                this.form.controls.freqRecurrenceFactor.setValue(scheduleTypeDailyForm.freqRecurrenceFactor, { emitEvent: false });
            });
    }

    private subscribeToTypeDailyFormChanges() {
        this.form.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe(_value => {
                this.scheduleStore.updateScheduleTypeDailyForm({
                    freqRecurrenceFactor: _value.freqRecurrenceFactor,
                    isValid: this.form.valid
                });
            });
    }
}
