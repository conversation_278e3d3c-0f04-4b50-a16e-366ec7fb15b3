﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class CloudInfraMappingAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public CloudInfraMappingAuthorizeFilter(IUserContextService userContextService,
            IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public override async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // Fetch orgId
            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int organizationId);
            var hasMapping = await _entityAuthorizationService.GetCloudInfraMappingIdByOrganizationId(organizationId);

            if (hasMapping)
            {
                if (!_userContextService.HasPermission(userId, organizationId, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, organizationId);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
                return;
            }

            return;
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class CloudInfraMappingAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public CloudInfraMappingAuthorizeAttribute(params Perms[] perms) : base(typeof(CloudInfraMappingAuthorizeFilter), perms)
        {
            Name = "organizationId";
        }
    }
}