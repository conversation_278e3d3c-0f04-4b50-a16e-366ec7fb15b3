<div class="mt-3">
    @if (currentUser$ | async; as currentUser) {
        <form [formGroup]="form" (submit)="submitForm()" novalidate>
            <div class="mb-3">
                <label class="form-label">First Name<span class="required-asterisk">*</span></label>
                <input class="form-control" formControlName="firstName" placeholder="First Name"
                    [class]="{ 'is-invalid': form.controls.firstName.invalid && isSubmitted }" required />
            </div>
            <div class="mb-3">
                <label class="form-label">Last Name<span class="required-asterisk">*</span></label>
                <input class="form-control" formControlName="lastName" placeholder="Last Name"
                    [class]="{ 'is-invalid': form.controls.lastName.invalid && isSubmitted }" required />
            </div>
            <div class="mb-3">
                <label class="form-label">Email address</label>
                <input class="form-control" value="{{currentUser.email}}" readonly />
            </div>
            <div class="d-flex mb-3">
                <button type="submit" class="ms-auto btn btn-primary">Save</button>
            </div>
        </form>
    }

    @if (passwordPolicy$ | async; as passwordPolicy) {
        <form id="passwordForm" [formGroup]="passwordForm" (submit)="submitPasswordForm()" novalidate>
            <div class="mb-3">
                <label class="form-label">New Password<span class="required-asterisk">*</span></label>
                <input class="form-control" type="Password" formControlName="newPassword" placeholder="New Password"
                    required id="newPasswordInput" />
            </div>
            @if (newPasswordControl.errors?.isValidPassword) {
                <label class="text-danger">
                    This password does not meet the requirements: more than {{passwordPolicy.length}} characters,
                    {{passwordPolicy.upperCase}} upper case letter,
                    {{passwordPolicy.lowerCase}} lower case letter,
                    {{passwordPolicy.digits}} number, not the same as your username, not the same as your last
                    {{passwordPolicy.passwordHistory}} passwords.
                </label>
            }
            <div class="mb-3">
                <label class="form-label">Confirm New Password<span class="required-asterisk">*</span></label>
                <input class="form-control" type="Password" formControlName="confirmNewPassword"
                    placeholder="Confirm New Password" id="confirmPasswordInput" required />
            </div>
            @if (confirmNewPasswordControl.errors?.isValidConfirmPassword) {
                <label class="text-danger">
                    Passwords do not match.
                </label>
            }
            <div class="d-flex mb-3">
                <button type="submit" class="ms-auto btn btn-primary">Reset Password</button>
            </div>
        </form>
    }
</div>
