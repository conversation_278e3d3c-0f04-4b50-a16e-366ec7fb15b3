import { ChangeDetectionStrategy, Component, ElementRef, inject, input, OnD<PERSON>roy, OnInit, signal, viewChild } from '@angular/core';
import { ShellType } from '@app/shared/models/shell-type.enum';
import { TerminalConnectionRequest } from '@app/shared/requests/terminal-connection.request';
import { DevicesSharedService } from '@app/shared/services/devices-shared.service';
import { FitAddon } from '@xterm/addon-fit';
import { Terminal } from '@xterm/xterm';
import { take } from 'rxjs';

@Component({
    selector: 'app-terminal-legacy',
    templateUrl: './terminal.component.html',
    styleUrl: './terminal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})

export class TerminalLegacyComponent implements OnInit, OnDestroy {
    private readonly devicesService = inject(DevicesSharedService);

    readonly id = input.required<number>();
    readonly type = input.required<ShellType>();

    protected readonly backgroundColor = signal<string>('#1e1e1e');

    private readonly terminalContainer = viewChild.required<ElementRef>('terminalContainer');

    private terminal!: Terminal;
    private fitAddon!: FitAddon;
    private socket!: WebSocket;
    private currentCommandBuffer = '';
    private cursorPosition = 0;
    private commandHistory: string[] = [];
    private historyIndex = -1;
    private lastSentCommand = '';
    private promptPrefix = 'C:\\> ';
    private echoBuffer = '';
    private wsUrl = '';

    ngOnInit(): void {
        const request: TerminalConnectionRequest = {
            shellType: this.type(),
            cols: 80, // Default terminal width
            rows: 24 // Default terminal height
        };
        this.devicesService.getTerminalURL(this.id(), request)
            .pipe(take(1))
            .subscribe(response => {
                this.wsUrl = response.data;
                this.initializeTerminal();
                this.connectToWebSocket();
            });

    }

    private initializeTerminal(): void {
        const color = +this.type() === ShellType.CMD ? '#1e1e1e' : '#012456';
        this.backgroundColor.set(color);
        this.terminal = new Terminal({
            cursorBlink: true,
            fontSize: 14,
            convertEol: true,
            theme: { background: color },
            scrollback: 9999999,
        });

        this.fitAddon = new FitAddon();
        this.terminal.loadAddon(this.fitAddon);
        this.terminal.open(this.terminalContainer().nativeElement);
        this.fitAddon.fit();
        this.terminal.writeln('');
        this.terminal.writeln('Starting terminal...');
        this.terminal.writeln('Type a command and press Enter');

        this.terminal.onData(data => {
            this.handleInput(data);
        });
    }

    private connectToWebSocket(): void {
        this.socket = new WebSocket(this.wsUrl);

        this.socket.onopen = () => {
            this.terminal.writeln('\r\nConnected to relay...');
        };

        this.socket.onmessage = event => {
            const data = event.data;
            this.promptPrefix = data;
            try {
                const message = JSON.parse(data);
                // If backend returns updated prompt
                if (message.output) {
                    this.terminal.write(message.output);
                }

                if (message.prompt) {
                    this.promptPrefix = message.prompt;
                    this.terminal.write(`\r\n${this.promptPrefix} `);
                }
            } catch {
                // Fallback if not JSON

                // Handle echoed command suppression
                const expected = this.lastSentCommand;
                const incoming = data.replace(/[\r\n]+/g, '');

                if (expected && expected.startsWith(this.echoBuffer + incoming)) {
                    this.echoBuffer += incoming;

                    if (expected === this.echoBuffer) {
                        this.echoBuffer = '';
                        this.lastSentCommand = '';
                    }

                } else {
                    // Not part of echo, write it
                    this.terminal.write(data);
                }
            }

            this.terminal.scrollToBottom();
        };

        this.socket.onclose = () => {
            this.terminal.writeln('\r\n[Error] Connection closed. You can no longer enter commands.');
        };

        this.socket.onerror = () => {
            this.terminal.writeln('\r\nError connecting to server.');
        };
    }

    private handleInput(data: string): void {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
            this.terminal.writeln('\r\n[Error] Connection closed. Unable to enter commands.');
            return;
        }
        if (data === '\r' || data === '\n') {
            this.terminal.write('\r\n');

            if (this.currentCommandBuffer.trim() !== '') {
                this.executeCommand(this.currentCommandBuffer);
                this.commandHistory.push(this.currentCommandBuffer);
            }

            this.currentCommandBuffer = '';
            this.cursorPosition = 0;
            this.historyIndex = this.commandHistory.length;

        } else if (data === '\u007F') {
            // Handle Backspace (ASCII 127)
            if (this.cursorPosition > 0) {
                this.currentCommandBuffer = this.currentCommandBuffer.slice(0, this.cursorPosition - 1) + this.currentCommandBuffer.slice(this.cursorPosition);
                this.cursorPosition--;

                this.terminal.write('\b \b');
            }
        } else if (data.startsWith('\u001b')) {
            switch (data[2]) {
                case 'A': // Up Arrow (Previous Command)
                    if (this.historyIndex > 0) {
                        this.historyIndex--;
                        this.replaceCurrentCommand(this.commandHistory[this.historyIndex]);
                    }
                    break;
                case 'B': // Down Arrow (Next Command)
                    if (this.historyIndex < this.commandHistory.length - 1) {
                        this.historyIndex++;
                        this.replaceCurrentCommand(this.commandHistory[this.historyIndex]);
                    } else {
                        this.historyIndex = this.commandHistory.length;
                        this.replaceCurrentCommand('');
                    }
                    break;
                case 'C': // Right Arrow (Move cursor right)
                    if (this.cursorPosition < this.currentCommandBuffer.length) {
                        this.terminal.write('\u001b[C');
                        this.cursorPosition++;
                    }
                    break;
                case 'D': // Left Arrow (Move cursor left)
                    if (this.cursorPosition > 0) {
                        this.terminal.write('\u001b[D');
                        this.cursorPosition--;
                    }
                    break;
            }
        } else {
            const start = this.currentCommandBuffer.slice(0, this.cursorPosition);
            const end = this.currentCommandBuffer.slice(this.cursorPosition);
            this.currentCommandBuffer = start + data + end;

            this.terminal.write(data);
            this.cursorPosition += data.length;
        }
    }

    private replaceCurrentCommand(newCommand: string): void {
        this.clearCurrentLine();
        this.terminal.write(newCommand);
        this.currentCommandBuffer = newCommand;
        this.cursorPosition = newCommand.length;
    }

    private clearCurrentLine(): void {
        this.terminal.write('\x1b[2K\r');
        this.terminal.write(`${this.promptPrefix} `);
        this.currentCommandBuffer = '';
        this.cursorPosition = 0;
    }

    private executeCommand(command: string): void {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
            this.terminal.writeln('\r\n[Error] WebSocket is not connected.');
            return;
        }

        this.lastSentCommand = command.trim();
        this.echoBuffer = '';
        this.socket.send(command);
    }

    ngOnDestroy(): void {
        if (this.socket) {
            this.socket.close();
        }
    }
}
