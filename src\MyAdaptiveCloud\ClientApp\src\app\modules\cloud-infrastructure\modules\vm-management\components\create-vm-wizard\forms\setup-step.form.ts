import { FormControl } from '@angular/forms';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { OsTypeFilter } from '../../../models/os-type-filter.enum';
import { OsType } from '../../../models/os-type.enum';
import { TemplateViewModel } from '../../../models/template.view.model';

export interface SetupStepForm {
    zone: FormControl<ZoneViewModel>;
    template: FormControl<TemplateViewModel>;
    osType: FormControl<OsType>;
    osTypeFilter: FormControl<OsTypeFilter>;
    virtualMachineName: FormControl<string | null>;
}

export interface SetupStepFormValue {
    zone: ZoneViewModel;
    template: TemplateViewModel;
    osType: OsType;
    osTypeFilter: OsTypeFilter;
    virtualMachineName: string | null;
}
