import { AsyncPipe, DatePipe } from '@angular/common';
import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { BackComponent } from '@app/shared/components/back/back.component';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { Observable, map } from 'rxjs';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-acusage-runlog-details',
    imports: [BackComponent, NgxDatatableModule, AsyncPipe, DatePipe],
    templateUrl: './acusage-runlog-details.component.html'
})
export class AcusageRunlogDetailsComponent implements OnInit {
    private readonly usageService = inject(AdaptiveCloudUsageService);
    private readonly route = inject(ActivatedRoute);
    private readonly destroyRef = inject(DestroyRef);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    details: Observable<any>;
    columns: TableColumn[];

    ngOnInit(): void {
        const id = parseInt(this.route.snapshot.paramMap.get('id'), 10);
        this.columns = [
            { name: 'CreatedAt' },
            { name: 'Level Name' },
            { name: 'Message', prop: 'msg' }
        ];
        this.onActivate(id);
    }

    onActivate(id: number) {
        this.details = this.usageService.getAcUsageRunLogDetails(id)
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                map(res => res.data.sort((a, b) => a.createdAt.localeCompare(b.createdAt)))
            );
    }
}
