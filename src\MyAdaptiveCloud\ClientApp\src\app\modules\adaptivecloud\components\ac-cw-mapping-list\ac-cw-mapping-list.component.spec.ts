import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ModalService } from '@app/shared/services/modal.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { BillingStartDateStatus } from '../../models/ac-mapping-billing-start-date-status.model';
import { BillingStatus } from '../../models/ac-mapping-billing-status.model';
import { AcMapping } from '../../models/ac-mapping.model';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';
import { AcToCwMappingListComponent } from './ac-cw-mapping-list.component';

describe('AcToCwMappingListComponent', () => {

    let component: AcToCwMappingListComponent;
    let fixture: ComponentFixture<AcToCwMappingListComponent>;
    let mockMappingService: jasmine.SpyObj<AcToCwMappingService>;

    const data: AcMapping[] = [
        {
            acId: '1',
            acName: 'Mapping 1',
            acEntityExists: true,
            acType: 'type',
            billingStartDateStatus: BillingStartDateStatus.LocalInTheFuture,
            billingStatus: BillingStatus.BillingFuture,
            cwAgreementName: 'Agreement 1',
            cwCompanyName: 'Company 1',
            cwCompanyIdentifier: 'CIW',
            enabled: true,
            id: 1
        },
        {
            acId: '2',
            acName: 'Mapping 2',
            acEntityExists: true,
            acType: 'type',
            billingStartDateStatus: BillingStartDateStatus.Default,
            billingStatus: BillingStatus.NewDomain,
            cwAgreementName: 'Agreement 2',
            cwCompanyName: 'Company 2',
            cwCompanyIdentifier: 'CIW2',
            enabled: true,
            id: 2
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AcToCwMappingListComponent
            ],
            providers: [
                provideMock(AcToCwMappingService),
                provideMock(PermissionService),
                provideMock(NgbModal),
                provideMock(ModalService)
            ]
        });

        mockMappingService = TestBed.inject(AcToCwMappingService) as jasmine.SpyObj<AcToCwMappingService>;
        mockMappingService.getMappings.and.returnValue(of({ data, totalCount: 2 }));

        fixture = TestBed.createComponent(AcToCwMappingListComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();

    });

    describe('Initialization', () => {

        it('should have the same amount of rows as data', () => {
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
            expect(mockMappingService.getMappings).toHaveBeenCalledOnceWith(component.pagination);
        });

    });
});
