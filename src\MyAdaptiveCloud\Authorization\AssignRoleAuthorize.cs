﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{

    /// <summary>
    ///     Verifies that the role to add is available to the organization and that the user has the necessary permissions
    ///     The organization must be supplied in the route data, and the role must be supplied in the request body as "RoleId"
    ///     Also checks that the user has the AssignRestrictedRoles permission if the role is restricted, regardless of the permissions passes to the attribute
    /// </summary>
    public class AssignRoleAuthorizeFilter : BaseAsyncBulkAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public AssignRoleAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
            IUserContextService userContextService, Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                await next();
            }

            context.ActionArguments.TryGetValue(_requestName, out var request);
            var roleIdToAssign = (int)request.GetType().GetProperty("RoleId").GetValue(request);

            _ = int.TryParse(context.RouteData.Values["organizationId"].ToString(), out int organizationIdToAssignRoleIn);
            var roleIsAvailableToOrganization = await _entityAuthorizationService.RoleIsAvailableToOrganization(roleIdToAssign, organizationIdToAssignRoleIn);

            // If the role to add is not available to the organization, don't authorize
            if (!roleIsAvailableToOrganization)
            {
                context.Result = new ForbidResult();
                return;
            }

            // If the role to add is restricted, check that the caller has AssignRestrictedRoles, regardless of the permissions passed to the attribute
            if ((await _entityAuthorizationService.RoleIsRestricted(roleIdToAssign)) && !_userContextService.HasPermission(userId, organizationIdToAssignRoleIn, 0, Perms.AssignRestrictedRoles))
            {
                context.Result = new ForbidResult();
                return;
            }

            if (!_userContextService.HasPermission(userId, organizationIdToAssignRoleIn, 0, _perms))
            {
                context.Result = new ForbidResult();
                return;
            }

            await next();
        }
    }

    public class AssignRoleAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public AssignRoleAuthorizeAttribute(params Perms[] perms) : base(typeof(AssignRoleAuthorizeFilter), perms)
        {
            Name = "roleId";
        }
    }
}

