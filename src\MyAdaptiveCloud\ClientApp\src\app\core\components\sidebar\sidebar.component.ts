import { UpperCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, OnInit, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NavigationEnd, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { HubCountComponent } from '@app/shared/components/hub-count/hub-count.component';
import { MenuItem } from '@app/shared/models/menu-item';
import { UserContext } from '@app/shared/models/user-context.model';
import { GlobalEventsService } from '@app/shared/services/global-events.service';
import { MenuService } from '@app/shared/services/menu.service';
import { NgbCollapse, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { filter } from 'rxjs';

@Component({
    selector: 'app-sidebar',
    imports: [RouterLink, NgbCollapse, NgbPopover, HubCountComponent, RouterLinkActive, UpperCasePipe],
    templateUrl: './sidebar.component.html',
    styleUrl: './sidebar.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SidebarComponent implements OnInit {
    private readonly menuService = inject(MenuService);
    private readonly router = inject(Router);
    private readonly destroyRef = inject(DestroyRef);
    private readonly globalEventsService = inject(GlobalEventsService);

    readonly userContext = input<UserContext>();
    protected readonly menuItems = signal<MenuItem[]>([]);
    protected readonly expandedSubMenuId = signal<number>(null);
    protected readonly navbarExpanded = signal<boolean>(true);
    protected readonly currentRoute = signal<string>(null);

    ngOnInit(): void {
        this.currentRoute.set(`/${this.router.url.split('/')[1]}`);
        this.router.events.pipe(
            filter(event => event instanceof NavigationEnd),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(() => {
            this.setSelectedMenuItems();
            this.currentRoute.set(`/${this.router.url.split('/')[1]}`);
        });

        this.menuService.reloadMenuItems$.pipe(
            filter(reload => reload),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(() => {
            this.setMenuItems();
        });

        this.setMenuItems();
    }

    private setMenuItems() {
        this.menuService.getItems(this.userContext().organizationId).subscribe(res => {
            this.menuItems.set(res.data);
            this.setSelectedMenuItems();
        });
    }

    private setSelectedMenuItems(): void {
        for (let index = 0; this.menuItems() && index < this.menuItems().length; index++) {
            const menuItem = this.menuItems()[index];
            const selectedSubMenuItem = menuItem.submenu?.find(subMenu => subMenu.route === this.router.url);
            if (selectedSubMenuItem) {
                this.expandedSubMenuId.set(index);
                break;
            }
        }
    }

    protected topLevelNavigate(): void {
        this.expandedSubMenuId.set(null);
    }

    protected toggleSubmenu(id: number): void {
        this.expandedSubMenuId.update(value => (value === id ? null : id));
    }

    protected isSubMenuCollapsed(id: number): boolean {
        return this.expandedSubMenuId() !== id;
    }

    protected toggleNavbar(state: boolean): void {
        this.navbarExpanded.set(state);
        this.globalEventsService.toggleSidenav(state);
    }
}
