import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { CloudInfraDomainViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-domain.view-model';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { Network } from '../../../../models/network';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { NetworkingService } from '../../services/networking.service';
import { ListNetworksComponent } from './list-networks.component';
import { ActivatedRoute } from '@angular/router';

describe('ListNetworksComponent', () => {
    let fixture: ComponentFixture<ListNetworksComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockNetworkingService: jasmine.SpyObj<NetworkingService>;
    let mockNetworkingPermissionService: jasmine.SpyObj<NetworkingPermissionService>;

    const networks: Network[] = [
        {
            id: 'b403fc9e-380e-41c5-9aff-2c6b1bdc9f12',
            name: 'Gorilla L2',
            displaytext: 'Gorilla L2',
            broadcastdomaintype: 'Vlan',
            traffictype: 'Guest',
            zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
            zonename: 'DC1',
            networkofferingid: 'c79b6615-9b2a-4d34-b43d-bc866d262cb3',
            networkofferingname: 'DefaultL2NetworkOffering',
            networkofferingdisplaytext: 'Offering for L2 networks',
            networkofferingconservemode: true,
            networkofferingavailability: 'Optional',
            issystem: false,
            state: 'Implemented',
            related: 'b403fc9e-380e-41c5-9aff-2c6b1bdc9f12',
            broadcasturi: 'vlan://327',
            dns1: '*************',
            dns2: '*************',
            type: 'L2',
            vlan: '327',
            acltype: 'Account',
            account: 'Gorilla',
            domainid: 'dda0f2d2-b998-4d92-806b-8380b830f08c',
            domain: 'gregsgaming',
            physicalnetworkid: '8257e2b2-5170-45b0-a35d-eb19989c1598',
            restartrequired: false,
            specifyipranges: false,
            canusefordeploy: true,
            ispersistent: false,
            tags: [],
            displaynetwork: true,
            strechedl2subnet: false,
            redundantrouter: false,
            supportsvmautoscaling: false,
            created: '2025-06-12T13:42:56+0000',
            receivedbytes: 0,
            sentbytes: 0,
            publicmtu: 1500,
            privatemtu: 1500,
            ip6dns1: '',
            ip6dns2: '',
            hasannotations: false
        },
        {
            id: '935cbe23-781a-4737-91bb-2ef0b510887d',
            name: 'bhar test',
            displaytext: 'vb',
            broadcastdomaintype: 'Vlan',
            traffictype: 'Guest',
            zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
            zonename: 'DC1',
            networkofferingid: 'c79b6615-9b2a-4d34-b43d-bc866d262cb3',
            networkofferingname: 'DefaultL2NetworkOffering',
            networkofferingdisplaytext: 'Offering for L2 networks',
            networkofferingconservemode: true,
            networkofferingavailability: 'Optional',
            issystem: false,
            state: 'Implementing',
            related: '935cbe23-781a-4737-91bb-2ef0b510887d',
            broadcasturi: 'vlan://321',
            dns1: '*************',
            dns2: '*************',
            type: 'L2',
            vlan: '321',
            acltype: 'Account',
            account: 'Root',
            domainid: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
            domain: 'ROOT',
            physicalnetworkid: '8257e2b2-5170-45b0-a35d-eb19989c1598',
            restartrequired: false,
            specifyipranges: false,
            canusefordeploy: true,
            ispersistent: false,
            tags: [],
            displaynetwork: true,
            strechedl2subnet: false,
            redundantrouter: false,
            supportsvmautoscaling: false,
            created: '2025-06-11T16:35:27+0000',
            receivedbytes: 0,
            sentbytes: 0,
            publicmtu: 1500,
            privatemtu: 1500,
            ip6dns1: '',
            ip6dns2: '',
            hasannotations: false,
            vpcname: 'bhar-test-vpc',
            cidr: '********/24',
        }, {
            id: '0acf880d-ff14-4c3c-a020-9663622c02bc',
            name: 'kyle-test',
            displaytext: 'test',
            broadcastdomaintype: 'Vlan',
            traffictype: 'Guest',
            cidr: '********/24',
            zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
            zonename: 'DC1',
            networkofferingid: '013f55d9-c410-4f2d-874c-57e4b4b0cb1c',
            networkofferingname: 'DefaultNetworkOfferingforKubernetesService',
            networkofferingdisplaytext: 'Network Offering used for CloudStack Kubernetes service',
            networkofferingconservemode: true,
            networkofferingavailability: 'Required',
            issystem: false,
            state: 'Allocated',
            related: '0acf880d-ff14-4c3c-a020-9663622c02bc',
            dns1: '*************',
            dns2: '*************',
            type: 'Isolated',
            acltype: 'Account',
            account: 'kyle-testNFS4',
            domainid: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
            domain: 'ROOT',
            networkdomain: 'cs1d9cloud.internal',
            physicalnetworkid: '8257e2b2-5170-45b0-a35d-eb19989c1598',
            restartrequired: false,
            specifyipranges: false,
            canusefordeploy: true,
            ispersistent: false,
            tags: [],
            displaynetwork: true,
            strechedl2subnet: false,
            redundantrouter: false,
            supportsvmautoscaling: true,
            created: '2025-06-03T20:21:34+0000',
            receivedbytes: 0,
            sentbytes: 0,
            egressdefaultpolicy: true,
            publicmtu: 1500,
            privatemtu: 1500,
            ip6dns1: '',
            ip6dns2: '',
            hasannotations: false
        }
    ];

    beforeEach(async () => {

        const mockZoneDomainAccountStore = {
            ...getMockZoneDomainAccountStore(),
            selectedDomain: signal({ id: '351434a0-c7e0-11eb-bbc8-005056b1c79a' } as CloudInfraDomainViewModel),
            getAccount: signal(null),
            zones: signal([{ name: 'DC1', id: 'c0665c38-48b7-456b-9629-7327bc4f90f2' }])
        };
        TestBed.configureTestingModule({
            imports: [
                ListNetworksComponent
            ],
            providers: [
                provideMock(UserContextService),
                provideMock(NetworkingService),
                provideMock(NetworkingPermissionService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
                provideMock(ActivatedRoute)
            ]
        });

        mockNetworkingService = TestBed.inject(NetworkingService) as jasmine.SpyObj<NetworkingService>;
        mockNetworkingService.getNetworks.and.returnValue(of(networks));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        mockUserContextService.currentUser = {
            organizationId: 1,
            cloudInfraUserContext: {
                accountName: 'account',
                domainId: 'domainId',
                hasMappedDomain: true,
            } as CloudInfraUserContext
        } as UserContext;

        mockNetworkingPermissionService = TestBed.inject(NetworkingPermissionService) as jasmine.SpyObj<NetworkingPermissionService>;
        mockNetworkingPermissionService.canViewNetworkList.and.returnValue(true);

        fixture = TestBed.createComponent(ListNetworksComponent);
    });

    describe('Initialization', () => {

        it('should call getNetworks without account when context have a mapped domain', () => {
            mockUserContextService.currentUser = {
                organizationId: 1,
                cloudInfraUserContext: {
                    accountName: 'account',
                    domainId: 'domainId',
                    hasMappedDomain: true,
                } as CloudInfraUserContext
            } as UserContext;

            fixture.detectChanges();

            expect(mockNetworkingService.getNetworks).toHaveBeenCalledOnceWith('domainId', null);

        });

        it('should call getNetworks with account when context does not have a mapped domain', () => {
            mockUserContextService.currentUser = {
                organizationId: 1,
                cloudInfraUserContext: {
                    accountName: 'account',
                    domainId: 'domainId',
                    hasMappedDomain: false,
                } as CloudInfraUserContext
            } as UserContext;

            fixture.detectChanges();

            expect(mockNetworkingService.getNetworks).toHaveBeenCalledOnceWith('domainId', 'account');
        });

        it('should have the same amount of rows as data', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(networks.length - 1); // Exclude the first network which does not match the domain
        });
    });

    describe('Data Binding', () => {

        it('should display the network state and icon in the 1 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(1)').textContent.trim()).toEqual(networks[1].state);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(1)').textContent.trim()).toEqual(networks[2].state);

            // Check if contains the correct icon for the state
            const icons = dataTable.querySelectorAll('.datatable-body-cell:nth-child(1) .status-icon');
            expect(icons[0].classList).toContain('icon-network-implementing');
            expect(icons[1].classList).toContain('text-primary');
        });

        it('should display the network name in the 2 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(networks[1].name);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(networks[2].name);
        });

        it('should display the account name in the 3 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(networks[1].account);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(networks[2].account);
        });

        it('should display the network type in the 4 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(networks[1].type);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(networks[2].type);
        });

        it('should display the network zone name in the 7 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(7)').textContent.trim()).toEqual(networks[1].zonename);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(7)').textContent.trim()).toEqual(networks[2].zonename);
        });

    });
});
