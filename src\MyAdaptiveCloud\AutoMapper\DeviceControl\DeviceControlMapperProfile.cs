﻿using AutoMapper;
using MyAdaptiveCloud.Api.Requests.DeviceControl;

namespace MyAdaptiveCloud.Api.AutoMapper.DeviceControl
{
    public class DeviceControlMapperProfile : Profile
    {
        public DeviceControlMapperProfile()
        {
            CreateMap<DeviceControlRequest, Services.Requests.DeviceControl.CreateDeviceControlRequest>()
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceId, opt => opt.Ignore());
        }
    }
}
