<nav class="navbar" role="navigation">
    <div class="d-flex w-100">
        @if (userContext().isRegistered) {
            <div class="logo">
                <img [appWhitelabelImage]="whiteLabelImageType.headerLogo" [organizationId]="userContext().organizationId"
                    alt="{{ userContext().organizationName }}" />
            </div>
        }
        @else
        {
            <div class="logo">
                <img [appWhitelabelImage]="whiteLabelImageType.headerLogo" alt="logo" />
            </div>
        }
        <div class="ms-auto">
            @if (userContext().isRegistered) {
                <button type="button" title="Select Organization" class="btn btn-primary" (click)="orgSelectModal()">{{
                    userContext().organizationName }}</button>
                <a class="btn text-white" title="Profile" (click)="editProfile()">
                    <i class="fas fa-user"></i>
                </a>
                @if (showNotificationTray()) {
                    <app-notification-tray />
                }
            }
            <a class="btn text-white" title="Logout" (click)="logout()">
                <i class="fa fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</nav>
