services:
  worker-services:
    user: "${MY_UID}:${MY_GID}"
    container_name: "${COMPOSE_PROJECT_NAME}-worker-services"
    build:
      context: ./
      dockerfile: ./dockerfile
    working_dir: /home/<USER>/myac-worker
    command: dotnet MyAdaptiveCloud.WorkerServices.dll
    restart: always
    logging:
      driver: syslog
      options:
        tag: "{{.Name}}"
    volumes:
      - type: bind
        source: /home/<USER>
        target: /home/<USER>
      - ./docker_passwd:/etc/passwd
      - ./docker_group:/etc/group
      # Mount the NFS volume that is used by AdaptiveCloud Drive
      - /mnt/dsm1_adaptivecloud_drive01:/mnt/dsm1_adaptivecloud_drive01
    extra_hosts:
      - "cloud.adaptivecloud.com:************"
      - "sso.adaptivecloud.com:************"
      - "remote-api.adaptivecloud.com:************"
      - "imap.ippathways.com:**********"
