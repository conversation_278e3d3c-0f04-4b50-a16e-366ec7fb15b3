using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class UserRoleAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public UserRoleAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService,
            IIdentityService identityService, IUserContextService userContextService, Perms[] perms, int distance, string name)
            : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int userOrganizationRoleId);

            var userRoleOrganizationId = await _entityAuthorizationService.GetUserRoleOrganizationId(userOrganizationRoleId);
            if (userRoleOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, userRoleOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, userRoleOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class UserRoleAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public UserRoleAuthorizeAttribute(params Perms[] perms) : base(typeof(UserRoleAuthorizeFilter), perms)
        {
            Name = "userOrganizationRoleId";
        }
    }
}