import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { NetworkingService } from '../../services/networking.service';
import { RestartNetworkComponent } from './restart-network.component';

describe('RestartNetworkComponent', () => {

    let component: RestartNetworkComponent;
    let fixture: ComponentFixture<RestartNetworkComponent>;
    let mockNetworkingServiceService: jasmine.SpyObj<NetworkingService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [RestartNetworkComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(NetworkingService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(RestartNetworkComponent);
        component = fixture.componentInstance;
        mockNetworkingServiceService = TestBed.inject(NetworkingService) as jasmine.SpyObj<NetworkingService>;
        mockNetworkingServiceService.restartNetwork.and.returnValue(of('jobId1'));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.networkId.set('test-id');
        fixture.detectChanges();
    });

    it('should close modal on cancel', () => {
        const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
        cancelButton.click();
        fixture.detectChanges();

        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    it('should close modal with response on successful rebootVirtualMachine call', () => {
        const cleanupControl = fixture.debugElement.query(By.css('[data-testid="cleanup-checkbox"]')).nativeElement as HTMLInputElement;
        cleanupControl.checked = true;
        cleanupControl.dispatchEvent(new Event('change'));
        fixture.detectChanges();

        const makeRedundantControl = fixture.debugElement.query(By.css('[data-testid="make-redundant-checkbox"]')).nativeElement as HTMLInputElement;
        makeRedundantControl.checked = true;
        makeRedundantControl.dispatchEvent(new Event('change'));
        fixture.detectChanges();

        const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
        submit.click();
        fixture.detectChanges();

        expect(mockNetworkingServiceService.restartNetwork).toHaveBeenCalledOnceWith('test-id', true, true);
        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

});

