@if ((userContextService.currentUser$ | async); as userContext) {
    <div class="wrapper">
        <div class="topnavbar-wrapper">
            <app-header [userContext]="userContext" />
        </div>
        <div class="main-section-wrapper">
            @if (userContext.isRegistered && userContext.isApproved) {
                <div class="aside-container">
                    <app-sidebar [userContext]="userContext" />
                </div>
            }
            <section class="section-container">
                <div class="content-wrapper">
                    <router-outlet />
                </div>
                @if (userContext.isRegistered && userContext.isApproved) {
                    <app-footer />
                }
            </section>
        </div>
    </div>
}
<app-loading />
<app-notification />
