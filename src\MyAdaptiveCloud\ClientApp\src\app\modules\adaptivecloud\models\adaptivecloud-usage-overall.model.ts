export class AdaptiveCloudUsage {
    public usageType: string;
    public productMapId: number;
    public label: string;
    public units: string;
    public actualUsage: number;
    public quantity: number;
    public unitPrice: number;
    public cost: number;
    public month: Date;
}

export class AdaptiveCloudUsageOverall {
    public acId: string;
    public acName: string;
    public acType: string;
    public vCPUs: AdaptiveCloudUsage;
    public ram: AdaptiveCloudUsage;
    public ipAddresses: AdaptiveCloudUsage;
    public networkBytes: AdaptiveCloudUsage;
    public primaryStorage: AdaptiveCloudUsage;
    public secondaryStorage: AdaptiveCloudUsage;
    public licensing: AdaptiveCloudUsage[];
}
