using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    ///     Verifies that the current logged in user has permission to execute the operation in all of the target person's organizations,
    ///     or, when the <see cref="_requirePermissionOnAllOrganizations"/> parameter is false, in at least one of the target person's organizations.
    ///     If the current logged in user and the target user are the same, and the <see cref="_allowSelf"/> parameter is true, then the operation is allowed, 
    ///     regardless of organization permissions.
    ///     This attribute won't set the organization id in the context, so the use of [FromAuth] won't be available.
    /// </summary>
    public class PersonAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        private readonly bool _allowSelf;
        private readonly bool _requirePermissionOnAllOrganizations;

        public PersonAuthorizeFilter(IUserContextService userContextService, IIdentityService identityService, IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name, bool allowSelf, bool requirePermissionOnAllOrganizations) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
            _allowSelf = allowSelf;
            _requirePermissionOnAllOrganizations = requirePermissionOnAllOrganizations;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            int.TryParse(val, out int targetPersonId);

            var targetPerson = await _entityAuthorizationService.GetPerson(targetPersonId);

            if (targetPerson == null)
            {
                context.Result = new BadRequestResult();
                return;
            }

            if (targetPersonId != 0 && _allowSelf && targetPersonId == userId)
            {
                // This should be allowed, as it is a person operating on themself, and the allowSelf flag is true
                return;
            }

            // Gets the active organization ids where the target person in a contact, and
            // check if the caller user has permissions in at least one of them
            var organizationIds = await _entityAuthorizationService.GetPersonOrganizationIds(targetPersonId);

            // If the target person is not a member of any active organization, then the caller user is not authorized
            if (!organizationIds.Any())
            {
                context.Result = new ForbidResult();
                return;
            }

            var isAuthorized = false;

            // The caller user should have permissions in at least one if the organizations the target user is member of
            if (!_requirePermissionOnAllOrganizations)
            {
                foreach (var organizationId in organizationIds)
                {
                    if (_perms != null && _userContextService.HasPermission(userId, organizationId, _distance, _perms))
                    {
                        isAuthorized = true;
                        break;
                    }
                }
            }
            // The caller user should have permissions in all the organizations the target user is member of
            else
            {
                isAuthorized = true;
                foreach (var organizationId in organizationIds)
                {
                    if (_perms != null && !_userContextService.HasPermission(userId, organizationId, _distance, _perms))
                    {
                        isAuthorized = false;
                        break;
                    }
                }
            }

            if (!isAuthorized)
            {
                context.Result = new ForbidResult();
                return;
            }
        }

        /// <summary>
        /// Specifies what minimum Role is required within the target Organization to access this endpoint.
        /// </summary>
        /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
        [AttributeUsage(AttributeTargets.Method, Inherited = false)]
        public class PersonAuthorizeAttribute : BaseAuthorizeAttribute
        {
            private bool _allowSelf = false;
            private bool _requirePermissionOnAllOrganizations = true;

            /// <summary>
            /// Set to true if this should also allow the user to operate on themself, regardless of their role
            /// </summary>
            public bool AllowSelf
            {
                get { return _allowSelf; }
                set
                {
                    _allowSelf = value;
                    Arguments = new object[] { _perms, _name, _distance, _allowSelf, _requirePermissionOnAllOrganizations };
                }
            }

            /// <summary>
            ///     This flag indicates if the caller should have permissions in all the organizations the target user is member of. Default is true.
            /// </summary>
            public bool RequirePermissionOnAllOrganizations
            {
                get { return _requirePermissionOnAllOrganizations; }
                set
                {
                    _requirePermissionOnAllOrganizations = value;
                    Arguments = new object[] { _perms, _name, _distance, _allowSelf, _requirePermissionOnAllOrganizations };
                }
            }

            public PersonAuthorizeAttribute(params Perms[] perms) : base(typeof(PersonAuthorizeFilter), perms)
            {
                Name = "userId";
                Arguments = new object[] { _perms, _name, _distance, _allowSelf, _requirePermissionOnAllOrganizations };
            }
        }
    }
}