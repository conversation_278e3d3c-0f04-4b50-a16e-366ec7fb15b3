import { signal } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { of } from 'rxjs';
import { OsTypeFilter } from '../../../../models/os-type-filter.enum';
import { OsType } from '../../../../models/os-type.enum';
import { TemplateViewModel } from '../../../../models/template.view.model';
import { VmNetwork } from '../../../../models/vm-network.model';
import { VmAffinityGroupsService } from '../../../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../../../services/vm-management.service';
import { VmMediaService } from '../../../../services/vm-media-service';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { CreateVmWizardStepEnum } from '../../models/create-vm-wizard-steps.enum';
import { DiskOffering } from '../../models/disk-offering.model';
import { ServiceOfferingViewModel } from '../../models/service-offering.view-model';
import { CreateVmComputeService } from '../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../services/create-vm-network.service';
import { CreateVmService } from '../../services/create-vm-service';
import { CreateVmStorageService } from '../../services/create-vm-storage.service';
import { CreateVmWizardStorageComponent } from './storage.component';

describe('CreateVmWizardStorageComponent', () => {
    let component: CreateVmWizardStorageComponent;
    let fixture: ComponentFixture<CreateVmWizardStorageComponent>;
    let mockCreateVmComputeService: jasmine.SpyObj<CreateVmComputeService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockStorageService: jasmine.SpyObj<CreateVmStorageService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateNetworkService: jasmine.SpyObj<CreateNetworkService>;
    let mockCreateVmNetworkService: jasmine.SpyObj<CreateVmNetworkService>;

    const mockDiskOfferings: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        },
        {
            id: '3',
            offeringName: 'Custom Offering',
            diskSize: null,
            description: 'Custom Offering description',
            isCustomized: true
        }
    ];

    const zones: ZoneViewModel[] = [
        {
            id: '1',
            name: 'Zone 1'
        },
        {
            id: '2',
            name: 'Zone 2'
        },
        {
            id: '3',
            name: 'Zone 3'
        }
    ];

    const mockServiceOfferings: ServiceOfferingViewModel[] = [
        {
            name: 'name 1',
            id: '1',
            cpuNumber: 4,
            memory: 4096,
            isCustom: false
        }, {
            name: 'name 2',
            id: '2',
            cpuNumber: 2,
            memory: 2048,
            isCustom: false
        },
        {
            name: 'name 3',
            id: '3',
            cpuNumber: null,
            memory: null,
            isCustom: true
        }
    ];

    const zone1ISOs: TemplateViewModel[] = [
        {
            name: 'CentOS 7.8',
            id: '1',
            size: 123456,
            description: 'CentOS 7.8',
        }
    ];

    const zone1Templates: TemplateViewModel[] = [
        {
            name: 'CentOS 8',
            id: '2',
            size: 100,
            description: 'CentOS 8',
        }
    ];

    const mockVmNetwork: VmNetwork = {
        id: '1',
        name: 'Test Network',
        cidr: '***********/24',
        type: 'Private',
        vpcname: 'Test VPC',
        ipaddress: '***********',
        macaddress: '00:11:22:33:44:55',
        gateway: '*************'
    };

    beforeEach(() => {

        const mockZoneDomainAccountStore = { ...getMockZoneDomainAccountStore(), zones: signal(zones) };

        TestBed.configureTestingModule({
            imports: [CreateVmWizardStorageComponent],
            providers: [
                CreateVMWizardStore,
                provideMock(CreateVmComputeService),
                provideMock(CreateVmStorageService),
                provideMock(VmMediaService),
                provideMock(CreateNetworkService),
                provideMock(CreateVmNetworkService),
                provideMock(CloudInfraPermissionService),
                provideMock(CreateVmService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(UserContextService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
            ]
        });

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.getFeaturedISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyISOsByZoneId.and.returnValue(of(zone1ISOs));

        mockVmMediaService.getFeaturedTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyTemplatesByZoneId.and.returnValue(of(zone1ISOs));

        mockCreateVmComputeService = TestBed.inject(CreateVmComputeService) as jasmine.SpyObj<CreateVmComputeService>;
        mockCreateVmComputeService.getServiceOfferings.and.returnValue(of(mockServiceOfferings));

        mockStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockStorageService.getDiskOfferings.and.returnValue(of(mockDiskOfferings));

        mockCreateNetworkService = TestBed.inject(CreateNetworkService) as jasmine.SpyObj<CreateNetworkService>;
        mockCreateNetworkService.getLayer2NetworkOfferings.and.returnValue(of([{ id: '1', name: 'L2 Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getIsolatedNetworkOfferings.and.returnValue(of([{ id: '10', name: 'Isolated Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getSharedNetworkOfferings.and.returnValue(of([{ id: '100', name: 'Shared Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getVpcOfferings.and.returnValue(of([{ id: '1000', name: 'VPC Offering', cidr: '' }]));

        mockCreateVmNetworkService = TestBed.inject(CreateVmNetworkService) as jasmine.SpyObj<CreateVmNetworkService>;
        mockCreateVmNetworkService.getNetworks.and.returnValue(of([mockVmNetwork]));

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of([]));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of([]));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'test-account',
                domainId: 'test-domain-id',
                diskSizeCustomOfferingMaxValue: 1024,
                diskSizeCustomOfferingMinValue: 1,
            }
        } as UserContext;
        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

        fixture = TestBed.createComponent(CreateVmWizardStorageComponent);
        component = fixture.componentInstance;
        component.store.setZone(zones[0]);
        component.store.loadDataByZoneId();
    });

    describe('Initialization', () => {

        beforeEach(() => {
            component.store.setTemplate({ zone: zones[0], osType: OsType.Template, osTypeFilter: OsTypeFilter.Featured, template: zone1Templates[0], virtualMachineName: 'VM Name' }, true);
            component.store.navigateToStep(CreateVmWizardStepEnum.Storage);
            fixture.detectChanges();
        });

        it('should set disk options', () => {
            expect(component.store.storageStep.diskOfferings()).toEqual(mockDiskOfferings);
            expect(component.store.storageStep.rootDisk()).toEqual({
                id: '',
                offeringName: 'Default',
                description: 'Default Root Disk',
                diskSize: null,
                isCustomized: false,
            });
            expect(component.diskOptions().length).toBe(3);
        });

    });

    describe('Template storage', () => {

        beforeEach(() => {
            component.store.setTemplate({ zone: zones[0], osType: OsType.Template, osTypeFilter: OsTypeFilter.Featured, template: zone1Templates[0], virtualMachineName: 'VM Name' }, true);
            component.store.navigateToStep(CreateVmWizardStepEnum.Storage);
            fixture.detectChanges();
        });

        it('should set root disk', () => {
            expect(component.store.storageStep.rootDisk()).toEqual({
                id: '',
                offeringName: 'Default',
                description: 'Default Root Disk',
                diskSize: null,
                isCustomized: false,
            });
            expect(component.store.storageStep.isValid()).toBeTrue();
        });

        it('should add disk', fakeAsync(() => {
            expect(component.store.storageStep.selectedDataDisks.length).toBe(0);

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(1);
            expect(component.store.storageStep.selectedDataDisks()[0]).toEqual(mockDiskOfferings[0]);
            expect(component.store.storageStep.isValid()).toBeTrue();
        }));

        it('should remove disk', fakeAsync(() => {
            expect(component.store.storageStep.selectedDataDisks.length).toBe(0);

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            const removeDiskButton = fixture.debugElement.query(By.css('#remove-disk')).nativeElement as HTMLButtonElement;
            removeDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks.length).toBe(0);

        }));

        it('should add custom disk', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 2, true, 0);
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(0);

            const inputDiskSize = fixture.debugElement.queryAll(By.css('#inputDiskSize'))[1].nativeElement as HTMLInputElement;
            inputDiskSize.value = '100';
            inputDiskSize.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(1);
            expect(component.store.storageStep.selectedDataDisks()[0]).toEqual({
                id: '3',
                offeringName: 'Custom Offering',
                description: 'Custom Offering description',
                diskSize: 100,
                isCustomized: true,
            });
        }));

        it('should not add custom disk when disk size is invalid', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 2, true, 0);
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(0);

            const inputDiskSize = fixture.debugElement.queryAll(By.css('#inputDiskSize'))[0].nativeElement as HTMLInputElement;
            inputDiskSize.value = '0';
            inputDiskSize.dispatchEvent(new Event('input'));
            tick(251);
            fixture.detectChanges();

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(0);
        }));

        it('should not add custom disk when disk size exceeds maximum value', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 2, true, 0);
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(0);

            const inputDiskSize = fixture.debugElement.queryAll(By.css('#inputDiskSize'))[0].nativeElement as HTMLInputElement;
            inputDiskSize.value = '1025';
            inputDiskSize.dispatchEvent(new Event('input'));
            tick(251);
            fixture.detectChanges();

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(0);
        }));

        it('should add and remove disks', fakeAsync(() => {
            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();
            addDiskButton.click();
            fixture.detectChanges();
            expect(component.store.storageStep.isValid()).toBeTrue();

            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();
            addDiskButton.click();
            fixture.detectChanges();
            expect(component.store.storageStep.isValid()).toBeTrue();

            selectOption(fixture, 'ng-select', 2, true, 0);
            fixture.detectChanges();
            expect(component.store.storageStep.isValid()).toBeTrue();

            const inputDiskSize = fixture.debugElement.queryAll(By.css('#inputDiskSize'))[1].nativeElement as HTMLInputElement;
            inputDiskSize.value = '100';
            inputDiskSize.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            addDiskButton.click();
            fixture.detectChanges();
            expect(component.store.storageStep.isValid()).toBeTrue();

            const removeDiskButtons = fixture.debugElement.queryAll(By.css('#remove-disk'))[1].nativeElement as HTMLButtonElement;
            removeDiskButtons.click();
            fixture.detectChanges();

            expect(component.store.storageStep.isValid()).toBeTrue();

            const rootInputDiskSize = fixture.debugElement.queryAll(By.css('#inputDiskSize'))[0].nativeElement as HTMLInputElement;
            rootInputDiskSize.value = '0';
            rootInputDiskSize.dispatchEvent(new Event('input'));
            tick(251);
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(2);
            expect(component.store.storageStep.isValid()).toBeFalse();
        }));

    });

    describe('ISO storage', () => {

        beforeEach(() => {
            component.store.setTemplate({ zone: zones[0], osType: OsType.ISO, osTypeFilter: OsTypeFilter.Featured, template: zone1Templates[0], virtualMachineName: 'VM Name' }, true);
            component.store.navigateToStep(CreateVmWizardStepEnum.Storage);
            fixture.detectChanges();
        });

        it('should set root disk', () => {
            expect(component.store.storageStep.rootDisk()).toBeNull();
            expect(component.store.storageStep.isValid()).toBeFalse();
        });

        it('should add root disk', fakeAsync(() => {

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.rootDisk()).toEqual(mockDiskOfferings[0]);
            expect(component.store.storageStep.selectedDataDisks.length).toBe(0);
            expect(component.store.storageStep.isValid()).toBeTrue();
        }));

        it('should add data disk', fakeAsync(() => {

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 1);

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks().length).toBe(1);
            expect(component.store.storageStep.selectedDataDisks()[0]).toEqual(mockDiskOfferings[1]);
            expect(component.store.storageStep.isValid()).toBeTrue();
        }));

        it('should remove data disk', fakeAsync(() => {

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();
            selectOption(fixture, 'ng-select', 1, true, 1);

            const addDiskButton = fixture.debugElement.query(By.css('#add-disk')).nativeElement as HTMLButtonElement;
            addDiskButton.click();
            fixture.detectChanges();

            const removeDiskButton = fixture.debugElement.query(By.css('#remove-disk')).nativeElement as HTMLButtonElement;
            removeDiskButton.click();
            fixture.detectChanges();

            expect(component.store.storageStep.selectedDataDisks.length).toBe(0);
        }));

        it('should set custom disk offering as root', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 2, true, 0);
            fixture.detectChanges();
            expect(component.store.storageStep.isValid()).toBeFalse();

            const inputDiskSize = fixture.debugElement.query(By.css('#inputDiskSize')).nativeElement as HTMLInputElement;
            inputDiskSize.value = '100';
            inputDiskSize.dispatchEvent(new Event('input'));
            tick(251);
            fixture.detectChanges();

            expect(component.store.storageStep.rootDisk()).toEqual({
                id: '3',
                offeringName: 'Custom Offering',
                description: 'Custom Offering description',
                diskSize: 100,
                isCustomized: true,
            });
            expect(component.store.storageStep.isValid()).toBeTrue();
        }));

    });

});
