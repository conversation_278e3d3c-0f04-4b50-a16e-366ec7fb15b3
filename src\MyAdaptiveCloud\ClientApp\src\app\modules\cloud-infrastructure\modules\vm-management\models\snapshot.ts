import { SnapshotState } from './snapshot-state.enum';

export interface Snapshot {
    id: string;
    account: string;
    created: string;
    current: boolean;
    description: string;
    displayname: string;
    domain: string;
    domainid: string;
    hypervisor: string;
    name: string;
    parent?: string;
    parentName?: string;
    project: string;
    projectid: string;
    state: SnapshotState;
    type: 'Disk' | 'DiskAndMemory';
    virtualmachineid: string;
    virtualmachinename: string;
    zoneid: string;
    zonename: string;
}
