import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ConfirmationDialogComponent } from '@app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { TaskStatusEnum } from '@app/shared/models/ddos-mitigation/task-status.enum';
import { DDoSMitigationQueueService } from '@app/shared/services/ddos-mitigation-job-queue.service';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { MitigationViewModel } from '../../models/mitigation-view-model';
import { DDoSMitigationBlackholeService } from '../../services/ddos-mitigation-blackhole-service';
import { DDoSMitigationMitigationService } from '../../services/ddos-mitigation-mitigation.service';
import { DDoSMitigationScrubService } from '../../services/ddos-mitigation-scrub.service';
import { DdosMitigationMessagesComponent } from '../ddos-mitigation-messages/ddos-mitigation-messages.component';
import { DdosMitigationComponent } from './ddos-mitigation.component';

describe('DdosMitigationComponent', () => {
    let fixture: ComponentFixture<DdosMitigationComponent>;
    let mockDDoSMitigationMitigationService: jasmine.SpyObj<DDoSMitigationMitigationService>;
    let mockDDoSMitigationQueueService: jasmine.SpyObj<DDoSMitigationQueueService>;
    let mockModalService: jasmine.SpyObj<ModalService>;

    const mockBlackholeMitigation: MitigationViewModel = {
        cidr: '192.168.0.0/24',
        mitigationType: 'Blackhole',
        taskStatus: TaskStatusEnum.Successful,
        taskStatusMessage: 'Task is currently running',
        status: 1,
        startTime: new Date('2025-04-15T10:00:00Z'),
        stopTime: null,
        messages: [
            'Mitigation requested',
            'Task created',
            'Mitigation successfully initiated.'
        ],
        taskId: 'task-001',
        simulate: false,
        active: true
    };

    const mockScrubMitigation: MitigationViewModel = {
        cidr: '192.168.0.25/124',
        mitigationType: 'Scrub',
        taskStatus: TaskStatusEnum.Successful,
        taskStatusMessage: 'Task is currently running',
        status: 1,
        startTime: new Date('2025-04-15T10:00:00Z'),
        stopTime: null,
        messages: [
            'Mitigation requested',
            'Task created',
            'Mitigation successfully initiated.'
        ],
        taskId: 'task-001',
        simulate: false,
        active: true
    };

    const mockFailedScrubMitigation: MitigationViewModel = {
        cidr: '10.0.0.0/16',
        mitigationType: 'Scrub',
        taskStatus: TaskStatusEnum.Failed,
        taskStatusMessage: 'Task failed due to timeout',
        status: 2,
        startTime: new Date('2025-04-14T08:30:00Z'),
        stopTime: new Date('2025-04-14T09:00:00Z'),
        messages: [
            'Mitigation requested',
            'Task created',
            'Timeout occurred while applying mitigation.'
        ],
        taskId: 'task-002',
        simulate: true,
        active: false
    };

    const mockFailedBlackholeMitigation: MitigationViewModel = {
        cidr: '10.0.0.0/16',
        mitigationType: 'Blackhole',
        taskStatus: TaskStatusEnum.Failed,
        taskStatusMessage: 'Task failed due to timeout',
        status: 2,
        startTime: new Date('2025-04-14T08:30:00Z'),
        stopTime: new Date('2025-04-14T09:00:00Z'),
        messages: [
            'Mitigation requested',
            'Task created',
            'Timeout occurred while applying mitigation.'
        ],
        taskId: 'task-002',
        simulate: true,
        active: false
    };

    const data = [mockBlackholeMitigation, mockScrubMitigation, mockFailedScrubMitigation, mockFailedBlackholeMitigation];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationComponent],
            providers: [
                provideMock(DDoSMitigationMitigationService),
                provideMock(DDoSMitigationScrubService),
                provideMock(DDoSMitigationBlackholeService),
                provideMock(DDoSMitigationQueueService),
                provideMock(NotificationService),
                provideMock(NgbActiveModal),
                provideMock(ModalService),
                ConfirmationDialogComponent,
                DdosMitigationMessagesComponent
            ],
        })
            .compileComponents();

        mockDDoSMitigationMitigationService = TestBed.inject(DDoSMitigationMitigationService) as jasmine.SpyObj<DDoSMitigationMitigationService>;
        mockDDoSMitigationMitigationService.getList.and.returnValue(of({ data, message: '' }));

        mockDDoSMitigationQueueService = TestBed.inject(DDoSMitigationQueueService) as jasmine.SpyObj<DDoSMitigationQueueService>;
        mockDDoSMitigationQueueService.taskUpdates$ = of();

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;

        fixture = TestBed.createComponent(DdosMitigationComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            fixture.detectChanges();
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
        });

        it('should call getMitigationStatusList', () => {
            expect(mockDDoSMitigationMitigationService.getList).toHaveBeenCalledTimes(1);
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });
    });

    describe('Actions', () => {

        let dataTableDebugElement: DebugElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
        });

        it('Start action available when item is inactive', () => {
            const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
            expect(rows[1].query(By.css('.fa-solid.fa-rotate-right'))).toBeDefined();
        });

        it('View Messages action available always', () => {
            const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
            expect(rows[0].query(By.css('.icon-viewDetails'))).toBeDefined();
            expect(rows[1].query(By.css('.icon-viewDetails'))).toBeDefined();
        });

        it('should call open messages modal when clicking on the action', () => {
            const modalRef = {
                closed: of(true),
                componentInstance: TestBed.inject(DdosMitigationMessagesComponent)
            } as NgbModalRef;
            mockModalService.openModalComponent.and.returnValue(modalRef);

            const action = dataTableDebugElement.queryAll(By.directive(TableActionComponent))[1];
            action.query(By.css('.table-action-container')).nativeElement.click();
            fixture.detectChanges();

            expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            expect((modalRef.componentInstance as DdosMitigationMessagesComponent).messages()).toEqual(mockBlackholeMitigation.messages);
        });

        describe('Stop action', () => {

            it('should stop blackhole mitigation action', () => {

                const modalRef: NgbModalRef = {
                    closed: of({ data: { taskId: 'task-001' } }),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent),
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                const stopAction = dataTableDebugElement.queryAll(By.directive(TableActionComponent))[0];
                stopAction.query(By.css('.table-action-container')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Are you sure you want to stop this Blackhole Mitigation?', title: 'Stop Blackhole Mitigation', showCancelButton: true });
            });

            it('should stop scrub mitigation action', () => {

                const modalRef: NgbModalRef = {
                    closed: of({ data: { taskId: 'task-001' } }),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent),
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                const stopAction = dataTableDebugElement.queryAll(By.directive(TableActionComponent))[2];
                stopAction.query(By.css('.table-action-container')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Are you sure you want to stop this Scrub Mitigation?', title: 'Stop Scrub Mitigation', showCancelButton: true });
            });

        });

        describe('Restart action', () => {

            it('should restart Blackhole mitigation action', () => {

                const modalRef: NgbModalRef = {
                    closed: of({ data: { taskId: 'task-001' } }),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent),
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                const stopAction = dataTableDebugElement.queryAll(By.directive(TableActionComponent))[6];
                stopAction.query(By.css('.table-action-container')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Are you sure you want to start this Blackhole Mitigation?', title: 'Start Blackhole Mitigation', showCancelButton: true });
            });

            it('should restart Scrub mitigation action', () => {

                const modalRef: NgbModalRef = {
                    closed: of({ data: { taskId: 'task-001' } }),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent),
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                const stopAction = dataTableDebugElement.queryAll(By.directive(TableActionComponent))[4];
                stopAction.query(By.css('.table-action-container')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Are you sure you want to start this Scrub Mitigation?', title: 'Start Scrub Mitigation', showCancelButton: true });
            });

        });

    });

});
