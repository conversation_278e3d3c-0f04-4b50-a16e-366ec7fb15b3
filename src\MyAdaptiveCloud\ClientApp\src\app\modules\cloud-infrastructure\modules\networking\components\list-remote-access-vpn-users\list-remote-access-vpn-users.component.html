<div class="card-body">
    <div class="content-sub-heading d-flex justify-content-between">
        <div class="search-bar">
            <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)"
                [dataItemName]="'searchTerm'" />
        </div>
        @if (networkingPermissionService.canAddVpnUser()) {
            <div class="action-buttons">
                <div class="d-inline"
                    [title]="!store.getAccount() ? 'Select an account to create a VPN user' : ''">
                    <button class="btn btn-primary" [disabled]="!store.getAccount()" data-testid="add-vpn-user-button"
                        (click)="openAddVpnUserModal()">
                        Add VPN User
                    </button>
                </div>
            </div>
        }
    </div>

    <div class="card card-default">
        <div class="card-body">
            <ngx-datatable #table class='table bootstrap no-detail-row' (sort)="onSorting($event)"
                (page)="onPageChanged($event)" />
        </div>
    </div>

    <ng-template #statusRow let-row="row">
        @if (toItem(row); as row) {
            <div class="d-flex align-items-center justify-content-space">
                <div class="col-3 d-flex">
                    @switch (row.state) {
                        @case ('Active') {
                            <span [title]="row.state" class="circle text-bg-success me-1">
                            </span>
                        }
                        @case ('Add') {
                            <span [title]="row.state" class="circle text-bg-secondary me-1">
                            </span>
                        }
                        @case ('Revoke') {
                            <span [title]="row.state" class="circle text-bg-danger me-1">
                            </span>
                        }
                    }
                </div>
                <div class="col-9">
                    {{ row.state }}
                </div>
            </div>
        }
    </ng-template>

    <ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
        <span (click)="sort()" class="clickable">
            {{ column.name }}
            <span
                [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
            </span>
        </span>
    </ng-template>

    <ng-template #actionsTemplate let-row="row">
        @if (toItem(row); as row) {
            @if (networkingPermissionService.canDeleteVpnUser()) {
                <app-table-action [icon]="'far fa-trash-can'" [title]="'Delete VPN User'"
                    (clickHandler)="deleteVpnUser(row)" />
            }
        }
    </ng-template>

</div>
