import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';
import { SelectableOrganization } from '../models/selectable-organization';

@Injectable({
    providedIn: 'root'
})
export class SelectableOrganizationService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'organization';

    /*
      Gets the list of organizations that are selectable by the current user
      This is intended to be consumed only by the Organization selector component.
  */
    getSelectable(): Observable<ApiDataResult<SelectableOrganization[]>> {
        return this.apiService.get<ApiDataResult<SelectableOrganization[]>>(`${this.endpoint}/selectable`, null);
    }

}
