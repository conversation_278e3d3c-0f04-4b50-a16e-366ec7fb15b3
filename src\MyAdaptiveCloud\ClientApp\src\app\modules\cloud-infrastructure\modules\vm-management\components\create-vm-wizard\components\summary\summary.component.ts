import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { Ngb<PERSON>ropdown, NgbDropdownItem, NgbDropdownMenu, NgbDropdownToggle } from '@ng-bootstrap/ng-bootstrap';
import { OsType } from '../../../../models/os-type.enum';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { CreateVmWizardStepEnum } from '../../models/create-vm-wizard-steps.enum';
import { CreateVmWizardConstants } from '../../models/create-vm-wizard.constants';

@Component({
    selector: 'app-create-vm-wizard-summary',
    imports: [NgbDropdownToggle, NgbDropdownItem, NgbDropdown, NgbDropdownMenu],
    templateUrl: './summary.component.html',
    styleUrls: ['./summary.component.scss', '../../create-vm-wizard-common.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmWizardSummaryComponent {

    public readonly store = inject(CreateVMWizardStore);

    protected readonly labels = CreateVmWizardConstants;
    protected readonly steps = CreateVmWizardStepEnum;
    protected readonly osType = OsType;

    navigateToStep(step: number, isStepAccessible: boolean): void {
        if (!isStepAccessible) {
            return;
        }
        this.store.navigateToStep(step);
    }

    protected createVirtualMachine(powerOn: boolean) {
        if (this.store.canCreateVirtualMachine()) {
            this.store.createVirtualMachine(powerOn);
        }
    }
}
