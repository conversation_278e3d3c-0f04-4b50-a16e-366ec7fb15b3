import { inject, Injectable } from '@angular/core';
import { Vpc } from '@app/modules/cloud-infrastructure/models/vpc.model';
import { ListVirtualPrivateCloudResponse, ListVirtualPrivateClouds } from '@app/modules/cloud-infrastructure/responses/list-virtual-private-cloud.response';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { forkJoin, map, Observable, of, switchMap } from 'rxjs';
import { NETWORKING_ENDPOINT_NAMES } from '../models/networking.constants';
import { VirtualPrivateCloudOfferingViewModel } from '../models/virtual-private-cloud-offering.view-model';
import { CreateVirtualPrivateCloudRequest } from '../requests/create-virtual-private-cloud.request';
import { CreateVpnUserResponse } from '../responses/create-vpn-user.response';
import { DeleteVirtualPrivateCloudResponse } from '../responses/delete-virtual-private-cloud.response';
import { ListVirtualPrivateCloudOfferingResponse } from '../responses/list-virtual-private-cloud-offerings.response';
import { RestartVirtualPrivateCloudResponse } from '../responses/restart-virtual-private-cloud.response';
import { EditVpnUserResponse } from '../responses/edit-virtual-private-cloud.response';

@Injectable({
    providedIn: 'root'
})
export class VirtualPrivateCloudService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getVirtualPrivateCloudList(): Observable<Vpc[]> {
        const pageSize = 500;

        // Fetch the first batch, which will return the total count, then fetch the rest of the records in parallel
        return this.getVirtualPrivateCloudListBatch(1, pageSize).pipe(
            map(res => {
                const records = [...res?.vpc ?? []];
                const remainingRecords = res.count - pageSize;

                if (remainingRecords > 0) {
                    const countOfRequestBatches = Math.ceil(remainingRecords / pageSize);
                    const requests: Observable<ListVirtualPrivateClouds>[] = [];
                    for (let i = 2; i <= countOfRequestBatches + 1; i++) {
                        requests.push(this.getVirtualPrivateCloudListBatch(i, pageSize));
                    }
                    return forkJoin(requests).pipe(map(responses => {
                        responses.forEach(response => {
                            records.push(...response.vpc);
                        });
                        return records;
                    }));
                }
                return of(records);

            }),
            switchMap(records => records)
        );
    }

    getVpcOfferings(zoneId: string): Observable<VirtualPrivateCloudOfferingViewModel[]> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.listVirtualPrivateCloudOfferings,
            state: 'Enabled',
            response: 'json',
            zoneid: zoneId,
        };

        return this.cloudInfraApiService.get<ListVirtualPrivateCloudOfferingResponse>(params)
            .pipe(map(response => (response?.listvpcofferingsresponse?.vpcoffering ?? [])
                .sort((a, b) => a.displaytext.localeCompare(b.displaytext))
                .map(vpc => ({
                    id: vpc.id,
                    name: vpc.name,
                    description: vpc.displaytext,
                }))));
    }

    deleteVirtualPrivateCloud(id: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.deleteVirtualPrivateCloud,
            id,
        };

        return this.cloudInfraApiService.get<DeleteVirtualPrivateCloudResponse>(params).pipe(map(response => response.deletevpcresponse?.jobid));
    }

    createVirtualPrivateCloud(request: CreateVirtualPrivateCloudRequest, domainId: string, account: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.createVirtualPrivateCloud,
            zoneid: request.zoneId,
            vpcofferingid: request.vpcOfferingId,
            name: request.name,
            cidr: request.cidr
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        if (request.description) {
            params['displaytext'] = request.description;
        }

        if (request.networkDomain) {
            params['networkdomain'] = request.networkDomain;
        }

        return this.cloudInfraApiService.get<CreateVpnUserResponse>(params).pipe(map(response => response.addvpnuserresponse?.jobid));
    }

    editVirtualPrivateCloud(virtualPrivateCloudId: string, name: string, description: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.editVirtualPrivateCloud,
            id: virtualPrivateCloudId,
            name
        };

        if (description) {
            params['displaytext'] = description;
        }

        return this.cloudInfraApiService.get<EditVpnUserResponse>(params).pipe(map(response => response.updatevpcresponse?.jobid));
    }

    restartVirtualPrivateCloud(networkId: string, cleanup: boolean, livePatchNetworkRouters: boolean | null, makeRedundant: boolean | null): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.restartVirtualPrivateCloud,
            id: networkId,
            cleanup: cleanup ? 'true' : 'false',
        };

        if (!cleanup) {
            params['livepatch'] = livePatchNetworkRouters ? 'true' : 'false';
        }

        if (makeRedundant !== null) {
            params['redundant'] = makeRedundant ? 'true' : 'false';

            if (makeRedundant) {
                params['cleanup'] = 'true'; // Force cleanup if making redundant
            }
        }

        return this.cloudInfraApiService.get<RestartVirtualPrivateCloudResponse>(params).pipe(map(response => response.restartvpcresponse?.jobid));
    }

    private getVirtualPrivateCloudListBatch(currentPage: number, pageSize: number): Observable<ListVirtualPrivateClouds> {
        const params: Record<string, string> = {
            command: NETWORKING_ENDPOINT_NAMES.listVirtualPrivateClouds,
            listall: 'true',
            pagesize: pageSize.toString(),
            page: currentPage.toString()
        };

        return this.cloudInfraApiService.get<ListVirtualPrivateCloudResponse>(params)
            .pipe(map((response: ListVirtualPrivateCloudResponse) => (response.listvpcsresponse)));
    }

}
