<div class="modal-header">
    <h4 class="modal-title">Edit Isolated Network</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form">
        <div class="row mb-3">
            <label for="name" class="col-3 col-form-label">Name<span class="required-asterisk">*</span></label>
            <div class="col">
                <input class="form-control" data-testid="name-input" formControlName="name" id="name"
                    [class]="{ 'is-invalid': form.controls.name.invalid && form.controls.name.dirty }"
                    autocomplete="off" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="description" class="col-3 col-form-label">Description</label>
            <div class="col">
                <input class="form-control" data-testid="description-input" formControlName="description"
                    id="description"
                    [class]="{ 'is-invalid': form.controls.description.invalid && form.controls.description.dirty }" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="cidr" class="col-3 col-form-label">CIDR<span class="required-asterisk">*</span>
            </label>
            <div class="col">
                <input class="form-control" data-testid="cidr-input" id="cidr" formControlName="cidr"
                    [class]="{ 'is-invalid': form.controls.cidr.invalid && form.controls.cidr.dirty }" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="networkDomain" class="col-3 col-form-label">Network Domain</label>
            <div class="col">
                <input class="form-control" data-testid="networkDomain-input" formControlName="networkDomain"
                    id="networkDomain"
                    [class]="{ 'is-invalid': form.controls.networkDomain.invalid && form.controls.networkDomain.dirty }">
            </div>
        </div>
        <div class="row mb-3">
            <label class="col-3 col-form-label">Network Offering<span class="required-asterisk">*</span> </label>
            <div class="col">
                <ng-select [items]="networkOfferings()" bindLabel="name" bindValue="id"
                    formControlName="networkOffering" data-testid="network-offering-select"
                    [class]="{ 'is-invalid': form.controls.networkOffering.invalid && form.controls.networkOffering.dirty }" />
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!form?.valid || !networkOfferings()?.length"
        (submitClickEvent)="submit()">Save
    </app-btn-submit>
</div>
