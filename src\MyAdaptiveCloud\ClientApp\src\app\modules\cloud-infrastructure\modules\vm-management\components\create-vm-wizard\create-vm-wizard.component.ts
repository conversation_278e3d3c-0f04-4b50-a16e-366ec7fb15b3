import { NgComponentOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, effect, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { JobQueueEvent, JobQueueEventDescription } from '@app/shared/models/job-queue/job-queue-event.enum';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { ModalService } from '@app/shared/services/modal.service';
import { filter, take, tap } from 'rxjs';
import { VM_ROUTE_SEGMENTS } from '../../constants/vm-management-route-segments';
import { CreateVmWizardAdvancedSettingsComponent } from './components/advanced-settings/advanced-settings.component';
import { CreateVmWizardComputeComponent } from './components/compute/compute.component';
import { CreateVmWizardNetworkComponent } from './components/network/network.component';
import { CreateVmWizardSetupComponent } from './components/setup/setup.component';
import { CreateVmWizardStepperComponent } from './components/stepper/stepper.component';
import { CreateVmWizardStorageComponent } from './components/storage/storage.component';
import { CreateVmWizardSummaryComponent } from './components/summary/summary.component';
import { CreateVMWizardStore } from './create-vm-wizard-store';
import { CreateVmRequestStatus } from './models/create-vm-request-status.emun';
import { CreateVmWizardStepEnum } from './models/create-vm-wizard-steps.enum';

@Component({
    selector: 'app-create-vm-wizard',
    imports: [CreateVmWizardStepperComponent, CreateVmWizardSummaryComponent, NgComponentOutlet],
    templateUrl: './create-vm-wizard.component.html',
    styleUrl: './create-vm-wizard.component.scss',
    providers: [CreateVMWizardStore],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVMWizardComponent {

    public readonly store = inject(CreateVMWizardStore);
    private readonly router = inject(Router);
    private readonly modalService = inject(ModalService);
    private readonly cloudInfrastructureQueueService = inject(CloudInfrastructureJobQueueService);
    private readonly activatedRoute = inject(ActivatedRoute);

    protected readonly stepsEnum = CreateVmWizardStepEnum;
    public readonly steps = [];

    constructor() {
        this.steps[CreateVmWizardStepEnum.Setup] = CreateVmWizardSetupComponent;
        this.steps[CreateVmWizardStepEnum.Compute] = CreateVmWizardComputeComponent;
        this.steps[CreateVmWizardStepEnum.Storage] = CreateVmWizardStorageComponent;
        this.steps[CreateVmWizardStepEnum.Network] = CreateVmWizardNetworkComponent;
        this.steps[CreateVmWizardStepEnum.AdvancedSettings] = CreateVmWizardAdvancedSettingsComponent;

        effect(() => {
            if (this.store.create.requestStatus() === CreateVmRequestStatus.Success && this.store.create.createVirtualMachineJobId()) {
                const virtualMachineName = this.store.setupStep().form.virtualMachineName ?? `VM-${this.store.create.virtualMachineId()}`;
                this.cloudInfrastructureQueueService.addToQueue(this.store.create.createVirtualMachineJobId(), virtualMachineName, JobQueueEventDescription[JobQueueEvent.CreateVirtualMachine], this.store.create.virtualMachineId());
                if (this.store.create.startVirtualMachineJobId()) {
                    this.cloudInfrastructureQueueService.addToQueue(this.store.create.startVirtualMachineJobId(), virtualMachineName, JobQueueEventDescription[JobQueueEvent.StartVirtualMachine], this.store.create.virtualMachineId());
                }
                this.router.navigate([`${VM_ROUTE_SEGMENTS.LIST}`], { relativeTo: this.activatedRoute.parent });
            }
        });
    }

    protected cancel(): void {
        this.modalService.openConfirmationDialog({
            title: 'Cancel',
            confirmButtonText: 'Yes, cancel',
            cancelButtonText: 'No, go back',
            content: 'Do you want to cancel creating a new virtual machine?',
            showCancelButton: true
        }).closed.pipe(
            filter(result => !!result),
            tap(() => this.router.navigate([`${VM_ROUTE_SEGMENTS.LIST}`], { relativeTo: this.activatedRoute.parent })),
            take(1),
        ).subscribe();
    }

}
