@if (store.currentStep() !== 0) {
    <button [disabled]="!canNavigateToPreviousStep()" title="Previous" class="btn btn-primary"
        (click)="store.navigateToStep(store.currentStep() - 1)">
        <i class="fas fa-chevron-left"></i>
        Back
    </button>
}

<button [disabled]="!canNavigateToNextStep()" title="Next" class="ms-2 btn btn-primary" [style]="hiddenBtn()"
    (click)="store.navigateToStep(store.currentStep() + 1)">
    Next
    <i class="fas fa-chevron-right"></i>
</button>
