﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Requests.DeviceControl;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class DeviceControlBulkAuthorizeFilter : BaseAsyncBulkAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public DeviceControlBulkAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
          IUserContextService userContextService, Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }

            context.ActionArguments.TryGetValue(_requestName, out var request);

            // If the request body parameter is not found, then the request is invalid
            if (request == null)
            {
                context.Result = new ForbidResult();
            }
            else
            {
                var idsToDelete = request.GetType().GetProperty(_name).GetValue(request, null) as List<int>;
                var devicesControlToUpdate = request.GetType().GetProperty("DevicesControlsToAdd").GetValue(request, null) as List<DeviceControlRequest>;
                var idsToUpdate = devicesControlToUpdate.Where(a => a.DeviceControlId.HasValue).Select(a => a.DeviceControlId.Value).ToList();
                var deviceControlIds = new List<int>();
                deviceControlIds.AddRange(idsToUpdate);
                deviceControlIds.AddRange(idsToDelete);
                // If there are any elements in the list, proceed to authorize each one, otherwise, treat as a non-op and proceed to the next action in the pipeline 
                if (deviceControlIds.Any())
                {
                    var controlsOrganizationIds = await _entityAuthorizationService.GetDevicesControlsOrganizationId(deviceControlIds);
                    var allOrgsAuthorized = false;
                    if (controlsOrganizationIds.Any())
                    {
                        allOrgsAuthorized = controlsOrganizationIds.All(orgId => _userContextService.HasPermission(userId, orgId, _distance, _perms));
                    }

                    if (allOrgsAuthorized)
                    {
                        await next();
                    }
                    else
                    {
                        context.Result = new ForbidResult();
                    }
                }
                else
                {
                    await next();
                }

            }
        }

    }

    public class DeviceControlBulkAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public DeviceControlBulkAuthorizeAttribute(params Perms[] perms) : base(typeof(DeviceControlBulkAuthorizeFilter), perms)
        {
            Name = "DevicesControlsIdsToDelete";            
        }
    }
}
