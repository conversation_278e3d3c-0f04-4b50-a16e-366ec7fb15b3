import { inject, Injectable } from '@angular/core';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { map, Observable } from 'rxjs';
import { ServiceOfferingModel } from '../models/service-offering.model';
import { ServiceOfferingViewModel as ServiceOfferingsViewModel } from '../models/service-offering.view-model';
import { ListServiceOfferingsResponse } from '../responses/list-service-offerings.response';

@Injectable({
    providedIn: 'root'
})
export class CreateVmComputeService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getServiceOfferings(zoneId: string, domainId: string, account: string): Observable<ServiceOfferingsViewModel[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listServiceOfferings,
            listall: 'true',
            issystem: 'false',
            zoneId
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListServiceOfferingsResponse>(params)
            .pipe(map((response: ListServiceOfferingsResponse) => (response?.listserviceofferingsresponse?.serviceoffering ?? [])
                .filter(t => !!t)
                .sort(sortByProperty('name'))
                .map(t => this.mapServiceOfferingsViewModel(t))));
    }

    private mapServiceOfferingsViewModel(serviceOfferings: ServiceOfferingModel): ServiceOfferingsViewModel {
        const viewModel: ServiceOfferingsViewModel = {
            name: serviceOfferings.name,
            id: serviceOfferings.id,
            cpuNumber: serviceOfferings.cpunumber ? parseInt(serviceOfferings.cpunumber, 10) : null,
            memory: serviceOfferings.memory ? parseInt(serviceOfferings.memory, 10) : null,
            isCustom: serviceOfferings.iscustomized
        };
        return viewModel;
    }
}
