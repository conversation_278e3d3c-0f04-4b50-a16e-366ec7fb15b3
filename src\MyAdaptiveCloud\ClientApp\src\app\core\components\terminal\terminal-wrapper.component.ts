import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DeviceTerminalCapabilitiesService } from '@app/core/services/device-terminal-capabilities.service';
import { ShellType } from '@app/shared/models/shell-type.enum';
import { map } from 'rxjs/operators';
import { TerminalLegacyComponent } from './terminal-legacy.component';
import { TerminalComponent } from './terminal.component';

@Component({
    selector: 'app-terminal-wrapper',
    imports: [TerminalLegacyComponent, TerminalComponent],
    templateUrl: './terminal-wrapper.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class TerminalWrapperComponent implements OnInit {

    private readonly agentTerminalCapabilitiesService = inject(DeviceTerminalCapabilitiesService);
    private readonly route = inject(ActivatedRoute);

    readonly type = signal<ShellType>(null);
    readonly agentId = signal<number>(null);

    protected readonly canRemoteCommandsKeyByKey = signal<boolean | null>(null);

    ngOnInit() {
        this.agentId.set(+this.route.snapshot.paramMap.get('id'));
        this.type.set(this.route.snapshot.paramMap.get('type') === '0' ? ShellType.CMD : ShellType.POWERSHELL);
        this.agentTerminalCapabilitiesService.canRemoteCommandsKeyByKey(this.agentId())
            .pipe(map(res => res.data))
            .subscribe(res => {
                this.canRemoteCommandsKeyByKey.set(res);
            });
    }

}
