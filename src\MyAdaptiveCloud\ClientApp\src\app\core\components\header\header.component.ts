import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { Router } from '@angular/router';
import { DEVICES_ROUTES_SEGMENTS } from '@app/shared/constants/routes-segments';
import { ORGANIZATION_FOLDER_ID_PREFIX } from '@app/shared/constants/shared-folder-devices-constants';
import { WhitelabelImageDirective } from '@app/shared/directives/whitelabel-image.directive';
import { HubService } from '@app/shared/hubs/services/hub.service';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { WhiteLabelImageType } from '@app/shared/models/whitelabel-image-type';
import { AuthService } from '@app/shared/services/auth.service';
import { ModalService } from '@app/shared/services/modal.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { filter, take } from 'rxjs';
import { SelectedOrganizationComponent } from '../selected-organization/selected-organization.component';
import { NotificationTrayComponent } from './notification-tray/notification-tray.component';
import { UserPreferencesTabComponent } from './user-preferences-tab/user-preferences-tab.component';

@Component({
    selector: 'app-header',
    imports: [WhitelabelImageDirective, NotificationTrayComponent],
    templateUrl: './header.component.html',
    styleUrl: './header.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class HeaderComponent {
    readonly userContext = input<UserContext>();
    private readonly authService = inject(AuthService);
    private readonly modalService = inject(ModalService);
    private readonly userContextService = inject(UserContextService);
    private readonly hubService = inject(HubService);
    private readonly permissionService = inject(PermissionService);
    private readonly router = inject(Router);

    protected readonly whiteLabelImageType = WhiteLabelImageType;
    protected readonly showNotificationTray = computed(() => (this.permissionService.canManageCloudInfra() && this.userContextService.getFeatureFlagState(FeatureFlag.FeatureFlagVirtualMachines)) ||
        this.permissionService.canManagDeviceReleases());

    protected orgSelectModal(): void {
        const modalRef = this.modalService.openModalComponent(SelectedOrganizationComponent, { size: 'lg' });
        modalRef.closed
            .pipe(take(1))
            .subscribe(res => {
                this.selectOrganization(res);
            });
    }

    protected selectOrganization(organizationId: number): void {
        const previousOrganizationId =
            this.userContextService.currentUser.organizationId;
        this.authService
            .getUserContextWithOrganization(organizationId)
            .pipe(take(1))
            .subscribe(() => {
                this.hubService.removeFromOrganizationGroup(previousOrganizationId);
                const currentUrl = this.router.url;
                if (currentUrl.includes(`/${DEVICES_ROUTES_SEGMENTS.BASE_DEVICES_MANAGEMENT}`)) {
                    this.router.navigate([`/${DEVICES_ROUTES_SEGMENTS.BASE_DEVICES_MANAGEMENT}/${DEVICES_ROUTES_SEGMENTS.DEVICES}`, `${ORGANIZATION_FOLDER_ID_PREFIX}${organizationId}`]).then(() => {
                        window.location.reload();
                    });
                } else {
                    window.location.reload();
                }
            });
    }

    protected logout(): void {
        const previousOrganizationId =
            this.userContextService.currentUser.organizationId;
        this.modalService
            .openConfirmationDialog({
                title: 'Logout',
                content:
                    'This will log you out from the portal. You will have to close all open browser windows to log out of the SSO.',
                showCancelButton: true,
            })
            .closed
            .pipe(
                filter(result => !!result),
                take(1)
            )
            .subscribe(() => {
                window?.sessionStorage.clear();
                document?.getElementById('favicon').removeAttribute('href');
                this.authService.logout();
                this.hubService.removeFromOrganizationGroup(previousOrganizationId);
            });
    }

    protected editProfile(): void {
        const modalOptions: NgbModalOptions = {
            size: 'lg'
        };
        this.modalService.openModalComponent(UserPreferencesTabComponent, modalOptions);
    }

}
