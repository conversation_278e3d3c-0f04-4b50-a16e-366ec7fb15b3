import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { HubService } from '@app/shared/hubs/services/hub.service';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { AuthService } from '@app/shared/services/auth.service';
import { JobQueueService } from '@app/shared/services/job-queue.service';
import { ModalService } from '@app/shared/services/modal.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { HeaderComponent } from './header.component';
import { NotificationTrayComponent } from './notification-tray/notification-tray.component';

describe('HeaderComponent', () => {
    let fixture: ComponentFixture<HeaderComponent>;
    let mockPermissionService: jasmine.SpyObj<PermissionService>;
    let mockJobQueueService: jasmine.SpyObj<JobQueueService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    const userContext = {
        isRegistered: true,
        organizationId: 1,
        organizationName: 'test'
    } as UserContext;

    beforeEach(() => {

        TestBed.configureTestingModule({
            providers: [
                provideMock(AuthService),
                provideMock(ModalService),
                provideMock(UserContextService),
                provideMock(HubService),
                provideMock(PermissionService),
                provideMock(JobQueueService)
            ],
            imports: [
                HeaderComponent
            ]
        });

        mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
        mockJobQueueService = TestBed.inject(JobQueueService) as jasmine.SpyObj<JobQueueService>;
        mockJobQueueService.jobQueue$ = of();
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = userContext;

        fixture = TestBed.createComponent(HeaderComponent);
        fixture.componentRef.setInput('userContext', userContext);
    });

    describe('notification tray', () => {

        it('should have the notification tray when the virtual machines feature flag is on and the user does have manage Cloud Infra permission', () => {
            mockUserContextService.currentUser.featureFlags = [FeatureFlag.FeatureFlagVirtualMachines];
            fixture.detectChanges();

            expect(fixture.debugElement.query(By.directive(NotificationTrayComponent))).toBeDefined();
        });

        it('should not have the notification tray when the virtual machines feature flag is off and the user does have manage Cloud Infra permission', () => {
            mockUserContextService.currentUser.featureFlags = [];
            mockPermissionService.canManageCloudInfra.and.returnValue(true);

            fixture.detectChanges();

            expect(fixture.debugElement.query(By.directive(NotificationTrayComponent))).toBeNull();
        });

        it('should not have the notification tray when the virtual machines feature flag is on and the user does not have manage Cloud Infra permission', () => {
            mockUserContextService.currentUser.featureFlags = [FeatureFlag.FeatureFlagVirtualMachines];
            mockPermissionService.canManageCloudInfra.and.returnValue(false);

            fixture.detectChanges();

            expect(fixture.debugElement.query(By.directive(NotificationTrayComponent))).toBeNull();
        });

        it('should not have the notification tray when the virtual machines feature flag is on and the user does not have manage Cloud Infra permission', () => {
            mockUserContextService.currentUser.featureFlags = [FeatureFlag.FeatureFlagVirtualMachines];
            mockPermissionService.canManageCloudInfra.and.returnValue(false);

            fixture.detectChanges();

            expect(fixture.debugElement.query(By.directive(NotificationTrayComponent))).toBeNull();
        });

    });

});
