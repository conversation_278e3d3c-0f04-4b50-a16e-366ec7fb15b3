/* eslint-disable @typescript-eslint/no-explicit-any */
/* Legacy service created before enforcing the no-any rule */

import { inject, Injectable } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { IPagination } from '@app/shared/models/datatable/pagination.model';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';
import { AdaptiveCloudUsageDetails } from '../models/adaptive-cloud-usage-details.model';
import { AdaptiveCloudUsageLogModel } from '../models/adaptivecloud-usage-log.model';
import { AdaptiveCloudUsageOverall } from '../models/adaptivecloud-usage-overall.model';
import { AdaptiveCloudUsageDetailsRequest } from '../requests/adaptivecloud-usage-details.request';
import { AdaptiveCloudUsageReportRequest } from '../requests/adaptivecloud-usage-report.request';

@Injectable({
    providedIn: 'root'
})
export class AdaptiveCloudUsageService {
    private readonly apiService = inject(ApiService);

    public readonly endpoint = 'adaptivecloud';

    getUsagePeriods(organizationId: number): Observable<ApiDataResult<string[]>> {
        return this.apiService.get<ApiDataResult<string[]>>(`${this.endpoint}/usage/organization/${organizationId}/periods`);
    }

    getUsageReport(organizationId: number, request: AdaptiveCloudUsageReportRequest): void {
        this.apiService.downloadFile(`${this.endpoint}/usage/organization/${organizationId}/report`, request);
    }

    getUsageDetails(request: AdaptiveCloudUsageDetailsRequest): Observable<ApiDataResult<AdaptiveCloudUsageDetails>> {
        return this.apiService
            .get<ApiDataResult<AdaptiveCloudUsageDetails>>(`${this.endpoint}/usage/organization/${request.organizationId}/account/${request.accountId}`, { period: request.period });
    }

    getUsagePeriodsOverall(): Observable<ApiDataResult<string[]>> {
        return this.apiService.get<ApiDataResult<string[]>>(`${this.endpoint}/usage/periods`);
    }

    getUsageOverall(period: string): Observable<ApiDataResult<AdaptiveCloudUsageOverall[]>> {
        return this.apiService.get<ApiDataResult<AdaptiveCloudUsageOverall[]>>(`${this.endpoint}/usage/overall`, { period });
    }

    getUsageOverallSummaryCsv(period: string): void {
        this.apiService.downloadFile(`${this.endpoint}/usage/overall/summary`, { period });
    }

    getVirtualMachines(acId?: string): Observable<any> {
        return this.apiService.get<any>(`${this.endpoint}/virtualMachines${acId ? `?acId=${acId}` : ''}`);
    }

    getOsCategories(organizationId: number): Observable<any> {
        return this.apiService.get<any>(`${this.endpoint}/osCategories/${organizationId}`);
    }

    getOsTypes(organizationId: number, osCategoryId?: string): Observable<any> {
        if (!osCategoryId) {
            return this.apiService.get<any>(`${this.endpoint}/osTypes/${organizationId}`);
        }
        return this.apiService.get<any>(`${this.endpoint}/osTypes/${organizationId}`, { osCategoryId });
    }

    getAcUsageRunLogs(request: IPagination): Observable<ApiDatasetResult<AdaptiveCloudUsageLogModel[]>> {
        return this.apiService.get<ApiDatasetResult<AdaptiveCloudUsageLogModel[]>>(`${this.endpoint}/acUsageRunLogs`, request);
    }

    getAcUsageRunLogDetails(runlogId: number): Observable<any> {
        return this.apiService.get<any>(`${this.endpoint}/acUsageRunLogDetails?runlogId=${runlogId}`);
    }
}
