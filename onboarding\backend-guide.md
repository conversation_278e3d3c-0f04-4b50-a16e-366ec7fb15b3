# Backend Development Guide

## Table of Contents
- [Architecture Overview](#architecture-overview)
- [Project Structure](#project-structure)
- [Dependency Injection](#dependency-injection)
- [Controllers](#controllers)
- [Services Layer](#services-layer)
- [Data Access Layer](#data-access-layer)
- [Authentication & Authorization](#authentication--authorization)
- [API Design Patterns](#api-design-patterns)
- [Middleware](#middleware)
- [Configuration](#configuration)

## Architecture Overview

The backend follows a clean architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Controllers Layer                        │
│  (API endpoints, request/response handling)                 │
├─────────────────────────────────────────────────────────────┤
│                   Services Layer                           │
│  (Business logic, external API integration)                │
├─────────────────────────────────────────────────────────────┤
│                 Repositories Layer                         │
│  (Data access abstraction)                                 │
├─────────────────────────────────────────────────────────────┤
│                Entity Framework Core                       │
│  (ORM, database context)                                   │
└─────────────────────────────────────────────────────────────┘
```

### Key Principles
- **Dependency Injection**: All dependencies are injected via constructor
- **Repository Pattern**: Data access is abstracted through repositories
- **Service Layer**: Business logic is encapsulated in services
- **AutoMapper**: Object-to-object mapping for DTOs
- **Custom Authorization**: Role-based permissions with custom filters

## Project Structure

```
src/MyAdaptiveCloud/
├── Controllers/              # API controllers
├── Authorization/            # Custom authorization filters
├── Services/                 # Application services
├── Middleware/              # Custom middleware
├── Identity/                # Identity and user context
├── Requests/                # Request models
├── ViewModel/               # Response models
├── Startup/                 # Configuration extensions
├── Hubs/                    # SignalR hubs
└── Program.cs               # Application entry point

src/MyAdaptiveCloud.Services/
├── Services/                # Business logic services
├── DTOs/                    # Data transfer objects
├── Apis/                    # External API integrations
└── AutoMapper/              # Mapping profiles

src/MyAdaptiveCloud.Data/
├── Repositories/            # Data access repositories
├── MyAdaptiveCloud/         # EF Core models and contexts
├── Agent/                   # Agent-specific data models
└── Interceptors/            # EF Core interceptors
```

## Dependency Injection

The application uses .NET's built-in DI container configured in `Program.cs`:

### Service Registration
```csharp
// Repository registration
services.AddScopedRepositories();

// Service registration  
services.AddScopedServices();

// AutoMapper
services.AddAutoMapper(Assembly.GetExecutingAssembly());

// Database contexts
services.ConfigureDbContexts();
```

### Repository Registration Pattern
```csharp
public static IServiceCollection AddScopedRepositories(this IServiceCollection services)
{
    services.AddScoped<IOrganizationRepository, OrganizationRepository>();
    services.AddScoped<IUserRepository, UserRepository>();
    services.AddScoped<IDeviceRepository, DeviceRepository>();
    // ... more repositories
    return services;
}
```

### Service Registration Pattern
```csharp
public static IServiceCollection AddScopedServices(this IServiceCollection services)
{
    services.AddScoped<IUserService, UserService>();
    services.AddScoped<IOrganizationService, OrganizationService>();
    services.AddScoped<IDeviceService, DeviceService>();
    // ... more services
    return services;
}
```

## Controllers

Controllers inherit from `AuthenticatedControllerBase` and follow RESTful conventions:

### Base Controller Pattern
```csharp
public abstract class AuthenticatedControllerBase : ControllerBase
{
    protected readonly IIdentityService _identityService;
    protected readonly IMapper _mapper;

    protected AuthenticatedControllerBase(IIdentityService identityService, IMapper mapper)
    {
        _identityService = identityService;
        _mapper = mapper;
    }
}
```

### Example Controller Implementation
```csharp
public class ServiceController : AuthenticatedControllerBase
{
    private readonly IServicesService _serviceService;

    public ServiceController(
        IIdentityService identityService,
        IMapper mapper,
        IServicesService serviceService)
        : base(identityService, mapper)
    {
        _serviceService = serviceService;
    }

    [OrgAuthorize(Perms.ViewServices, Perms.ManageServices)]
    [HttpGet("{organizationId:int}/list")]
    public async Task<ActionResult<ApiDataSetResult<List<ServiceDTO>>>> GetServices(
        [FromRoute] int organizationId)
    {
        var result = await _serviceService.GetList(organizationId);
        return new ApiDataSetResult<List<ServiceDTO>>
        {
            Data = result,
            TotalCount = result.Count,
        };
    }
}
```

### Controller Conventions
- **Route Patterns**: `[Route("api/[controller]")]`
- **HTTP Methods**: Use appropriate HTTP verbs (GET, POST, PUT, DELETE)
- **Authorization**: Apply custom authorization attributes
- **Response Types**: Return `ApiResult`, `ApiDataResult<T>`, or `ApiDataSetResult<T>`
- **Validation**: Use model validation attributes

## Services Layer

Services contain business logic and coordinate between controllers and repositories:

### Service Interface Pattern
```csharp
public interface IDeviceControlService
{
    Task<List<DeviceControlDTO>> GetDeviceControls(int deviceId);
    Task UpdateDeviceControls(UpdateDeviceControlRequest request, int createdBy, int agentId);
    Task RemoveDeviceControl(int agentId, int userId);
    Task DeleteDeviceControl(int deviceControlId);
    Task<bool> HasControlledDevices(string userEmail);
}
```

### Service Implementation Pattern
```csharp
public class DeviceControlService : IDeviceControlService
{
    private readonly IDeviceControlRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<DeviceControlService> _logger;

    public DeviceControlService(
        IDeviceControlRepository repository,
        IMapper mapper,
        ILogger<DeviceControlService> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<DeviceControlDTO>> GetDeviceControls(int deviceId)
    {
        var entities = await _repository.GetByDeviceId(deviceId);
        return _mapper.Map<List<DeviceControlDTO>>(entities);
    }
}
```

### Service Responsibilities
- **Business Logic**: Implement domain rules and validation
- **Data Transformation**: Map between DTOs and entities
- **External APIs**: Integrate with third-party services
- **Caching**: Implement caching strategies
- **Logging**: Log business operations

## Data Access Layer

### Repository Pattern
```csharp
public interface IOrganizationRepository
{
    Task<Organization> GetByIdAsync(int id);
    Task<List<Organization>> GetAllAsync();
    Task<Organization> CreateAsync(Organization entity);
    Task UpdateAsync(Organization entity);
    Task DeleteAsync(int id);
}

public class OrganizationRepository : IOrganizationRepository
{
    private readonly MyAdaptiveCloudContext _context;

    public OrganizationRepository(MyAdaptiveCloudContext context)
    {
        _context = context;
    }

    public async Task<Organization> GetByIdAsync(int id)
    {
        return await _context.Organization
            .FirstOrDefaultAsync(o => o.Id == id);
    }
}
```

### Entity Framework Configuration
- **Multiple Contexts**: Separate contexts for different domains
- **Connection Strings**: Configured per environment
- **Interceptors**: Custom save changes and read-only interceptors
- **Migrations**: Liquibase for database schema management

## Authentication & Authorization

### Authentication Schemes
The application supports multiple authentication methods:

```csharp
services.AddAuthentication(options =>
{
    options.DefaultScheme = "SHA256_COOKIE";
    options.DefaultChallengeScheme = "SHA256_COOKIE";
})
.AddCookie(CookieAuthenticationDefaults.AuthenticationScheme)
.AddOpenIdConnect(oidc => { /* KeyCloak configuration */ })
.AddHMACSigAuthAuthentication("MYAC-HMAC-SHA256")
.AddBasicAuthAuthentication("MYAC Basic")
.AddPolicyScheme("SHA256_COOKIE", "SHA256_COOKIE", options => { /* Policy selection */ });
```

### Custom Authorization Filters
```csharp
[OrgAuthorize(Perms.ViewServices, Perms.ManageServices)]
public async Task<ActionResult> GetServices([FromRoute] int organizationId)
{
    // Method implementation
}
```

### Authorization Filter Implementation
```csharp
public class OrgAuthorizeAttribute : BaseAsyncAuthorizationFilter
{
    public override async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
        if (!_userContextService.HasPermission(userId, organizationId, _distance, _perms))
        {
            context.Result = new ForbidResult();
        }
    }
}
```

### Permission System
- **Role-Based**: Users have roles within organizations
- **Hierarchical**: Permissions can be inherited from parent organizations
- **Granular**: Fine-grained permissions for specific actions

## API Design Patterns

### Response Models
```csharp
public class ApiResult
{
    public string Message { get; set; }
    public bool Success { get; set; } = true;
}

public class ApiDataResult<T> : ApiResult
{
    public T Data { get; set; }
}

public class ApiDataSetResult<T> : ApiDataResult<T>
{
    public int TotalCount { get; set; }
}
```

### Request/Response Flow
1. **Request Validation**: Model binding and validation
2. **Authorization**: Custom authorization filters
3. **Service Call**: Delegate to appropriate service
4. **Data Mapping**: AutoMapper for DTO conversion
5. **Response**: Standardized response format

### Error Handling
- **Global Exception Handler**: Centralized error handling
- **Custom Exceptions**: Domain-specific exceptions
- **Logging**: Structured logging with Serilog
- **Response Format**: Consistent error response structure

## Middleware

### Custom Middleware Components
```csharp
// HTTP Request Logging
app.UseHttpCustomLogging();

// Time Zone Middleware
app.UseHttpRequestTimeZoneMiddleware();

// Exception Handling
if (isDevelopmentEnvironment)
{
    app.UseExceptionHandler("/api/error-development");
}
else
{
    app.UseExceptionHandler("/api/error");
}
```

### Middleware Order
1. **Exception Handling**: Global error handling
2. **HTTPS Redirection**: Force HTTPS in production
3. **Static Files**: Serve Angular static files
4. **Routing**: Route matching
5. **Authentication**: User authentication
6. **Authorization**: Permission checking
7. **Custom Middleware**: Time zone, logging, etc.

## Configuration

### Application Settings
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=myacdb01;Database=myadaptivecloud;",
    "ACAgentConnection": "Server=myacdb01;Database=acagent;",
    "LogsConnection": "Server=myacdb01;Database=myadaptivecloudlogs;",
    "BillingConnection": "Server=sql01;Database=Services;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  }
}
```

### Environment-Specific Configuration
- **Development**: `appsettings.Development.json`
- **Production**: `appsettings.json` + secrets
- **Secrets**: `appsettings.secret.json` for sensitive data
- **Permissions**: `appsettings.permissions.json` for role definitions

### Key Configuration Areas
- **Database Connections**: Multiple database contexts
- **Authentication**: KeyCloak, HMAC, Basic auth settings
- **External APIs**: CloudStack, ConnectWise, etc.
- **SignalR**: Real-time communication settings
- **File Upload**: Size limits and storage paths

## Best Practices

### Code Organization
- **Single Responsibility**: Each class has one responsibility
- **Dependency Injection**: Constructor injection for all dependencies
- **Async/Await**: Use async patterns for I/O operations
- **Error Handling**: Proper exception handling and logging

### Performance Considerations
- **Database Queries**: Use appropriate EF Core patterns
- **Caching**: Implement caching for frequently accessed data
- **Pagination**: Use pagination for large data sets
- **Connection Pooling**: Configure EF Core connection pooling

### Security Best Practices
- **Input Validation**: Validate all user inputs
- **Authorization**: Apply authorization at controller level
- **SQL Injection**: Use parameterized queries
- **Secrets Management**: Store secrets securely
