<div class="modal-header">
    <h4 class="modal-title">Edit Virtual Private Cloud</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form">
        <div class="row mb-3">
            <label for="name" class="col-3 col-form-label">Name<span class="required-asterisk">*</span></label>
            <div class="col">
                <input class="form-control" data-testid="name" formControlName="name" id="name"
                    [class]="{ 'is-invalid': form.controls.name.invalid && form.controls.name.dirty }"
                    autocomplete="off" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="description" for="description" class="col-3 col-form-label">Description</label>
            <div class="col">
                <input class="form-control" data-testid="description" formControlName="description" id="description"
                    [class]="{ 'is-invalid': form.controls.description.invalid && form.controls.description.dirty }" />
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!form?.valid" (submitClickEvent)="submit()">OK
    </app-btn-submit>
</div>
