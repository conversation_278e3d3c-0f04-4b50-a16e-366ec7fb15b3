@if(domain()) {
    @let currentDomainIsSelected = domain().id === store.selectedDomain()?.id;
    @let selectedDomainClass = currentDomainIsSelected ? 'bg-tree-secondary':'text-secondary';

    <div class="fill-content" [style]="{'padding-left': domain().level * 25 + 'px' }">
        <ng-container *ngTemplateOutlet="treeContent" />
    </div>

    <ng-template #treeContent>
        <div class="py-2 d-flex clickable" [class]="selectedDomainClass">
            <span class="col-1 text-center" (click)="store.onToggleDomain(domain().id, domain().isExpanded)">
                <i class="fa-solid toggle-arrow" [class]="domain().isExpanded ? 'fa-angle-down' : 'fa-angle-right'"></i>
            </span>
            <span class="col-1 text-center">
                <i class="fa-solid" [class]="domain().level === 0 ?  'fa-cubes' : 'fa-cube'"></i>
            </span>
            <span (click)="store.setSelectedDomain(domain())" class="ms-2 col-8 clickable" data-testid="domain-name"
                [title]="domain().name">
                {{domain().name}}
            </span>

        </div>

        @if (domain().isExpanded) {
            <div>
                @for(subDomain of domain().subDomains; track subDomain.id) {
                    <app-domain-account-tree [domain]="subDomain" />
                }
                @for(account of domain().accounts; track account.id) {
                    <div class="py-2 d-flex clickable" data-testid="account-name" (click)="store.setSelectedAccount(account)"
                        [class]="account.id === store.selectedAccount()?.id ? 'bg-tree-secondary' : 'text-secondary'"
                        [style]="{'padding-left':  domain().level * 25 + 'px' }">
                        <span class="col-1 text-center">&nbsp;</span>
                        <span class="col-1 text-center">
                            <i class="fa-solid fa-user"></i>
                        </span>
                        <span class="col-8">
                            {{ account.name }}
                        </span>
                    </div>
                }
            </div>
        }

    </ng-template>

}

@if ((!domain()) && store.selectedAccount()?.id) {
    <div class="py-2 d-flex clickable" data-testid="account-name" [class]=" 'bg-tree-secondary'"
        [style]="{'padding-left':  '25px' }">
        <span class="col-1 text-center">&nbsp;</span>
        <span class="col-1 text-center">
            <i class="fa-solid fa-user"></i>
        </span>
        <span class="col-8">
            {{ store.selectedAccount().name }}
        </span>
    </div>
}
