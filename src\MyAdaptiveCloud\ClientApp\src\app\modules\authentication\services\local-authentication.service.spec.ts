import { TestBed } from '@angular/core/testing';
import { ApiService } from '@app/shared/services/api.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { LoginRequest } from '../requests/login.request';
import { LocalAuthenticationService } from './local-authentication.service';

describe('LocalAuthenticationService', () => {
    let service: LocalAuthenticationService;
    let mockApiService: jasmine.SpyObj<ApiService>;
    const endpoint = 'localauthentication';

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ApiService),
                LocalAuthenticationService
            ]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(LocalAuthenticationService);
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(service).toBeTruthy();
        });
    });

    describe('API', () => {
        it('should use the right endpoint for login', () => {
            const request = { email: '<EMAIL>', password: 'ultrasercretpswd' } as LoginRequest;
            service.login(request);
            expect(mockApiService.post).toHaveBeenCalledWith(`${endpoint}/authenticate`, request);
        });
    });
});
