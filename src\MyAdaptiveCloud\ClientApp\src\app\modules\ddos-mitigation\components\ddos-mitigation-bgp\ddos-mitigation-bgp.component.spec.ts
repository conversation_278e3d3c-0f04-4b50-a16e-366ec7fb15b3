import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideMock } from '@app/shared/specs/spy-helpers';

import { ActivatedRoute } from '@angular/router';
import { DdosMitigationBgpComponent } from './ddos-mitigation-bgp.component';

describe('DdosMitigationBgpComponent', () => {
    let component: DdosMitigationBgpComponent;
    let fixture: ComponentFixture<DdosMitigationBgpComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationBgpComponent],
            providers: [provideMock(ActivatedRoute)],
        })
            .compileComponents();

        fixture = TestBed.createComponent(DdosMitigationBgpComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
