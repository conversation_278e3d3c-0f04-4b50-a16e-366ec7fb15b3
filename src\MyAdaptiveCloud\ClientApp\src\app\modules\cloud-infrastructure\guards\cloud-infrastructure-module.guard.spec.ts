import { TestBed } from '@angular/core/testing';
import { PermissionService } from '@app/shared/services/permission.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { CloudInfrastructureModuleGuard } from './cloud-infrastructure-module.guard';

describe('CloudInfrastructureModuleGuard', () => {
    let guard: CloudInfrastructureModuleGuard;
    let mockPermissionService: jasmine.SpyObj<PermissionService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(PermissionService),
                CloudInfrastructureModuleGuard
            ]
        });

        mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
        guard = TestBed.inject(CloudInfrastructureModuleGuard);
    });

    it('should return false when canViewCloudInfra is not allowed ', () => {
        mockPermissionService.canViewCloudInfra.and.returnValue(false);
        expect(guard.canActivate()).toBeFalse();
    });

    it('should return true when canViewCloudInfra is allowed ', () => {
        mockPermissionService.canViewCloudInfra.and.returnValue(true);
        expect(guard.canActivate()).toBeTrue();
    });

});
