import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { IsolatedNetworkComponent } from '@app/modules/cloud-infrastructure/components/isolated-network/isolated-network.component';
import { Layer2NetworkComponent } from '@app/modules/cloud-infrastructure/components/layer2-network/layer2-network.component';
import { SharedNetworkComponent } from '@app/modules/cloud-infrastructure/components/shared-network/shared-network.component';
import { CreateNetworkZoneForm } from '@app/modules/cloud-infrastructure/forms/create-network-zone.form';
import { AddNetworkTabs } from '@app/modules/cloud-infrastructure/models/add-network-tabs.enum';
import { NetworkOfferingViewModel } from '@app/modules/cloud-infrastructure/models/network-offering.view-model';
import { VpcViewModel } from '@app/modules/cloud-infrastructure/models/vpc.view-model';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { CreateIsolatedNetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-isolated-network.request';
import { CreateLayer2NetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-layer2-network.request';
import { SharedNetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-shared-network';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbNav, NgbNavContent, NgbNavItem, NgbNavLink, NgbNavOutlet } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { filter, forkJoin, startWith, switchMap, tap } from 'rxjs';

@Component({
    selector: 'app-create-network',
    imports: [BtnSubmitComponent, NgbNav, NgbNavItem, NgbNavOutlet, NgbNavLink, NgbNavContent, ReactiveFormsModule, IsolatedNetworkComponent, Layer2NetworkComponent, SharedNetworkComponent, NgSelectComponent],
    templateUrl: './create-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateNetworkComponent implements OnInit {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly createNetworkService = inject(CreateNetworkService);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);
    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly isolatedNetworkComponent = viewChild(IsolatedNetworkComponent);
    protected readonly layer2NetworkComponent = viewChild(Layer2NetworkComponent);
    protected readonly sharedNetworkComponent = viewChild(SharedNetworkComponent);

    readonly zones = signal<ZoneViewModel[]>([]);
    readonly domainId = signal<string>(null);
    readonly account = signal<string>(null);

    protected readonly form = signal<FormGroup<CreateNetworkZoneForm>>(null);

    protected readonly isolatedNetworkOfferings = signal<NetworkOfferingViewModel[]>([]);
    protected readonly layer2NetworkOfferings = signal<NetworkOfferingViewModel[]>([]);
    protected readonly sharedNetworkOfferings = signal<NetworkOfferingViewModel[]>([]);
    protected readonly vpcOfferings = signal<VpcViewModel[]>([]);

    protected readonly addNetworkTabs = AddNetworkTabs;
    protected activeTab = AddNetworkTabs.Isolated;

    ngOnInit(): void {
        const form = this.formBuilder.group<CreateNetworkZoneForm>({
            zoneId: this.formBuilder.control<string>(this.zones()[0].id, Validators.required),
        });

        form.controls.zoneId.valueChanges
            .pipe(
                startWith(form.controls.zoneId.value),
                filter(zoneId => !!zoneId),
                switchMap(zoneId => forkJoin([
                    this.createNetworkService.getIsolatedNetworkOfferings(zoneId, this.domainId()).pipe(tap(isolatedOfferings => this.isolatedNetworkOfferings.set(isolatedOfferings))),
                    this.createNetworkService.getLayer2NetworkOfferings(zoneId, this.domainId()).pipe(tap(layer2NetworkOfferings => this.layer2NetworkOfferings.set(layer2NetworkOfferings))),
                    this.createNetworkService.getSharedNetworkOfferings(zoneId, this.domainId()).pipe(tap(sharedNetworkOfferings => this.sharedNetworkOfferings.set(sharedNetworkOfferings))),
                    this.createNetworkService.getVpcOfferings(zoneId, this.domainId(), this.account()).pipe(tap(vpcOfferings => this.vpcOfferings.set(vpcOfferings)))
                ])),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe();

        this.form.set(form);
    }

    protected submitForm() {
        switch (this.activeTab) {
            case AddNetworkTabs.Isolated:
                if (this.isolatedNetworkComponent().form.valid) {
                    const form = this.isolatedNetworkComponent().form.value;
                    const request: CreateIsolatedNetworkRequest = {
                        description: form.description,
                        gateway: form.gateway,
                        name: form.name,
                        netmask: form.netmask,
                        networkDomain: form.networkDomain,
                        networkOfferingId: form.networkOffering.id,
                        vpc: form.vpc
                    };
                    this.createNetworkService.createIsolatedNetwork(this.form().controls.zoneId.value, this.domainId(), this.account(), request)
                        .subscribe(res => {
                            this.activeModal.close(res);
                        });
                }
                break;
            case AddNetworkTabs.Layer2:
                if (this.layer2NetworkComponent().form.valid) {
                    const form = this.layer2NetworkComponent().form.value;
                    const request: CreateLayer2NetworkRequest = {
                        description: form.description,
                        name: form.name,
                        networkOfferingId: form.networkOffering.id,
                        vLan: form.vlan,
                        bypassVLanId: form.bypassVLanId,
                        secondaryVLanID: form.secondaryVLanId,
                        secondaryVLanType: form.secondaryVLanType
                    };
                    this.createNetworkService.createLayer2Network(this.form().controls.zoneId.value, this.domainId(), this.account(), request)
                        .subscribe(res => {
                            this.activeModal.close(res);
                        });

                }
                break;
            case AddNetworkTabs.Shared:
                if (this.sharedNetworkComponent().form.valid) {
                    const form = this.sharedNetworkComponent().form.value;

                    const request : SharedNetworkRequest = {
                        name: form.name,
                        displaytext: form.description,
                        networkofferingid: form.networkOffering?.id,
                        hideipaddressusage: form.hideIpAddressUsage,
                        gateway: form.ipv4Gateway,
                        netmask: form.ipv4Netmask,
                        startip: form.ipv4StartIp,
                        endip: form.ipv4EndIp,
                        gatewayv6: form.ipv6Gateway,
                        cidr: form.ipv6CIDR,
                        startipv6: form.ipv6StartIp,
                        endipv6: form.ipv6EndIp,
                        networkdomain: form.networkDomain,
                        vlan: form.vlanVni
                    };

                    this.createNetworkService.createSharedNetwork(
                        this.form().controls.zoneId.value,
                        this.domainId(),
                        this.account(),
                        request
                    ).subscribe(res => {
                        this.activeModal.close(res);
                    });
                }

                break;
        }
    }

}
