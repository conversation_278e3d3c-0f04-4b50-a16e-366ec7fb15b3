export enum VolumeState {
    Allocated = 'Allocated',
    Attaching = 'Attaching',
    Copying = 'Copying',
    Creating = 'Creating',
    Destroy = 'Destroy',
    Destroying = 'Destroying',
    Expunged = 'Expunged',
    Expunging = 'Expunging',
    Migrating = 'Migrating',
    NotUploaded = 'NotUploaded',
    Ready = 'Ready',
    Resizing = 'Resizing',
    Restoring = 'Restoring',
    RevertSnapshotting = 'RevertSnapshotting',
    Snapshotting = 'Snapshotting',
    UploadAbandoned = 'UploadAbandoned',
    UploadError = 'UploadError',
    UploadInProgress = 'UploadInProgress',
    UploadNotStarted = 'Upload Not Started',
    UploadOp = 'UploadOp',
    Uploaded = 'Uploaded',
}
