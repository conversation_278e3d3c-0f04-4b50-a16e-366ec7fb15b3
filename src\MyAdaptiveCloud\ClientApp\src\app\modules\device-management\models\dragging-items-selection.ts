import { DeviceFolderTreeNode } from './device-folder-tree-node';
import { DeviceTreeNode } from './device-tree-node';
import { ItemsSelection } from './items-selection';

export class DraggingItemsSelection extends ItemsSelection {
    draggingItem: DeviceTreeNode | DeviceFolderTreeNode;

    constructor(selectedDevices?: DeviceTreeNode[], selectedFolders?: DeviceFolderTreeNode[], draggingItem?: DeviceFolderTreeNode | DeviceTreeNode) {
        super(selectedDevices, selectedFolders);

        if (draggingItem) {
            this.setDraggingSelection(selectedDevices ?? [], selectedFolders ?? [], draggingItem);
        }
    }

    private setDraggingSelection(selectedDevices: DeviceTreeNode[], selectedFolders: DeviceFolderTreeNode[], draggingItem: DeviceFolderTreeNode | DeviceTreeNode) {
        const isDraggingItemPartOfSelection = draggingItem instanceof DeviceTreeNode ?
            selectedDevices.some(d => d.agentId === draggingItem.agentId) : selectedFolders.some(f => f.folderId === draggingItem.folderId);

        if (isDraggingItemPartOfSelection) {
            this._devices = selectedDevices;
            this._folders = selectedFolders;
        } else {
            this._devices = draggingItem instanceof DeviceTreeNode ? [draggingItem] : [];
            this._folders = draggingItem instanceof DeviceFolderTreeNode ? [draggingItem] : [];
        }
        this.draggingItem = draggingItem;
    }

    isDragging(): boolean {
        return !((this.draggingItem ?? null) === null);
    }
}
