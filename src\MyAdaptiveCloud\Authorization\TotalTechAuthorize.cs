using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class TotalTechAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IConfigurationService _configurationService;
        private readonly IIdentityService _identityService;

        public TotalTechAuthorizeFilter(
            IConfigurationService configurationService,
            IEntityAuthorizationService entityAuthorizationService,
            IIdentityService identityService)
        {
            _configurationService = configurationService;
            _entityAuthorizationService = entityAuthorizationService;
            _identityService = identityService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int currentUserId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (currentUserId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            var configuration = await _configurationService.GetTotalTechConfiguration();
            if (configuration.ServiceIds == null || configuration.ServiceIds.Count == 0)
            {
                context.Result = new BadRequestResult();
            }
            else
            {
                string val = AuthorizeFilterHelpers.GetEntityValue(context, "organizationId");
                _ = int.TryParse(val, out int organizationId);

                var hasTotalTechService = await _entityAuthorizationService.HasTotalTechService(configuration.ServiceIds, organizationId);
                if (!hasTotalTechService)
                {
                    context.Result = new ForbidResult();
                }
            }
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class TotalTechAuthorizeAttribute : TypeFilterAttribute
    {
        public TotalTechAuthorizeAttribute() : base(typeof(TotalTechAuthorizeFilter))
        {
        }
    }
}