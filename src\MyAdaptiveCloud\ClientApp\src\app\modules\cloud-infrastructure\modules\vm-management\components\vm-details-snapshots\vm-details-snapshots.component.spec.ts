import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { getMockZoneDomainAccountStore, MockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { ConfirmationDialogComponent } from '@app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { VmStateEnum } from '@app/shared/models/cloud-infra/vm-state.enum';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { ModalService } from '@app/shared/services/modal.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { Snapshot } from '../../models/snapshot';
import { SnapshotState } from '../../models/snapshot-state.enum';
import { VmDetails } from '../../models/vm-detail.model';
import { VmActionsService } from '../../services/vm-actions.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { VmManagementService } from '../../services/vm-management.service';
import { VmMediaService } from '../../services/vm-media-service';
import { CreateSnapshotFromVmSnapshotComponent } from '../create-snapshot-from-vm-snapshot/create-snapshot-from-vm-snapshot.component';
import { VmDetailsSnapshotsComponent } from './vm-details-snapshots.component';

describe('VmDetailsSnapshotsComponent', () => {
    let fixture: ComponentFixture<VmDetailsSnapshotsComponent>;
    let vmDetailsStateService: VmDetailsStateService;
    let mockVmManagementPermissionService: jasmine.SpyObj<VmManagementPermissionService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockModalService: jasmine.SpyObj<ModalService>;

    let snapshots: Snapshot[];

    let dataTableDebugElement: DebugElement;
    let dataTable: HTMLElement;

    let mockZoneDomainAccountStore: MockZoneDomainAccountStore;

    beforeEach(() => {

        mockZoneDomainAccountStore = getMockZoneDomainAccountStore();

        const vm = {
            id: 'vm1',
            state: VmStateEnum.Running,
            name: 'VM 1',
            domainid: 'domain-id',
            account: 'account',
            domain: 'domain'
        } as VmDetails;

        snapshots = [
            {
                id: 'snap1', name: 'Snapshot 1', description: 'Test Snapshot 1', created: new Date(2025, 4, 4, 12, 5, 5).toISOString(), account: 'account', domain: 'domain',
                domainid: 'domain-id', current: true, displayname: 'Snapshot 1 Display', parent: 'parent', type: 'Disk', state: 'Ready'
            } as Snapshot,
            {
                id: 'snap2', name: 'Snapshot 2', description: 'Test Snapshot 2', created: new Date(2025, 4, 5, 12, 3, 4).toISOString(), account: 'account', domain: 'domain',
                domainid: 'domain-id', current: false, parent: 'parent', type: 'DiskAndMemory', state: 'Error'
            } as Snapshot
        ];

        TestBed.configureTestingModule({
            imports: [
                VmDetailsSnapshotsComponent
            ],
            providers: [
                VmDetailsStateService,
                provideMock(VmManagementPermissionService),
                provideMock(NgbActiveModal),
                provideMock(ModalService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
                provideMock(VmManagementService),
                provideMock(ModalService),
                provideMock(CloudInfrastructureJobQueueService),
                provideMock(VmMediaService),
                VmActionsService,
                CreateSnapshotFromVmSnapshotComponent,
                ConfirmationDialogComponent
            ]
        });

        vmDetailsStateService = TestBed.inject(VmDetailsStateService);

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getVirtualMachineSnapshots.and.returnValue(of(snapshots));

        mockVmManagementPermissionService = TestBed.inject(VmManagementPermissionService) as jasmine.SpyObj<VmManagementPermissionService>;
        mockVmManagementPermissionService.canUpdateVmAffinityGroups.and.returnValue(true);
        mockVmManagementPermissionService.canCreateAffinityGroup.and.returnValue(true);
        mockVmManagementService.deleteVirtualMachineSnapshot.and.returnValue(of('job-id'));
        mockVmManagementService.revertToVirtualMachineSnapshot.and.returnValue(of('job-id'));

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;

        vmDetailsStateService.selectedVM.set(vm);

        fixture = TestBed.createComponent(VmDetailsSnapshotsComponent);

        dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
        dataTable = dataTableDebugElement.nativeElement;
    });

    describe('Initialization', () => {

        it('should load the VM snapshots', () => {
            fixture.detectChanges();
            expect(mockVmManagementService.getVirtualMachineSnapshots).toHaveBeenCalledOnceWith('vm1');
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(snapshots.length);
        });

        it('should show the Create Snapshot button when user has permission and enable it when there is an account selected', () => {
            mockVmManagementPermissionService.canSnapshotVirtualMachine.and.returnValue(true);
            fixture.detectChanges();
            const addButton = fixture.debugElement.query(By.css('[data-testid="create-snapshot-button"]'));
            expect(addButton).toBeDefined();
            expect((addButton.nativeElement as HTMLButtonElement).disabled).toBeFalse();
        });

        it('should show disable the Create Snapshot button when user has permission and the vm is not running', () => {
            mockVmManagementPermissionService.canSnapshotVirtualMachine.and.returnValue(true);
            vmDetailsStateService.selectedVM().state = VmStateEnum.Stopped;
            fixture.detectChanges();
            const addButton = fixture.debugElement.query(By.css('[data-testid="create-snapshot-button"]'));
            expect(addButton).toBeDefined();
            expect((addButton.nativeElement as HTMLButtonElement).disabled).toBeTrue();
        });

        it('should show the expected data in the grid', () => {
            fixture.detectChanges();
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            const secondRow = rows[1];
            const secondRowCells = secondRow.querySelectorAll('.datatable-body-cell');

            expect(firstRowCells[0].textContent.trim()).toBe(snapshots[0].state);
            expect(firstRowCells[1].textContent).toBe(snapshots[0].displayname);
            expect(firstRowCells[2].textContent).toBe(snapshots[0].type);
            expect(firstRowCells[3].textContent).toBe(' Yes ');
            expect(firstRowCells[4].textContent).toBe(snapshots[0].parent);
            expect(firstRowCells[5].textContent).toBe((' 2025-05-04 12:05:05 '));

            expect(secondRowCells[0].textContent.trim()).toBe(snapshots[1].state);
            expect(secondRowCells[1].textContent).toBe(snapshots[1].name);
            expect(secondRowCells[2].textContent).toBe(snapshots[1].type);
            expect(secondRowCells[3].textContent).toBe(' No ');
            expect(secondRowCells[4].textContent).toBe(snapshots[1].parent);
            expect(secondRowCells[5].textContent).toBe((' 2025-05-05 12:03:04 '));
        });

    });

    describe('Component Interaction', () => {

        it('should not have actions when the user does not have the permissions', () => {
            fixture.detectChanges();

            const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
            expect(actions.length).toBe(0);
        });

        describe('create from snapshot', () => {

            it('should open the create from snapshot modal when clicking on the action and user has permission', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(CreateSnapshotFromVmSnapshotComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[0].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as CreateSnapshotFromVmSnapshotComponent).account()).toBe('account');
                expect((modalRef.componentInstance as CreateSnapshotFromVmSnapshotComponent).domainId()).toBe('domain-id');
                expect((modalRef.componentInstance as CreateSnapshotFromVmSnapshotComponent).snapshotName()).toBe(snapshots[0].displayname);
                expect((modalRef.componentInstance as CreateSnapshotFromVmSnapshotComponent).virtualMachineId()).toBe('vm1');
                expect((modalRef.componentInstance as CreateSnapshotFromVmSnapshotComponent).virtualMachineSnapshotId()).toBe(snapshots[0].id);

            });

        });

        describe('revert', () => {

            beforeEach(() => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);
            });

            it('should open the revert to snapshot modal when clicking on the action and the VM is running', () => {
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[1].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Are you sure you want to revert to this snapshot?', title: 'Revert to Snapshot' });
                expect(mockVmManagementService.revertToVirtualMachineSnapshot).toHaveBeenCalledOnceWith(snapshots[0].id);
            });

            it('should open the revert to snapshot modal when clicking on the action and the VM is stopped', () => {
                vmDetailsStateService.selectedVM().state = VmStateEnum.Stopped;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[1].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Are you sure you want to revert to this snapshot?', title: 'Revert to Snapshot' });
                expect(mockVmManagementService.revertToVirtualMachineSnapshot).toHaveBeenCalledOnceWith(snapshots[0].id);
            });
        });

        describe('delete', () => {

            beforeEach(() => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;
                mockModalService.openDeleteConfirmationDialog.and.returnValue(modalRef);
            });

            it('should open the delete snapshot modal when clicking on the action and the snapshot is in ready state', () => {
                snapshots[0].state = SnapshotState.Ready;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[2].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openDeleteConfirmationDialog).toHaveBeenCalledOnceWith('Are you sure you want to delete this snapshot?', 'Delete', 'Delete');
                expect(mockVmManagementService.deleteVirtualMachineSnapshot).toHaveBeenCalledOnceWith(snapshots[0].id);
            });

            it('should open the delete snapshot modal when clicking on the action and the snapshot is in error state', () => {
                snapshots[0].state = SnapshotState.Error;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[2].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openDeleteConfirmationDialog).toHaveBeenCalledOnceWith('Are you sure you want to delete this snapshot?', 'Delete', 'Delete');
                expect(mockVmManagementService.deleteVirtualMachineSnapshot).toHaveBeenCalledOnceWith(snapshots[0].id);
            });

            it('should open the delete snapshot modal when clicking on the action and the snapshot is in expunging state', () => {
                snapshots[0].state = SnapshotState.Error;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[2].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openDeleteConfirmationDialog).toHaveBeenCalledOnceWith('Are you sure you want to delete this snapshot?', 'Delete', 'Delete');
                expect(mockVmManagementService.deleteVirtualMachineSnapshot).toHaveBeenCalledOnceWith(snapshots[0].id);
            });

            it('should not open the delete snapshot modal when clicking on the action and the snapshot is allocated', () => {
                snapshots[0].state = SnapshotState.Allocated;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[2].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openDeleteConfirmationDialog).not.toHaveBeenCalled();
                expect(mockVmManagementService.deleteVirtualMachineSnapshot).not.toHaveBeenCalled();
            });

            it('should not open the delete snapshot modal when clicking on the action and the snapshot is creating', () => {
                snapshots[0].state = SnapshotState.Creating;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[2].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openDeleteConfirmationDialog).not.toHaveBeenCalled();
                expect(mockVmManagementService.deleteVirtualMachineSnapshot).not.toHaveBeenCalled();
            });

            it('should not open the delete snapshot modal when clicking on the action and the snapshot is reverting', () => {
                snapshots[0].state = SnapshotState.Reverting;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[2].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openDeleteConfirmationDialog).not.toHaveBeenCalled();
                expect(mockVmManagementService.deleteVirtualMachineSnapshot).not.toHaveBeenCalled();
            });

            it('should not open the delete snapshot modal when clicking on the action and the snapshot is removed', () => {
                snapshots[0].state = SnapshotState.Removed;
                mockVmManagementPermissionService.canRevertToVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot.and.returnValue(true);
                mockVmManagementPermissionService.canDeleteVirtualMachineSnapshot.and.returnValue(true);
                fixture.detectChanges();

                const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
                actions[2].query(By.css('span')).nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openDeleteConfirmationDialog).not.toHaveBeenCalled();
                expect(mockVmManagementService.deleteVirtualMachineSnapshot).not.toHaveBeenCalled();
            });

        });

    });

});
