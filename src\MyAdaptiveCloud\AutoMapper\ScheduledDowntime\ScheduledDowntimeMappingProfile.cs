﻿using AutoMapper;
using MyAdaptiveCloud.Api.Requests.ScheduledDowntime;
using MyAdaptiveCloud.Services.DTOs.ScheduledDowntime;


namespace MyAdaptiveCloud.Api.AutoMapper.ScheduledDowntime
{
    public class ScheduledDowntimeMappingProfile : Profile
    {
        public ScheduledDowntimeMappingProfile()
        {
            CreateMap<CreateEditScheduledDowntimeRequest, ScheduledDowntimeDTO>()
                .ForMember(dest => dest.ScheduledDowntimeId, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationId, opt => opt.Ignore())
                .ForMember(dest => dest.MatchedDevices, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByName, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedByName, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.MetricsToShow, opt => opt.Ignore());

            CreateMap<ScheduledDowntimeDeviceRequest, ScheduledDowntimeDeviceDTO>()
                .ForMember(dest => dest.ScheduledDowntimeDeviceId, opt => opt.Ignore())
                .ForMember(dest => dest.Folder, opt => opt.Ignore())
                .ForMember(dest => dest.FolderId, opt => opt.Ignore())
                .ForMember(dest => dest.Path, opt => opt.Ignore())
                .ForMember(dest => dest.Name, opt => opt.Ignore());
        }
    }
}