<form [formGroup]="form()">
    <div class="row py-2 d-flex align-items-center border-bottom">

        <div class="col-4 pt-1">
            <h6>
                {{ componentName() ?? component.name }}
                @if (componentName() === ALL_DRIVES_NAME_CONSTANT || component.name === ALL_DRIVES_NAME_CONSTANT) {
                <span>
                    <i title="These settings will be applied to all drives that are set to Inherit"
                        class="mx-2 text-secondary fa fa-info-circle"></i>
                </span>
                }
            </h6>
        </div>

        <div class="col-5 align-self-center inheritanceTypeDdl">
            @if (component.metrics) {
            @for (deviceAlertThresholdType of component.deviceAlertThresholdTypes; track deviceAlertThresholdType) {
            <div class="form-check form-check-inline"
                [class]="{'invisible' : component.deviceAlertThresholdTypes.length === 1}">
                <label class="form-check-label">
                    {{ deviceAlertThresholdType.name === '%' ? '% Used' :
                    deviceAlertThresholdType.name === 'GB' ? 'GB Free' : '' }}
                </label>
                <input type="radio" class="form-check-input" [value]="deviceAlertThresholdType.id"
                    formControlName="deviceAlertThresholdType" />
            </div>
            }
            }
        </div>
        <div class="col-3">
            <select class="form-select form-select-sm inheritanceTypeDdl" formControlName="inheritanceType">
                @for (inheritanceType of inheritanceTypes(); track inheritanceType) {
                <option [value]="inheritanceType.id">{{
                    inheritanceType.name }}</option>
                }
            </select>
        </div>
    </div>

    @if (+form().controls.inheritanceType.value === +inheritanceType.Inherit ) {
    <div class="row pt-2">
        <div class="mb-2 d-flex align-items-center justify-content-end text-primary pb-2"
            [title]="component.inheritedFromPath ?? ''" [class]="isDiskUsage() ? 'pt-0': 'pt-2'">
            <i class="mx-2 fa-solid fa-circle-exclamation"></i>
            <small> Settings inherited from: {{form().controls.inheritFrom.value ?? (isDiskUsage() ?
                ALL_DRIVES_NAME_CONSTANT : '') }}</small>
        </div>
    </div>
    }

    @if (component.metrics) {
    <div>
        <app-device-thresholds-metrics [form]="form().controls.metrics"
            [deviceAlertThresholdTypeName]="selectedDeviceAlertThresholdType?.name"
            [deviceAlertTypeName]="component.name" [intervals]="intervals()"
            [useIntervalsForMetrics]="useIntervalsForMetrics()"
            (thresholdWarningChanged)="updateOtherComponentMetrics($event)" />
    </div>
    }
    <div class="mt-3">
        <app-device-thresholds-intervals (thresholdIntervalChanged)="updateOtherComponentIntervals($event)"
            [intervals]="intervals()" [useIntervalsForMetrics]="useIntervalsForMetrics()"
            [form]="form().controls.intervals" />
    </div>
</form>
