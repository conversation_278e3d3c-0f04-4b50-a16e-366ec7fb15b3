<div class="content-sub-heading d-flex justify-content-between">
    <div class="search-bar">
        <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" [dataItemName]="'searchTerm'" />
    </div>
    <div class="action-buttons">
        <app-filters-form [filters]="filters" (filtersApplied)="onFilterFormChanged()"
            (filtersReset)="onFilterFormChanged()">
            <app-vm-list-filters [filters]="filters" [zones]="store.zones()" />
        </app-filters-form>
        <div class="d-inline"
            [title]="!store.getAccount() ? 'Select an Account to create a Network' : ''">
            <button class="btn btn-primary" [disabled]="!store.getAccount()"
                (click)="createVirtualMachine()">Create VM</button>
        </div>
    </div>
</div>

<div class="content-sub-heading pt-0">
    <app-pill-filter [appliedFilters]="appliedFilters | async"
        (filterRemoved)="onFilterRemoved($event)" />
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class='table bootstrap no-detail-row' [rowClass]="getDisabledRowClass"
            (sort)="onSorting($event)" (page)="onPageChanged($event)" />
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #statusRow let-row="row">

    @if (toItem(row)) {
        @let vmRunning = row.state === vmStateEnum.Running;
        @let vmDestroyed = row.state === vmStateEnum.Destroyed;
        @let vmStopped = row.state === vmStateEnum.Stopped;

        @if (vmRunning || vmDestroyed || vmStopped) {
            <div [ngbPopover]="statusPopTemplate" [popoverContext]="{ row: row }" triggers="hover" container="body"
                class="d-flex text-secondary justify-content-center">
                <i class="vm-icon-status" [class]="{
                            'icon-vm-running-with-agent': vmRunning && row.isAgentInstalled,
                            'icon-vm-running-without-agent': vmRunning && !row.isAgentInstalled,
                            'icon-vm-stopped-with-agent': vmStopped && row.isAgentInstalled,
                            'icon-vm-stopped-without-agent': vmStopped && !row.isAgentInstalled,
                            'icon-vm-destroyed-with-agent': vmDestroyed && row.isAgentInstalled,
                            'icon-vm-destroyed-without-agent': vmDestroyed && !row.isAgentInstalled,
                        }"></i>
            </div>

            <ng-template #statusPopTemplate let-row="row">
                @if (toItem(row); as row) {
                    <div [class]="{'text-success': vmRunning, 'text-danger': !vmRunning}">
                        <i [class]="row.state === vmStateEnum.Running ? 'fa-solid fa-check' : 'fa-solid fa-stop'"></i>
                        <span>&nbsp; {{ row.state }}</span>
                    </div>
                }
            </ng-template>
        } @else {
            <div class="d-flex justify-content-center" [ngbPopover]="row.state" triggers="hover" container="body">
                <div class="spinner-border spinner-border-sm text-secondary" role="status">
                    <span class="visually-hidden">{{ row.state }}</span>
                </div>
            </div>
        }
    }
</ng-template>

<ng-template #nameRow let-row="row">
    @if (toItem(row); as row) {
        @if(hasVirtualMachineDetailsFeatureFlag) {
            <a [routerLink]="row.id" [relativeTo]="activatedRoute.parent" class="text-decoration-none">
                {{ row.name }}
            </a>
        } @else {
            {{ row.name }}
        }
    }
</ng-template>

<ng-template #actionsTemplate let-row="row">

    @if (toItem(row); as row) {
        <div class="row g-0">
            <div class="col-8">
                @if (row.state === vmStateEnum.Stopped && vmPermissionService.canStartVirtualMachine()) {
                    <app-table-action (clickHandler)="vmActionsService.openStartVmModal(row.id, row.name, row.zoneId)" [title]="'Start VM'"
                        [icon]="'fa-solid fa-play text-success'" />
                }
                @if (row.state === vmStateEnum.Running) {
                    @if (vmPermissionService.canStopVirtualMachine()) {
                        <app-table-action [icon]="'fa-solid fa-circle-stop text-danger'"
                            (clickHandler)="vmActionsService.openStopVmModal(row.id, row.name)" [title]="'Stop VM'" />
                    }

                    @if (vmPermissionService.canRebootVirtualMachine()) {
                        <app-table-action [icon]="'fa-solid fa-arrow-rotate-left'"
                            (clickHandler)="vmActionsService.openRebootVmModal(row.id, row.name)" [title]="'Reboot VM'" />
                    }

                    @if (vmPermissionService.canAttachIso() && !row.attachedIsoId) {
                        <app-table-action [icon]="'fa-solid fa-compact-disc'" (clickHandler)="vmActionsService.openAttachIsoModal(row.id, row.zoneId, row.domainId, row.account, row.name)"
                            [title]="'Attach ISO'" />
                    }

                    @if (vmPermissionService.canEjectIso() && row.attachedIsoId) {
                        <app-table-action [icon]="'fa-solid fa-eject'" (clickHandler)="vmActionsService.openEjectIsoModal(row.id, row.name)"
                            [title]="'Eject ISO'" />
                    }

                    <app-table-action [icon]="'remote-control'" (clickHandler)="vmActionsService.openConsole(row.id)" [title]="'View Console'" />
                }
            </div>
            <div class="col-4">
                @if (row.state === vmStateEnum.Destroyed || row.state === vmStateEnum.Stopped || row.state === vmStateEnum.Running) {
                    <span ngbDropdown class="dropdown" container="body">
                        <button class="btn btn-link kebab-menu" ngbDropdownToggle>
                            <i class="fa-solid fa-ellipsis-vertical"></i>
                        </button>
                        <div ngbDropdownMenu class="custom-dropdown-menu">
                            @if (row.state === vmStateEnum.Running) {
                                @if (vmPermissionService.canSnapshotVirtualMachine()) {
                                    <button class="dropdown-item snapshot-vm" (click)="vmActionsService.openSnapshotVmModal(row.id,row.name)">Take VM
                                        snapshot</button>
                                }
                                @if (vmPermissionService.canSnapshotVolume()) {
                                    <button class="dropdown-item snapshot-volume" (click)="vmActionsService.openVolumeSnapshotModal(row.id, row.domainId, row.account, row.name)">Take Volume
                                        snapshot</button>
                                }
                                @if (vmPermissionService.canMigrateVirtualMachineHost()) {
                                    <button title="Migrate VM host" class="dropdown-item migrate-host"
                                        (click)="vmActionsService.openMigrateHostModal(row.id,row.name)">Migrate VM
                                        host</button>
                                }
                                @if (vmPermissionService.canReinstallVirtualMachine()) {
                                    <button class="dropdown-item reinstall-vm" (click)="vmActionsService.openReinstallVirtualMachineModal(row.id, row.zoneId, row.domainId, row.account, row.name, row.attachedIsoId)">Reinstall
                                        VM</button>
                                }
                            }
                            @if (row.state === vmStateEnum.Stopped) {
                                @if (vmPermissionService.canResetVirtualMachinePassword() && row.passwordEnabled) {
                                    <button class="dropdown-item reset-password"
                                        (click)="vmActionsService.openResetPasswordModal(row.id, row.name)">Reset
                                        Password</button>
                                }
                                @if (vmPermissionService.canResetSSHKeyPairForVirtualMachine()) {
                                    <button class="dropdown-item reset-ssh-pair" (click)="vmActionsService.openResetSSHKeyPairModal(row.id, row.domainId, row.account, row.name)">Reset SSH
                                        key pair</button>
                                }
                                @if (vmPermissionService.canReinstallVirtualMachine()) {
                                    <button class="dropdown-item reinstall-vm" (click)="vmActionsService.openReinstallVirtualMachineModal(row.id, row.zoneId, row.domainId, row.account, row.name, row.attachedIsoId)">Reinstall
                                        VM</button>
                                }
                                @if (vmPermissionService.canDestroyVirtualMachine()) {
                                    <button class="dropdown-item destroy-vm" (click)="vmActionsService.openDestroyVmModal(row.id, row.domainId, row.account, row.name)">Destroy</button>
                                }
                            }
                            @if (row.state === vmStateEnum.Destroyed) {
                                @if (vmPermissionService.canExpungeVirtualMachine()) {
                                    <button class="dropdown-item expunge-vm"
                                        (click)="vmActionsService.openExpungeDestroyedVirtualMachineModal(row.id,row.name)">Expunge</button>
                                }
                                @if (vmPermissionService.canRecoverVirtualMachine()) {
                                    <button class="dropdown-item recover-vm"
                                        (click)="vmActionsService.openRecoverVirtualMachineModal(row.id,row.name)">Recover</button>
                                }
                            }
                        </div>
                    </span>
                }
            </div>
        </div>
    }
</ng-template>
