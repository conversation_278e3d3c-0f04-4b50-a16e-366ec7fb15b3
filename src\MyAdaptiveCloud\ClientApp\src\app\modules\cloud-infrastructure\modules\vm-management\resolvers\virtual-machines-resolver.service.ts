import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { NotificationType } from '@app/shared/models/notification-type';
import { NotificationService } from '@app/shared/services/notification.service';
import { map, Observable, of } from 'rxjs';
import { VmDetailsStateService } from '../services/vm-details.state.service';
import { VmManagementService } from '../services/vm-management.service';

@Injectable({
    providedIn: 'root'
})
export class VirtualMachinesResolverService implements Resolve<string> {

    private readonly service = inject(VmManagementService);
    private readonly vmDetailsStateService = inject(VmDetailsStateService);
    private readonly notificationService = inject(NotificationService);

    resolve(route: ActivatedRouteSnapshot): Observable<string> {
        const vmId = route.paramMap.get('vmId');
        if (vmId) {
            this.vmDetailsStateService.isLoading$.next(true);
            const response = this.service.getVirtualMachineListByIds([vmId]);
            return response.pipe(map(result => {
                if (result[0]?.id) {
                    this.vmDetailsStateService.selectedVM.set(this.vmDetailsStateService.mapVmInstanceToVmDetails(result[0]));
                    this.vmDetailsStateService.isLoading$.next(false);
                    return result[0].displayname ?? result[0].name;
                }
                this.notificationService.notify('Virtual Machine not found or does not exist.', NotificationType.Error);
                this.vmDetailsStateService.isLoading$.next(false);
                return '';
            }));
        }
        return of(null);
    }
}
