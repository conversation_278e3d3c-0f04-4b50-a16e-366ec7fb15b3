import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { Neighbor } from '../../models/neighbor.model';
import { DDoSMitigationNeighborsService } from '../../services/ddos-mitigation-neighbors.service';
import { DdosMitigationBgpNeighborsComponent } from './ddos-mitigation-bgp-neighbors.component';

describe('DdosMitigationBgpNeighborsComponent', () => {

    const mockNeighbor1: Neighbor = {
        ip: '***********',
        localAs: 65001,
        remoteAs: 65100,
        state: 'Established',
        updown: '00:45:12',
        prefixesReceived: 1200,
        prefixesSent: 300,
        prefixesSentSimulated: 290,
        holdTime: 180,
        retryInterval: 30,
        scrubPhase1Community: '65100:100',
        scrubPhase2Community: '65100:200',
        scrubPostCommunity: '65100:300',
        blackholeCommunity: '65100:999'
    };

    const mockNeighbor2: Neighbor = {
        ip: '********',
        localAs: 65002,
        remoteAs: 65200,
        state: 'Idle',
        updown: 'N/A',
        prefixesReceived: 0,
        prefixesSent: 0,
        prefixesSentSimulated: 0,
        holdTime: 90,
        retryInterval: 60,
        scrubPhase1Community: '65200:101',
        scrubPhase2Community: '65200:201',
        scrubPostCommunity: '65200:301',
        blackholeCommunity: '65200:999'
    };

    const data = [mockNeighbor1, mockNeighbor2];

    let fixture: ComponentFixture<DdosMitigationBgpNeighborsComponent>;
    let mockDDoSMitigationNeighborsService: jasmine.SpyObj<DDoSMitigationNeighborsService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationBgpNeighborsComponent],
            providers: [
                provideMock(DDoSMitigationNeighborsService),
            ]
        })
            .compileComponents();

        mockDDoSMitigationNeighborsService = TestBed.inject(DDoSMitigationNeighborsService) as jasmine.SpyObj<DDoSMitigationNeighborsService>;
        mockDDoSMitigationNeighborsService.getList.and.returnValue(of({ data, message: '' }));

        fixture = TestBed.createComponent(DdosMitigationBgpNeighborsComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            fixture.detectChanges();
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
        });

        it('should call getNeighborsList', () => {
            expect(mockDDoSMitigationNeighborsService.getList).toHaveBeenCalledTimes(1);
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });
    });

    describe('Actions', () => {
        it('View Details action available', () => {
            const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
            expect(rows[0].query(By.css('.icon-viewDetails'))).toBeDefined();
        });
    });
});
