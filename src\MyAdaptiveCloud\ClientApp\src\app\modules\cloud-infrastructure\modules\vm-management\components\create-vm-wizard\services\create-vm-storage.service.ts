import { TitleCasePipe } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { map, Observable } from 'rxjs';
import { DiskOffering } from '../models/disk-offering.model';
import { ListDiskOfferingsResponse } from '../responses/list-disk-offerings.response';

@Injectable({
    providedIn: 'root'
})
export class CreateVmStorageService {
    private titleCasePipe = new TitleCasePipe();
    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getDiskOfferings(zoneId: string, domainId: string, account: string): Observable<DiskOffering[]> {
        const params = {
            command: 'listDiskOfferings',
            listall: 'true',
            zoneid: zoneId
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListDiskOfferingsResponse>(params)
            .pipe(map(response => (response?.listdiskofferingsresponse?.diskoffering ?? [])
                .map(diskOffering => ({
                    id: diskOffering.id,
                    offeringName: this.titleCasePipe.transform(diskOffering.name),
                    diskSize: diskOffering.disksize,
                    description: diskOffering.displaytext,
                    isCustomized: diskOffering.iscustomized,
                }))));
    }
}
