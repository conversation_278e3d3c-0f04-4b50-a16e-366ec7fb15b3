import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiService } from '@app/shared/services/api.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { Observable } from 'rxjs';
import { MitigationValidation } from '../models/mitigation-validation.model';
import { AsyncTaskResponse } from '../models/async-task-response.model';
import { StartMitigationRequest } from '../requests/start-mitigation.request';
import { StopMitigationRequest } from '../requests/stop-mitigation.request';
import { DDOS_MITIGATION_ENDPOINT_SEGMENTS } from './ddos-mitigation-endpoint-segments';

@Injectable({
    providedIn: 'root'
})
export class DDoSMitigationScrubService {

    private readonly apiService = inject(ApiService);
    private readonly userContext = inject(UserContextService);

    stop(prefix: string): Observable<ApiDataResult<AsyncTaskResponse>> {
        const body: StopMitigationRequest = {
            prefix
        };
        return this.apiService.post(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/${DDOS_MITIGATION_ENDPOINT_SEGMENTS.SCRUB}/stop/${this.userContext.currentUser.organizationId}`, body);
    }

    start(prefix: string, simulate: boolean): Observable<ApiDataResult<AsyncTaskResponse>> {
        const body: StartMitigationRequest = {
            prefix,
            simulate
        };
        return this.apiService.post(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/${DDOS_MITIGATION_ENDPOINT_SEGMENTS.SCRUB}/start/${this.userContext.currentUser.organizationId}`, body);
    }

    validate(cidr: string): Observable<ApiDataResult<MitigationValidation>> {
        return this.apiService.post(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/${DDOS_MITIGATION_ENDPOINT_SEGMENTS.SCRUB}/validate/${this.userContext.currentUser.organizationId}`, { cidr });
    }

}

