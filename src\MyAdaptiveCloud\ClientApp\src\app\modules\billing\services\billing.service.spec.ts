import { TestBed } from '@angular/core/testing';
import { ApiService } from '@app/shared/services/api.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { BillingService } from './billing.service';

describe('BillingService', () => {
    let service: BillingService;
    let mockApiService: jasmine.SpyObj<ApiService>;
    const endpoint = 'billing';

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ApiService),
                BillingService
            ]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(BillingService);
    });

    describe('API', () => {
        it('should use the right endpoint for getInvoicesByOrganization', () => {
            const organizationId = 3;
            service.getInvoicesByOrganization(organizationId);
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/organization/${organizationId}`, null);
        });

        it('should use the right endpoint for download Invoice', () => {
            const invoiceId = 2;
            service.downloadInvoice(invoiceId);
            expect(mockApiService.downloadFile).toHaveBeenCalledOnceWith(`${endpoint}/invoice/${invoiceId}`);
        });

    });
});
