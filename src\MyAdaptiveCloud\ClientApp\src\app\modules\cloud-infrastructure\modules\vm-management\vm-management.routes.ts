import { Routes } from '@angular/router';
import { cloudInfrastructureCredentialsGuard } from '../../guards/cloud-infrastructure-credentials.guard';
import { VM_ROUTE_SEGMENTS } from './constants/vm-management-route-segments';
import { domainAccountTreeAccountSelectedGuard } from './guards/domain-account-tree-account-selected.guard';
import { virtualMachinesDetailsGuard } from './guards/virtual-machines-details.guard';
import { VirtualMachinesResolverService } from './resolvers/virtual-machines-resolver.service';

export const vmRoutes: Routes = [
    {
        path: '',
        loadComponent: () => import('./components/vm-management/vm-management.component').then(m => m.VmManagementComponent),
        canActivate: [cloudInfrastructureCredentialsGuard],
        canActivateChild: [cloudInfrastructureCredentialsGuard],
        data: {
            breadcrumb: 'Virtual Machines'
        },
        children: [
            {
                path: '',
                redirectTo: VM_ROUTE_SEGMENTS.LIST,
                pathMatch: 'full'
            },
            {
                path: VM_ROUTE_SEGMENTS.LIST,
                data: {
                    breadcrumb: 'List'
                },
                loadComponent: () => import('./components/vm-list/vm-list.component').then(m => m.VmListComponent),
            },
            {
                path: 'create',
                loadComponent: () => import('./components/create-vm-wizard/create-vm-wizard.component').then(m => m.CreateVMWizardComponent),
                canActivate: [domainAccountTreeAccountSelectedGuard]
            },
            {
                path: ':vmId',
                canActivateChild: [virtualMachinesDetailsGuard],
                canActivate: [virtualMachinesDetailsGuard],
                loadComponent: () => import('./components/vm-details-container/vm-details-container.component').then(m => m.VmDetailsContainerComponent),
                data: {
                    tabsVisible: false,
                    breadcrumb: data => `${data.selectedVM}`,
                },
                resolve: {
                    selectedVM: VirtualMachinesResolverService,
                },
                children: [
                    {
                        path: '',
                        redirectTo: VM_ROUTE_SEGMENTS.DETAILS,
                        pathMatch: 'full'
                    },
                    {
                        path: VM_ROUTE_SEGMENTS.DETAILS,
                        loadComponent: () => import('./components/vm-details/vm-details.component').then(m => m.VmDetailsComponent),
                        data: {
                            breadcrumb: 'Details',
                        },
                    },
                    {
                        path: VM_ROUTE_SEGMENTS.NICS,
                        loadComponent: () => import('./components/vm-details-nic/vm-details-nic.component').then(m => m.VmDetailsNicComponent),
                        data: {
                            breadcrumb: 'NICs',
                        }
                    },
                    {
                        path: VM_ROUTE_SEGMENTS.VOLUMES,
                        loadComponent: () => import('./components/vm-details-volumes/vm-details-volumes.component').then(m => m.VmDetailsVolumesComponent),
                        data: {
                            breadcrumb: 'Volumes',
                        }
                    },
                    {
                        path: VM_ROUTE_SEGMENTS.SNAPSHOTS,
                        loadComponent: () => import('./components/vm-details-snapshots/vm-details-snapshots.component').then(m => m.VmDetailsSnapshotsComponent),
                        data: {
                            breadcrumb: 'Snapshots',
                        }
                    },
                    {
                        path: VM_ROUTE_SEGMENTS.AFFINITY_GROUPS,
                        loadComponent: () => import('./components/vm-details-affinity-groups/vm-details-affinity-groups.component').then(m => m.VmDetailsAffinityGroupsComponent),
                        data: {
                            breadcrumb: 'Affinity Groups',
                        }
                    },
                ]
            }
        ]
    }
];
