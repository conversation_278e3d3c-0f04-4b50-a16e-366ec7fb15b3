<div class="modal-header">
    <h4 class="modal-title">Create Network</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    @if (form()) {
        <form class="form-horizontal" [formGroup]="form()">
            <div class="mb-3 row">
                <div class="col-3">
                    <label class="col-3 col-form-label">Zone<span class="required-asterisk">*</span></label>
                </div>
                <div class="col-9">
                    <ng-select [items]="zones()" bindLabel="name" bindValue="id" formControlName="zoneId"
                        [clearable]="false" />
                </div>
            </div>
        </form>
        <nav ngbNav #nav="ngbNav" class="nav-tabs" [(activeId)]="activeTab" [destroyOnHide]="false">
            <ng-container [ngbNavItem]="addNetworkTabs.Isolated">
                <a ngbNavLink>Isolated</a>
                <ng-template [ngbNavContent]>
                    <app-add-network-isolated [networkOfferings]="isolatedNetworkOfferings()" [vpcs]="vpcOfferings()" />
                </ng-template>
            </ng-container>
            <ng-container [ngbNavItem]="addNetworkTabs.Layer2">
                <a ngbNavLink>Layer-2</a>
                <ng-template [ngbNavContent]>
                    <app-add-network-layer2 [networkOfferings]="layer2NetworkOfferings()" />
                </ng-template>
            </ng-container>
            @if (cloudInfraPermissionService.isRootAdmin()) {
                <ng-container [ngbNavItem]="addNetworkTabs.Shared">
                    <a ngbNavLink>Shared</a>
                    <ng-template [ngbNavContent]>
                        <app-add-network-shared [networkOfferings]="sharedNetworkOfferings()" />
                    </ng-template>
                </ng-container>
            }
        </nav>
        <div [ngbNavOutlet]="nav"></div>
    }
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!form()?.valid ||
                activeTab === addNetworkTabs.Isolated && isolatedNetworkComponent()?.form.invalid ||
                activeTab === addNetworkTabs.Layer2 && layer2NetworkComponent()?.form.invalid ||
                activeTab === addNetworkTabs.Shared && sharedNetworkComponent()?.form.invalid"
        (submitClickEvent)="submitForm()">OK
    </app-btn-submit>
</div>
