/* eslint-disable @typescript-eslint/no-explicit-any */
/* Legacy service created before enforcing the no-any rule */
import { AsyncPipe } from '@angular/common';
import { Component, DestroyRef, Input, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule, NgForm } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbActiveModal, NgbDateStruct, NgbInputDatepicker, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable, Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { AcCwProductMap } from '../../models/ac-cw-product-map.model';
import { AcCwVmMap } from '../../models/ac-cw-vm-map.model';
import { AcCwVmMappingRequest } from '../../requests/ac-cw-vm.mapping.request';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-create-ac-cw-vm-mapping',
    imports: [FormsModule, NgSelectModule, BtnSubmitComponent, NgbInputDatepicker, NgbPopover, AsyncPipe],
    templateUrl: './create-ac-cw-vm-mapping.component.html',
    styleUrl: './create-ac-cw-vm-mapping.component.scss'
})
export class CreateAcCwVmMappingComponent implements OnInit {
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly mappingService = inject(AcToCwMappingService);
    private readonly usageService = inject(AdaptiveCloudUsageService);
    private readonly notificationService = inject(NotificationService);
    private readonly userContextService = inject(UserContextService);
    private readonly destroyRef = inject(DestroyRef);

    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() mapping: AcCwVmMap;

    selectedProduct: AcCwProductMap;
    selectedType: any;
    selectedTarget: any;
    selectedPriority: number;
    isFormula = false;
    selectedValue: any;
    usageLabel: string;
    Products$: Observable<any[]>;
    MappingTypes = [
        { name: 'OsCategory' },
        { name: 'OsType' },
        { name: 'VirtualMachine' },
        { name: 'Template', disabled: true } // TODO Future implementation
    ];
    VMs$: Observable<any[]>;
    Companies$: Observable<any[]>;
    OsCats$: Observable<any[]>;
    OsTypes$: Observable<any[]>;

    companyInput$ = new Subject<string>();
    companiesLoading = false;
    startDate: NgbDateStruct;
    endDate: NgbDateStruct;

    ngOnInit(): void {
        this.Products$ = this.mappingService.getProductMappings()
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                map(res => res.data.filter(product => product.usageType === 'computelicense'))
            );

        const today = new Date();
        this.startDate = {
            year: today.getUTCFullYear(),
            month: today.getUTCMonth() + 1, // Month in Date is zero based, but NgbDate is 1 based
            day: today.getUTCDate()
        };

        if (this.mapping) {
            this.selectedProduct = this.mapping.productMap;
            this.selectedType = this.mapping?.acType ? { name: this.mapping.acType } : null;
            if (this.selectedType) {
                this.selectedTarget = {
                    id: this.mapping?.acId,
                    name: this.mapping?.acName,
                    domain: this.mapping?.domain,
                    account: this.mapping?.account
                };
            }
            this.selectedPriority = this.mapping.priority;
            this.onChangeMappingType();
            this.isFormula = this.mapping.isFormula;
            this.selectedValue = this.mapping.quantityValue;
            this.usageLabel = this.mapping.productMap.label;
            if (this.mapping.startDate) {
                this.startDate = {
                    year: this.mapping.startDate.getUTCFullYear(),
                    month: this.mapping.startDate.getUTCMonth() + 1,
                    day: this.mapping.startDate.getUTCDate()
                };
            }
            if (this.mapping.endDate) {
                this.endDate = {
                    year: this.mapping.endDate.getUTCFullYear(),
                    month: this.mapping.endDate.getUTCMonth() + 1,
                    day: this.mapping.endDate.getUTCDate()
                };
            }
        }
    }

    loadVMs(): void {
        // Cache the VMs, don't load if they're already loaded
        if (!this.VMs$) {
            this.VMs$ = this.usageService.getVirtualMachines().pipe(
                takeUntilDestroyed(this.destroyRef),
                map(res => {
                    res.data.map(vm => {
                        vm.acctdom = `Domain: ${vm.domain} - Account: ${vm.account}`;
                    });
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                    return res.data.sort((a, b) => {
                        const domainCmp = a.domain.localeCompare(b.domain);
                        if (domainCmp === 0) {
                            const accountCmp = a.account.localeCompare(b.account);
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                            return accountCmp !== 0 ? accountCmp : a.name.localeCompare(b.name);
                        }
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                        return domainCmp;

                    });
                })
            );
        }
    }

    loadOsCategories(): void {
        // Cache the Os Categories
        if (!this.OsCats$) {
            this.OsCats$ = this.usageService.getOsCategories(this.userContextService.currentUser.organizationId).pipe(
                takeUntilDestroyed(this.destroyRef),
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                map<ApiDataResult<any>, any>(res => res.data.sort((a, b) => a.name.localeCompare(b.name)))
            );
        }
    }

    loadOsTypes(): void {
        // Cache the Os Types
        if (!this.OsTypes$) {
            this.loadOsCategories();
            this.OsCats$.subscribe(categories => {
                this.OsTypes$ = this.usageService.getOsTypes(this.userContextService.currentUser.organizationId).pipe(
                    takeUntilDestroyed(this.destroyRef),
                    map<ApiDataResult<any>, any>(res => {
                        res.data.map(ostype => {
                            ostype.OsCategory = categories.find(cat => cat.id === ostype.osCategoryId).name;
                            ostype.name = ostype.description;
                        });
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                        return res.data.sort((a, b) => {
                            const catCmp = a.OsCategory.localeCompare(b.OsCategory);
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                            return catCmp ? catCmp : a.description.localeCompare(b.description);
                        });
                    })
                );
            });
        }
    }

    onChangeMappingType(): void {
        switch (this.selectedType?.name) {
            case 'VirtualMachine':
                this.loadVMs();
                this.selectedPriority = this.selectedPriority ? this.selectedPriority : 1;
                break;
            case 'OsCategory':
                this.loadOsCategories();
                this.selectedPriority = this.selectedPriority ? this.selectedPriority : 10;
                break;
            case 'OsType':
                this.loadOsTypes();
                this.selectedPriority = this.selectedPriority ? this.selectedPriority : 5;
                break;
            case 'Template':
                this.selectedPriority = this.selectedPriority ? this.selectedPriority : 5;
                break;
            default:
                // Unsupported or empty mapping type
                break;
        }
    }

    filterInt(value) {
        if (/^[-+]?(\d+|Infinity)$/.test(value)) {
            return Number(value);
        }
        return NaN;

    }

    vmCustomSearchFn(term: string, vm) {
        term = term.toLowerCase();
        return vm.name.toLowerCase().indexOf(term) > -1
            || vm.domain.toLowerCase().indexOf(term) > -1
            || vm.account.toLowerCase().indexOf(term) > -1;
    }

    submitForm(form: NgForm) {
        if (form.valid) {

            const request: AcCwVmMappingRequest = {
                acId: this.selectedTarget.id,
                acName: this.selectedTarget.name,
                acType: this.selectedType.name,
                priority: this.selectedPriority,
                quantityValue: this.selectedValue,
                isFormula: this.isFormula,
                productMapId: this.selectedProduct.id,
                domain: this.selectedTarget.domain,
                account: this.selectedTarget.account,
                startDate: new Date(this.startDate.year, this.startDate.month - 1, this.startDate.day),
                endDate: this.endDate ? new Date(this.endDate.year, this.endDate.month - 1, this.endDate.day) : null
            };

            const service = this.mapping ?
                this.mappingService.updateVmMapping(this.mapping.id, request)
                : this.mappingService.createVmMapping(request);

            service
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });
        }
    }

}
