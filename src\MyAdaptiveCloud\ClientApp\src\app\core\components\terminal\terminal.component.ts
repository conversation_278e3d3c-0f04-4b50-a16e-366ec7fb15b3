import { ChangeDetectionStrategy, Component, computed, ElementRef, HostListener, inject, input, On<PERSON><PERSON><PERSON>, OnInit, viewChild } from '@angular/core';
import { ShellType } from '@app/shared/models/shell-type.enum';
import { TerminalConnectionRequest } from '@app/shared/requests/terminal-connection.request';
import { DevicesSharedService } from '@app/shared/services/devices-shared.service';
import { FitAddon } from '@xterm/addon-fit';
import { Terminal } from '@xterm/xterm';
import { take } from 'rxjs';

@Component({
    selector: 'app-terminal',
    imports: [],
    templateUrl: './terminal.component.html',
    styleUrl: './terminal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class TerminalComponent implements OnInit, OnDestroy {
    private readonly devicesService = inject(DevicesSharedService);

    readonly id = input.required<number>();
    readonly type = input.required<ShellType>();

    protected readonly backgroundColor = computed(() => (this.type() === ShellType.CMD ? '#1e1e1e' : '#012456'));

    private readonly terminalContainer = viewChild.required<ElementRef>('terminalContainer');

    private terminal: Terminal;
    private fitAddon: FitAddon;
    private socket: WebSocket;
    private initialCols: number;
    private initialRows: number;
    private wsUrl: string;

    ngOnInit(): void {
        const request: TerminalConnectionRequest = {
            shellType: this.type(),
            cols: this.initialCols,
            rows: this.initialRows
        };

        this.devicesService.getTerminalURL(this.id(), request)
            .pipe(take(1))
            .subscribe(res => {
                this.wsUrl = res.data;
                this.initializeTerminal();
                this.connectToWebSocket();
            });
    }

    private initializeTerminal(): void {

        this.terminal = new Terminal({
            cursorBlink: true,
            fontSize: 14,
            theme: { background: this.backgroundColor() },
            scrollback: 9999999,
        });

        this.fitAddon = new FitAddon();
        this.terminal.loadAddon(this.fitAddon);
        this.terminal.open(this.terminalContainer().nativeElement);

        this.resizeTerminal();
        this.initialCols = this.terminal.cols;
        this.initialRows = this.terminal.rows;

        this.terminal.writeln('');
        this.terminal.writeln('Connecting...');

        this.terminal.onData(data => {
            if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                this.socket.send(data);
            }
        });

        let resizeTimeout: ReturnType<typeof setTimeout> | undefined;
        this.terminal.onResize(({ cols, rows }) => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                if (this.socket.readyState === WebSocket.OPEN) {
                    this.socket.send(JSON.stringify({ type: 'resize', cols, rows }));
                }
            }, 100);
        });

    }

    @HostListener('window:resize')
    private resizeTerminal(): void {
        this.fitAddon.fit();
    }

    private connectToWebSocket(): void {
        this.socket = new WebSocket(this.wsUrl);

        this.socket.onopen = () => {
            this.socket.send(JSON.stringify({ type: 'resize', cols: this.terminal.cols, rows: this.terminal.rows }));
            this.terminal.writeln('\r\nWaiting for remote side...');
        };

        this.socket.onmessage = event => {
            this.terminal.write(event.data);
        };

        this.socket.onclose = () => {
            this.terminal.writeln('\r\n[Connection closed]');
        };

        this.socket.onerror = () => {
            this.terminal.writeln('\r\n[Connection error]');
        };
    }

    ngOnDestroy(): void {
        if (this.socket) {
            this.socket.close();
        }
    }
}
