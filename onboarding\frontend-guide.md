# Frontend Development Guide

## Table of Contents
- [Architecture Overview](#architecture-overview)
- [Project Structure](#project-structure)
- [Component Architecture](#component-architecture)
- [Routing & Navigation](#routing--navigation)
- [State Management](#state-management)
- [Services & HTTP](#services--http)
- [Guards & Interceptors](#guards--interceptors)
- [UI Components & Styling](#ui-components--styling)
- [Build Configuration](#build-configuration)

## Architecture Overview

The frontend is built with Angular 20 using modern patterns and standalone components:

```
┌─────────────────────────────────────────────────────────────┐
│                    App Component                            │
├─────────────────────────────────────────────────────────────┤
│                Authenticated Layout                        │
├─────────────────────────────────────────────────────────────┤
│    Header    │              Main Content                   │
│   Sidebar    │         (Feature Modules)                  │
│   Footer     │                                            │
├─────────────────────────────────────────────────────────────┤
│              Shared Services & Components                  │
└─────────────────────────────────────────────────────────────┘
```

### Key Architectural Patterns
- **Standalone Components**: No NgModules, using standalone components
- **Lazy Loading**: Feature modules loaded on demand
- **Signal-based State**: NgRx Signals for reactive state management
- **Dependency Injection**: Modern inject() function usage
- **OnPush Change Detection**: Performance optimization

## Project Structure

```
src/app/
├── core/                     # Core application components
│   ├── components/           # Layout components
│   └── services/             # Core services
├── shared/                   # Shared utilities
│   ├── components/           # Reusable UI components
│   ├── services/             # Shared services
│   ├── guards/               # Route guards
│   ├── interceptors/         # HTTP interceptors
│   ├── models/               # TypeScript interfaces
│   └── utils/                # Utility functions
├── modules/                  # Feature modules
│   ├── home/                 # Dashboard module
│   ├── device-management/    # Device management
│   ├── cloud-infrastructure/ # Cloud infrastructure
│   └── organizations/        # Organization management
├── app.component.ts          # Root component
├── app.config.ts             # Application configuration
└── app.routes.ts             # Route configuration
```

## Component Architecture

### Standalone Component Pattern
```typescript
@Component({
    selector: 'app-home',
    imports: [NotificationSummaryComponent],
    templateUrl: './home.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class HomeComponent {
    private readonly homeService = inject(HomeService);
    
    protected readonly homeScreenData = toSignal(
        this.homeService.getHomeScreenData().pipe(map(res => res.data))
    );
}
```

### Component Conventions
- **OnPush Change Detection**: All components use OnPush strategy
- **Signal-based**: Use signals for reactive data
- **Dependency Injection**: Use inject() function instead of constructor injection
- **Template Syntax**: Use new Angular control flow (@if, @for)

### Layout Components
```typescript
// Authenticated Layout Structure
@Component({
    selector: 'app-authenticated-layout',
    template: `
        @if ((userContextService.currentUser$ | async); as userContext) {
            <div class="wrapper">
                <div class="topnavbar-wrapper">
                    <app-header [userContext]="userContext" />
                </div>
                <div class="main-section-wrapper">
                    @if (userContext.isRegistered && userContext.isApproved) {
                        <div class="aside-container">
                            <app-sidebar [userContext]="userContext" />
                        </div>
                    }
                    <section class="section-container">
                        <div class="content-wrapper">
                            <router-outlet />
                        </div>
                    </section>
                </div>
            </div>
        }
    `
})
```

## Routing & Navigation

### Route Configuration
```typescript
export const routes: Routes = [
    {
        path: '',
        loadComponent: () => import('@app/core/components/authenticated-layout/authenticated-layout.component')
            .then(m => m.AuthenticatedLayoutComponent),
        canActivate: [AuthGuard],
        children: [
            {
                path: 'home',
                loadChildren: () => import('./modules/home/<USER>').then(m => m.routes),
                canActivate: [RegisteredGuard, ApprovedGuard]
            },
            {
                path: 'device-management',
                loadChildren: () => import('./modules/device-management/device-management.routes').then(m => m.routes),
                canActivate: [AllowIfMenuItemIsPresentGuard]
            }
        ]
    }
];
```

### Lazy Loading Pattern
- **Feature Modules**: Each major feature is a separate lazy-loaded module
- **Route-based**: Modules loaded when routes are accessed
- **Code Splitting**: Automatic code splitting for better performance

### Route Guards
```typescript
@Injectable({ providedIn: 'root' })
export class AuthGuard {
    private readonly userService = inject(UserContextService);
    private readonly authService = inject(AuthService);

    canActivate(): boolean {
        const currentUser = this.userService.currentUser;
        if (!currentUser) {
            this.authService.redirectToSSOLogin(true);
            return false;
        }
        return true;
    }
}
```

## State Management

### NgRx Signals Store
```typescript
export const ZoneDomainAccountStore = signalStore(
    withState(initialState),
    withMethods((
        store,
        cloudInfraSessionService = inject(CloudInfrastructureSessionService),
        userContextService = inject(UserContextService),
        domainService = inject(CloudInfraDomainService)
    ) => ({
        loadSubDomainsByDomainId: rxMethod<string>(pipe(
            switchMap(domainId => domainService.getDomainChildrenList(domainId)
                .pipe(tapResponse({
                    next: res => {
                        const updatedDomains = updateSubDomainsByDomainId(domainId, store.domains(), res);
                        patchState(store, state => ({
                            ...state,
                            loadingId: null,
                            domains: [...updatedDomains]
                        }));
                    },
                    error: () => EMPTY
                }))
            )
        ))
    })),
    withHooks({
        onInit: store => {
            store.loadFolders();
        }
    })
);
```

### State Management Patterns
- **Signal Stores**: Feature-specific state management
- **Reactive Methods**: rxMethod for async operations
- **Immutable Updates**: Use patchState for state updates
- **Error Handling**: Consistent error handling patterns

## Services & HTTP

### Service Pattern
```typescript
@Injectable({ providedIn: 'root' })
export class ApiService {
    private readonly httpClient = inject(HttpClient);
    private readonly loadingService = inject(LoadingService);
    private readonly notificationService = inject(NotificationService);

    public get<TResult>(endpoint: string, params: any = null, showLoader = true): Observable<TResult> {
        let requestId: string;
        if (showLoader) {
            requestId = this.startRequest();
        }
        
        return this.httpClient.get<TResult>(environment.server + endpoint, {
            params: this.queryStringBuilder(params),
            headers: { 'Content-Type': 'application/json' }
        }).pipe(
            finalize(() => {
                if (showLoader) {
                    this.loadingService.endRequest(requestId);
                }
            }),
            catchError(error => this.handleError(error))
        );
    }
}
```

### HTTP Communication
- **Base API Service**: Centralized HTTP operations
- **Loading Management**: Automatic loading indicators
- **Error Handling**: Consistent error handling
- **Type Safety**: Strong typing for API responses

## Guards & Interceptors

### HTTP Interceptor
```typescript
@Injectable()
export class ApiRequestInterceptor implements HttpInterceptor {
    private readonly authService = inject(AuthService);
    private readonly loadingService = inject(LoadingService);
    private readonly notificationService = inject(NotificationService);

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        // Add timezone header
        request = request.clone({
            headers: request.headers.set('X-IPP-UI-Client-TimeZone', 
                Intl.DateTimeFormat().resolvedOptions().timeZone)
        });

        return next.handle(request).pipe(
            catchError(err => this.handleError(err))
        );
    }

    private handleError(error: HttpErrorResponse): Observable<never> {
        this.loadingService.endAllRequests();
        
        switch (error.status) {
            case 401:
                this.authService.redirectToSSOLoginWithPrompt(true);
                break;
            case 403:
                this.notificationService.notify('Access denied', NotificationType.Error);
                break;
        }
        
        return throwError(() => error);
    }
}
```

### Guard Types
- **AuthGuard**: Ensures user is authenticated
- **RegisteredGuard**: Checks if user is registered
- **ApprovedGuard**: Verifies user approval status
- **AllowIfMenuItemIsPresentGuard**: Permission-based access

## UI Components & Styling

### Component Library
The application uses a combination of:
- **Bootstrap 5.3**: Base UI framework
- **Custom Components**: Application-specific components
- **Third-party Libraries**: Specialized components

### Key UI Libraries
```typescript
// Package.json dependencies for UI
"@ng-bootstrap/ng-bootstrap": "^19.0.1",     // Bootstrap components
"@ng-select/ng-select": "^20.1.2",          // Select dropdowns
"@swimlane/ngx-datatable": "^22.0.0",       // Data tables
"highcharts": "11.4.7",                     // Charts and graphs
"ngx-quill": "^28.0.1",                     // Rich text editor
"@xterm/xterm": "^5.5.0"                    // Terminal component
```

### Styling Architecture
```scss
// SCSS structure
src/styles/
├── app/
│   ├── _variables.scss      # Custom variables
│   ├── _mixins.scss         # Utility mixins
│   └── _components.scss     # Component styles
├── styles.scss              # Global styles
└── themes/                  # Theme configurations
```

### Component Styling Patterns
```typescript
@Component({
    selector: 'app-example',
    styleUrls: ['./example.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ExampleComponent {
    // Component implementation
}
```

## Build Configuration

### Angular Configuration
```json
// angular.json key configurations
{
    "build": {
        "builder": "@angular/build:application",
        "options": {
            "outputPath": { "base": "dist", "browser": "" },
            "index": "src/index.html",
            "browser": "src/main.ts",
            "polyfills": ["zone.js", "@angular/localize/init"],
            "tsConfig": "tsconfig.app.json",
            "inlineStyleLanguage": "scss",
            "assets": ["src/favicon.ico", "src/assets"],
            "styles": [
                "src/styles.scss",
                "./node_modules/quill/dist/quill.core.css",
                "./node_modules/@xterm/xterm/css/xterm.css"
            ],
            "allowedCommonJsDependencies": [
                "highcharts", "@xterm/addon-fit", "@xterm/xterm"
            ]
        }
    }
}
```

### Environment Configuration
```typescript
// environment.ts
export const environment: EnvironmentModel = {
    production: false,
    server: '/api/'
};

// environment.prod.ts
export const environment: EnvironmentModel = {
    production: true,
    server: '/api/'
};
```

### Build Scripts
```json
// package.json scripts
{
    "scripts": {
        "start": "ng serve --ssl --ssl-key .\\certs\\localhost.key --ssl-cert .\\certs\\localhost.crt",
        "build": "npm run lint && npm run test-ci && ng build",
        "build-prod": "npm run lint && npm run test-ci && ng build --configuration=production",
        "test": "ng test",
        "test-ci": "ng test --configuration=ci",
        "lint": "ng lint"
    }
}
```

### Development vs Production
- **Development**: Source maps, verbose logging, hot reload
- **Production**: Minification, tree shaking, optimized bundles
- **SSL**: HTTPS support for local development
- **Proxy**: API proxy configuration for development

## Best Practices

### Code Organization
- **Feature Modules**: Organize by business domain
- **Shared Components**: Reusable UI components
- **Barrel Exports**: Use index.ts for clean imports
- **Path Mapping**: Use @app alias for imports

### Performance Optimization
- **OnPush Strategy**: Minimize change detection cycles
- **Lazy Loading**: Load features on demand
- **TrackBy Functions**: Optimize *ngFor performance
- **Signal-based**: Use signals for reactive programming

### Development Guidelines
- **TypeScript Strict Mode**: Enable strict type checking
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting
- **Testing**: Unit tests with Jasmine/Karma
