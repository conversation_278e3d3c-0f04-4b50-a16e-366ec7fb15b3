import { ChangeDetectionStrategy, Component, inject, Input, signal } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-reset-password',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgbPopover],
    templateUrl: './reset-password.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ResetPasswordComponent {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) virtualMachineId: string;

    protected readonly isSubmitting = signal<boolean>(false);

    protected cancel() {
        this.activeModal.close();
    }

    protected resetPassword() {
        this.isSubmitting.set(true);

        this.vmManagementService.resetVirtualMachinePassword(this.virtualMachineId)
            .subscribe(jobId => {
                this.isSubmitting.set(false);
                if (jobId) {
                    this.activeModal.close(jobId);
                }
            });
    }

}
