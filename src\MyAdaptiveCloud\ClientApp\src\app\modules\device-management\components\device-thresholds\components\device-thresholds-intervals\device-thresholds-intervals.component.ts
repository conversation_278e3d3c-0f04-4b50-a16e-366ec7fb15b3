import { Component, DestroyRef, inject, Input, OnInit, output, input } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, filter } from 'rxjs';
import { DeviceThresholdIntervalsForm } from '../../forms/device-threshold-intervals.form';
import { DeviceThresholdInterval } from '../../models/device-threshold-interval';
import { DeviceThresholdIntervals } from '../../models/device-threshold-intervals';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-device-thresholds-intervals',
    imports: [ReactiveFormsModule],
    templateUrl: './device-thresholds-intervals.component.html'
})
export class DeviceThresholdsIntervalsComponent implements OnInit {

    private readonly destroyRef = inject(DestroyRef);

    // TODO: Skipped for migration because:
    //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
    //  and migrating would break narrowing currently.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() form: FormGroup<DeviceThresholdIntervalsForm>;
    readonly intervals = input<DeviceThresholdInterval[]>(undefined);
    readonly useIntervalsForMetrics = input<boolean>(undefined);

    readonly thresholdIntervalChanged = output<DeviceThresholdIntervals>();

    ngOnInit() {
        this.form.valueChanges.pipe(
            filter(() => !this.form.pristine && this.form.touched && this.form.valid),
            debounceTime(500),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(value => {
            this.thresholdIntervalChanged.emit(value as DeviceThresholdIntervals);
        });
    }
}
