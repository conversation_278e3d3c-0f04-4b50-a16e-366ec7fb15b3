@if (monthlyScheduleForm) {
<form class="form-horizontal" [formGroup]="monthlyScheduleForm" novalidate>
  @let monthsControl = monthlyScheduleForm.controls.months;
  <div class="mb-3 row">
    <div class="col-xl-12">
      <label class="form-label is-invalid">Months</label>
      <ng-select placeholder="Select Months" [items]="months" [multiple]="true" [closeOnSelect]="false"
        [searchable]="true" bindLabel="name" bindValue="value" formControlName="months"
        [class]="{'is-invalid': monthsControl.invalid && monthsControl.touched}">
        <ng-template ng-header-tmp>
          <div>
            <button class="btn btn-link" (click)="onSelectAll('months', 'months')">
              Select All
            </button>
            <button class="btn btn-link" (click)="onClearAll('months')">Clear All</button>
          </div>
        </ng-template>
      </ng-select>
      <!-- TODO: Check validation behavior -->
      @if (monthsControl.invalid && monthsControl.touched) {
      <div class="invalid-feedback">Months are required</div>
      }
    </div>
  </div>
  <!-- Days settings start -->
  <div class="mb-3 row">
    <div class="col-2 mt-2">
      <div class=" form-check">
        <input class="form-check-input" type="radio" formControlName="selectByDaysOrWeeksRadios"
          [value]="scheduleTypeMonthsTypeEnum.Days" id="monthsDailyId" autocomplete="off">
        <label class="form-check-label">
          <span class="mleft-5">Days:</span>
        </label>
      </div>
    </div>
    <div class="col-10" formGroupName="monthDaysGroup">
      <ng-select placeholder="Select Days" [items]="monthDays" [multiple]="true" [closeOnSelect]="false"
        [searchable]="true" bindLabel="name" bindValue="value" formControlName="monthDays" [class]="{'is-invalid':
                monthlyScheduleForm.controls.monthDaysGroup.controls.monthDays.invalid
                &&
                monthlyScheduleForm.controls.monthDaysGroup.controls.monthDays.touched
                &&
                monthlyScheduleForm.controls.selectByDaysOrWeeksRadios.value === scheduleTypeMonthsTypeEnum.Days}">
        <ng-template ng-header-tmp>
          <div>
            <button class="btn btn-link" (click)="onSelectAll('monthDaysGroup.monthDays', 'monthDays')">
              Select All
            </button>
            <button class="btn btn-link" (click)="onClearAll('monthDaysGroup.monthDays')">
              Clear All
            </button>
          </div>
        </ng-template>
      </ng-select>
    </div>
  </div>
  <!-- Days settings end -->
  <!-- On settings start -->
  <div class="mb-3 row">
    <div class="col-2 mt-2">
      <div class="form-check">
        <input class="form-check-input" type="radio" formControlName="selectByDaysOrWeeksRadios"
          [value]="scheduleTypeMonthsTypeEnum.On" id="monthsOnId" autocomplete="off">
        <label class="form-check-label">
          <span class="mleft-5">On:</span>
        </label>
      </div>
    </div>
    <div class="col-10" formGroupName="weeksOnMonthGroup">
      <div class="mb-3 row">
        <div class="col-6">
          <ng-select placeholder="Select Week Number" [items]="monthWeeks" [multiple]="true" [closeOnSelect]="false"
            [searchable]="true" bindLabel="name" bindValue="value" formControlName="monthOnWeekNumber">
            <ng-template ng-header-tmp>
              <div>
                <button class="btn btn-link" (click)="onSelectAll('weeksOnMonthGroup.monthOnWeekNumber',
                                'monthWeeks')">Select All</button>
                <button class="btn btn-link" (click)="onClearAll('weeksOnMonthGroup.monthOnWeekNumber')">Clear
                  All</button>
              </div>
            </ng-template>
          </ng-select>
        </div>
        <div class="col-6">
          <ng-select placeholder="Select Day Of Week" [items]="weekDays" [multiple]="true" [closeOnSelect]="false"
            [searchable]="true" bindLabel="name" bindValue="value" formControlName="monthOnWeek">
            <ng-template ng-header-tmp>
              <div>
                <button class="btn btn-link" (click)="onSelectAll('weeksOnMonthGroup.monthOnWeek', 'weekDays')">Select
                  All</button>
                <button class="btn btn-link" (click)="onClearAll('weeksOnMonthGroup.monthOnWeek')">Clear All</button>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
    </div>
  </div>
  <!-- On settings end -->
</form>
}
