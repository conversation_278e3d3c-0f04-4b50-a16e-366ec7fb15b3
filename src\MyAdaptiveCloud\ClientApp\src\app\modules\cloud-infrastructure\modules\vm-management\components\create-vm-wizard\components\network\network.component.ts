import { CDK_DRAG_CONFIG, CdkDrag, CdkDragDrop, CdkDropList, DragDropConfig, moveItemInArray } from '@angular/cdk/drag-drop';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, Injector, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgSelectComponent } from '@ng-select/ng-select';
import { TableColumn } from '@swimlane/ngx-datatable';
import { map, take } from 'rxjs';
import { VmNetwork } from '../../../../models/vm-network.model';
import { VmManagementPermissionService } from '../../../../services/vm-management-permission.service';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { macAddressValidator, vmIpCidrValidator } from '../../forms/validators/create-vm-wizard.validators';
import { CreateVmWizardConstants } from '../../models/create-vm-wizard.constants';
import { NetworkSelectForm } from '../../models/network-select.form';
import { CreateVmCreateNetworkComponent } from './create-network/create-vm-create-network.component';

const DragConfig: DragDropConfig = {
    dragStartThreshold: 0,
    pointerDirectionChangeThreshold: 5,
    zIndex: 10000
};

@Component({
    selector: 'app-create-vm-wizard-network',
    imports: [ReactiveFormsModule, CdkDropList, CdkDrag, NgSelectComponent],
    templateUrl: './network.component.html',
    styleUrl: '../../create-vm-wizard-common.scss',
    providers: [{ provide: CDK_DRAG_CONFIG, useValue: DragConfig }],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmWizardNetworkComponent implements OnInit {

    public readonly store = inject(CreateVMWizardStore);
    private readonly modalService = inject(ModalService);
    private readonly injector = inject(Injector);
    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);
    private readonly notificationService = inject(NotificationService);
    protected readonly vmManagementPermissionService = inject(VmManagementPermissionService);

    protected readonly label = CreateVmWizardConstants.NetworkLabel;

    protected readonly typeHelpText = `Isolated: Standard network type usually with native routing capabilities.
Layer-2: Layer - 2 only network with no native routing capabilities.Routing may be provided outside of Cloud Infrastructure`;

    protected readonly ipHelpText = `Network: X.X.X.X/XX
If the Network provides DHCP, this will create a DHCP reservation.
If left blank(default ), then no reservation will be created and DHCP(if available) will assign an IP address.`;

    protected readonly macHelpText = `MAC address format: XX:XX:XX:XX:XX:XX
If left blank (default), MAC will be auto-generated.`;

    private readonly tableColumns: TableColumn[] = [
        {
            name: 'NIC',
            cellClass: 'nic-cell col-1',
        },
        {
            name: 'Network',
            cellClass: 'network-cell col-3',
        },
        {
            name: 'Type',
            cellClass: 'type-cell col',
            prop: `Isolated: Standard network type usually with native routing capabilities.
Layer-2: Layer-2 only network with no native routing capabilities. Routing may be provided outside of Cloud Infrastructure `
        },
        {
            name: 'VPC',
            cellClass: 'vpc-cell col',
        },
        {
            name: 'IP Address',
            cellClass: 'ip-cell col',
        },
        {
            name: 'MAC Address',
            cellClass: 'mac-cell col',
        },
        {
            name: '',
            cellClass: 'actions-cell col',
        }
    ];

    protected readonly selectedNetwork = signal<VmNetwork>(null);
    protected readonly availableNetworks = computed(() => this.store.networkStep.networks().filter(network => !this.store.networkStep.selectedNetworks().map(n => n.id)
        .includes(network.id))
        .sort((a, b) => a.name.localeCompare(b.name)));

    protected readonly form = this.formBuilder.group<NetworkSelectForm>({
        networkSelectedId: this.formBuilder.control<string | null>(null, Validators.required),
        ipAddress: this.formBuilder.control<string | null>(null),
        macAddress: this.formBuilder.control<string | null>(null, { validators: macAddressValidator() })
    }, { validators: vmIpCidrValidator(this.store.networkStep.networks()) });

    ngOnInit(): void {
        this.form.controls.networkSelectedId.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(networkId => {
                this.selectedNetwork.set(this.availableNetworks().find(a => a.id === networkId));
            });
    }

    protected drop(event: CdkDragDrop<string[]>) {
        const movedItems = this.store.networkStep.selectedNetworks();
        const updatedItems = [...movedItems];
        moveItemInArray(updatedItems, event.previousIndex, event.currentIndex);
        this.store.setSelectedNetworks(updatedItems);
    }

    protected addNetwork(): void {
        const selectedNetwork = this.availableNetworks().find(a => a.id === this.form.value.networkSelectedId);

        if (selectedNetwork) {
            // Create a new copy of the selected network with updated properties
            const updatedNetwork: VmNetwork = {
                ...selectedNetwork,
                ipaddress: this.form.value.ipAddress,
                macaddress: this.form.value.macAddress,
            };

            // Add the updated network to the store
            this.store.addSelectedNetwork(updatedNetwork);

            // Reset the form and clear the selected network
            this.form.reset();
            this.selectedNetwork.set(null);
        }
    }

    protected removeSelectedNetwork(networkId: string): void {
        this.store.removeSelectedNetwork(networkId);
    }

    protected openAddNetworkModal() {
        if (this.vmManagementPermissionService.canCreateNetwork()) {
            this.modalService.openModalComponent(CreateVmCreateNetworkComponent, { size: 'lg', injector: this.injector })
                .closed.pipe(
                    take(1),
                    map(network => (network as VmNetwork))
                ).subscribe(() => {
                    this.notificationService.notify('Network created successfully.');
                    this.form.setValidators(vmIpCidrValidator(this.store.networkStep.networks()));
                    this.form.updateValueAndValidity({ emitEvent: false });
                });
        }
    }

}
