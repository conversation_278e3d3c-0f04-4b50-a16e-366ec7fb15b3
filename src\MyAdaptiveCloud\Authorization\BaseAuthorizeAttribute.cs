using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Core.Permissions;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    /// Specifies what minimum Role is required within the target Organization to access this endpoint.
    /// The target Organization is determined via organizationId or parentOrganizationId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = true)]
    public class BaseAuthorizeAttribute : TypeFilterAttribute
    {
        protected string _name;
        protected int _distance;
        protected Perms[] _perms;

        public Perms[] Perms
        {
            get { return _perms; }
            set
            {
                _perms = value;
                Arguments = new object[] { _perms, _distance, _name };
            }
        }

        public string Name
        {
            get { return _name; }
            set
            {
                _name = value;
                Arguments = new object[] { _perms, _distance, _name };
            }
        }

        /// <summary>
        /// The minimum distance up the organization hierarchy that the role must be in order to qualify as permissible. This defaults to 0 (zero).
        /// An example would be Roles.Admin with Distance = 1, designating that the user must be an Admin, and the Organization cannot be the
        /// organization that the user's role is assigned to, but rather a Suborganization of it.
        /// </summary>
        public int Distance
        {
            get { return _distance; }
            set
            {
                _distance = value;
                Arguments = new object[] { _perms, _distance, _name };
            }
        }

        public BaseAuthorizeAttribute(Type FilterType) : base(FilterType)
        {
            _distance = 0;
            _perms = null;
            _name = null;
            Arguments = new object[] { _perms, _distance, _name };
        }

        public BaseAuthorizeAttribute(Type FilterType, params Perms[] perms) : base(FilterType)
        {
            _perms = perms;
            Arguments = new object[] { _perms, _distance, _name };
        }
    }
}