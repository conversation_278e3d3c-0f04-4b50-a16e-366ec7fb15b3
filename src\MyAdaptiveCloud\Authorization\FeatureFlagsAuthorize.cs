using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class FeatureFlagsAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IFeatureFlagService _featureFlagService;
        private readonly string[] _featureFlags;

        public FeatureFlagsAuthorizeFilter(IFeatureFlagService featureFlagService, params string[] featureFlags)
        {
            _featureFlagService = featureFlagService;
            _featureFlags = featureFlags;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var featureFlagsEnabled = false;

            /// If at least one feature flag in the argument list is enabled, pass authorization.
            foreach (var featureFlag in _featureFlags)
            {
                var organizationId = AuthorizeFilterHelpers.GetOrganizationId(context) ?? Constants.RootOrganizationId;

                if (await _featureFlagService.IsFeatureEnabled(featureFlag, organizationId))
                {
                    featureFlagsEnabled = true;
                    break;
                }
            }

            if (!featureFlagsEnabled)
            {
                context.Result = new ForbidResult();
            }
        }
    }

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class FeatureFlagsAuthorizeAttribute : TypeFilterAttribute
    {
        public FeatureFlagsAuthorizeAttribute(params string[] featureFlags)
            : base(typeof(FeatureFlagsAuthorizeFilter))
        {
            Arguments = [featureFlags];
            // This allow execute after AthorizeFilter to get the organizationId
            // by default the order = 0 from controller to methods
            Order = 1;
        }
    }
}