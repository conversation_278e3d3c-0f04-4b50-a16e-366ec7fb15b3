<div class="card-body">
    <div class="content-sub-heading d-flex justify-content-between">
        <div class="search-bar">
            <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)"
                [dataItemName]="'searchTerm'" />
        </div>
        <div class="action-buttons">
            @if (networkingPermissionService.canCreateRemoteVpnGateway()) {
                <div class="d-inline"
                    [title]="!domainAccountTreeStore.getAccount() ? 'Select an account to create a Remote VPN Gateway' : ''">
                    <button class="btn btn-primary" (click)="openCreateRemoteVpnGatewayModal()"
                        [disabled]="!domainAccountTreeStore.getAccount()" data-testid="add-remote-vpn-gateway-button">
                        Create Remote VPN Gateway
                    </button>
                </div>
            }
        </div>
    </div>

    <div class="card card-default">
        <div class="card-body">
            <ngx-datatable #table class='table bootstrap no-detail-row' (sort)="onSorting($event)"
                (page)="onPageChanged($event)" />
        </div>
    </div>

    <ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
        <span (click)="sort()" class="clickable">
            {{ column.name }}
            <span
                [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
            </span>
        </span>
    </ng-template>

    <ng-template #actionsTemplate let-row="row">
        @if (toItem(row); as row) {
            @if (networkingPermissionService.canDeleteRemoteVpnGateway()) {
                <app-table-action [icon]="'far fa-trash-can'" [title]="'Delete Remote VPN Gateway'"
                    (clickHandler)="deleteRemoteVpnGateway(row.id)" />
            }
        }
    </ng-template>

</div>
