import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { AlertThresholdLevelEnum } from '../../models/alert-rule-threshold-level.model';
import { DeviceAlerts } from '../../models/device-alerts';
import { AlertedDevicesDetailsComponent } from './alerted-devices-details.component';

describe('AlertedDevicesDetailsComponent', () => {
    let component: AlertedDevicesDetailsComponent;
    let fixture: ComponentFixture<AlertedDevicesDetailsComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    const data: Device<PERSON>lerts[] = [
        {
            alertType: 'Memory',
            alertStatus: 'Critical',
            deviceAlertId: 3,
            acknowledgedBy: '<PERSON>',
            acknowledgedDate: new Date('2023-10-25T11:20:00'),
            notes: 'Low Memory warning.',
            alertThresholdLevel: AlertThresholdLevelEnum.Critical,
            agentId: 25,
            selected: true,
            inScheduledDownTime: false,
            selectable: false,
            scheduleDowntimeEndDate: null,
            alertTypeId: null
        },
        {
            alertType: 'Heartbeat',
            alertStatus: 'Warning',
            deviceAlertId: 1,
            acknowledgedBy: 'John Doe',
            acknowledgedDate: new Date('2023-10-15T09:30:00'),
            notes: 'Device temperature is too high.',
            alertThresholdLevel: AlertThresholdLevelEnum.Warning,
            agentId: 25,
            selected: true,
            inScheduledDownTime: false,
            selectable: false,
            scheduleDowntimeEndDate: null,
            alertTypeId: null
        },
        {
            alertType: 'CPU',
            alertStatus: 'Error',
            deviceAlertId: 2,
            acknowledgedBy: 'Jane Smith',
            acknowledgedDate: new Date('2023-10-20T14:45:00'),
            notes: 'Connection issue resolved.',
            alertThresholdLevel: AlertThresholdLevelEnum.Error,
            agentId: 25,
            selected: true,
            inScheduledDownTime: false,
            selectable: false,
            scheduleDowntimeEndDate: null,
            alertTypeId: null
        }];

    const mockOrgId = 0;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(UserContextService)
            ],
            imports: [
                AlertedDevicesDetailsComponent
            ]
        })
            .compileComponents();

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: mockOrgId
        } as UserContext;

        fixture = TestBed.createComponent(AlertedDevicesDetailsComponent);
        component = fixture.componentInstance;
        fixture.componentRef.setInput('agentId', 25);
        fixture.componentRef.setInput('agentSelected', true);
        fixture.componentRef.setInput('activeAlerts', data);

        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should process device alerts', () => {
        const mockResponse: ApiDatasetResult<DeviceAlerts[]> = {
            data,
            totalCount: 1
        };

        const processedData = component['processDevicesAlerts'](mockResponse.data);

        const processedAlertCritical = processedData[0];
        expect(processedAlertCritical.alertThresholdLevelClass).toBeDefined();
        expect(processedAlertCritical.alertThresholdLevelClass).toBe(component.acknowledgedCriticalClass);

        const processedAlertWarning = processedData[1];
        expect(processedAlertWarning.alertThresholdLevelClass).toBeDefined();
        expect(processedAlertWarning.alertThresholdLevelClass).toBe(component.acknowledgedWarningClass);

        const processedAlertError = processedData[2];
        expect(processedAlertError.alertThresholdLevelClass).toBeDefined();
        expect(processedAlertError.alertThresholdLevelClass).toBe(component.acknowledgedErrorClass);
    });

    it('should set alert selection', () => {
        const selectedAlerts: DeviceAlerts[] = [
            {
                alertType: 'Memory',
                alertStatus: 'Critical',
                deviceAlertId: 3,
                acknowledgedBy: 'Robert Johnson',
                acknowledgedDate: new Date('2023-10-25T11:20:00'),
                notes: 'Low Memory warning.',
                alertThresholdLevel: AlertThresholdLevelEnum.Critical,
                agentId: 25,
                selected: true,
                inScheduledDownTime: false,
                selectable: false, scheduleDowntimeEndDate: null, alertTypeId: null
            },
            {
                alertType: 'Memory',
                alertStatus: 'Critical',
                deviceAlertId: 3,
                acknowledgedBy: 'Robert Johnson',
                acknowledgedDate: new Date('2023-10-25T11:20:00'),
                notes: 'Low battery warning.',
                alertThresholdLevel: AlertThresholdLevelEnum.Critical,
                agentId: 25,
                selected: true,
                inScheduledDownTime: false,
                selectable: false, scheduleDowntimeEndDate: null, alertTypeId: null
            },
        ];

        component['setAlertsSelection'](selectedAlerts);
        expect(component.selectedAlerts()).toEqual(selectedAlerts);
    });
});
