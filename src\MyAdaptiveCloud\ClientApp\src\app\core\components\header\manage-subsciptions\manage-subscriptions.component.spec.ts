import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { BtnSwitchComponent } from '@app/shared/components/btn-switch/btn-switch.component';
import { NotificationType } from '@app/shared/models/notification-type';
import { SubscriptionModel } from '@app/shared/models/profile/subscription';
import { NotificationService } from '@app/shared/services/notification.service';
import { ProfileService } from '@app/shared/services/profile.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { ManageSubscriptionsComponent } from './manage-subscriptions.component';

describe('ManageSubscriptionsComponent', () => {
    let fixture: ComponentFixture<ManageSubscriptionsComponent>;
    let mockProfileService: jasmine.SpyObj<ProfileService>;
    let mockNotificationService: jasmine.SpyObj<NotificationService>;
    let subscriptions: SubscriptionModel[];

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ProfileService),
                provideMock(NotificationService),
            ],
            imports: [
                ManageSubscriptionsComponent
            ]
        });

        subscriptions = [
            {
                serviceId: 1,
                serviceName: 'Service 1',
                isSubscribed: true,
                organizationId: 1,
                organizationName: 'Organization 1'
            },
            {
                serviceId: 2,
                serviceName: 'Service 2',
                isSubscribed: false,
                organizationId: 2,
                organizationName: 'Organization 2'
            },
            {
                serviceId: 3,
                serviceName: 'Service 3',
                isSubscribed: true,
                organizationId: 2,
                organizationName: 'Organization 2'
            }
        ];
        mockProfileService = TestBed.inject(ProfileService) as jasmine.SpyObj<ProfileService>;
        mockProfileService.getSubscriptions.and.returnValue(of({ data: subscriptions, message: '' }));
        mockProfileService.createSubscription.and.returnValue(of({ message: 'Subscription created' }));
        mockProfileService.cancelSubscription.and.returnValue(of({ message: 'Subscription cancelled' }));
        mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;

        fixture = TestBed.createComponent(ManageSubscriptionsComponent);
        fixture.detectChanges();
    });

    describe('initialization', () => {

        it('should load data on init', () => {
            expect(mockProfileService.getSubscriptions).toHaveBeenCalledTimes(1);
        });

    });

    describe('toggle', () => {

        it('should cancel subscription', () => {
            const toggle = fixture.debugElement.query(By.directive(BtnSwitchComponent));
            toggle.query(By.css('.switch')).nativeElement.click();
            fixture.detectChanges();

            expect(mockProfileService.cancelSubscription).toHaveBeenCalledOnceWith(1, subscriptions[0].serviceId);
            expect(mockNotificationService.notify).toHaveBeenCalledOnceWith('Subscription cancelled', NotificationType.Success);
        });

        it('should create subscription', () => {
            const toggle = fixture.debugElement.queryAll(By.directive(BtnSwitchComponent));
            toggle[1].query(By.css('.switch')).nativeElement.click();
            fixture.detectChanges();

            expect(mockProfileService.createSubscription).toHaveBeenCalledOnceWith(2, subscriptions[1].serviceId);
            expect(mockNotificationService.notify).toHaveBeenCalledOnceWith('Subscription created', NotificationType.Success);
        });

        it('should toggle subscription', () => {
            const toggle = fixture.debugElement.query(By.directive(BtnSwitchComponent));
            const switchElement = toggle.query(By.css('.switch')).nativeElement;
            switchElement.click();
            fixture.detectChanges();

            expect(mockProfileService.cancelSubscription).toHaveBeenCalledOnceWith(1, subscriptions[0].serviceId);
            expect(mockNotificationService.notify).toHaveBeenCalledWith('Subscription cancelled', NotificationType.Success);

            switchElement.click();
            fixture.detectChanges();

            expect(mockProfileService.createSubscription).toHaveBeenCalledOnceWith(1, subscriptions[0].serviceId);
            expect(mockNotificationService.notify).toHaveBeenCalledWith('Subscription created', NotificationType.Success);
        });

    });

});
