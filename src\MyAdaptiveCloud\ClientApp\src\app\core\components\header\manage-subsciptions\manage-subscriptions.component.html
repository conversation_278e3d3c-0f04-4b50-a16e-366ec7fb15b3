<div class="mt-3">
    <div class="fw-bolder mb-2">Subscription List</div>
    <div class="card card-default">
        <div class="card-body">
            <ngx-datatable #table class="table bootstrap no-detail-row" />
        </div>
    </div>

    <ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
        <span (click)="sort()" class="clickable">
            {{ column.name }}
            <span
                [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
            </span>
        </span>
    </ng-template>

    <ng-template #actionsTemplate let-row="row">
        @if (toItem(row); as row) {
            <app-btn-switch [isActiveInput]="row.isSubscribed" (isActiveChange)="toggleSubscription(row)" />
        }
    </ng-template>
</div>
