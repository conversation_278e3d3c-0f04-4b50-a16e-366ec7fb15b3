<div class="modal-header">
    <h4 class="modal-title">Restart Network</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <p>Are you sure you want to restart this Network?</p>
    <form [formGroup]="form" class="row g-3">
        <div class="form-check">
            <label class="form-check-label clickable">Clean Up
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Cleans up old network elements.'" triggers="hover" placement="right"
                    container="body">
                </i>
                <input type="checkbox" class="form-check-input clickable" formControlName="cleanup"
                    data-testid="cleanup-checkbox" />
            </label>
        </div>
        <div class="form-check">
            <label class="form-check-label clickable">Make Redundant
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Turns the network into a network with redundant routers.'" triggers="hover" placement="right"
                    container="body">
                </i>
                <input type="checkbox" class="form-check-input clickable" formControlName="makeRedundant"
                    data-testid="make-redundant-checkbox" />
            </label>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    <app-btn-submit [disabled]="form.invalid || isSubmitting()" [btnClasses]="'btn-primary'"
        (submitClickEvent)="restartNetwork()">Restart</app-btn-submit>
</div>
