<div class="modal-header">
  <h4 class="modal-title">Map Cloud Infrastructure Account/Domain to ConnectWise Company and Agreement</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
  <form class="form-horizontal" name="createmapping" #createmapping="ngForm" novalidate>
    <div class="mb-3 row">
      <div class="col-xl-12">
        <label class="form-label">Cloud Infrastructure account/domain</label>
        <ng-select placeholder="Select Account/Domain" [items]="accountsDomains" bindLabel="name" groupBy="acType"
          [(ngModel)]="selectedAccountDomain" name="acname" [disabled]="!!mappingId" #AcctDom="ngModel"
          [class]="{ 'is-invalid': createmapping.submitted && AcctDom.invalid }" required />
      </div>
    </div>
    <div class="mb-3 row">
      <div class="col-xl-12">
        <label class="form-label">ConnectWise company</label>
        <ng-select placeholder="Select ConnectWise Company" autocomplete="false" autocorrect="false" spellcheck="false"
          [items]="companies$ | async" bindLabel="name" [(ngModel)]="selectedCompany" name="company" [minTermLength]="3"
          typeToSearchText="Please enter 3 or more characters" [loading]="companiesLoading" [typeahead]="companyInput$"
          (change)="onChangeCompany()" (clear)="onClearCompany()" (close)="onCloseCompany()" />
      </div>
    </div>
    <div class="mb-3 row">
      <div class="col-xl-12">
        <label class="form-label">ConnectWise agreement</label>
        <ng-select placeholder="Select Agreement" [items]="agreements$ | async" bindLabel="name"
          [(ngModel)]="selectedAgreement" (change)="onChangeAgreement()" name="agreement" />
      </div>
    </div>
    <div class="mb-3 row">
      <div class="col-xl-12">
        <div>
          <label class="form-label">ConnectWise Billing Start Date: {{ (cwBillingStartDate) ?
            (cwBillingStartDate | date: 'yyyy-MM-dd':'UTC') : '' }}</label>
        </div>
      </div>
    </div>
    <div class="mb-3 row">
      <div class="col-xl-12">
        <div class="form-check">
          <input type="checkbox" class="form-check-input" value="" name="isEnabled" id="is-billable-check"
            [(ngModel)]="isEnabled" />
          <label class="form-check-label" for="is-billable-check">
            Billable
          </label>
        </div>
      </div>
    </div>
    @if (isEnabled) {
    <div class="mb-3">
      <label class="form-label">Billing Start Date</label>
      <div class="input-group">
        <input class="form-control" placeholder="yyyy-mm-dd" [(ngModel)]="ngbStartDate" [minDate]="ngbMinDate"
          [ngModelOptions]="{standalone: true}" ngbDatepicker #sd="ngbDatepicker">
        <div>
          <button class="btn btn-outline-secondary calendar" (click)="sd.toggle()" type="button">
            <i class="fas fa-calendar-alt"></i>
          </button>
        </div>
      </div>
    </div>
    }
  </form>
</div>
<div class="modal-footer">
  <button class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
  <app-btn-submit (submitClickEvent)="submitForm(createmapping)" [disabled]="createmapping.form?.invalid"
    [btnClasses]="'ms-2 btn-primary'">Save</app-btn-submit>
</div>
