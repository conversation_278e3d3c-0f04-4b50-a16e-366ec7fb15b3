﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace MyAdaptiveCloud.Api.Authorization
{
    public static class AuthorizeFilterHelpers
    {
        public static string GetEntityValue(AuthorizationFilterContext context, string name)
        {
            string entityValue = null;
            var bindingSource = context.ActionDescriptor.Parameters.Where(parm => parm.Name == name).FirstOrDefault()?.BindingInfo.BindingSource;
            if (bindingSource == BindingSource.Path)
            {
                if (context.HttpContext.Request.RouteValues.ContainsKey(name))
                {
                    entityValue = context.HttpContext.Request.RouteValues[name] as string;
                }
            }
            else if (bindingSource == BindingSource.Query)
            {
                if (context.HttpContext.Request.Query.ContainsKey(name))
                {
                    entityValue = context.HttpContext.Request.Query[name];
                }
            }
            else if (context.RouteData.Values.ContainsKey(name))
            {
                if (context.HttpContext.Request.RouteValues.ContainsKey(name))
                {
                    entityValue = context.HttpContext.Request.RouteValues[name] as string;
                }
            }
            else
            {
                throw new ArgumentException("Invalid or missing binding source", name);
            }

            return entityValue;
        }

        /// <summary>
        ///     Adds the resolved organizationId to the HttpContext, so it can be used by the FromAuth attribute
        /// </summary>
        /// <param name="context"></param>
        /// <param name="organizationId"></param>
        public static void SetOrganizationId(FilterContext context, int organizationId)
        {
            context.HttpContext.Items["organizationId"] = organizationId;
        }

        /// <summary>
        /// Gets the organizationId from the HttpContext, if present
        /// </summary>
        /// <param name="context"></param>
        /// <returns>The organizationId if present, otherwise null</returns>
        public static int? GetOrganizationId(FilterContext context)
        {
            if (context.HttpContext.Items.TryGetValue("organizationId", out var organizationIdValue) && organizationIdValue is int organizationId)
            {
                return organizationId;
            }

            return null;
        }
    }
}