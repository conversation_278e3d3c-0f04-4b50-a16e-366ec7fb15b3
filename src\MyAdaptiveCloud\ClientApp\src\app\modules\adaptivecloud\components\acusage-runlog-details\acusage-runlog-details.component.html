<div class="content-heading">
  Cloud Infrastructure - Usage Billing Run Log Details
</div>
<div class="content-sub-heading">
  <div class="action-buttons">
    <app-back [buttonText]="'Back'" />
  </div>
</div>

<div class="card card-default">
  <div class="card-body">
    <div class="table-responsive">
      <ngx-datatable class='material bootstrap expandable' [columns]="columns" columnMode="force" [headerHeight]="50"
        [rows]='details | async' [rowHeight]="'auto'">
        <ngx-datatable-column name="Time" [width]="10">
          <ng-template let-row="row" ngx-datatable-cell-template>
            {{ row.createdAt | date: 'yyyy-MM-dd HH:mm:ss':'UTC' }}
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Level" prop="levelName" [width]="10" class="abc">

          <ng-template let-value="value" ngx-datatable-cell-template>
            @if (value==='ERROR') {
              <span style="color: red;"> {{value}}</span>
            }
            @if (value==='WARNING') {
              <span style="color: yellow;"> {{value}}</span>
            }
            @if (value==='INFO') {
              <span>{{value}}</span>
            }
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Message" prop="msg" [width]="600">
          <ng-template let-value="value" ngx-datatable-cell-template>
            {{ value }}
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
</div>
