import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { EditPasswordForm } from '@app/core/forms/edit-password.form';
import { EditUserNameForm } from '@app/core/forms/edit-user-name.form';
import { PasswordPolicy } from '@app/shared/models/password-policy';
import { EditProfileRequest } from '@app/shared/models/profile/edit-profile.request';
import { ResetPasswordRequest } from '@app/shared/models/profile/reset-password.request';
import { NotificationService } from '@app/shared/services/notification.service';
import { ProfileService } from '@app/shared/services/profile.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { map, tap } from 'rxjs';

@Component({
    selector: 'app-edit-profile',
    imports: [ReactiveFormsModule, AsyncPipe],
    templateUrl: './edit-profile.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})

export class EditProfileComponent {

    private readonly formBuilder = inject(FormBuilder);
    public readonly activeModal = inject(NgbActiveModal);
    private readonly profileService = inject(ProfileService);
    private readonly notificationService = inject(NotificationService);

    public form: FormGroup<EditUserNameForm>;
    public passwordForm: FormGroup<EditPasswordForm>;
    protected isSubmitted = false;
    protected passwordPolicy$ = this.profileService.getPasswordPolicy().pipe(
        map(res => res.data),
        tap(passwordPolicy => {
            if (passwordPolicy) {
                this.passwordForm = this.formBuilder.group<EditPasswordForm>({
                    newPassword: new FormControl<string>('', [Validators.required, this.validatePasswordPolicy(passwordPolicy)]),
                    confirmNewPassword: new FormControl<string>('', [Validators.required, this.validateConfirmPassword()])
                });
            }
        })
    );

    protected currentUser$ = this.profileService.getCurrentProfile().pipe(
        map(res => res.data),
        tap(user => {
            this.form = this.formBuilder.group<EditUserNameForm>({
                firstName: new FormControl<string>(user.firstName, [Validators.required, Validators.maxLength(100)]),
                lastName: new FormControl<string>(user.lastName, [Validators.required, Validators.maxLength(100)])
            });
        })
    );

    get newPasswordControl(): AbstractControl {
        return this.passwordForm?.controls.newPassword;
    }

    get confirmNewPasswordControl(): AbstractControl {
        return this.passwordForm?.controls.confirmNewPassword;
    }

    submitForm(): void {
        this.isSubmitted = true;
        if (this.form.valid) {
            this.profileService.editProfile(this.form.getRawValue() as EditProfileRequest)
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });
        }
    }

    submitPasswordForm(): void {
        this.isSubmitted = true;

        if (this.passwordForm.valid) {
            this.profileService.resetPassword(this.passwordForm.getRawValue() as ResetPasswordRequest)
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });

        }
    }

    validatePasswordPolicy(passwordPolicy: PasswordPolicy): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value;
            if (!value) {
                return null;
            }
            const hasUpperCase = new RegExp(`[A-Z]{${passwordPolicy.upperCase}}`).test(value);
            const hasLowerCase = new RegExp(`[a-z]{${passwordPolicy.lowerCase}}`).test(value);
            const hasDigits = new RegExp(`[0-9]{${passwordPolicy.digits}}`).test(value);
            const hasLength = new RegExp(`^.{${passwordPolicy.length},}$`).test(value);
            const hasUsername = new RegExp(`^(?!${passwordPolicy.username}$)`).test(value);
            const passwordValid = hasUpperCase && hasLowerCase && hasDigits && hasLength && hasUsername;
            return !passwordValid ? { isValidPassword: true } : null;
        };
    }

    validateConfirmPassword(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value ?? null;
            if (value) {
                return null;
            }
            const newPassword = this.passwordForm?.controls.newPassword.value;
            const confirmNewPassword = this.passwordForm?.controls.confirmNewPassword.value;
            return newPassword !== confirmNewPassword ? { isValidConfirmPassword: true } : null;
        };
    }
}
