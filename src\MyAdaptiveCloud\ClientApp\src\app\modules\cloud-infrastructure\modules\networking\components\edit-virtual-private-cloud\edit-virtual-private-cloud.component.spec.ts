import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';
import { EditVirtualPrivateCloudComponent } from './edit-virtual-private-cloud.component';

describe('EditVirtualPrivateCloudComponent', () => {

    let fixture: ComponentFixture<EditVirtualPrivateCloudComponent>;
    let component: EditVirtualPrivateCloudComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [EditVirtualPrivateCloudComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VirtualPrivateCloudService)
            ]
        });

        fixture = TestBed.createComponent(EditVirtualPrivateCloudComponent);
        component = fixture.componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

});
