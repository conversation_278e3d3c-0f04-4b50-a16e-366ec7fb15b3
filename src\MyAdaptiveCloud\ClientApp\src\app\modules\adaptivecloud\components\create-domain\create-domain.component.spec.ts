import { TestBed } from '@angular/core/testing';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { CreateDomainComponent } from './create-domain.component';

describe('CreateDomainComponent', () => {

    let component: CreateDomainComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateDomainComponent],
            providers: [
                provideMock(UserContextService),
                provideMock(NotificationService)
            ]
        });

        component = TestBed.createComponent(CreateDomainComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
