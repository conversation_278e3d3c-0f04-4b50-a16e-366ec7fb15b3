import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { CreateRemoteVpnGatewayForm } from '../../forms/create-remote-vpn-gateway.form';
import { CreateRemoteVpnGatewayRequest } from '../../requests/create-remote-vpn-gateway.request';
import { RemoteVpnGatewayService } from '../../services/remote-vpn-gateway.service';

@Component({
    selector: 'app-create-remote-vpn-gateway',
    imports: [BtnSubmitComponent, ReactiveFormsModule, NgSelectModule, NgbPopover],
    templateUrl: './create-remote-vpn-gateway.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateRemoteVpnGatewayComponent implements OnInit {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly remoteVpnGatewayService = inject(RemoteVpnGatewayService);
    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);

    readonly domainId = signal<string | null>(null);
    readonly account = signal<string | null>(null);

    protected readonly encryptionMethods = ['aes128', 'aes192', 'aes256', '3des'];
    protected readonly hashes = ['sha1', 'sha256', 'sha384', 'sha512', 'md5'];
    protected readonly ikeVersions = ['ike', 'ikev1', 'ikev2'];
    protected readonly dhGroupList = [
        { name: 'Group 2', value: 'modp1024' },
        { name: 'Group 5', value: 'modp1536' },
        { name: 'Group 14', value: 'modp2048' },
        { name: 'Group 15', value: 'modp3072' },
        { name: 'Group 16', value: 'modp4096' },
        { name: 'Group 17', value: 'modp6144' },
        { name: 'Group 18', value: 'modp8192' }
    ];

    protected readonly form = this.formBuilder.group<CreateRemoteVpnGatewayForm>({
        cidrList: this.formBuilder.control<string>(null, Validators.required),
        deadPeerDetection: this.formBuilder.control<boolean>(false, Validators.required),
        espLifetime: this.formBuilder.control<number>(3600, [Validators.required, Validators.min(1), Validators.max(86400)]),
        espHash: this.formBuilder.control<string>(this.hashes[0], Validators.required),
        espEncryption: this.formBuilder.control<string>(this.encryptionMethods[0], Validators.required),
        forceEncapsulation: this.formBuilder.control<boolean>(false, Validators.required),
        gateway: this.formBuilder.control<string>(null, Validators.required),
        ikeDH: this.formBuilder.control<string>(this.dhGroupList[1].value, Validators.required),
        ikeEncryption: this.formBuilder.control<string>(this.encryptionMethods[0], Validators.required),
        ikeHash: this.formBuilder.control<string>(this.hashes[0], Validators.required),
        ikeLifetime: this.formBuilder.control<number>(86400, [Validators.required, Validators.min(1), Validators.max(86400)]),
        ikeVersion: this.formBuilder.control<string>(this.ikeVersions[0], Validators.required),
        ipSecurityPreSharedKey: this.formBuilder.control<string>(null, Validators.required),
        name: this.formBuilder.control<string>(null, Validators.required),
        perfectForwardSecrecy: this.formBuilder.control<string>(this.dhGroupList[1].value, Validators.required),
        splitConnections: this.formBuilder.control<boolean | null>(false)
    });

    ngOnInit(): void {
        this.form.controls.ikeVersion.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                if (value === this.ikeVersions[1]) {
                    this.form.controls.splitConnections.setValue(null);
                    this.form.controls.splitConnections.disable();
                } else {
                    this.form.controls.splitConnections.setValue(false);
                    this.form.controls.splitConnections.enable();
                }
                this.form.controls.splitConnections.updateValueAndValidity();
            });
    }

    protected submitForm() {
        if (this.form.valid) {
            const form = this.form.value;
            const request: CreateRemoteVpnGatewayRequest = {
                account: this.account(),
                cidrList: form.cidrList,
                domainId: this.domainId(),
                deadPeerDetection: form.deadPeerDetection,
                espLifetime: form.espLifetime,
                espEncryption: form.espEncryption,
                espHash: form.espHash,
                forceEncapsulation: form.forceEncapsulation,
                gateway: form.gateway,
                ikeDH: form.ikeDH,
                ikeEncryption: form.ikeEncryption,
                ikeHash: form.ikeHash,
                ikeLifetime: form.ikeLifetime,
                ikeVersion: form.ikeVersion,
                ipSecurityPreSharedKey: form.ipSecurityPreSharedKey,
                name: form.name,
                perfectForwardSecrecy: form.perfectForwardSecrecy,
                splitConnections: form.splitConnections
            };

            this.remoteVpnGatewayService.createRemoteVpnGateway(request)
                .subscribe(res => {
                    this.activeModal.close(res);
                });
        }
    }

}
