import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, OnInit, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { map, Observable } from 'rxjs';
import { VmStartForm } from '../../forms/vm-start.form';
import { NameIdModel } from '../../models/name-id.model';
import { MAX_BOOT_DELAY } from '../../models/vm.constants';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-start-vm',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgSelectComponent, NgbPopover, AsyncPipe],
    templateUrl: './start-vm.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class StartVmComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) virtualMachineId: string;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) zoneId: string;

    protected pods$: Observable<NameIdModel[]>;
    protected clusters$: Observable<NameIdModel[]>;
    protected hosts$: Observable<NameIdModel[]>;

    private autoSelectOption = { name: 'Auto-Select', id: '' };

    protected form = this.formBuilder.group<VmStartForm>({
        pod: this.formBuilder.control<string>(''),
        cluster: this.formBuilder.control<string>(''),
        host: this.formBuilder.control<string>(''),
        bootDelay: this.formBuilder.control<number>(0, [Validators.min(0), Validators.max(MAX_BOOT_DELAY), Validators.pattern('^[0-9]*$')])
    });

    protected readonly isSubmitting = signal<boolean>(false);

    ngOnInit(): void {
        if (this.cloudInfraPermissionService.isRootAdmin()) {
            this.pods$ = this.vmManagementService.getPodList(this.zoneId).pipe(map(pods => [this.autoSelectOption].concat(pods)));
            this.clusters$ = this.vmManagementService.getClusterList(this.zoneId).pipe(map(clusters => [this.autoSelectOption].concat(clusters)));
            this.hosts$ = this.vmManagementService.getHostList(this.zoneId, 'Routing', 'Up').pipe(map(hosts => [this.autoSelectOption].concat(hosts)));
        }
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected startVirtualMachine() {
        this.isSubmitting.set(true);
        if (this.form.valid) {
            const { pod, cluster, host, bootDelay } = this.form.value;

            this.vmManagementService.startVirtualMachine(this.virtualMachineId, pod, cluster, host, Number(bootDelay))
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
