using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Core.Permissions;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    /// Verifies that the current user's Role is authorized to access the target Organization's information
    /// </summary>
    public class RootOrgAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public RootOrgAuthorizeFilter(IUserContextService userContextService, IIdentityService identityService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                if (_perms != null && _perms.Count() > 0)
                {
                    if (!_userContextService.HasPermission(userId, Constants.RootOrganizationId, _distance, _perms))
                    {
                        context.Result = new ForbidResult();
                    }
                    else
                    {
                        AuthorizeFilterHelpers.SetOrganizationId(context, Constants.RootOrganizationId);
                    }
                }
                else
                {
                    context.Result = new ForbidResult();
                }
            }

            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Specifies what minimum Role is required within the target Organization to access this endpoint.
    /// The target Organization is determined via organizationId or parentOrganizationId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class RootOrgAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public RootOrgAuthorizeAttribute(params Perms[] perms) : base(typeof(RootOrgAuthorizeFilter), perms)
        {
            Name = "";
        }
    }
}