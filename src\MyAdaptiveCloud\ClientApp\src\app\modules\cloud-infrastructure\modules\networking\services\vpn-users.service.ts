import { inject, Injectable } from '@angular/core';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { forkJoin, map, Observable, of, switchMap } from 'rxjs';
import { NETWORKING_ENDPOINT_NAMES } from '../models/networking.constants';
import { VpnUser } from '../models/vpn-user';
import { ListVpnUser, ListVpnUsersResponse } from '../responses/list-vpn-users.response';
import { DeleteVpnUserResponse } from '../responses/delete-vpn-user.response';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CreateVpnUserResponse } from '../responses/create-vpn-user.response';

@Injectable({
    providedIn: 'root'
})
export class VpnUsersService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getVpnUsers(): Observable<VpnUser[]> {
        const pageSize = 500;

        // Fetch the first batch, which will return the total count, then fetch the rest of the records in parallel
        return this.getVpnUsersListBatch(1, pageSize).pipe(
            map(res => {
                const records = [...res?.vpnuser ?? []];
                const remainingRecords = res.count - pageSize;

                if (remainingRecords > 0) {
                    const countOfRequestBatches = Math.ceil(remainingRecords / pageSize);
                    const requests: Observable<ListVpnUser>[] = [];
                    for (let i = 2; i <= countOfRequestBatches + 1; i++) {
                        requests.push(this.getVpnUsersListBatch(i, pageSize));
                    }
                    return forkJoin(requests).pipe(map(responses => {
                        responses.forEach(response => {
                            records.push(...response.vpnuser);
                        });
                        return records;
                    }));
                }
                return of(records);

            }),
            switchMap(records => records)
        );
    }

    deleteVpnUser(userName: string, domainId: string, account: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.deleteVpnUser,
            username: userName,
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<DeleteVpnUserResponse>(params).pipe(map(response => response.deletvpnuserresponse?.jobid));
    }

    createVpnUser(userName: string, password: string, domainId: string, account: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.addVpnUser,
            username: userName,
            password,
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<CreateVpnUserResponse>(params).pipe(map(response => response.addvpnuserresponse?.jobid));
    }

    private getVpnUsersListBatch(currentPage: number, pageSize: number): Observable<ListVpnUser> {
        const params: Record<string, string> = {
            command: NETWORKING_ENDPOINT_NAMES.listVpnUsers,
            listall: 'true',
            pagesize: pageSize.toString(),
            page: currentPage.toString()
        };

        return this.cloudInfraApiService.get<ListVpnUsersResponse>(params)
            .pipe(map((response: ListVpnUsersResponse) => (response.listvpnusersresponse)));
    }

}
