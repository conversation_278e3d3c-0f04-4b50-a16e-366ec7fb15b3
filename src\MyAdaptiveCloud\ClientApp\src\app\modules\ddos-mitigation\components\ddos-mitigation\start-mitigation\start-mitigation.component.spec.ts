import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { MitigationTypeEnum } from '@app/modules/ddos-mitigation/models/mitigation-type.enum';
import { MitigationValidation } from '@app/modules/ddos-mitigation/models/mitigation-validation.model';
import { AsyncTaskResponse } from '@app/modules/ddos-mitigation/models/async-task-response.model';
import { DDoSMitigationBlackholeService } from '@app/modules/ddos-mitigation/services/ddos-mitigation-blackhole-service';
import { DDoSMitigationScrubService } from '@app/modules/ddos-mitigation/services/ddos-mitigation-scrub.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NotificationService } from '@app/shared/services/notification.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { StartMitigationComponent } from './start-mitigation.component';

describe('StartMitigationComponent', () => {

    let fixture: ComponentFixture<StartMitigationComponent>;
    let mockDDoSMitigationScrubService: jasmine.SpyObj<DDoSMitigationScrubService>;
    let mockDDoSMitigationBlackholeService: jasmine.SpyObj<DDoSMitigationBlackholeService>;
    let mockNotificationService: jasmine.SpyObj<NotificationService>;
    let mockNgbActiveModal: jasmine.SpyObj<NgbActiveModal>;

    let cidrInput: HTMLInputElement;
    let simulateInput: HTMLInputElement;
    let validateCidrButton: HTMLButtonElement;
    let submitButton: HTMLButtonElement;

    beforeEach(() => {

        TestBed.configureTestingModule({
            imports: [
                StartMitigationComponent
            ],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(DDoSMitigationScrubService),
                provideMock(DDoSMitigationBlackholeService),
                provideMock(NotificationService)
            ]
        });

        mockDDoSMitigationScrubService = TestBed.inject(DDoSMitigationScrubService) as jasmine.SpyObj<DDoSMitigationScrubService>;
        mockDDoSMitigationBlackholeService = TestBed.inject(DDoSMitigationBlackholeService) as jasmine.SpyObj<DDoSMitigationBlackholeService>;
        mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
        mockNgbActiveModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        fixture = TestBed.createComponent(StartMitigationComponent);
        fixture.detectChanges();

        cidrInput = fixture.debugElement.query(By.css('#cidr')).nativeElement as HTMLInputElement;
        simulateInput = fixture.debugElement.query(By.css('#simulate')).nativeElement as HTMLInputElement;
        validateCidrButton = fixture.debugElement.query(By.css('#validate-cidr')).nativeElement as HTMLButtonElement;
        const submitButtonElement = fixture.debugElement.query(By.directive(BtnSubmitComponent));
        submitButton = submitButtonElement.query(By.css('button')).nativeElement;
    });

    describe('Validate', () => {

        it('should validate blackhole mitigation and sent the form state when validation fails', () => {

            fixture.componentInstance.mitigationType.set(MitigationTypeEnum.Blackhole);

            const validationResponse: MitigationValidation = {
                cidr: '',
                messages: ['Invalid CIDR'],
            };
            mockDDoSMitigationBlackholeService.validate.and.returnValue(of({ data: validationResponse, message: '' }));

            cidrInput.value = 'invalid-cidr';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            validateCidrButton.click();
            fixture.detectChanges();

            const cidrValidationDiv = fixture.debugElement.query(By.css('#cidr-validation')).nativeElement as HTMLDivElement;

            expect(cidrValidationDiv.textContent).toContain('Validation failed');
            expect(mockDDoSMitigationBlackholeService.validate).toHaveBeenCalledOnceWith('invalid-cidr');
            expect(cidrInput.value).toBe('invalid-cidr');
            expect(fixture.componentInstance.validatedCidr()).toEqual(validationResponse);
            expect(fixture.componentInstance.isCidrValid()).toBeFalse();
            expect(simulateInput.disabled).toBeTrue();
            expect(submitButton.disabled).toBeTrue();

        });

        it('should validate blackhole mitigation and sent the form state when validation succeeds', () => {

            fixture.componentInstance.mitigationType.set(MitigationTypeEnum.Blackhole);

            const validationResponse: MitigationValidation = {
                cidr: '192.168.100.100/124',
                messages: ['Valid CIDR', 'CIDR is valid'],
            };
            mockDDoSMitigationBlackholeService.validate.and.returnValue(of({ data: validationResponse, message: '' }));

            cidrInput.value = 'valid-cidr';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            validateCidrButton.click();
            fixture.detectChanges();

            const cidrValidationDiv = fixture.debugElement.query(By.css('#cidr-validation')).nativeElement as HTMLDivElement;

            expect(cidrValidationDiv.textContent).toContain('Validation succeeded');
            expect(mockDDoSMitigationBlackholeService.validate).toHaveBeenCalledOnceWith('valid-cidr');
            expect(cidrInput.value).toBe(validationResponse.cidr);
            expect(fixture.componentInstance.validatedCidr()).toEqual(validationResponse);
            expect(fixture.componentInstance.isCidrValid()).toBeTrue();
            expect(simulateInput.disabled).toBeFalse();
            expect(submitButton.disabled).toBeFalse();

        });

        it('should validate scrub mitigation and sent the form state when validation fails', () => {

            fixture.componentInstance.mitigationType.set(MitigationTypeEnum.Scrub);

            const validationResponse: MitigationValidation = {
                cidr: '',
                messages: ['Invalid CIDR'],
            };
            mockDDoSMitigationScrubService.validate.and.returnValue(of({ data: validationResponse, message: '' }));

            cidrInput.value = 'invalid-cidr';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            validateCidrButton.click();
            fixture.detectChanges();

            const cidrValidationDiv = fixture.debugElement.query(By.css('#cidr-validation')).nativeElement as HTMLDivElement;

            expect(cidrValidationDiv.textContent).toContain('Validation failed');
            expect(mockDDoSMitigationScrubService.validate).toHaveBeenCalledOnceWith('invalid-cidr');
            expect(cidrInput.value).toBe('invalid-cidr');
            expect(fixture.componentInstance.validatedCidr()).toEqual(validationResponse);
            expect(fixture.componentInstance.isCidrValid()).toBeFalse();
            expect(simulateInput.disabled).toBeTrue();
            expect(submitButton.disabled).toBeTrue();

        });

        it('should validate scrub mitigation and sent the form state when validation succeeds', () => {

            fixture.componentInstance.mitigationType.set(MitigationTypeEnum.Scrub);

            const validationResponse: MitigationValidation = {
                cidr: '192.168.100.100/124',
                messages: ['Valid CIDR', 'CIDR is valid'],
            };
            mockDDoSMitigationScrubService.validate.and.returnValue(of({ data: validationResponse, message: '' }));

            cidrInput.value = 'valid-cidr';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            validateCidrButton.click();
            fixture.detectChanges();

            const cidrValidationDiv = fixture.debugElement.query(By.css('#cidr-validation')).nativeElement as HTMLDivElement;

            expect(cidrValidationDiv.textContent).toContain('Validation succeeded');
            expect(mockDDoSMitigationScrubService.validate).toHaveBeenCalledOnceWith('valid-cidr');
            expect(cidrInput.value).toBe(validationResponse.cidr);
            expect(fixture.componentInstance.validatedCidr()).toEqual(validationResponse);
            expect(fixture.componentInstance.isCidrValid()).toBeTrue();
            expect(simulateInput.disabled).toBeFalse();
            expect(submitButton.disabled).toBeFalse();

        });

    });

    describe('Submit Scrum Mitigation', () => {

        const validationResponse: MitigationValidation = {
            cidr: '192.168.100.100/124',
            messages: ['Valid CIDR', 'CIDR is valid'],
        };

        const taskResponse: AsyncTaskResponse = {
            message: 'Mitigation started',
            taskId: 'task-001',
        };

        beforeEach(() => {
            fixture.componentInstance.mitigationType.set(MitigationTypeEnum.Scrub);

            mockDDoSMitigationScrubService.validate.and.returnValue(of({ data: validationResponse, message: '' }));
            mockDDoSMitigationScrubService.start.and.returnValue(of({ data: taskResponse, message: 'Mitigation requested' }));

            cidrInput.value = 'valid-cidr';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            validateCidrButton.click();
            fixture.detectChanges();

        });

        it('should submit simulated mitigation', () => {

            submitButton.click();
            fixture.detectChanges();

            expect(mockDDoSMitigationScrubService.start).toHaveBeenCalledOnceWith(validationResponse.cidr, true);
            expect(mockNotificationService.notify).toHaveBeenCalledOnceWith('Mitigation requested');
            expect(mockNgbActiveModal.close).toHaveBeenCalledOnceWith({ taskId: taskResponse.taskId, cidr: validationResponse.cidr, message: taskResponse.message, simulate: true });

        });

        it('should submit mitigation', () => {

            simulateInput.click();
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockDDoSMitigationScrubService.start).toHaveBeenCalledOnceWith(validationResponse.cidr, false);
            expect(mockNotificationService.notify).toHaveBeenCalledOnceWith('Mitigation requested');
            expect(mockNgbActiveModal.close).toHaveBeenCalledOnceWith({ taskId: taskResponse.taskId, cidr: validationResponse.cidr, message: taskResponse.message, simulate: false });

        });

    });

    describe('Submit Blackhole Mitigation', () => {

        const validationResponse: MitigationValidation = {
            cidr: '192.168.100.100/124',
            messages: ['Valid CIDR', 'CIDR is valid'],
        };

        const taskResponse: AsyncTaskResponse = {
            message: 'Mitigation started',
            taskId: 'task-001',
        };

        beforeEach(() => {
            fixture.componentInstance.mitigationType.set(MitigationTypeEnum.Blackhole);

            mockDDoSMitigationBlackholeService.validate.and.returnValue(of({ data: validationResponse, message: '' }));
            mockDDoSMitigationBlackholeService.start.and.returnValue(of({ data: taskResponse, message: 'Mitigation requested' }));

            cidrInput.value = 'valid-cidr';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            validateCidrButton.click();
            fixture.detectChanges();

        });

        it('should submit simulated mitigation', () => {

            submitButton.click();
            fixture.detectChanges();

            expect(mockDDoSMitigationBlackholeService.start).toHaveBeenCalledOnceWith(validationResponse.cidr, true);
            expect(mockNotificationService.notify).toHaveBeenCalledOnceWith('Mitigation requested');
            expect(mockNgbActiveModal.close).toHaveBeenCalledOnceWith({ taskId: taskResponse.taskId, cidr: validationResponse.cidr, message: taskResponse.message, simulate: true });

        });

        it('should submit mitigation', () => {

            simulateInput.click();
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockDDoSMitigationBlackholeService.start).toHaveBeenCalledOnceWith(validationResponse.cidr, false);
            expect(mockNotificationService.notify).toHaveBeenCalledOnceWith('Mitigation requested');
            expect(mockNgbActiveModal.close).toHaveBeenCalledOnceWith({ taskId: taskResponse.taskId, cidr: validationResponse.cidr, message: taskResponse.message, simulate: false });

        });

    });

});
