import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { ROUTE_SEGMENTS } from '@app/modules/cloud-infrastructure/models/route-segments';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';

export const domainAccountTreeAccountSelectedGuard: CanActivateFn = () => {
    const store = inject(ZoneDomainAccountStore);
    const router = inject(Router);

    if (!store.selectedAccount()) {
        return router.createUrlTree(['/', ROUTE_SEGMENTS.MANAGEMENT, ROUTE_SEGMENTS.VM_MANAGEMENT]);
    }
    return true;
};
