import { inject, Injectable } from '@angular/core';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { forkJoin, map, Observable, of, switchMap } from 'rxjs';
import { Network } from '../../../models/network';
import { NETWORKING_ENDPOINT_NAMES } from '../models/networking.constants';
import { ListNetworks, ListNetworksResponse } from '../responses/list-network.response';
import { RestartNetworkResponse } from '../responses/restart-network.response';
import { DeleteNetworkResponse } from '../responses/delete-network.response';
import { EditNetworkResponse } from '../responses/edit-network.response';

@Injectable({
    providedIn: 'root'
})
export class NetworkingService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    /*
    * This method retrieves a list of networks for a given domain and account.
    * The domainId argument is required and it expects either the domainId in the cloud infra user context.
    * The account argument is optional and it should only be used when the context does not have its own domain.
    */
    getNetworks(domainId: string, account: string | null): Observable<Network[]> {

        const pageSize = 500;

        // Fetch the first batch, which will return the total count, then fetch the rest of the records in parallel
        return this.getNetworkListBatch(domainId, account, 1, pageSize).pipe(
            map(res => {
                const records = [...res?.network ?? []];
                const remainingRecords = res.count - pageSize;

                if (remainingRecords > 0) {
                    const countOfRequestBatches = Math.ceil(remainingRecords / pageSize);
                    const requests: Observable<ListNetworks>[] = [];
                    for (let i = 2; i <= countOfRequestBatches + 1; i++) {
                        requests.push(this.getNetworkListBatch(domainId, account, i, pageSize));
                    }
                    return forkJoin(requests).pipe(map(responses => {
                        responses.forEach(response => {
                            records.push(...response.network);
                        });
                        return records;
                    }));
                }
                return of(records);

            }),
            switchMap(records => records)
        );
    }

    restartNetwork(networkId: string, cleanup: boolean, makeRedundant: boolean): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.restartNetwork,
            id: networkId,
            cleanup: cleanup ? 'true' : 'false',
            makeredundant: makeRedundant ? 'true' : 'false'
        };

        return this.cloudInfraApiService.get<RestartNetworkResponse>(params).pipe(map(response => response.restartnetworkresponse?.jobid));
    }

    deleteNetwork(networkId: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.deleteNetwork,
            id: networkId,
        };

        return this.cloudInfraApiService.get<DeleteNetworkResponse>(params).pipe(map(response => response.deletenetworkresponse?.jobid));
    }

    editIsolatedNetwork(networkId: string, name: string, description: string, cidr: string, networkOfferingId: string, networkDomain: string | null): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.editNetwork,
            id: networkId,
            displaytext: description,
            name,
            cidr,
            networkofferingid: networkOfferingId
        };

        if (networkDomain) {
            params['networkdomain'] = networkDomain;
        }

        return this.cloudInfraApiService.get<EditNetworkResponse>(params).pipe(map(response => response.editnetworkresponse?.jobid));
    }

    editLevel2Network(networkId: string, name: string, description: string, cidr: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.editNetwork,
            id: networkId,
            displaytext: description,
            name,
            cidr,
        };

        return this.cloudInfraApiService.get<EditNetworkResponse>(params).pipe(map(response => response.editnetworkresponse?.jobid));
    }

    private getNetworkListBatch(domainId: string, account: string | null, currentPage: number, pageSize: number): Observable<ListNetworks> {
        const params: Record<string, string> = {
            command: NETWORKING_ENDPOINT_NAMES.listNetworks,
            details: 'min',
            listall: 'true',
            isrecursive: 'true',
            pagesize: pageSize.toString(),
            page: currentPage.toString()
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;

        if (account) {
            params[CloudInfraParamsEnum.ACCOUNT] = account;
        }

        return this.cloudInfraApiService.get<ListNetworksResponse>(params)
            .pipe(map((response: ListNetworksResponse) => (response.listnetworksresponse)));
    }

    getNetworkById(id: string): Observable<Network | null> {
        const params: Record<string, string> = {
            command: NETWORKING_ENDPOINT_NAMES.listNetworks,
            details: 'min',
            listall: 'true',
            isrecursive: 'false',
            id
        };

        return this.cloudInfraApiService.get<ListNetworksResponse>(params)
            .pipe(map((response: ListNetworksResponse) => {
                const networks = response.listnetworksresponse?.network;
                return networks && networks.length > 0 ? networks[0] : null;
            }));
    }

}
