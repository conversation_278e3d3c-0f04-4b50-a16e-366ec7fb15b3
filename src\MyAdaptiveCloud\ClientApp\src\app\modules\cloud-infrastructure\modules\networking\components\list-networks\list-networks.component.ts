import { Network } from './../../../../models/network';
import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { FiltersFormComponent } from '@app/shared/components/datatable/filters-form/filters-form.component';
import { PillFilterComponent } from '@app/shared/components/datatable/pill-filter/pill-filter.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, map, Observable, of, skip, switchMap, take } from 'rxjs';
import { NETWORK_LIST_CONSTANTS } from '../../models/network-list.constants';
import { NetworkListViewModel } from '../../models/network-list.view-model';
import { NetworkListFilters } from '../../requests/network-list.filter';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { NetworkingService } from '../../services/networking.service';
import { CreateNetworkComponent } from '../create-network/create-network.component';
import { EditIsolatedNetworkComponent } from '../edit-network/edit-isolated-network.component';
import { EditLevel2NetworkComponent } from '../edit-network/edit-level2-network.component';
import { ListNetworksFilterComponent } from '../list-networks-filter/list-networks-filter.component';
import { RestartNetworkComponent } from '../restart-network/restart-network.component';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { NETWORKING_ROUTE_SEGMENTS } from '../../models/route.segments';

@Component({
    selector: 'app-list-networks',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent, FiltersFormComponent, ListNetworksFilterComponent, PillFilterComponent, AsyncPipe, RouterLink],
    templateUrl: './list-networks.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListNetworksComponent extends BaseListComponent<NetworkListViewModel> implements OnInit {
    private readonly userContextService = inject(UserContextService);
    private readonly networkingService = inject(NetworkingService);
    private readonly modalService = inject(ModalService);
    private readonly columns = signal<TableColumn[]>(null);
    private readonly networkList = signal<NetworkListViewModel[]>([]);

    private readonly statusRow = viewChild.required<TemplateRef<never>>('statusRow');
    private readonly detailNetworkRow = viewChild.required<TemplateRef<never>>('detailNetworkRow');

    protected readonly networkingPermissionService = inject(NetworkingPermissionService);
    protected readonly store = inject(ZoneDomainAccountStore);

    protected readonly activateRoute = inject(ActivatedRoute);

    protected readonly NETWORKING_ROUTE_SEGMENTS = NETWORKING_ROUTE_SEGMENTS;
    private readonly selectedDomain$ = toObservable(this.store.selectedDomain);
    private readonly selectedAccount$ = toObservable(this.store.selectedAccount);

    ngOnInit(): void {
        this.filters = new NetworkListFilters(this.store.zones());
        this.pagination = this.filters.request;

        this.columns.set([
            {
                cellTemplate: this.statusRow(),
                name: 'State',
                prop: NETWORK_LIST_CONSTANTS.stateKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 150
            },
            {
                name: 'Name',
                prop: NETWORK_LIST_CONSTANTS.nameKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                canAutoResize: true,
                resizeable: false,
                cellTemplate: this.detailNetworkRow()
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: NETWORK_LIST_CONSTANTS.domainKey,
                name: 'Domain'
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: NETWORK_LIST_CONSTANTS.accountKey,
                name: 'Account',
            },
            {
                name: 'Type',
                prop: NETWORK_LIST_CONSTANTS.typeKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 120
            },
            {
                name: 'VPC',
                prop: NETWORK_LIST_CONSTANTS.vpcKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'CIDR',
                prop: NETWORK_LIST_CONSTANTS.cidrKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Zone',
                prop: NETWORK_LIST_CONSTANTS.zoneNameKey,
                headerTemplate: this.headerTemplateSortable(),
                resizeable: false,
                canAutoResize: true,
                width: 100
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 120
            }
        ]);

        // When the user context has its own domain, do not pass account so the result includes all VMs in the domain
        const accountName = this.userContextService.currentUser.cloudInfraUserContext.hasMappedDomain ? null : this.userContextService.currentUser.cloudInfraUserContext.accountName;
        this.networkingService.getNetworks(this.userContextService.currentUser.cloudInfraUserContext.domainId, accountName)
            .pipe(take(1))
            .subscribe(res => {
                this.networkList.set(this.mapNetworkListResponse(res));
                this.onDomainAccountChanged(this.userContextService.currentUser.cloudInfraUserContext.domainId, accountName);
            });

        this.selectedDomain$.pipe(
            skip(1),
            filter(domain => !!domain),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(domain => {
            this.onDomainAccountChanged(domain.id, this.store.getAccount());
        });

        this.selectedAccount$.pipe(
            skip(1),
            filter(account => !!account),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(account => {
            this.onDomainAccountChanged(this.store.getDomainId(), account.name);
        });

    }

    protected openAddNetworkModal(): void {
        if (this.networkingPermissionService.canCreateNetwork() && this.store.getAccount()) {
            // Added injector to resolve the domain account tree store in the modal component
            const modalRef = this.modalService.openModalComponent(CreateNetworkComponent, { size: 'lg' });
            const component = modalRef.componentInstance as CreateNetworkComponent;
            component.zones.set(this.store.zones());
            component.domainId.set(this.store.getDomainId());
            component.account.set(this.store.getAccount());
            modalRef.closed
                .pipe(
                    take(1),
                    filter(res => !!(res as Network)),
                    map(res => this.mapNetworkListResponse([res]))
                )
                .subscribe(res => {
                    this.networkList.update(value => [...value, ...res]);
                    super.initialize(() => this.getNetworkList$(), this.columns());
                });
        }
    }

    protected deleteNetwork(network: NetworkListViewModel): void {
        if (!network.isSystem && this.networkingPermissionService.canDeleteNetwork()) {
            this.modalService.openDeleteConfirmationDialog('Delete Network', 'Are you sure you want to delete this network?', 'delete')
                .closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                    switchMap(() => this.networkingService.deleteNetwork(network.id))
                ).subscribe();
        }
    }

    protected restartNetwork(network: NetworkListViewModel): void {
        if ((network.state === 'Implemented' || network.state === 'Setup') && this.networkingPermissionService.canRestartNetwork()) {
            const modal = this.modalService.openModalComponent(RestartNetworkComponent);
            (modal.componentInstance as RestartNetworkComponent).networkId.set(network.id);
            modal
                .closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                ).subscribe();
        }
    }

    protected editNetwork(network: NetworkListViewModel): void {
        if (this.networkingPermissionService.canEditNetwork()) {
            let modal: NgbModalRef;
            switch (network.type) {
                case 'Isolated':
                    modal = this.modalService.openModalComponent(EditIsolatedNetworkComponent, { size: 'lg' });
                    (modal.componentInstance as EditIsolatedNetworkComponent).network.set(network);
                    break;
                case 'L2':
                    modal = this.modalService.openModalComponent(EditLevel2NetworkComponent, { size: 'lg' });
                    (modal.componentInstance as EditLevel2NetworkComponent).network.set(network);
                    break;
            }
            modal.closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                )
                .subscribe();
        }
    }

    private onDomainAccountChanged(domainId: string, account: string): void {
        let columns = [...this.columns()];
        if (account) {
            columns = [...columns.filter(c => c.prop !== NETWORK_LIST_CONSTANTS.accountKey && c.prop !== NETWORK_LIST_CONSTANTS.domainKey)];
        } else if (domainId && !this.store.isRootDomainSelected()) {
            columns = [...columns.filter(c => c.prop !== NETWORK_LIST_CONSTANTS.domainKey)];
        }

        if (this.table().columns?.length) {
            this.table().columns = [...columns];
        }

        super.initialize(() => this.getNetworkList$(), columns);
    }

    private mapNetworkListResponse(networks: Network[]): NetworkListViewModel[] {
        let networkViewModelList: NetworkListViewModel[] = [];
        if (networks?.length > 0) {
            networkViewModelList = networks.map(network => {
                const viewModel: NetworkListViewModel = {
                    account: network.account?.trim() ?? '',
                    cidr: network.cidr?.trim() ?? '',
                    description: network.displaytext?.trim() ?? null,
                    domain: network.domain?.trim(),
                    domainId: network.domainid,
                    id: network.id,
                    isSystem: network.issystem,
                    name: network.name?.trim(),
                    networkDomain: network.networkdomain?.trim() ?? null,
                    networkOfferingId: network.networkofferingid,
                    state: network.state,
                    type: network.type,
                    vpc: network.vpcname ?? '',
                    zoneName: network.zonename,
                    zoneId: network.zoneid
                };
                return viewModel;
            });
        }
        return networkViewModelList;
    }

    private getNetworkList$(): Observable<ApiDatasetResult<NetworkListViewModel[]>> {
        // Apply filters, including selected domain and account, search term and zone and state filters
        const filteredNetworkList = [...this.networkList().filter(network => {
            const matchesDomain = this.store.selectedDomain() ? network.domainId === this.store.selectedDomain().id : true;
            const matchesAccount = this.store.getAccount() ? network.account === this.store.getAccount() : true;
            const matchesSearchTerm = !this.pagination.searchTerm ||
                network.name.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                network.state.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                network.type.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                network.vpc.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                network.cidr.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                network.zoneName.toLowerCase().includes(this.pagination.searchTerm.toLowerCase());

            const allZoneIds = this.store.zones()?.map(z => z.id);
            const selectedZoneIds = allZoneIds.filter(id => this.pagination[id] === true);

            const matchesZoneFilter =
                selectedZoneIds.length === 0
                    ? false
                    : selectedZoneIds.includes(network.zoneId);

            const isIsolatedSelected = this.pagination[NETWORK_LIST_CONSTANTS.isolatedKey];
            const isL2Selected = this.pagination[NETWORK_LIST_CONSTANTS.l2Key];
            const isSharedSelected = this.pagination[NETWORK_LIST_CONSTANTS.shared];

            const matchTypeFilter =
                (isIsolatedSelected && isL2Selected) ||
                (isIsolatedSelected && network.type === NETWORK_LIST_CONSTANTS.isolatedKey) ||
                (isL2Selected && network.type === NETWORK_LIST_CONSTANTS.l2Key) ||
                (isSharedSelected && network.type === NETWORK_LIST_CONSTANTS.shared);

            const isVpcSelected = this.pagination[NETWORK_LIST_CONSTANTS.vpcKey];
            const isNonVpcSelected = this.pagination[NETWORK_LIST_CONSTANTS.nonVpcKey];

            const matchesVpcFilter =
                (isVpcSelected && isNonVpcSelected) ||
                (isVpcSelected && network.vpc !== '') ||
                (isNonVpcSelected && network.vpc === '');

            return matchesDomain && matchesAccount && matchesSearchTerm && matchesZoneFilter && matchTypeFilter && matchesVpcFilter;
        })];

        // Apply sorting
        if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.nameKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.nameKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.stateKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.stateKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.typeKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.typeKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.vpcKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.vpcKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.cidrKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.cidrKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.zoneNameKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.zoneNameKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.domainKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.domainKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === NETWORK_LIST_CONSTANTS.accountKey) {
            filteredNetworkList.sort(sortByProperty(NETWORK_LIST_CONSTANTS.accountKey as keyof NetworkListViewModel, this.pagination.orderDir === 'asc'));
        }

        // Apply pagination
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = startIndex + this.pagination.pageSize;
        const paginatedList = filteredNetworkList.slice(startIndex, endIndex);

        return of({ data: paginatedList, totalCount: filteredNetworkList.length });
    }
}
