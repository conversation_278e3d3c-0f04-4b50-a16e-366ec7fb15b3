<button type="button" class="notification-tray-toggle btn p-0" (click)="toggleTray()">
    <i class="fa-solid fa-bell"></i>
    <span class="position-relative translate-middle badge rounded-pill bg-danger">
        {{ unreadNotificationCount() }}
        <span class="visually-hidden">{{ unreadNotificationCount() }}</span>
    </span>
</button>

@if (showTray()) {
    <div class="notification-tray border">
        <div class="modal-header p-3 border-bottom">
            <p class="modal-title fw-bolder">Notifications</p>
            <button type="button" class="btn-close ms-auto" aria-label="Close" (click)="toggleTray()"></button>
        </div>
        <div class="modal-body">
            <ul class="ps-0">
                @if (notifications().length === 0) {
                    <p>There are no notifications</p>
                }
                @for (notification of notifications(); track notification.id + notification.type) {
                    <li class="border-top border-bottom p-2 d-flex" [class]="!notification.isRead ? 'unread' : ''">
                        <div class="col-1 status-container">
                            <span class="status"
                                [class]="notification.status === jobQueueStatus.Completed || notification.status === jobQueueStatus.InProgress ? 'ok' : 'error'">
                                <span class="visually-hidden">{{ notification.status }}</span>
                            </span>
                        </div>
                        <div class="col-10 description-container">
                            <strong>{{ notification.event }} - {{ notification.title }}</strong><br />
                            {{ notification.elapsedTime }} -
                            @switch(notification.status) {
                                @case(jobQueueStatus.Completed) {
                                    Completed
                                }
                                @case(jobQueueStatus.InProgress) {
                                    In Progress
                                }
                                @case(jobQueueStatus.Failed) {
                                    Failed
                                }
                                @case(jobQueueStatus.Cancelled) {
                                    Cancelled
                                }
                                @case(jobQueueStatus.Unknown) {
                                    Unknown
                                }
                            }
                            @if (notification.eventDescription) {
                                <br />
                                <span>{{ notification.eventDescription }}</span>
                            }
                        </div>
                        <div class="col-1 remove-container">
                            <button type="button" class="btn-close remove-button" aria-label="Dismiss"
                                (click)="dismiss(notification)"></button>
                        </div>
                    </li>
                }
            </ul>
        </div>
    </div>
}
