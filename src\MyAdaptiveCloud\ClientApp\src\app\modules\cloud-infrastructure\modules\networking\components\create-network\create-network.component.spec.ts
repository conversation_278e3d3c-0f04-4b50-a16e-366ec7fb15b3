import { DebugElement } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { IsolatedNetworkComponent } from '@app/modules/cloud-infrastructure/components/isolated-network/isolated-network.component';
import { Layer2NetworkComponent } from '@app/modules/cloud-infrastructure/components/layer2-network/layer2-network.component';
import { SharedNetworkComponent } from '@app/modules/cloud-infrastructure/components/shared-network/shared-network.component';
import { SecondaryVlanType } from '@app/modules/cloud-infrastructure/models/secondary-vlan-type.enum';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { CreateNetworkComponent } from './create-network.component';

describe('CreateNetworkComponent', () => {

    let fixture: ComponentFixture<CreateNetworkComponent>;
    let component: CreateNetworkComponent;

    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateNetworkService: jasmine.SpyObj<CreateNetworkService>;

    let layer2Tab: HTMLAnchorElement;
    let sharedTab: HTMLAnchorElement;
    let nameInput: HTMLInputElement;
    let descriptionInput: HTMLInputElement;
    let submitButton: HTMLButtonElement;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateNetworkComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(CreateNetworkService),
                provideMock(CloudInfraPermissionService)
            ]
        });

        mockCreateNetworkService = TestBed.inject(CreateNetworkService) as jasmine.SpyObj<CreateNetworkService>;
        mockCreateNetworkService.getLayer2NetworkOfferings.and.returnValue(of([
            { id: '1', name: 'L2 Network Offering', forVPC: false, specifyVLan: false },
            { id: '2', name: 'L2 Network Offering 2', forVPC: false, specifyVLan: true }
        ]));
        mockCreateNetworkService.getIsolatedNetworkOfferings.and.returnValue(of([
            { id: '10', name: 'Isolated Network Offering', forVPC: false, specifyVLan: false },
            { id: '11', name: 'Isolated Network Offering 2', forVPC: true, specifyVLan: false }
        ]));
        mockCreateNetworkService.getSharedNetworkOfferings.and.returnValue(of([{ id: '0', name: 'Shared Network Offering 0', forVPC: false, specifyVLan: false }, { id: '1', name: 'Shared Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getVpcOfferings.and.returnValue(of([{ id: '1000', name: 'VPC Offering', cidr: '' }]));

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

        fixture = TestBed.createComponent(CreateNetworkComponent);
        component = fixture.componentInstance;
        component.zones.set([{ id: '1', name: 'Test Zone' }, { id: '2', name: 'Test Zone 2' }]);
        component.domainId.set('domain-id');
        component.account.set('account');
    });

    describe('Tabs', () => {

        it('should display the appropriate tabs when the user has isRootAdmin permission', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);
            fixture.detectChanges();

            const tabTitles = fixture.nativeElement.querySelectorAll('.nav-link');
            expect(tabTitles.length).toBe(3);
            expect(tabTitles[0].textContent).toBe('Isolated');
            expect(tabTitles[1].textContent).toBe('Layer-2');
            expect(tabTitles[2].textContent).toBe('Shared');
        });

        it('should display the appropriate tabs when the user does not have isRootAdmin permission', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);
            fixture.detectChanges();

            const tabTitles = fixture.nativeElement.querySelectorAll('.nav-link');
            expect(tabTitles.length).toBe(2);
            expect(tabTitles[0].textContent).toBe('Isolated');
            expect(tabTitles[1].textContent).toBe('Layer-2');
        });

        it('should select the first tab by default and its contents should match the app-add-network-isolated component', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            fixture.detectChanges();
            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            expect(activeTabContent.query(By.directive(IsolatedNetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeNull();
            expect(activeTabContent.query(By.directive(SharedNetworkComponent))).toBeNull();
        });

        it('should select the Layer-2 tab without destroying Isolated', fakeAsync(() => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            fixture.detectChanges();

            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeNull();

            const tabs = fixture.debugElement.queryAll(By.css('.nav-link'));
            layer2Tab = tabs[1].nativeElement;
            layer2Tab.click();
            layer2Tab.dispatchEvent(new Event('click'));

            fixture.detectChanges();
            tick(1000);

            expect(activeTabContent.query(By.directive(IsolatedNetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(SharedNetworkComponent))).toBeNull();
        }));

        it('should select the Shared tab without destroying Isolated and Layer-2', fakeAsync(() => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);
            fixture.detectChanges();

            const tabs = fixture.debugElement.queryAll(By.css('.nav-link'));
            layer2Tab = tabs[1].nativeElement;
            layer2Tab.click();
            layer2Tab.dispatchEvent(new Event('click'));
            fixture.detectChanges();
            tick(1000);

            sharedTab = tabs[2].nativeElement;
            sharedTab.click();
            sharedTab.dispatchEvent(new Event('click'));

            fixture.detectChanges();
            tick(1000);

            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            expect(activeTabContent.query(By.directive(IsolatedNetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(SharedNetworkComponent))).toBeDefined();
        }));

    });

    describe('Isolated Networks', () => {

        let isolatedNetworkComponent: DebugElement;

        beforeEach(() => {
            mockCreateNetworkService.createIsolatedNetwork.and.returnValue(of());
            fixture.detectChanges();
            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            isolatedNetworkComponent = activeTabContent.query(By.directive(IsolatedNetworkComponent));
            submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            nameInput = isolatedNetworkComponent.query(By.css('#name')).nativeElement;
            descriptionInput = isolatedNetworkComponent.query(By.css('#description')).nativeElement;
        });

        it('should submit with required fields when selecting a non vpc network offering', fakeAsync(() => {

            expect(submitButton.disabled).toBeTrue();
            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));

            selectOption(fixture, 'ng-select', 0, true, 1);

            const networkDomain = isolatedNetworkComponent.query(By.css('#networkDomain')).nativeElement;
            networkDomain.value = 'test-domain';
            networkDomain.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockCreateNetworkService.createIsolatedNetwork).toHaveBeenCalledOnceWith('1', 'domain-id', 'account', {
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '10',
                gateway: null,
                netmask: null,
                networkDomain: 'test-domain',
                vpc: null,
            });

        }));

        it('should submit with required fields when selecting a vpc network offering', fakeAsync(() => {

            expect(submitButton.disabled).toBeTrue();
            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));

            selectOption(fixture, 'ng-select', 1, true, 1);
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();

            selectOption(fixture, 'ng-select', 0, true, 2);
            fixture.detectChanges();

            const gateway = isolatedNetworkComponent.query(By.css('#gateway')).nativeElement;
            gateway.value = 'gateway';
            gateway.dispatchEvent(new Event('input'));

            const netmask = isolatedNetworkComponent.query(By.css('#netmask')).nativeElement;
            netmask.value = 'netmask';
            netmask.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockCreateNetworkService.createIsolatedNetwork).toHaveBeenCalledOnceWith('1', 'domain-id', 'account', {
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '11',
                gateway: 'gateway',
                netmask: 'netmask',
                networkDomain: null,
                vpc: '1000',
            });

        }));

    });

    describe('Layer-2 Networks', () => {

        let layer2NetworkComponent: DebugElement;

        beforeEach(fakeAsync(() => {
            mockCreateNetworkService.createLayer2Network.and.returnValue(of());
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

            fixture.detectChanges();

            const tabs = fixture.debugElement.queryAll(By.css('.nav-link'));
            layer2Tab = tabs[1].nativeElement;
            layer2Tab.click();
            layer2Tab.dispatchEvent(new Event('click'));

            tick(1000);
            fixture.detectChanges();

            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            layer2NetworkComponent = activeTabContent.query(By.directive(Layer2NetworkComponent));
            submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            nameInput = layer2NetworkComponent.query(By.css('#name')).nativeElement;
            descriptionInput = layer2NetworkComponent.query(By.css('#description')).nativeElement;

        }));

        it('should submit with required fields when selecting a non vlan network offering', fakeAsync(() => {

            expect(submitButton.disabled).toBeTrue();

            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 0, true, 2);
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockCreateNetworkService.createLayer2Network).toHaveBeenCalledOnceWith('1', 'domain-id', 'account', {
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '1',
                vLan: null,
                secondaryVLanID: null,
                bypassVLanId: null,
                secondaryVLanType: null
            });
        }));

        it('should not submit when missing required fields for a vlan network offering', fakeAsync(() => {
            expect(submitButton.disabled).toBeTrue();

            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 2);
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockCreateNetworkService.createLayer2Network).not.toHaveBeenCalled();

        }));

        it('should submit with required fields for a vlan network offering', fakeAsync(() => {

            expect(submitButton.disabled).toBeTrue();

            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 2);
            fixture.detectChanges();

            const vlanInput = layer2NetworkComponent.query(By.css('#vlan')).nativeElement;
            vlanInput.value = '1';
            vlanInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockCreateNetworkService.createLayer2Network).toHaveBeenCalledOnceWith('1', 'domain-id', 'account', {
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '2',
                vLan: '1',
                secondaryVLanID: null,
                bypassVLanId: null,
                secondaryVLanType: SecondaryVlanType.None
            });

        }));

    });

    describe('Shared Networks', () => {
        let sharedNetworkComponent: DebugElement;

        beforeEach(fakeAsync(() => {
            mockCreateNetworkService.createSharedNetwork.and.returnValue(of());
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            fixture.detectChanges();

            const tabs = fixture.debugElement.queryAll(By.css('.nav-link'));
            const tabShared = tabs[2].nativeElement;
            tabShared.click();
            tabShared.dispatchEvent(new Event('click'));

            tick(1000);
            fixture.detectChanges();

            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            sharedNetworkComponent = activeTabContent.query(By.directive(SharedNetworkComponent));
            submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            nameInput = sharedNetworkComponent.query(By.css('#name')).nativeElement;
            descriptionInput = sharedNetworkComponent.query(By.css('#description')).nativeElement;
        }));

        it('should submit with required fields using shared network offering', fakeAsync(() => {
            expect(submitButton.disabled).toBeTrue();

            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            descriptionInput.value = 'Test Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            sharedNetworkComponent.componentInstance.form.controls.networkOffering.setValue({name: 'test', id: '123', forVPC: false, specifyVLan: false });

            const vlanInput = sharedNetworkComponent.query(By.css('#vlanVni')).nativeElement;
            vlanInput.value = '100';
            vlanInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const ipv6GatewayInput = sharedNetworkComponent.query(By.css('#ipv6Gateway')).nativeElement;
            ipv6GatewayInput.value = 'fd00:abcd::1';
            ipv6GatewayInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const ipv6CidrInput = sharedNetworkComponent.query(By.css('#ipv6CIDR')).nativeElement;
            ipv6CidrInput.value = '***********/24';
            ipv6CidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const startIpv6Input = sharedNetworkComponent.query(By.css('#ipv6StartIp')).nativeElement;
            startIpv6Input.value = 'fd00:abcd::10';
            startIpv6Input.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const endIpv6Input = sharedNetworkComponent.query(By.css('#ipv6EndIp')).nativeElement;
            endIpv6Input.value = 'fd00:abcd::1f';
            endIpv6Input.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const networkDomain = sharedNetworkComponent.query(By.css('#networkDomain')).nativeElement;
            networkDomain.value = 'test-domain';
            networkDomain.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(mockCreateNetworkService.createSharedNetwork).toHaveBeenCalledOnceWith(
                '1',
                'domain-id', 'account', {
                    name: 'TestNetwork',
                    displaytext: 'Test Description',
                    networkofferingid: '123',
                    hideipaddressusage: false,
                    vlan: '100',
                    gatewayv6: 'fd00:abcd::1',
                    cidr: '***********/24',
                    startipv6: 'fd00:abcd::10',
                    endipv6: 'fd00:abcd::1f',
                    networkdomain: 'test-domain',
                    gateway: null,
                    netmask: null,
                    startip: null,
                    endip: null
                }
            );

        }));
    });

});
