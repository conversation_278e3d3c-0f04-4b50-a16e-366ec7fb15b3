import { CloudInfraAccountViewModel } from '../models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '../models/cloud-infra-domain.view-model';
import { ZoneViewModel } from '../models/zone.view-model';

export interface ZoneDomainAccountState {
    domains: CloudInfraDomainViewModel[];
    accounts: CloudInfraAccountViewModel[];
    selectedDomain: CloudInfraDomainViewModel;
    selectedAccount: CloudInfraAccountViewModel;
    zones: ZoneViewModel[];
}
