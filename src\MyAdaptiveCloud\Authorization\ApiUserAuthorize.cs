using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    /// Verifies that the current user's Role is authorized to access the target user, via the target user's primary organization
    /// </summary>
    public class ApiUserAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public ApiUserAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService,
            IUserContextService userContextService,
            IIdentityService identityService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int currentUserId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (currentUserId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int apiUserId);

            var userOrganizationId = await _entityAuthorizationService.GetApiUserOrganizationId(apiUserId);
            if (userOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(currentUserId, userOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, userOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    /// <summary>
    /// Specifies what minimum Role is required within the target Organization to access this endpoint.
    /// The target Organization is determined via organizationId or parentOrganizationId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class ApiUserAuthorizeAttribute : BaseAuthorizeAttribute
    {

        public ApiUserAuthorizeAttribute(params Perms[] perms) : base(typeof(ApiUserAuthorizeFilter), perms)
        {
            Name = "apiUserId";
        }

    }
}