import { Routes } from '@angular/router';
import { ddosMitigationPermissionGuard } from './guards/ddos-mitigation-permission.guard';
import { ddosMitigationHealthcheckGuard } from './guards/ddos-mitigation-health.guard';

export const routes: Routes = [
    {
        path: '',
        canActivate: [ddosMitigationPermissionGuard, ddosMitigationHealthcheckGuard],
        canActivateChild: [ddosMitigationPermissionGuard],
        loadComponent: () => import('./components/ddos-mitigation-management/ddos-mitigation-management.component').then(m => m.DdosMitigationManagementComponent),
        children: [
            {
                path: '',
                redirectTo: 'mitigation',
                pathMatch: 'full'
            },
            {
                path: 'mitigation',
                loadComponent: () => import('./components/ddos-mitigation/ddos-mitigation.component').then(m => m.DdosMitigationComponent)
            },
            {
                path: 'bgp',
                loadComponent: () => import('./components/ddos-mitigation-bgp/ddos-mitigation-bgp.component').then(m => m.DdosMitigationBgpComponent),
                children: [
                    {
                        path: '',
                        redirectTo: 'neighbors',
                        pathMatch: 'full'
                    },
                    {
                        path: 'neighbors',
                        loadComponent: () => import('./components/ddos-mitigation-bgp-neighbors/ddos-mitigation-bgp-neighbors.component').then(m => m.DdosMitigationBgpNeighborsComponent)
                    },
                    {
                        path: 'bgp-table',
                        loadComponent: () => import('./components/ddos-mitigation-bgp-table/ddos-mitigation-bgp-table.component').then(m => m.DdosMitigationBgpTableComponent)
                    },
                    {
                        path: 'advertisements',
                        loadComponent: () => import('./components/ddos-mitigation-bgp-advertisements/ddos-mitigation-bgp-advertisements.component').then(m => m.DdosMitigationBgpAdvertisementsComponent)
                    },
                ]
            },
            {
                path: 'tasks',
                loadComponent: () => import('./components/ddos-mitigation-tasks/ddos-mitigation-tasks.component').then(m => m.DdosMitigationTasksComponent)
            }
        ]
    },
];
