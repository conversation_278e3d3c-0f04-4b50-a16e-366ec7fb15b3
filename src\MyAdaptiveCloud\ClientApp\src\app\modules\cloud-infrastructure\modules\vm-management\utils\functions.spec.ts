import { byteToGB } from './functions';

describe('VM Management Utils Functions', () => {

    describe('byteToGB', () => {
        it('should convert bytes to gigabytes correctly', () => {
            // Test with 1 GB (1024^3 bytes)
            const oneGB = 1073741824;
            expect(byteToGB(oneGB)).toBe(1);
        });

        it('should convert zero bytes to zero gigabytes', () => {
            expect(byteToGB(0)).toBe(0);
        });

        it('should handle fractional gigabytes', () => {
            // Test with 0.5 GB (536870912 bytes)
            const halfGB = 536870912;
            expect(byteToGB(halfGB)).toBe(0.5);

            // Test with 2.5 GB
            const twoAndHalfGB = 2684354560;
            expect(byteToGB(twoAndHalfGB)).toBe(2.5);
        });

        it('should handle large byte values', () => {
            // Test with 1 TB (1024 GB)
            const oneTB = 1099511627776; // 1024^4 bytes
            expect(byteToGB(oneTB)).toBe(1024);

            // Test with 10 TB
            const tenTB = 10995116277760;
            expect(byteToGB(tenTB)).toBe(10240);
        });

        it('should handle small byte values (less than 1 GB)', () => {
            // Test with 1 MB (1024^2 bytes)
            const oneMB = 1048576;
            const result = byteToGB(oneMB);
            expect(result).toBeCloseTo(0.0009765625, 10); // 1/1024 GB

            // Test with 512 MB
            const fiveHundredTwelveMB = 536870912;
            expect(byteToGB(fiveHundredTwelveMB)).toBe(0.5);
        });

        it('should handle decimal inputs', () => {
            const decimalBytes = 1073741824.5;
            const result = byteToGB(decimalBytes);
            expect(result).toBeCloseTo(1.0000000004656613, 10);
        });

        it('should handle negative values', () => {
            expect(byteToGB(-1073741824)).toBe(-1);
            expect(byteToGB(-536870912)).toBe(-0.5);
        });

        it('should use the correct divider (1024^3)', () => {
            const expectedDivider = 1024 * 1024 * 1024; // 1073741824
            expect(byteToGB(expectedDivider)).toBe(1);
        });

        it('should return precise results for common storage sizes', () => {
            // Test common storage sizes
            const testCases = [
                { bytes: 1073741824, expectedGB: 1 }, // 1 GB
                { bytes: 2147483648, expectedGB: 2 }, // 2 GB
                { bytes: 4294967296, expectedGB: 4 }, // 4 GB
                { bytes: 8589934592, expectedGB: 8 }, // 8 GB
                { bytes: 17179869184, expectedGB: 16 }, // 16 GB
                { bytes: 34359738368, expectedGB: 32 }, // 32 GB
                { bytes: 68719476736, expectedGB: 64 }, // 64 GB
            ];

            testCases.forEach(testCase => {
                expect(byteToGB(testCase.bytes)).toBe(testCase.expectedGB);
            });
        });

        it('should handle edge cases', () => {
            // Test with very small positive number
            expect(byteToGB(1)).toBeCloseTo(9.313225746154785e-10, 15);

            // Test with Number.MAX_SAFE_INTEGER
            const maxSafeInteger = Number.MAX_SAFE_INTEGER;
            const result = byteToGB(maxSafeInteger);
            expect(result).toBeCloseTo(8388608, 0); // Approximately 8.4 million GB
        });

        it('should maintain precision for memory calculations', () => {
            // Test RAM sizes commonly used in VMs
            const ramSizes = [
                { bytes: 1073741824, expectedGB: 1 }, // 1 GB RAM
                { bytes: 2147483648, expectedGB: 2 }, // 2 GB RAM
                { bytes: 4294967296, expectedGB: 4 }, // 4 GB RAM
                { bytes: 8589934592, expectedGB: 8 }, // 8 GB RAM
                { bytes: 17179869184, expectedGB: 16 }, // 16 GB RAM
                { bytes: 34359738368, expectedGB: 32 }, // 32 GB RAM
                { bytes: 68719476736, expectedGB: 64 }, // 64 GB RAM
                { bytes: 137438953472, expectedGB: 128 }, // 128 GB RAM
            ];

            ramSizes.forEach(ramSize => {
                const result = byteToGB(ramSize.bytes);
                expect(result).toBe(ramSize.expectedGB);
            });
        });

        it('should handle float precision correctly', () => {
            // Test with a value that might cause floating point precision issues
            const trickyValue = 1073741825; // 1 byte more than 1 GB
            const result = byteToGB(trickyValue);
            expect(result).toBeCloseTo(1.0000000009313226, 10);
        });
    });
});
