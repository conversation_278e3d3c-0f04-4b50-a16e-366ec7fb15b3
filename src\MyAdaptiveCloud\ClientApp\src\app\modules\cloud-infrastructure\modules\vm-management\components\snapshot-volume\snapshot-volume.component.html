<div class="modal-header">
    <h4 class="modal-title">Take Volume Snapshot</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="cancel()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form" class="row g-3">
        <div>
            <label for="snapshotName" class="form-label">Snapshot Name
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'A unique name will be automatically generated if you leave this blank.'"
                    triggers="hover" container="body">
                </i>
            </label>
            <input id="snapshotName" type="text" class="form-control" formControlName="snapshotName">
        </div>
        <div class="col-12 mb-3">
            <label for="volumeId" class="form-label">Volume
                <i class="fa-solid fa-circle-info text-secondary" [ngbPopover]="'Select volume to create snapshot from.'"
                    triggers="hover" container="body">
                </i>
            </label>
            <ng-select id="volumeId" [items]="volumes$ | async" bindLabel="name" bindValue="id"
                formControlName="volumeId" />
        </div>
        <div class="mb-3 row">
            <div class="col-6 mb-2">
                <input id="asyncBackup" formControlName="asyncBackup" class="form-check-input me-2" type="checkbox" />
                <label for="asyncBackup" class="form-check-label">
                    Async Backup
                    <i class="fa-solid fa-circle-info text-secondary"
                        [ngbPopover]="'When Async is checked, the snapshot task will return finished as soon as the snapshot is taken, but before the movement of the snapshot to Secondary Storage is finished.  When Async is unchecked, the snapshot task will not return complete until the snapshot is also moved to Secondary Storage.'"
                        triggers="hover" container="body">
                    </i>
                </label>
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    <app-btn-submit [disabled]="form?.invalid || isSubmitting()" [btnClasses]="'btn-primary'"
        (submitClickEvent)="snapshotVolume()">OK</app-btn-submit>
</div>
