<div class="modal-header">
    <h4 class="modal-title">{{dialogTitle()}}</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="cancel()"></button>
</div>
<div class="modal-body">
    <div class="mb-6 row">
        <ul ngbNav #nav="ngbNav" [(activeId)]="activeTab" class="nav-tabs">
            <li [ngbNavItem]="1">
                <a ngbNavLink>Overview</a>
                <ng-template [ngbNavContent]>
                    <ng-container *ngComponentOutlet="scheduleInfoTab" />
                </ng-template>
            </li>
            <li [ngbNavItem]="2" [disabled]="!hasMicrosoftUpdates()">
                <a ngbNavLink>Microsoft Updates</a>
                <ng-template [ngbNavContent]>
                    <ng-container *ngComponentOutlet="scheduleTypeTab" />
                </ng-template>
            </li>
        </ul>

        <div [ngbNavOutlet]="nav" class="mt-2"></div>
    </div>
</div>
<div class="modal-footer">
    <button class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    @if (activeTab === 1 && hasMicrosoftUpdates()) {
        <button class="btn btn-primary ms-2" (click)="activeTab = activeTab + 1" [disabled]="!isInfoFormValid()">Next</button>
    }
    @if (activeTab === 1 && !hasMicrosoftUpdates()) {
        <app-btn-submit [disabled]="!isInfoFormValid() || isReadOnly()" (submitClickEvent)="submitForm()">
            {{ policyId() ? 'Save' : 'Create' }}
        </app-btn-submit>
    }
    @if (activeTab > 1) {
        <app-btn-submit [disabled]="!isFormSubmittable()" (submitClickEvent)="submitForm()"> {{submitButtonLabel()}}
        </app-btn-submit>
    }
</div>
