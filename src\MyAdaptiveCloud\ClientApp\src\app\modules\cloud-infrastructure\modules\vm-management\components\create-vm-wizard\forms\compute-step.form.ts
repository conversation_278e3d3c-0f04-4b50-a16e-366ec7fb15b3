import { FormControl, FormGroup } from '@angular/forms';
import { ServiceOfferingViewModel } from '../models/service-offering.view-model';

export interface ComputeStepForm {
    serviceOffering: FormControl<string | null>,
    customServiceOffering: FormGroup<ComputeStepCustomOfferingForm>
}

export interface ComputeStepCustomOfferingForm {
    cpuNumber: FormControl<number | null>;
    memory: FormControl<number | null>;
}

export interface ComputeStepFormValue {
    serviceOffering: ServiceOfferingViewModel;
}

