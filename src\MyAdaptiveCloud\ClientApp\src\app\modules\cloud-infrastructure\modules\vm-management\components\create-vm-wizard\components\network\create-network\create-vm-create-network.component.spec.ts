import { DebugElement, signal } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { IsolatedNetworkComponent } from '@app/modules/cloud-infrastructure/components/isolated-network/isolated-network.component';
import { Layer2NetworkComponent } from '@app/modules/cloud-infrastructure/components/layer2-network/layer2-network.component';
import { SharedNetworkComponent } from '@app/modules/cloud-infrastructure/components/shared-network/shared-network.component';
import { Network } from '@app/modules/cloud-infrastructure/models/network';
import { SecondaryVlanType } from '@app/modules/cloud-infrastructure/models/secondary-vlan-type.enum';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { TemplateViewModel } from '@app/modules/cloud-infrastructure/modules/vm-management/models/template.view.model';
import { VmNetwork } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm-network.model';
import { VmAffinityGroupsService } from '@app/modules/cloud-infrastructure/modules/vm-management/services/vm-affinity-groups.service';
import { VmManagementPermissionService } from '@app/modules/cloud-infrastructure/modules/vm-management/services/vm-management-permission.service';
import { VmManagementService } from '@app/modules/cloud-infrastructure/modules/vm-management/services/vm-management.service';
import { VmMediaService } from '@app/modules/cloud-infrastructure/modules/vm-management/services/vm-media-service';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { CreateVMWizardStore } from '../../../create-vm-wizard-store';
import { DiskOffering } from '../../../models/disk-offering.model';
import { ServiceOfferingViewModel } from '../../../models/service-offering.view-model';
import { CreateVmComputeService } from '../../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../../services/create-vm-network.service';
import { CreateVmService } from '../../../services/create-vm-service';
import { CreateVmStorageService } from '../../../services/create-vm-storage.service';
import { CreateVmCreateNetworkComponent } from './create-vm-create-network.component';

describe('CreateVmCreateNetworkComponent', () => {

    const zones: ZoneViewModel[] = [
        {
            id: '1',
            name: 'Zone 1'
        },
        {
            id: '2',
            name: 'Zone 2'
        },
        {
            id: '3',
            name: 'Zone 3'
        }
    ];

    const mockServiceOfferings: ServiceOfferingViewModel[] = [
        {
            name: 'name 1',
            id: '1',
            cpuNumber: 4,
            memory: 4096,
            isCustom: false
        }, {
            name: 'name 2',
            id: '2',
            cpuNumber: 2,
            memory: 2048,
            isCustom: false
        },
        {
            name: 'name 3',
            id: '3',
            cpuNumber: null,
            memory: null,
            isCustom: true
        }
    ];

    const zone1ISOs: TemplateViewModel[] = [
        {
            name: 'CentOS 7.8',
            id: '1',
            size: 123456,
            description: 'CentOS 7.8',
        }
    ];

    const mockVmNetwork: VmNetwork = {
        id: '2',
        name: 'Test Network 2',
        cidr: '***********/24',
        type: 'Private',
        vpcname: 'Test VPC 2',
        ipaddress: '***********',
        macaddress: '00:11:22:33:44:56',
        gateway: '***********54'
    };

    const mockNetwork: Network = {
        id: '2',
        name: 'Test Network 2',
        displaytext: 'Test Network 2',
        cidr: '***********/24',
        type: 'Isolated',
        vpcname: 'Test VPC 2',
        gateway: '***********54',
        broadcastdomaintype: 'Vlan',
        traffictype: 'Guest',
        zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
        zonename: 'DC1',
        networkofferingid: 'c79b6615-9b2a-4d34-b43d-bc866d262cb3',
        networkofferingname: 'DefaultL2NetworkOffering',
        networkofferingdisplaytext: 'Offering for L2 networks',
        networkofferingconservemode: true,
        networkofferingavailability: 'Optional',
        issystem: false,
        state: 'Implemented',
        related: 'b403fc9e-380e-41c5-9aff-2c6b1bdc9f12',
        broadcasturi: 'vlan://327',
        dns1: '*************',
        dns2: '*************',
        vlan: '327',
        acltype: 'Account',
        account: 'Gorilla',
        domainid: 'dda0f2d2-b998-4d92-806b-8380b830f08c',
        domain: 'gregsgaming',
        physicalnetworkid: '8257e2b2-5170-45b0-a35d-eb19989c1598',
        restartrequired: false,
        specifyipranges: false,
        canusefordeploy: true,
        ispersistent: false,
        tags: [],
        displaynetwork: true,
        strechedl2subnet: false,
        redundantrouter: false,
        supportsvmautoscaling: false,
        created: '2025-06-12T13:42:56+0000',
        receivedbytes: 0,
        sentbytes: 0,
        publicmtu: 1500,
        privatemtu: 1500,
        ip6dns1: '',
        ip6dns2: '',
        hasannotations: false
    };

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    let fixture: ComponentFixture<CreateVmCreateNetworkComponent>;
    let component: CreateVmCreateNetworkComponent;

    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateVmComputeService: jasmine.SpyObj<CreateVmComputeService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockCreateNetworkService: jasmine.SpyObj<CreateNetworkService>;
    let mockCreateVmNetworkService: jasmine.SpyObj<CreateVmNetworkService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;

    let layer2Tab: HTMLAnchorElement;
    let sharedTab: HTMLAnchorElement;
    let nameInput: HTMLInputElement;
    let descriptionInput: HTMLInputElement;
    let submitButton: HTMLButtonElement;

    beforeEach(() => {

        const mockZoneDomainAccountStore = { ...getMockZoneDomainAccountStore(), zones: signal(zones) };

        TestBed.configureTestingModule({
            imports: [CreateVmCreateNetworkComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementPermissionService),
                provideMock(CreateVmComputeService),
                provideMock(CreateNetworkService),
                provideMock(CreateVmNetworkService),
                provideMock(VmMediaService),
                provideMock(CreateVmService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(CreateVmStorageService),
                provideMock(CloudInfraPermissionService),
                provideMock(UserContextService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
                CreateVMWizardStore
            ]
        });
        mockCreateVmComputeService = TestBed.inject(CreateVmComputeService) as jasmine.SpyObj<CreateVmComputeService>;
        mockCreateVmComputeService.getServiceOfferings.and.returnValue(of(mockServiceOfferings));

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.getFeaturedISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyISOsByZoneId.and.returnValue(of(zone1ISOs));

        mockVmMediaService.getFeaturedTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyTemplatesByZoneId.and.returnValue(of(zone1ISOs));

        mockCreateVmNetworkService = TestBed.inject(CreateVmNetworkService) as jasmine.SpyObj<CreateVmNetworkService>;
        mockCreateVmNetworkService.getNetworks.and.returnValue(of([mockVmNetwork]));

        mockCreateVmNetworkService = TestBed.inject(CreateVmNetworkService) as jasmine.SpyObj<CreateVmNetworkService>;
        mockCreateVmNetworkService.getNetworks.and.returnValue(of([mockVmNetwork]));

        mockCreateNetworkService = TestBed.inject(CreateNetworkService) as jasmine.SpyObj<CreateNetworkService>;
        mockCreateNetworkService.getLayer2NetworkOfferings.and.returnValue(of([
            { id: '1', name: 'L2 Network Offering', forVPC: false, specifyVLan: false },
            { id: '2', name: 'L2 Network Offering 2', forVPC: false, specifyVLan: true }
        ]));
        mockCreateNetworkService.getIsolatedNetworkOfferings.and.returnValue(of([
            { id: '10', name: 'Isolated Network Offering', forVPC: false, specifyVLan: false },
            { id: '11', name: 'Isolated Network Offering 2', forVPC: true, specifyVLan: false }
        ]));
        mockCreateNetworkService.getSharedNetworkOfferings.and.returnValue(of([{ id: '100', name: 'Shared Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getVpcOfferings.and.returnValue(of([{ id: '1000', name: 'VPC Offering', cidr: '' }]));
        mockCreateNetworkService.createIsolatedNetwork.and.returnValue(of(mockNetwork));
        mockCreateNetworkService.createLayer2Network.and.returnValue(of(mockNetwork));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of([]));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'test-account',
                domainId: 'test-domain-id',
                cpuCustomOfferingMaxValue: 4,
                memoryCustomOfferingMaxValue: 8192
            }
        } as UserContext;

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of([]));

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

        fixture = TestBed.createComponent(CreateVmCreateNetworkComponent);
        component = fixture.componentInstance;

        component.store.setTemplate({ zone: zones[0] }, true);
        component.store.loadDataByZoneId();
    });

    describe('Tabs', () => {

        it('should display the appropriate tabs when the user has isRootAdmin permission', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);
            fixture.detectChanges();

            const tabTitles = fixture.nativeElement.querySelectorAll('.nav-link');
            expect(tabTitles.length).toBe(3);
            expect(tabTitles[0].textContent).toBe('Isolated');
            expect(tabTitles[1].textContent).toBe('Layer-2');
            expect(tabTitles[2].textContent).toBe('Shared');
        });

        it('should display the appropriate tabs when the user does not have isRootAdmin permission', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);
            fixture.detectChanges();

            const tabTitles = fixture.nativeElement.querySelectorAll('.nav-link');
            expect(tabTitles.length).toBe(2);
            expect(tabTitles[0].textContent).toBe('Isolated');
            expect(tabTitles[1].textContent).toBe('Layer-2');
        });

        it('should select the first tab by default and its contents should match the app-add-network-isolated component', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            fixture.detectChanges();
            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            expect(activeTabContent.query(By.directive(IsolatedNetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeNull();
            expect(activeTabContent.query(By.directive(SharedNetworkComponent))).toBeNull();
        });

        it('should select the Layer-2 tab without destroying Isolated', fakeAsync(() => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            fixture.detectChanges();

            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeNull();

            const tabs = fixture.debugElement.queryAll(By.css('.nav-link'));
            layer2Tab = tabs[1].nativeElement;
            layer2Tab.click();
            layer2Tab.dispatchEvent(new Event('click'));

            fixture.detectChanges();
            tick(1000);

            expect(activeTabContent.query(By.directive(IsolatedNetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(SharedNetworkComponent))).toBeNull();
        }));

        it('should select the Shared tab without destroying Isolated and Layer-2', fakeAsync(() => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);
            fixture.detectChanges();

            const tabs = fixture.debugElement.queryAll(By.css('.nav-link'));
            layer2Tab = tabs[1].nativeElement;
            layer2Tab.click();
            layer2Tab.dispatchEvent(new Event('click'));
            fixture.detectChanges();
            tick(1000);

            sharedTab = tabs[2].nativeElement;
            sharedTab.click();
            sharedTab.dispatchEvent(new Event('click'));

            fixture.detectChanges();
            tick(1000);

            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            expect(activeTabContent.query(By.directive(IsolatedNetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(Layer2NetworkComponent))).toBeDefined();
            expect(activeTabContent.query(By.directive(SharedNetworkComponent))).toBeDefined();
        }));

    });

    describe('Isolated Networks', () => {

        let isolatedNetworkComponent: DebugElement;

        beforeEach(() => {
            spyOn(component.store, 'createIsolatedNetwork').and.callThrough();
            fixture.detectChanges();
            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            isolatedNetworkComponent = activeTabContent.query(By.directive(IsolatedNetworkComponent));
            submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            nameInput = isolatedNetworkComponent.query(By.css('#name')).nativeElement;
            descriptionInput = isolatedNetworkComponent.query(By.css('#description')).nativeElement;
        });

        it('should submit with required fields when selecting a non vpc network offering', fakeAsync(() => {

            expect(submitButton.disabled).toBeTrue();
            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));

            selectOption(fixture, 'ng-select', 0, true, 0);

            const networkDomain = isolatedNetworkComponent.query(By.css('#networkDomain')).nativeElement;
            networkDomain.value = 'Test-Network-Domain';
            networkDomain.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(component.store.createIsolatedNetwork).toHaveBeenCalledOnceWith({
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '10',
                gateway: null,
                netmask: null,
                networkDomain: 'Test-Network-Domain',
                vpc: null,
            });

        }));

        it('should submit with required fields when selecting a vpc network offering', fakeAsync(() => {

            expect(submitButton.disabled).toBeTrue();
            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));

            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();

            selectOption(fixture, 'ng-select', 0, true, 1);
            fixture.detectChanges();

            const gateway = isolatedNetworkComponent.query(By.css('#gateway')).nativeElement;
            gateway.value = 'gateway';
            gateway.dispatchEvent(new Event('input'));

            const netmask = isolatedNetworkComponent.query(By.css('#netmask')).nativeElement;
            netmask.value = 'netmask';
            netmask.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(component.store.createIsolatedNetwork).toHaveBeenCalledOnceWith({
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '11',
                gateway: 'gateway',
                netmask: 'netmask',
                networkDomain: null,
                vpc: '1000',
            });

        }));

    });

    describe('Layer-2 Networks', () => {

        let layer2NetworkComponent: DebugElement;

        beforeEach(fakeAsync(() => {
            spyOn(component.store, 'createLayer2Network').and.callThrough();
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

            fixture.detectChanges();

            const tabs = fixture.debugElement.queryAll(By.css('.nav-link'));
            layer2Tab = tabs[1].nativeElement;
            layer2Tab.click();
            layer2Tab.dispatchEvent(new Event('click'));

            tick(1000);
            fixture.detectChanges();

            const activeTabContent = fixture.debugElement.query(By.css('.tab-content .tab-pane.active'));
            layer2NetworkComponent = activeTabContent.query(By.directive(Layer2NetworkComponent));
            submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            nameInput = layer2NetworkComponent.query(By.css('#name')).nativeElement;
            descriptionInput = layer2NetworkComponent.query(By.css('#description')).nativeElement;

        }));

        it('should submit with required fields when selecting a non vlan network offering', fakeAsync(() => {
            expect(submitButton.disabled).toBeTrue();

            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 0, true, 1);
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(component.store.createLayer2Network).toHaveBeenCalledOnceWith({
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '1',
                vLan: null,
                secondaryVLanID: null,
                bypassVLanId: null,
                secondaryVLanType: null
            });
        }));

        it('should not submit when missing required fields for a vlan network offering', fakeAsync(() => {
            expect(submitButton.disabled).toBeTrue();

            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 1);
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(component.store.createLayer2Network).not.toHaveBeenCalled();

        }));

        it('should submit whit required fields for a vlan network offering', fakeAsync(() => {
            expect(submitButton.disabled).toBeTrue();

            nameInput.value = 'TestNetwork';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            descriptionInput.value = 'Test Network Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 1);
            fixture.detectChanges();

            const vlanInput = layer2NetworkComponent.query(By.css('#vlan')).nativeElement;
            vlanInput.value = '1';
            vlanInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(component.store.createLayer2Network).toHaveBeenCalledOnceWith({
                description: 'Test Network Description',
                name: 'TestNetwork',
                networkOfferingId: '2',
                vLan: '1',
                secondaryVLanID: null,
                bypassVLanId: null,
                secondaryVLanType: SecondaryVlanType.None
            });

        }));

    });

});
