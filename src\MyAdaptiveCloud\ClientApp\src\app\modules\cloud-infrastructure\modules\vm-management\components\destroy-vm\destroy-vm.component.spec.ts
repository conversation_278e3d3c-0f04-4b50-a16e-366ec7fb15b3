import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { DestroyVmComponent } from './destroy-vm.component';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';

describe('DestroyVmComponent', () => {
    let component: DestroyVmComponent;
    let fixture: ComponentFixture<DestroyVmComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DestroyVmComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                provideMock(CloudInfraPermissionService),
                provideMock(VmManagementPermissionService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(DestroyVmComponent);
        component = fixture.componentInstance;
        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.destroyVirtualMachine.and.returnValue(of('jobId1'));
        mockVmManagementService.getVolumesByVirtualMachine.and.returnValue(of([]));

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.inputData.set({
            virtualMachineId: 'test-id',
            domainId: 'domain-id',
            account: 'account'
        });
    });

    it('should close modal on cancel', () => {

        fixture.detectChanges();
        const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
        cancelButton.click();
        fixture.detectChanges();

        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    it('should close modal with response on successful destroyVirtualMachine call', () => {
        mockCloudInfraPermissionService.isAdmin.and.returnValue(true);

        fixture.detectChanges();

        const expungeControl = fixture.debugElement.query(By.css('#expunge')).nativeElement as HTMLInputElement;
        expungeControl.checked = true;
        expungeControl.dispatchEvent(new Event('change'));
        fixture.detectChanges();

        const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
        submit.click();
        fixture.detectChanges();

        expect(mockVmManagementService.destroyVirtualMachine).toHaveBeenCalledOnceWith('test-id', true, []);
        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    it('should not have expunge option when user is not admin', () => {
        mockCloudInfraPermissionService.isAdmin.and.returnValue(false);

        fixture.detectChanges();
        const expunge = fixture.debugElement.query(By.css('#expunge'));
        expect(expunge).toBeNull();
    });

});

