import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { AcCwProductMap } from '../../models/ac-cw-product-map.model';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';

@Component({
    selector: 'app-ac-cw-product-list',
    imports: [NgxDatatableModule, DecimalPipe],
    templateUrl: './ac-cw-product-list.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AcCwProductListComponent extends BaseListClientComponent<AcCwProductMap> implements OnInit {
    private readonly mappingService = inject(AcToCwMappingService);

    readonly checkCell = viewChild<TemplateRef<never>>('checkCell');
    readonly dashCell = viewChild<TemplateRef<never>>('dashCell');
    readonly dashCellNumber = viewChild<TemplateRef<never>>('dashCellNumber');

    ngOnInit(): void {
        const columns = [
            {
                name: 'CW Product',
                prop: 'cwProductName',
                sortable: false
            },
            {
                name: 'Label',
                sortable: false
            },
            {
                name: 'Description',
                sortable: false
            },
            {
                name: 'Prorate',
                cellTemplate: this.checkCell(),
                sortable: false
            },
            {
                name: 'Tiered',
                cellTemplate: this.checkCell(),
                sortable: false
            },
            {
                name: 'Bandwidth Limit (GB)',
                prop: 'bandwidthGb',
                cellTemplate: this.dashCellNumber(),
                sortable: false
            },
            {
                name: 'Quantity Adjust.',
                prop: 'valueFn',
                cellTemplate: this.dashCell(),
                sortable: false
            }
        ];

        super.initialize(this.mappingService.getProductMappings.bind(this.mappingService), columns);
        this.table().groupRowsBy = 'usageType';
        this.table().groupExpansionDefault = true;
    }

}
