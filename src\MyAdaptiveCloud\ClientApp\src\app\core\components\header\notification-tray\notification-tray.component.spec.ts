import { DestroyRef } from '@angular/core';
import { ComponentFixture, discardPeriodicTasks, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { JobQueueStatus } from '@app/shared/models/job-queue/job-queue-status.enum';
import { JobQueueType } from '@app/shared/models/job-queue/job-queue-type.enum';
import { JobQueue } from '@app/shared/models/job-queue/job-queue.model';
import { JobQueueService } from '@app/shared/services/job-queue.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { NotificationTrayComponent } from './notification-tray.component';

describe('NotificationTrayComponent', () => {

    let fixture: ComponentFixture<NotificationTrayComponent>;
    let notificationTrayToggle: HTMLButtonElement;
    let mockJobQueueService: jasmine.SpyObj<JobQueueService>;

    const jobQueue: JobQueue[] = [
        {
            event: 'Starting VM',
            id: 'Job1',
            result: null,
            status: JobQueueStatus.InProgress,
            dateUtcIsoString: new Date(2025, 10, 10, 5, 30, 0).toISOString(),
            type: JobQueueType.CloudInfrastructureVm,
            title: 'VM1'
        },
        {
            event: 'Stopping VM',
            id: 'Job2',
            result: null,
            status: JobQueueStatus.Completed,
            dateUtcIsoString: new Date(2025, 10, 10, 5, 31, 0).toISOString(),
            type: JobQueueType.CloudInfrastructureVm,
            title: 'VM2'
        },
        {
            event: 'Stopping VM',
            id: 'Job3',
            result: null,
            status: JobQueueStatus.InProgress,
            dateUtcIsoString: new Date(2025, 10, 10, 5, 31, 10).toISOString(),
            type: JobQueueType.CloudInfrastructureVm,
            title: 'VM3'
        },
        {
            event: 'Stopping VM',
            id: 'Job4',
            result: null,
            status: JobQueueStatus.InProgress,
            dateUtcIsoString: new Date(2025, 10, 10, 5, 31, 20).toISOString(),
            type: JobQueueType.CloudInfrastructureVm,
            title: 'VM4'
        }
    ];

    beforeEach(() => {

        TestBed.configureTestingModule({
            imports: [
                NotificationTrayComponent
            ],
            providers: [
                provideMock(JobQueueService),
                DestroyRef
            ]
        });

        mockJobQueueService = TestBed.inject(JobQueueService) as jasmine.SpyObj<JobQueueService>;
        mockJobQueueService.jobQueue$ = of(jobQueue);

        fixture = TestBed.createComponent(NotificationTrayComponent);
        fixture.detectChanges();

        notificationTrayToggle = fixture.debugElement.query(By.css('.notification-tray-toggle')).nativeElement;
    });

    describe('Visibility Toggle', () => {

        it('should show the tray when clicking on the bell button icon ', () => {
            notificationTrayToggle.click();
            fixture.detectChanges();
            expect(fixture.debugElement.query(By.css('notification-tray'))).toBeDefined();
        });

        it('should hide the tray when it is toggled', () => {
            notificationTrayToggle.click();
            fixture.detectChanges();
            notificationTrayToggle.click();
            fixture.detectChanges();
            expect(fixture.debugElement.query(By.css('notification-tray'))).toBeNull();
        });

    });

    describe('Unread Notification Count', () => {

        let counterSpan: HTMLSpanElement;

        beforeEach(() => {
            counterSpan = (notificationTrayToggle.querySelector('span') as HTMLSpanElement);
        });

        it('should show the count of unread notifications by default', () => {
            expect(counterSpan.textContent).toBe(` ${jobQueue.length} ${jobQueue.length}`);
        });

        it('should update the read count when opening the tray and then closing, marking all notifications not in progress as read', () => {
            notificationTrayToggle.click();
            fixture.detectChanges();

            const notificationTray = fixture.debugElement.query(By.css('.notification-tray'));
            const notifications = notificationTray.queryAll(By.css('li'));
            expect(notifications.length).toBe(4);

            notificationTrayToggle.click();
            fixture.detectChanges();

            expect(counterSpan.textContent).toBe(' 3 3');
        });

        it('should update the read count when opening the tray and then closing, marking all notifications not in progress as read', () => {
            notificationTrayToggle.click();
            fixture.detectChanges();

            const notificationTray = fixture.debugElement.query(By.css('.notification-tray'));
            const removeButtons = notificationTray.queryAll(By.css('.remove-button'));
            removeButtons[3].nativeElement.click();
            fixture.detectChanges();

            notificationTrayToggle.click();
            fixture.detectChanges();

            const notifications = notificationTray.queryAll(By.css('li'));

            expect(counterSpan.textContent).toBe(' 2 2');
            expect(notifications.length).toBe(3);
        });

    });

    describe('Refresh', () => {

        it('should refresh elapsed time', fakeAsync(() => {

            // getElapsedTime should not be made public, and there is no indirect way of testing that it was called
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const elapsedTimeSpy = spyOn(fixture.componentInstance as any, 'getElapsedTime').and.callThrough();
            notificationTrayToggle.click();
            fixture.detectChanges();

            // it should call the getElapsedTime method for each item in the queue as soon as the tray is opened
            expect(elapsedTimeSpy).toHaveBeenCalledTimes(jobQueue.length);

            // it should call it again for each item in the queue after the first interval
            tick(fixture.componentInstance.interval + 1);
            expect(elapsedTimeSpy).toHaveBeenCalledTimes(jobQueue.length * 2);

            // it should call accumulate the calls for each item in the queue after 5 more intervals
            tick((fixture.componentInstance.interval * 5) + 1);
            expect(elapsedTimeSpy).toHaveBeenCalledTimes(jobQueue.length * 7);

            discardPeriodicTasks();
        }));

        it('should stop refreshing the elapsed time once the tray is closed', fakeAsync(() => {

            // getElapsedTime should not be made public, and there is no indirect way of testing that it was called
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const elapsedTimeSpy = spyOn(fixture.componentInstance as any, 'getElapsedTime').and.callThrough();
            notificationTrayToggle.click();
            fixture.detectChanges();

            tick((fixture.componentInstance.interval * 5) + 1);
            expect(elapsedTimeSpy).toHaveBeenCalledTimes(jobQueue.length * 6);

            discardPeriodicTasks();

            notificationTrayToggle.click();
            fixture.detectChanges();

            tick(100000);
            expect(elapsedTimeSpy).toHaveBeenCalledTimes(jobQueue.length * 6);
        }));

    });

});
