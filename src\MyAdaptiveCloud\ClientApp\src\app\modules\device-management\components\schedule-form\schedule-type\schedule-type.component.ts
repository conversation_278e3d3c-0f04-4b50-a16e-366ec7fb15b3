import { ChangeDetectionStrategy, Component, Des<PERSON>yRef, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DailyScheduleComponent } from '@app/modules/device-management/components/schedule-form/daily-schedule/daily-schedule.component';
import { MonthlyScheduleComponent } from '@app/modules/device-management/components/schedule-form/monthly-schedule/monthly-schedule.component';
import { WeeklyScheduleComponent } from '@app/modules/device-management/components/schedule-form/weekly-schedule/weekly-schedule.component';
import { ScheduleTypeForm } from '@app/modules/device-management/models/schedule/schedule-type.form';
import { UpdateCategoryAutoApprovalComponent } from '@app/modules/device-management/modules/policy/components/schedule-form/update-category-autoapproval/update-category-autoapproval.component';
import { DateTimePickerComponent } from '@app/shared/components/date-time-picker/date-time-picker.component';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { ScheduleType } from '@app/shared/models/schedule-type.enum';
import { UserContextService } from '@app/shared/services/user-context.service';
import { debounceTime, distinctUntilChanged, take } from 'rxjs';
import { ScheduleStore, ScheduleTypeStore } from '../schedule.component.store';

@Component({
    selector: 'app-schedule-type',
    imports: [
        ReactiveFormsModule,
        FormsModule,
        DailyScheduleComponent,
        MonthlyScheduleComponent,
        WeeklyScheduleComponent,
        UpdateCategoryAutoApprovalComponent,
        DateTimePickerComponent
    ],
    templateUrl: './schedule-type.component.html',
    styleUrl: './schedule-type.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ScheduleTypeComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    private readonly scheduleStore = inject(ScheduleStore);
    private readonly userContextService = inject(UserContextService);
    private readonly destroyRef = inject(DestroyRef);

    public readonly scheduleTypeEnum = ScheduleType;
    public readonly scheduleTypeForm = this.formBuilder.group<ScheduleTypeForm>({
        startDate: new FormControl(new Date(), [Validators.required]),
        scheduleType: new FormControl(ScheduleType.Daily, [Validators.required]),
    });

    protected readonly isPolicyAutoApprovalFlagEnabled = this.userContextService.getFeatureFlagState(FeatureFlag.FeatureFlagPolicyAutoApproval);

    ngOnInit(): void {

        this.scheduleStore.getScheduleTypeFormState$
            .pipe(take(1))
            .subscribe((scheduleTypeForm: ScheduleTypeStore) => {
                this.scheduleTypeForm.controls.startDate.setValue(scheduleTypeForm.startDateLocalized, { emitEvent: false });
                this.scheduleTypeForm.controls.scheduleType.setValue(scheduleTypeForm.scheduleType, { emitEvent: false });

                if (scheduleTypeForm.isReadOnly) {
                    this.scheduleTypeForm.disable();
                }
            });

        this.scheduleTypeForm.controls.scheduleType.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                debounceTime(200),
                distinctUntilChanged(),
            )
            .subscribe(value => {
                this.scheduleStore.updateSelectedScheduleTypeState(value);
            });

        this.scheduleTypeForm.controls.startDate.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                debounceTime(200),
                distinctUntilChanged(),
            )
            .subscribe(value => {
                this.scheduleStore.updateSelectedStartDateState(value);
            });
    }
}
