import { inject, Injectable } from '@angular/core';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { map, Observable } from 'rxjs';
import { CloudInfraAccount } from '../models/cloud-infra-account.model';
import { CloudInfraAccountViewModel } from '../models/cloud-infra-account.view-model';
import { CLOUD_INFRA_ENDPOINT_NAMES } from '../models/cloud-infra.constants';
import { ListAccountResponse } from '../responses/list-account.response';

@Injectable({
    providedIn: 'root'
})
export class CloudInfraAccountService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getAccountsByDomainId(domainid: string): Observable<CloudInfraAccountViewModel[]> {
        const params: Record<string, string> = {
            command: CLOUD_INFRA_ENDPOINT_NAMES.listAccounts,
            listall: 'true',
            details: 'min',
            domainid
        };

        return this.cloudInfraApiService.get<ListAccountResponse>(params)
            .pipe(map(response => response.listaccountsresponse?.account.map(account => this.mapAccount(account)).sort(sortByProperty('name')) ?? []));
    }

    getAccountById(accountId: string): Observable<CloudInfraAccountViewModel[]> {
        const params: Record<string, string> = {
            command: CLOUD_INFRA_ENDPOINT_NAMES.listAccounts,
            listall: 'true',
            id: accountId
        };

        return this.cloudInfraApiService.get<ListAccountResponse>(params)
            .pipe(map(response => response.listaccountsresponse?.account.map(account => this.mapAccount(account)).sort(sortByProperty('name')) ?? []));
    }

    private mapAccount(account: CloudInfraAccount): CloudInfraAccountViewModel {
        return {
            id: account.id,
            name: account.name?.trim(),
            domainId: account.domainid
        };
    }
}
