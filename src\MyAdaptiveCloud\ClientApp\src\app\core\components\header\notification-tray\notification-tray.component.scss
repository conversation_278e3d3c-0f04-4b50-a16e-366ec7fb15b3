@use "sass:map";
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "custom-variables.scss";

$success-color: map.get($theme-colors, "success");

i {
    color: white;
    font-size: 0.875rem;
}

.notification-tray {

    background-color: white;
    position: absolute;
    right: 20px;
    width: 300px;
    max-height: 500px;
    overflow: auto;

    li {
        list-style: none;
        align-items: center;

        .status-container {

            text-align: center;

            .status {
                height: 15px;
                width: 15px;
                border-radius: 50%;
                display: inline-block;
            }

            .status.ok {
                background-color: $success-color;
            }

            .status.error {
                background-color: $danger;
            }

        }

        .description-container {
            padding-left: 10px;
        }

        .remove-container {
            text-align: center;

            .btn-close {
                height: .25rem;
                width: .25rem;
            }
        }

    }

    li.unread {
        background-color: $secondary-05;
    }

}
