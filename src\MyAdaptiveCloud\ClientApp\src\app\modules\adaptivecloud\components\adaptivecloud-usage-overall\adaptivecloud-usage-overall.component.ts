import { AsyncPipe, CurrencyPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { PeriodPipe } from '@app/shared/pipes/period.pipe';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { distinctUntilChanged, filter, map, Observable, of, tap } from 'rxjs';
import { UsagePeriodForm } from '../../forms/usage-periods.form';
import { AdaptiveCloudUsage, AdaptiveCloudUsageOverall } from '../../models/adaptivecloud-usage-overall.model';
import { AdaptiveCloudUsageOverallViewModel } from '../../models/adaptivecloud-usage-overall.viewmodel';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';

@Component({
    selector: 'app-adaptivecloud-usage-overall',
    imports: [NgxDatatableModule, NgbPopover, CurrencyPipe, ReactiveFormsModule, AsyncPipe, NgSelectModule, PeriodPipe],
    templateUrl: './adaptivecloud-usage-overall.component.html',
    styleUrl: './adaptivecloud-usage-overall.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdaptiveCloudUsageOverallComponent extends BaseListClientComponent<AdaptiveCloudUsageOverallViewModel> implements OnInit {
    private readonly usageService = inject(AdaptiveCloudUsageService);
    private readonly formBuilder = inject(FormBuilder);

    readonly normalCell = viewChild<TemplateRef<never>>('normalCell');
    readonly summaryCell = viewChild<TemplateRef<never>>('summaryCell');

    protected periods$: Observable<string[]>;
    protected totalsRow = { totals: 0.0 };
    protected form: FormGroup<UsagePeriodForm>;

    ngOnInit(): void {
        const columns = [
            { name: 'Account', prop: 'name', summaryFunc: () => 'Totals' },
            { name: 'vCPUs', prop: 'vCPUs', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) },
            { name: 'RAM', prop: 'ram', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) },
            { name: 'IP Address', prop: 'ipAddresses', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) },
            { name: 'Network Bytes', prop: 'networkBytes', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) },
            { name: 'Primary Storage', prop: 'primaryStorage', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) },
            { name: 'Secondary Storage', prop: 'secondaryStorage', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) },
            { name: 'Licensing', prop: 'licensing', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) },
            { name: 'Total', prop: 'totals', cellTemplate: this.normalCell(), summaryTemplate: this.summaryCell(), comparator: this.cmpCost.bind(this) }
        ];

        this.form = this.formBuilder.group<UsagePeriodForm>({
            period: this.formBuilder.control<string>('', [Validators.required, Validators.minLength(6), Validators.maxLength(6)])
        });

        this.form.controls.period.valueChanges
            .pipe(
                filter(period => !!period),
                distinctUntilChanged(),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(period => {
                this.usageService.getUsageOverall(period)
                    .subscribe(res => {
                        columns.forEach(row => {
                            this.totalsRow[row.prop] = 0.0;
                        });
                        const data = this.mapData(res.data);
                        super.initialize(() => of({ data, message: '' }), columns);
                    });
            });

        this.periods$ = this.usageService.getUsagePeriodsOverall()
            .pipe(
                map(res => res.data),
                tap(periods => this.form.controls.period.setValue(periods[0]))
            );

        const table = this.table();
        table.summaryRow = true;
        table.summaryPosition = 'bottom';
    }

    private mapData(res: AdaptiveCloudUsageOverall[]): AdaptiveCloudUsageOverallViewModel[] {
        return res.map(entry => {
            const resp: AdaptiveCloudUsageOverallViewModel = {
                id: null,
                name: null,
                totals: null,
                vCPUs: this.calculateTotalCost(entry.vCPUs),
                ram: this.calculateTotalCost(entry.ram),
                ipAddresses: this.calculateTotalCost(entry.ipAddresses),
                networkBytes: this.calculateTotalCost(entry.networkBytes),
                primaryStorage: this.calculateTotalCost(entry.primaryStorage),
                secondaryStorage: this.calculateTotalCost(entry.secondaryStorage),
                licensing: this.calculateLicensingCost(entry.licensing)
            };
            let total = 0.0;
            Object.entries(resp).forEach(([key, value]) => {
                if (value?.cost) {
                    total += value.cost;
                    // eslint-disable-next-line no-prototype-builtins
                    if (!this.totalsRow.hasOwnProperty(key)) {
                        this.totalsRow[key] = 0;
                    }
                    this.totalsRow[key] += value.cost;
                    this.totalsRow['totals'] += value.cost;
                }
            });
            resp.id = entry.acId;
            resp.name = entry.acName;
            resp.totals = { cost: total, summary: '' };
            return resp;
        });
    }

    private cmpCost(propA, propB) {
        return propA.cost - propB.cost;
    }

    private calculateTotalCost(entry: AdaptiveCloudUsage) {
        let summary = 'No usage';
        const summaries: string[] = [];
        let cost = 0.0;
        if (entry) {
            if (Array.isArray(entry)) {
                // Licensing, iterate over all entries and sum them up
                entry.forEach(item => {
                    if (item.quantity !== null && item.unitPrice !== null) {
                        // cost += item.quantity * item.unitPrice;
                        cost += item.cost;
                        summaries.push(`${item.label}: ${item.quantity.toFixed(2)} @ $${item.unitPrice}`);
                    }
                });
            } else if (entry.quantity !== null && entry.unitPrice !== null) {
                // cost = entry.quantity * entry.unitPrice;
                cost = entry.cost;
                summary = `${entry.quantity + (entry.units ? ` ${entry.units}` : '')} @ $${entry.unitPrice.toFixed(2)} each`;
            }
        }
        return { cost, summary: summaries.length ? summaries : [summary] };
    }

    private calculateLicensingCost(entries: AdaptiveCloudUsage[]) {
        const summary = 'No usage';
        let cost = 0.0;
        const summaries: string[] = [];
        entries.forEach(item => {
            if (item.quantity !== null && item.unitPrice !== null) {
                // cost += item.quantity * item.unitPrice;
                cost += item.cost;
                summaries.push(`${item.label}: ${item.quantity.toFixed(2)} @ $${item.unitPrice}`);
            }
        });
        return { cost, summary: summaries.length ? summaries : [summary] };
    }

    protected getSummary() {
        if (this.form.valid) {
            this.usageService.getUsageOverallSummaryCsv(this.form.controls.period.value);
        }
    }

}
