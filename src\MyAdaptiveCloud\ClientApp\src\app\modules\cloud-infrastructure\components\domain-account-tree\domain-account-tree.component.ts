import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { CloudInfraDomainViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-domain.view-model';
import { ZoneDomainAccountStore } from '../../store/zone-domain-account-store';

@Component({
    selector: 'app-domain-account-tree',
    imports: [NgTemplateOutlet],
    templateUrl: './domain-account-tree.component.html',
    styleUrl: './domain-account-tree.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DomainAccountTreeComponent {

    public readonly domain = input<CloudInfraDomainViewModel>(null);
    protected readonly store = inject(ZoneDomainAccountStore);

}

