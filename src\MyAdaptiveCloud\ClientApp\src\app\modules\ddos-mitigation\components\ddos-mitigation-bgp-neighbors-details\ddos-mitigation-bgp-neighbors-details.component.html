<div class="modal-header">
    <h4 class="modal-title">Details</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body message-list-body">
    @if(neighborDetails()) {

    <div class="details container mt-2">
        <div class="ip-row d-flex border-bottom pb-2 mb-2">
            <span class="ip-row-label text-secondary fw-bold me-2">Neighbor IP:</span>
            <span class="ip-row-value">{{ neighborDetails().ip }}</span>
        </div>
        <div class="d-flex mb-2">
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Local AS</span>
                <span>{{ neighborDetails().localAs }}</span>
            </div>
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Hold Time</span>
                <span>{{ neighborDetails().holdTime }}</span>
            </div>
        </div>
        <div class="d-flex mb-2">
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Remote AS</span>
                <span>{{ neighborDetails().remoteAs }}</span>
            </div>
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Retry Interval</span>
                <span>{{ neighborDetails().retryInterval }}</span>
            </div>
        </div>
        <div class="d-flex mb-2">
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">State</span>
                <span>{{ neighborDetails().state }}</span>
            </div>
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Scrub Phase 1 Community</span>
                <span>{{ neighborDetails().scrubPhase1Community }}</span>
            </div>
        </div>
        <div class="d-flex mb-2">
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Up/Down</span>
                <span>{{ neighborDetails().updown }}</span>
            </div>
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Scrub Phase 2 Community</span>
                <span>{{ neighborDetails().scrubPhase2Community }}</span>
            </div>
        </div>
        <div class="d-flex mb-2">
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Prefixes Received</span>
                <span>{{ neighborDetails().prefixesReceived }}</span>
            </div>
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Scrub Post Community</span>
                <span>{{ neighborDetails().scrubPostCommunity }}</span>
            </div>
        </div>
        <div class="d-flex mb-2">
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Prefixes Sent</span>
                <span>{{ neighborDetails().prefixesSent }} ({{neighborDetails().prefixesSentSimulated}} Simulated)
                </span>
            </div>
            <div class="col d-flex flex-column">
                <span class="text-secondary fw-bold">Blackhole Community</span>
                <span>{{ neighborDetails().blackholeCommunity }}</span>
            </div>
        </div>
    </div>
    }
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-primary" (click)="activeModal.dismiss()">Close</button>
</div>
