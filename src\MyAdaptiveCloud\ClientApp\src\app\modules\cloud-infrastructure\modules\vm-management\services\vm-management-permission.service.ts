import { inject, Injectable } from '@angular/core';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContextService } from '@app/shared/services/user-context.service';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '../models/vm.constants';

@Injectable({
    providedIn: 'root'
})
export class VmManagementPermissionService {

    private readonly userContextService = inject(UserContextService);
    private readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);

    public canViewVirtualMachineList(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.listVirtualMachines);
    }

    public canStopVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.stopVirtualMachine);
    }

    public canDestroyVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.destroyVirtualMachine);
    }

    public canRebootVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.rebootVirtualMachine);
    }

    public canStartVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.startVirtualMachine);
    }

    public canResetSSHKeyPairForVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) ||
            (cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.resetSSHKeyForVirtualMachine) && cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.listSSHKeyPairs));
    }

    public canSnapshotVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.snapshotVirtualMachine);
    }

    public canExpungeVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return (this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.expungeVirtualMachine)) && this.cloudInfraPermissionService.isAdmin();
    }

    public canRecoverVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return (this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.recoverVirtualMachine)) && this.cloudInfraPermissionService.isAdmin();
    }

    public canAttachIso(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return (this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.attachIso));
    }

    public canEjectIso(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return (this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.ejectIso));
    }

    public canMigrateVirtualMachineHost(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.migrateVirtualMachine);
    }

    public canSnapshotVolume(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.snapshotVolume);
    }

    public canReinstallVirtualMachine(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.reinstallVirtualMachine);
    }

    public canResetVirtualMachinePassword(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.resetVirtualMachinePassword);
    }

    public canCreateNetwork(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.createNetwork);
    }

    public canListAffinityGroups(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.listAffinityGroups);
    }

    public canUpdateVmAffinityGroups(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.updateVirtualMachineAffinityGroups);
    }

    public canCreateAffinityGroup(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || (cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.listAffinityGroupTypes) &&
            cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.createAffinityGroup));
    }

    public canListVmSnapshots(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.listVirtualMachineSnapshots);
    }

    public canCreateSnapshotFromVirtualMachineSnapshot(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.createSnapshotFromVirtualMachineSnapshot);
    }

    public canDeleteVirtualMachineSnapshot(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.deleteVirtualMachineSnapshot);
    }

    public canRevertToVirtualMachineSnapshot(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(VIRTUAL_MACHINES_ENDPOINT_NAMES.revertToVirtualMachineSnapshot);
    }

    private hasAllPermissions(cloudInfraContext: CloudInfraUserContext): boolean {
        return cloudInfraContext?.permissions.length === 1 && cloudInfraContext.permissions[0] === '*';
    }

}
