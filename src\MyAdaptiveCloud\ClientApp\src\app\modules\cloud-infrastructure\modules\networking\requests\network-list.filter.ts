import { FilterGroupAndRequest } from '@app/shared/models/datatable/filters/filter-group-and-request';
import { ToggleFilterGroupOptions } from '@app/shared/models/datatable/filters/toggle-filter-group-options';
import { NETWORK_LIST_CONSTANTS } from './../models/network-list.constants';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';

export class NetworkListFilters extends FilterGroupAndRequest {

    constructor(zones: ZoneViewModel[]) {
        super();
        this.orderBy = 'name';
        this.orderDir = 'asc';
        this.filterGroupOptions.push(new ToggleFilterGroupOptions('Virtual Private Cloud', NETWORK_LIST_CONSTANTS.vpcKey, true, 'VPC Networks'));
        this.filterGroupOptions.push(new ToggleFilterGroupOptions('Virtual Private Cloud', NETWORK_LIST_CONSTANTS.nonVpcKey, true, 'Non-VPC Networks'));
        this.filterGroupOptions.push(new ToggleFilterGroupOptions('Type', NETWORK_LIST_CONSTANTS.isolatedKey, true, 'Isolated'));
        this.filterGroupOptions.push(new ToggleFilterGroupOptions('Type', NETWORK_LIST_CONSTANTS.l2Key, true, 'Layer-2'));
        this.filterGroupOptions.push(new ToggleFilterGroupOptions('Type', NETWORK_LIST_CONSTANTS.shared, true, 'Shared'));

        zones.forEach(zone => {
            this.filterGroupOptions.push(new ToggleFilterGroupOptions('Zone', zone.id, true, zone.name));
        });
    }
}

