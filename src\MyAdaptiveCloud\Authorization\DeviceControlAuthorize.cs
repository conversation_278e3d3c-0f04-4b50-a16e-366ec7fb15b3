using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class DeviceControlAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public DeviceControlAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService, IUserContextService userContextService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int deviceControlId);

            var deviceControlOrganizationId = await _entityAuthorizationService.GetDeviceControlOrganizationId(deviceControlId);
            if (deviceControlOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, deviceControlOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, deviceControlOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class DeviceControlAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public DeviceControlAuthorizeAttribute(params Perms[] perms) : base(typeof(DeviceControlAuthorizeFilter), perms)
        {
            Name = "deviceControlId";
        }
    }
}