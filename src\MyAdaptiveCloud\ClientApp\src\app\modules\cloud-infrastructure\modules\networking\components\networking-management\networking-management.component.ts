import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { DomainAccountTreeComponent } from '@app/modules/cloud-infrastructure/components/domain-account-tree/domain-account-tree.component';
import { BreadcrumbsComponent } from '@app/shared/components/breadcrumbs/breadcrumbs.component';
import { filter } from 'rxjs/internal/operators/filter';
import { NETWORKING_ROUTE_SEGMENTS } from '../../models/route.segments';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { InformationBannerComponent } from '@app/shared/components/information-banner/information-banner.component';
import { NetworkingPermissionService } from '../../services/networking-permission.service';

@Component({
    selector: 'app-networking-management',
    imports: [RouterOutlet, DomainAccountTreeComponent, BreadcrumbsComponent, InformationBannerComponent, RouterLink, RouterLinkActive],
    templateUrl: './networking-management.component.html',
    styleUrl: './networking-management.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class NetworkingManagementComponent implements OnInit {
    protected readonly store = inject(ZoneDomainAccountStore);
    protected readonly networkingPermissionService = inject(NetworkingPermissionService);
    protected readonly NETWORKING_ROUTE_SEGMENTS = NETWORKING_ROUTE_SEGMENTS;
    private readonly domainAccountSelected$ = toObservable(this.store.domainOrAccountSelected);
    private readonly router = inject(Router);
    private readonly activatedRoute = inject(ActivatedRoute);
    private readonly destroyRef = inject(DestroyRef);

    ngOnInit() {
        this.domainAccountSelected$
            .pipe(
                filter(domainAccount => !!domainAccount),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => this.router.navigate([NETWORKING_ROUTE_SEGMENTS.NETWORKS], { relativeTo: this.activatedRoute.parent }));
    }

}
