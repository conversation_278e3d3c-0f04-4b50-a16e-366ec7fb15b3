import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ModalService } from '@app/shared/services/modal.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { Msi } from '../../models/msi.model';
import { ServiceTypeEnum } from '../../models/service-type.enum';
import { MsiManagementService } from '../../services/msi-management.service';
import { MsiManagementListComponent } from './msi-management-list.component';
import { AgentApiHealthService } from '../../services/agent-api-health.service';

describe('MsiManagementListComponent', () => {
    let component: MsiManagementListComponent;
    let fixture: ComponentFixture<MsiManagementListComponent>;
    let mockMsiManagementService: jasmine.SpyObj<MsiManagementService>;
    let mockAgentApiHealthService: jasmine.SpyObj<AgentApiHealthService>;

    const agentVersions: Msi[] = [
        {
            name: 'adaptive-cloud-agent',
            serviceId: 1,
            releaseDate: new Date('2023-01-01'),
            version: '1.0.0',
            agentCount: 0,
            releaseTag: '',
            canDelete: false,
            checksum: '',
            checksumType: '',
            serviceType: ServiceTypeEnum.Agent,
            isObsolete: false,
            canMarkObsolete: false
        },
        {
            name: 'adaptive-cloud-agent',
            serviceId: 2,
            releaseDate: new Date('2024-01-01'),
            version: '1.2.0',
            agentCount: 0,
            releaseTag: '',
            canDelete: false,
            checksum: '',
            checksumType: '',
            serviceType: ServiceTypeEnum.Agent,
            isObsolete: false,
            canMarkObsolete: false
        },
        {
            name: 'adaptive-cloud-watchdog',
            serviceId: 3,
            releaseDate: new Date('2023-02-02'),
            version: '1.30.0',
            agentCount: 0,
            releaseTag: '',
            canDelete: false,
            checksum: '',
            checksumType: '',
            serviceType: ServiceTypeEnum.Watchdog,
            isObsolete: false,
            canMarkObsolete: false
        },
        {
            name: 'adaptive-cloud-watchdog',
            serviceId: 4,
            releaseDate: new Date('2024-06-05'),
            version: '1.32.0',
            agentCount: 0,
            releaseTag: '',
            canDelete: false,
            checksum: '',
            checksumType: '',
            serviceType: ServiceTypeEnum.Watchdog,
            isObsolete: false,
            canMarkObsolete: false
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                MsiManagementListComponent
            ],
            providers: [
                provideMock(ModalService),
                provideMock(MsiManagementService),
                provideMock(AgentApiHealthService)
            ]
        });

        mockMsiManagementService = TestBed.inject(MsiManagementService) as jasmine.SpyObj<MsiManagementService>;
        mockMsiManagementService.getList.and.returnValue(of({ data: agentVersions, message: '' }));

        mockAgentApiHealthService = TestBed.inject(AgentApiHealthService) as jasmine.SpyObj<AgentApiHealthService>;
        mockAgentApiHealthService.getHealthCheck.and.returnValue(of({ data: true, message: '' }));

        fixture = TestBed.createComponent(MsiManagementListComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should call services', () => {
            expect(mockMsiManagementService.getList).toHaveBeenCalledTimes(1);
        });

        it('should have the right amount of data', () => {
            expect(component.table().count).toBe(4);
            expect(component.table().rows.length).toBe(4);
        });
    });

});
