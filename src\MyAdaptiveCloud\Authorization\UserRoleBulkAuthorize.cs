using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class UserRoleBulkAuthorizeFilter : BaseAsyncBulkAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public UserRoleBulkAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
            IUserContextService userContextService, Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }

            context.ActionArguments.TryGetValue(_requestName, out var request);

            // If the request body parameter is not found, then the request is invalid
            if (request == null)
            {
                context.Result = new ForbidResult();
            }
            else
            {
                var userRoles = request.GetType().GetProperty(_name).GetValue(request, null) as List<int>;

                // If there are any elements in the list, proceed to authorize each one, otherwise, treat as a non-op and proceed to the next action in the pipeline 
                if (userRoles.Any())
                {
                    var userRoleOrganizationIds = await _entityAuthorizationService.GetUserRoleOrganizationIds(userRoles);

                    var allOrgsAuthorized = false;
                    if (userRoleOrganizationIds.Any())
                    {
                        allOrgsAuthorized = userRoleOrganizationIds.All(orgId => _userContextService.HasPermission(userId, orgId, _distance, _perms));
                    }

                    if (allOrgsAuthorized)
                    {
                        await next();
                    }
                    else
                    {
                        context.Result = new ForbidResult();
                    }
                }
                else
                {
                    await next();
                }
            }
        }
    }

    public class UserRoleBulkAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public UserRoleBulkAuthorizeAttribute(params Perms[] perms) : base(typeof(UserRoleBulkAuthorizeFilter), perms)
        {
            Name = "UserRoleIds";
        }
    }

}