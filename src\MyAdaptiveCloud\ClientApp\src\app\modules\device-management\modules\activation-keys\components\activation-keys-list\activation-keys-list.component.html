<div class="content-heading">
  Activation Keys
</div>

<div class="content-sub-heading">
  <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
  <div class="action-buttons">
    @if (permissionService.canManageActivationKey()) {
    <button class="btn btn-primary" type="button" (click)="createActivationKey()">
      Create Activation Key
    </button>
    }
  </div>
</div>

<div class="card card-default">
  <div class="card-body">
    <ngx-datatable #table class="table bootstrap no-detail-row" />
  </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
  <span (click)="sort()" class="clickable">
    {{ column.name }}
    <span
      [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
    </span>
  </span>
</ng-template>

<ng-template #keyCellTemplate let-row="row">
  @if (toItem(row); as row) {
  <span role="button">
    <i class="fa-solid fa-copy ms-2 text-secondary" (click)="copyActivationKey(row.key)"
      [ngbPopover]="'Copy Activation Key'" triggers="hover" container="body"></i>
  </span>
  }
</ng-template>

<ng-template #dateCellTemplate let-value="value">
  <span>
    {{ (value) ? (value | date: 'yyyy-MM-dd') : '-' }}
  </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
  @if (toItem(row); as row) {
  @if (row.canEdit === userActionState.Allowed) {
  <app-btn-switch [isActiveInput]="row.isActive" (isActiveChange)="toggleActivationKey(row)" />
  } @else {
  <span>
    {{ row.isActive ? 'Active': 'Inactive'}}
  </span>
  }
  }
</ng-template>
