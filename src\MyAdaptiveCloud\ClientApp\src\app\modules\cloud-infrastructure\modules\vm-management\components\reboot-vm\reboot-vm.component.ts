import { ChangeDetectionStrategy, Component, inject, Input, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAX_BOOT_DELAY } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { VmRebootForm } from '../../forms/vm-reboot.form';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-reboot-vm',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgbPopover],
    templateUrl: './reboot-vm.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class RebootVmComponent {
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) virtualMachineId: string;

    protected form = this.formBuilder.group<VmRebootForm>({
        bootDelay: this.formBuilder.control<number>(0, [Validators.min(0), Validators.max(MAX_BOOT_DELAY), Validators.pattern('^[0-9]*$')])
    });

    protected readonly isSubmitting = signal<boolean>(false);

    protected cancel() {
        this.activeModal.close();
    }

    protected rebootVirtualMachine() {
        this.isSubmitting.set(true);
        if (this.form.valid) {
            this.vmManagementService.rebootVirtualMachine(this.virtualMachineId, Number(this.form.controls.bootDelay.value))
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
