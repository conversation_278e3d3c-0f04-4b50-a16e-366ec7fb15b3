import { Routes } from '@angular/router';
import { cloudInfrastructureCredentialsGuard } from '../../guards/cloud-infrastructure-credentials.guard';
import { STORAGE_ROUTE_SEGMENTS } from './components/models/route.segments';

export const storageRoutes: Routes = [
    {
        path: '',
        loadComponent: () => import('./components/storage-management/storage-management.component').then(m => m.StorageManagementComponent),
        canActivate: [cloudInfrastructureCredentialsGuard],
        canActivateChild: [cloudInfrastructureCredentialsGuard],
        children: [
            {
                path: STORAGE_ROUTE_SEGMENTS.VOLUMES,
                loadComponent: () => import('./components/volumes/volumes.component').then(m => m.VolumesComponent),
            },
            {
                path: STORAGE_ROUTE_SEGMENTS.SNAPSHOTS,
                loadComponent: () => import('./components/snapshots/snapshots.component').then(m => m.SnapshotsComponent),
            }, {
                path: STORAGE_ROUTE_SEGMENTS.VM_SNAPSHOTS,
                loadComponent: () => import('./components/vm-snapshots/vm-snapshots.component').then(m => m.VMSnapshotsComponent),
            }
        ]
    }
];
