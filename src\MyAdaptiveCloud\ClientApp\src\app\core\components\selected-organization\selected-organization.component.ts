import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { SelectableOrganization } from '@app/core/models/selectable-organization';
import { SelectableOrganizationService } from '@app/core/services/selectable-organization.service';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';

@Component({
    selector: 'app-selected-organization',
    imports: [NgxDatatableModule, AutoSearchBoxComponent],
    templateUrl: './selected-organization.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SelectedOrganizationComponent extends BaseListClientComponent<SelectableOrganization> implements OnInit {
    protected readonly activeModal = inject(NgbActiveModal);

    protected readonly userContextService = inject(UserContextService);
    private readonly organizationService = inject(SelectableOrganizationService);
    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Organization',
                prop: 'name',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200
            },
            {
                name: '',
                cellTemplate: this.actionsTemplate(),
                width: 100,
                sortable: false,
                resizeable: false,
                canAutoResize: false
            }
        ];

        super.initialize(this.organizationService.getSelectable.bind(this.organizationService), columns);
        this.table().footerHeight = 60;
    }

    protected selectOrganization(organizationId: number) {
        this.activeModal.close(organizationId);
    }

}
