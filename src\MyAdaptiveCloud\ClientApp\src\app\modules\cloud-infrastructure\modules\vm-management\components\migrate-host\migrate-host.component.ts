import { ChangeDetectionStrategy, Component, inject, Input, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { FileSizeConverterPipe } from '@app/shared/pipes/file-size-converter.pipe';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, SelectionType, TableColumn } from '@swimlane/ngx-datatable';
import { map, Observable } from 'rxjs';
import { HostForMigrationViewModel } from '../../models/host-for-migration.view-model';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-vm-migrate-host',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgxDatatableModule, AutoSearchBoxComponent, FileSizeConverterPipe],
    templateUrl: './migrate-host.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class MigrateHostComponent extends BaseListClientComponent<HostForMigrationViewModel> implements OnInit {

    readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    readonly memoryUsedTemplate = viewChild<TemplateRef<never>>('memoryUsedTemplate');
    readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);
    protected readonly selectionType = SelectionType;

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) virtualMachineId: string;

    protected readonly isSubmitting = signal<boolean>(false);

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Name',
                prop: 'name',
                headerTemplate: this.headerTemplate(),
                sortable: true,
            },
            {
                name: 'Suitability',
                prop: 'suitableForMigration',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'CPU Utilized (%)',
                prop: 'cpuUsed',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
            },
            {
                name: 'Memory Used',
                prop: 'memoryUsed',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.memoryUsedTemplate(),
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Action',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 170
            }
        ];

        super.initialize(() => this.getHosts$(), columns);
    }

    private getHosts$(): Observable<ApiDataResult<HostForMigrationViewModel[]>> {
        return this.vmManagementService.findHostsForMigration(this.virtualMachineId)
            .pipe(map(res => ({
                data: res.host.map(host => {
                    const hostViewModel: HostForMigrationViewModel = {
                        cpuUsed: host.cpuused,
                        id: host.id,
                        memoryUsed: host.memoryused,
                        name: host.name,
                        suitableForMigration: host.suitableformigration ? 'Yes' : 'No'
                    };
                    return hostViewModel;
                }), message: ''
            })));
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected selectHost(host: HostForMigrationViewModel) {
        this.table().selected = [host];
    }

    protected migrateHost() {
        this.isSubmitting.set(true);
        if (this.table().selected?.length) {
            this.vmManagementService.migrateVirtualMachineHost(this.virtualMachineId, this.table().selected[0].id)
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }
}
