import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { NotificationType } from '@app/shared/models/notification-type';
import { NotificationService } from '@app/shared/services/notification.service';
import { map, tap } from 'rxjs';
import { DDoSMitigationHealthService } from '../services/ddos-mitigation-health.service';

export const ddosMitigationHealthcheckGuard: CanActivateFn = () => {

    const ddosMitigationHealthService = inject(DDoSMitigationHealthService);
    const notificationService = inject(NotificationService);

    return ddosMitigationHealthService.healthcheck().pipe(
        map(result => result.data),
        tap(isHealthy => {
            if (!isHealthy) {
                notificationService.notify('DDoS Mitigation service unavailable. Please try again later.', NotificationType.Error);
            }
        })
    );

};
