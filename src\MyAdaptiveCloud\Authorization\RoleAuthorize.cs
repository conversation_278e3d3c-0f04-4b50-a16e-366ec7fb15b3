﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class RoleAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public RoleAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IUserContextService userContextService,
            Perms[] perms, IIdentityService identityService, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int roleId);

            var role = await _entityAuthorizationService.GetRoleOrganizationId(roleId);

            if (role != null)
            {
                if (_perms != null && _perms.Count() > 0)
                {
                    // If the role is restricted, first check if the user has permission to create restricted roles
                    // If this check fails, reject request regardless of the actual permissions to be checked.
                    if (role.IsRestricted && !_userContextService.HasPermission(userId, role.OrganizationId, 0, Perms.CreateRestrictedRoles))
                    {
                        context.Result = new ForbidResult();
                    }
                    else
                    {
                        if (!_userContextService.HasPermission(userId, role.OrganizationId, _distance, _perms))
                        {
                            context.Result = new ForbidResult();
                        }
                        else
                        {
                            AuthorizeFilterHelpers.SetOrganizationId(context, role.OrganizationId);
                        }
                    }
                }
                else
                {
                    context.Result = new ForbidResult();
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    /// <summary>
    /// Specifies what minimum Role is required within the target Organization to access this endpoint.
    /// The target Organization is determined via organizationId or parentOrganizationId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class RoleAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public RoleAuthorizeAttribute(params Perms[] perms) : base(typeof(RoleAuthorizeFilter), perms)
        {
            Name = "roleId";
        }
    }
}