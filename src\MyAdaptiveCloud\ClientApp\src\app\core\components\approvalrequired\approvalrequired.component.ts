import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { UserContextService } from '@app/shared/services/user-context.service';

@Component({
    selector: 'app-approval-required',
    templateUrl: './approvalrequired.component.html',
    styleUrl: './approvalrequired.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ApprovalRequiredComponent {
    private readonly router = inject(Router);
    protected readonly userContextService = inject(UserContextService);

    protected continueOnceApproved(): void {
        this.userContextService.initializeUserContext().then((() => {
            this.router.navigate(['/home']);
        }));
    }

}
