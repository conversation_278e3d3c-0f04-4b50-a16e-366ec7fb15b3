import { computed, inject } from '@angular/core';
import { Network } from '@app/modules/cloud-infrastructure/models/network';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { CreateIsolatedNetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-isolated-network.request';
import { CreateLayer2NetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-layer2-network.request';
import { SharedNetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-shared-network';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { tapResponse } from '@ngrx/operators';
import { patchState, signalStore, withComputed, withHooks, withMethods, withState } from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { catchError, EMPTY, forkJoin, map, mergeMap, Observable, of, pipe, switchMap } from 'rxjs';
import { OsTypeFilter } from '../../models/os-type-filter.enum';
import { OsType } from '../../models/os-type.enum';
import { SSHKeyPair } from '../../models/ssh-key-pair';
import { VmNetwork } from '../../models/vm-network.model';
import { VmAffinityGroupsService } from '../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../services/vm-management.service';
import { VmMediaService } from '../../services/vm-media-service';
import { DEFAULT_KEYBOARD_LANG } from './components/advanced-settings/vm-available-keyboard-langs';
import { ComputeStepFormValue } from './forms/compute-step.form';
import { SetupStepFormValue } from './forms/setup-step.form';
import { AdvancedSettingsModel } from './models/advanced-settings.model';
import { CreateVmRequestStatus } from './models/create-vm-request-status.emun';
import { CreateVmWizardStepEnum } from './models/create-vm-wizard-steps.enum';
import { CreateVMWizardState } from './models/create-wizard.state';
import { DiskOffering } from './models/disk-offering.model';
import { CreateVirtualMachineRequest } from './requests/create-virtual-machine.request';
import { CreateVmComputeService } from './services/create-vm-compute.service';
import { CreateVmNetworkService } from './services/create-vm-network.service';
import { CreateVmService } from './services/create-vm-service';
import { CreateVmStorageService } from './services/create-vm-storage.service';

const initialState: CreateVMWizardState = {
    domainId: null,
    account: null,
    currentStep: 0,
    totalSteps: 5,
    zones: [],
    setupStep: {
        templates: {
            featuredTemplates: [],
            publicTemplates: [],
            myTemplates: [],
            featuredISOs: [],
            publicISOs: [],
            myISOs: []
        },
        isValid: false,
        form: {
            zone: null,
            template: null,
            osType: OsType.Template,
            osTypeFilter: OsTypeFilter.Featured,
            virtualMachineName: null
        }
    },
    computeStep: {
        presetServiceOfferings: [],
        customServiceOffering: null,
        isValid: false,
        form: {
            serviceOffering: null,
        }
    },
    storageStep: {
        selectedDataDisks: [],
        rootDisk: null,
        diskOfferings: [],
        isValid: false,
    },
    networkStep: {
        networks: [],
        selectedNetworks: [],
        availableNetworks: [],
        isValid: false,
        isolatedNetworkOfferings: [],
        layer2NetworkOfferings: [],
        sharedNetworkOfferings: [],
        vpcs: [],
        requestStatus: CreateVmRequestStatus.None
    },
    advancedSettingsStep: {
        isValid: true,
        isAccessible: false,
        affinityGroups: [],
        sshKeyPairs: [],
        selectedSettings: {
            affinityGroups: {
                selectAll: false,
                options: []
            },
            sshKeyPairs: {
                selectAll: false,
                options: []
            },
            userdata: '',
            keyboardLanguage: DEFAULT_KEYBOARD_LANG
        },
        displaySettings: false
    },
    create: {
        requestStatus: CreateVmRequestStatus.None,
        createVirtualMachineJobId: null,
        startVirtualMachineJobId: null,
        virtualMachineId: null
    }
};

function mapNetwork(network: Network): VmNetwork {
    const vmNetwork: VmNetwork = {
        id: network.id,
        name: network.name,
        cidr: network.cidr,
        vpcname: network.vpcname,
        type: network.type,
        gateway: network.gateway,
        ipaddress: '',
        macaddress: ''
    };
    return vmNetwork;
}

export const CreateVMWizardStore = signalStore(
    withState(initialState),
    withMethods((
        store,
        vmMediaService = inject(VmMediaService),
        createVmNetworkService = inject(CreateVmNetworkService),
        createNetworkService = inject(CreateNetworkService),
        vmAffinityGroupsService = inject(VmAffinityGroupsService),
        vmManagementService = inject(VmManagementService),
        createVmStorageService = inject(CreateVmStorageService),
        createVmComputeService = inject(CreateVmComputeService),
        cloudInfraPermissionService = inject(CloudInfraPermissionService)
    ) => ({
        setOsType(osType: OsType, osTypeFilter: OsTypeFilter): void {
            patchState(store, state => ({ ...state, setupStep: { ...state.setupStep, form: { ...state.setupStep.form, osType, osTypeFilter } } }));
        },
        setTemplate(value: Partial<SetupStepFormValue>, isValid: boolean): void {
            patchState(store, state => ({ ...state, setupStep: { ...state.setupStep, form: value, isValid } }));
            if (value.osType === OsType.Template && value.template) {
                const rootDisk: DiskOffering = {
                    id: '',
                    offeringName: 'Default',
                    description: 'Default Root Disk',
                    diskSize: null,
                    isCustomized: false,
                };
                patchState(store, state => ({ ...state, storageStep: { ...state.storageStep, rootDisk, selectedDataDisks: [], isValid: true } }));
            } else {
                patchState(store, state => ({ ...state, storageStep: { ...state.storageStep, rootDisk: null, selectedDataDisks: [], isValid: false } }));
            }
        },
        setVirtualMachineName(value: string, isValid: boolean): void {
            patchState(store, state => ({ ...state, setupStep: { ...state.setupStep, form: { ...state.setupStep.form, virtualMachineName: value }, isValid } }));
        },
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        loadDataByZoneId: rxMethod<void>(pipe(switchMap(() => forkJoin([
            vmMediaService.getFeaturedTemplatesByZoneId(store.setupStep.form.zone().id),
            vmMediaService.getPublicTemplatesByZoneId(store.setupStep.form.zone().id, store.domainId(), store.account()),
            vmMediaService.getMyTemplatesByZoneId(store.setupStep.form.zone().id, store.domainId(), store.account()),
            vmMediaService.getFeaturedISOsByZoneId(store.setupStep.form.zone().id),
            vmMediaService.getPublicISOsByZoneId(store.setupStep.form.zone().id, store.domainId(), store.account()),
            vmMediaService.getMyISOsByZoneId(store.setupStep.form.zone().id, store.domainId(), store.account()),
            createVmComputeService.getServiceOfferings(store.setupStep.form.zone().id, store.domainId(), store.account()),
            createVmStorageService.getDiskOfferings(store.setupStep.form.zone().id, store.domainId(), store.account()),
            createVmNetworkService.getNetworks(store.setupStep.form.zone().id, store.domainId(), store.account()),
            createNetworkService.getIsolatedNetworkOfferings(store.setupStep.form.zone.id(), store.domainId()),
            createNetworkService.getVpcOfferings(store.setupStep.form.zone.id(), store.domainId(), store.account()),
            createNetworkService.getLayer2NetworkOfferings(store.setupStep.form.zone.id(), store.domainId()),
            cloudInfraPermissionService.isRootAdmin ? createNetworkService.getSharedNetworkOfferings(store.setupStep.form.zone.id(), store.domainId()) : of([])
        ])
            .pipe(tapResponse({
                next: res => patchState(store, state => ({
                    setupStep: {
                        ...state.setupStep, templates: {
                            ...state.setupStep.templates,
                            featuredTemplates: res[0],
                            publicTemplates: res[1],
                            myTemplates: res[2],
                            featuredISOs: res[3],
                            publicISOs: res[4],
                            myISOs: res[5]
                        },
                        zoneDataLoadingStatus: CreateVmRequestStatus.Success,
                    },
                    computeStep: {
                        ...state.computeStep, presetServiceOfferings: res[6].filter(offering => !offering.isCustom), customServiceOffering: res[6].find(offering => offering.isCustom),
                    },
                    storageStep: {
                        ...state.storageStep, diskOfferings: res[7]
                    },
                    networkStep: {
                        ...state.networkStep, networks: res[8], isolatedNetworkOfferings: res[9], vpcs: res[10], layer2NetworkOfferings: res[11], sharedNetworkOfferings: res[12]
                    }
                })),
                error: () => {
                    patchState(store, state => ({
                        setupStep: { ...state.setupStep, zoneDataLoadingStatus: CreateVmRequestStatus.Error }
                    }));
                    return EMPTY;
                }
            }))))),
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        loadAffinityGroups: rxMethod<void>(pipe(switchMap(() => vmAffinityGroupsService.getAffinityGroups(store.domainId(), store.account())
            .pipe(tapResponse({
                next: res => patchState(store, state => ({ advancedSettingsStep: { ...state.advancedSettingsStep, affinityGroups: res } })),
                error: () => EMPTY
            }))))),
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        loadSSHKeyPairs: rxMethod<void>(pipe(switchMap(() => vmManagementService
            .getKeyPairList(store.domainId(), store.account())
            .pipe(
                map((response: SSHKeyPair[]) => response.map(kp => ({ ...kp, id: kp.name.replace(' ', '-') }))),
                tapResponse({
                    next: res => patchState(store, state => ({ advancedSettingsStep: { ...state.advancedSettingsStep, sshKeyPairs: res } })),
                    error: () => EMPTY
                })
            )))),
        setSetupStepValidity(isValid: boolean): void {
            patchState(store, state => ({ ...state, setupStep: { ...state.setupStep, isValid } }));
        }
    })),
    withMethods(store => ({
        setComputeStepFormValue(value: Partial<ComputeStepFormValue>, isValid: boolean): void {
            patchState(store, state => ({ ...state, computeStep: { ...state.computeStep, form: value, isValid } }));
        },
        setComputeStepCustomOfferFormValue(cpuNumber: number, memory: number): void {
            patchState(store, state => ({ ...state, computeStep: { ...state.computeStep, customServiceOffering: { ...state.computeStep.customServiceOffering, cpuNumber, memory } } }));
        }
    })),
    withMethods(store => ({
        setRootDisk(diskOffering: DiskOffering): void {
            patchState(store, state => ({ ...state, storageStep: { ...state.storageStep, rootDisk: diskOffering } }));
        },
        setSelectedDataDisk(selectedOfferings: DiskOffering[]): void {
            patchState(store, state => ({ ...state, storageStep: { ...state.storageStep, selectedDataDisks: selectedOfferings } }));
        },
        setStorageStepValidity(isValid: boolean): void {
            patchState(store, state => ({ ...state, storageStep: { ...state.storageStep, isValid } }));
        },
        addDataDisk(diskOffering: DiskOffering): void {
            patchState(store, state => (
                { ...state, storageStep: { ...state.storageStep, selectedDataDisks: [...state.storageStep.selectedDataDisks, diskOffering] } }));
        },
        removeDataDisk(index: number): void {
            patchState(store, state => {
                const selectedOfferings = [...state.storageStep.selectedDataDisks];
                selectedOfferings.splice(index, 1);
                return { ...state, storageStep: { ...state.storageStep, selectedDataDisks: selectedOfferings } };
            });
        },
    })),
    withMethods(store => ({
        setSelectedNetworks(networks: VmNetwork[]): void {
            patchState(store, state => ({ ...state, networkStep: { ...state.networkStep, selectedNetworks: networks, isValid: !!networks?.length } }));
        },
        addSelectedNetwork(network: VmNetwork): void {
            patchState(store, state => (
                { ...state, networkStep: { ...state.networkStep, selectedNetworks: [...state.networkStep.selectedNetworks, network], isValid: true } }));
        },
        removeSelectedNetwork(networkId: string): void {
            patchState(store, state => {
                const networks = [...state.networkStep.selectedNetworks.filter(network => network.id !== networkId)];
                return { ...state, networkStep: { ...state.networkStep, selectedNetworks: networks, isValid: !!networks?.length } };
            });
        },
        addNetwork(network: VmNetwork): void {
            patchState(store, state => (
                { ...state, networkStep: { ...state.networkStep, networks: [...state.networkStep.networks, network] } }));
        },
    })),
    withMethods((store, createVmNetworkService = inject(CreateNetworkService)) => ({
        createIsolatedNetwork: rxMethod<CreateIsolatedNetworkRequest>(pipe(switchMap(request => {
            patchState(store, state => ({
                networkStep: {
                    ...state.networkStep, requestStatus: CreateVmRequestStatus.Pending
                }
            }));
            return createVmNetworkService.createIsolatedNetwork(store.setupStep.form.zone.id(), store.domainId(), store.account(), request)
                .pipe(tapResponse({
                    next: network => {
                        const vmNetwork: VmNetwork = mapNetwork(network);
                        store.addNetwork(vmNetwork);
                        store.addSelectedNetwork(vmNetwork);
                        patchState(store, state => ({
                            networkStep: {
                                ...state.networkStep, requestStatus: CreateVmRequestStatus.Success
                            }
                        }));
                    },
                    error: () => {
                        patchState(store, state => ({
                            networkStep: {
                                ...state.networkStep, requestStatus: CreateVmRequestStatus.Error
                            }
                        }));
                        return EMPTY;
                    }
                }));
        }))),
        createLayer2Network: rxMethod<CreateLayer2NetworkRequest>(pipe(switchMap(request => {
            patchState(store, state => ({
                networkStep: {
                    ...state.networkStep, requestStatus: CreateVmRequestStatus.Pending
                }
            }));
            return createVmNetworkService.createLayer2Network(store.setupStep.form.zone.id(), store.domainId(), store.account(), request)
                .pipe(tapResponse({
                    next: network => {
                        const vmNetwork: VmNetwork = mapNetwork(network);
                        store.addNetwork(vmNetwork);
                        store.addSelectedNetwork(vmNetwork);
                        patchState(store, state => ({
                            networkStep: {
                                ...state.networkStep, requestStatus: CreateVmRequestStatus.Success
                            }
                        }));
                    },
                    error: () => {
                        patchState(store, state => ({
                            networkStep: {
                                ...state.networkStep, requestStatus: CreateVmRequestStatus.Error
                            }
                        }));
                        return EMPTY;
                    }
                }));
        }))),
        createSharedNetwork: rxMethod<SharedNetworkRequest>(pipe(switchMap(request => {
            patchState(store, state => ({
                networkStep: {
                    ...state.networkStep, requestStatus: CreateVmRequestStatus.Pending
                }
            }));
            return createVmNetworkService.createSharedNetwork(store.setupStep.form.zone.id(), store.domainId(), store.account(), request)
                .pipe(tapResponse({
                    next: network => {
                        const vmNetwork: VmNetwork = mapNetwork(network);
                        store.addNetwork(vmNetwork);
                        store.addSelectedNetwork(vmNetwork);
                        patchState(store, state => ({
                            networkStep: {
                                ...state.networkStep, requestStatus: CreateVmRequestStatus.Success
                            }
                        }));
                    },
                    error: () => {
                        patchState(store, state => ({
                            networkStep: {
                                ...state.networkStep, requestStatus: CreateVmRequestStatus.Error
                            }
                        }));
                        return EMPTY;
                    }
                }));
        })))
    })),
    withMethods(store => ({
        setAdvancedSettings(value: AdvancedSettingsModel, isValid: boolean): void {
            patchState(store, state => ({ ...state, advancedSettingsStep: { ...state.advancedSettingsStep, selectedSettings: value, isValid } }));
        },
        toggleAdvanceSettingsDisplay(value: boolean): void {
            patchState(store, state => ({ ...state, advancedSettingsStep: { ...state.advancedSettingsStep, displaySettings: value } }));
        }
    })),
    withComputed(store => ({
        selectedSshKeyPairsNames: computed(() => store.advancedSettingsStep.selectedSettings.sshKeyPairs.options().map(kp => kp.name)),
        selectedAffinityGroupsNames: computed(() => store.advancedSettingsStep.selectedSettings.affinityGroups.options().map(kp => kp.name)),
    })),
    withMethods(store => ({
        reset(): void {
            const intitialStateWithDefaults = { ...initialState };
            intitialStateWithDefaults.domainId = store.domainId();
            intitialStateWithDefaults.account = store.account();
            intitialStateWithDefaults.zones = store.zones();
            intitialStateWithDefaults.advancedSettingsStep = { ...initialState.advancedSettingsStep, affinityGroups: store.advancedSettingsStep.affinityGroups(), sshKeyPairs: store.advancedSettingsStep.sshKeyPairs() };
            patchState(store, { ...intitialStateWithDefaults });
        },
        navigateToStep(step: CreateVmWizardStepEnum): void {
            patchState(store, { currentStep: step });
        }
    })),
    withMethods((store, createVmService = inject(CreateVmService), vmMediaService = inject(VmMediaService), vmManagementService = inject(VmManagementService)) => ({
        createVirtualMachine: rxMethod<boolean>(pipe(switchMap(startVm => {
            patchState(store, state => ({
                create: {
                    ...state.create, requestStatus: CreateVmRequestStatus.Pending, createVirtualMachineJobId: null, startVirtualMachineJobId: null, virtualMachineId: null
                }
            }));

            const deployVirtualMachineRequest: CreateVirtualMachineRequest = {
                affinityGroups: store.advancedSettingsStep.selectedSettings.affinityGroups()?.options?.map(ag => ag.id) || [],
                diskOfferingId: store.setupStep.form.osType() === OsType.ISO ? store.storageStep.rootDisk.id() : null,
                hypervisor: 'KVM',
                keyboardLanguage: store.advancedSettingsStep.selectedSettings.keyboardLanguage(),
                keypairs: store.advancedSettingsStep.selectedSettings.sshKeyPairs()?.options?.map(kp => kp.name),
                name: store.setupStep.form.virtualMachineName(),
                networkIds: store.networkStep.selectedNetworks().map(network => network.id),
                rootDiskSize: store.setupStep.form.osType() === OsType.Template ? store.storageStep.rootDisk.diskSize() : null,
                serviceOffering: store.computeStep.form.serviceOffering(),
                size: store.setupStep.form.osType() === OsType.ISO && store.storageStep.rootDisk.isCustomized() ? store.storageStep.rootDisk.diskSize() : null,
                templateId: store.setupStep.form.template.id(),
                userdata: store.advancedSettingsStep.selectedSettings.userdata(),
                zoneId: store.setupStep.form.zone.id()
            };

            return createVmService.createVirtualMachine(deployVirtualMachineRequest, store.domainId(), store.account())
                .pipe(
                    mergeMap(createVmResult => {

                        patchState(store, state => ({
                            create: {
                                ...state.create,
                                createVirtualMachineJobId: createVmResult.jobId,
                                virtualMachineId: createVmResult.virtualMachineId
                            }
                        }));

                        if (store.storageStep.selectedDataDisks()?.length) {
                            const requests: Observable<string>[] =
                                store.storageStep.selectedDataDisks()?.map(disk => vmMediaService.createVolume(createVmResult.virtualMachineId, disk.id, store.setupStep.form.zone.id(), store.domainId(), store.account(), disk.isCustomized ? disk.diskSize : null));
                            return forkJoin(requests).pipe(mergeMap(() => {
                                if (startVm) {
                                    return vmManagementService.startVirtualMachine(createVmResult.virtualMachineId, null, null, null, null).pipe(tapResponse({
                                        next: startVmResult => {
                                            patchState(store, state => ({
                                                create: {
                                                    ...state.create,
                                                    startVirtualMachineJobId: startVmResult
                                                }
                                            }));
                                        },
                                        error: e => {
                                            throw e;
                                        }
                                    }));
                                }
                                return of(true);
                            }), catchError(e => {
                                throw e;
                            }));
                        }

                        if (startVm) {
                            return vmManagementService.startVirtualMachine(createVmResult.virtualMachineId, null, null, null, null).pipe(tapResponse({
                                next: startVmResult => {
                                    patchState(store, state => ({
                                        create: {
                                            ...state.create,
                                            startVirtualMachineJobId: startVmResult
                                        }
                                    }));
                                },
                                error: e => {
                                    throw e;
                                }
                            }));
                        }

                        return of(true);

                    }),
                    tapResponse({
                        next: () => {
                            patchState(store, state => ({
                                create: {
                                    ...state.create, requestStatus: CreateVmRequestStatus.Success
                                }
                            }));
                        },
                        error: () => {
                            patchState(store, state => ({
                                create: {
                                    ...state.create, requestStatus: CreateVmRequestStatus.Error
                                }
                            }));
                            return EMPTY;
                        }
                    })
                );
        }))),
    })),
    withMethods(store => ({
        setZone(value: ZoneViewModel): void {
            // Temp store the vm name so it can be restored after resetting the state
            const virtualMachineName = store.setupStep.form.virtualMachineName();
            store.reset();
            patchState(store, state => ({ ...state, setupStep: { ...state.setupStep, zoneDataLoadingStatus: CreateVmRequestStatus.Pending, form: { ...state.setupStep.form, zone: value, virtualMachineName } } }));
            store.loadDataByZoneId();
        },
    })),
    withComputed(store => ({
        isSetupAccessible: computed(() => true),
        isComputeAccessible: computed(() => true),
        isStorageAccessible: computed(() => store.setupStep.form.template() !== null),
        isNetworkAccessible: computed(() => true),
        isAdvancedSettingsAccessible: computed(() => !!store.advancedSettingsStep.affinityGroups() && !!store.advancedSettingsStep.sshKeyPairs()),
        isWizardValid: computed(() => store.setupStep.isValid() && store.computeStep.isValid() && store.storageStep.isValid() && store.networkStep.isValid() && store.advancedSettingsStep.isValid()),
        isLastStep: computed(() => store.currentStep() === store.totalSteps() - 1),
    })),
    withComputed(store => ({
        canCreateVirtualMachine: computed(() => store.isWizardValid() && store.create.requestStatus() !== CreateVmRequestStatus.Pending)
    })),
    withHooks({
        onInit(store, domainAccountTreeStore = inject(ZoneDomainAccountStore)) {
            patchState(store, {
                domainId: domainAccountTreeStore.getDomainId(),
                account: domainAccountTreeStore.getAccount(),
                zones: domainAccountTreeStore.zones()
            });
            store.loadAffinityGroups();
            store.loadSSHKeyPairs();
        }
    })
);
