import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { filter } from 'rxjs';
import { OsTypeFilter } from '../../../../models/os-type-filter.enum';
import { OsType } from '../../../../models/os-type.enum';
import { TemplateViewModel } from '../../../../models/template.view.model';
import { MediaSelectorComponent } from '../../../media-selector/media-selector.component';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { SetupStepForm } from '../../forms/setup-step.form';
import { CreateVmRequestStatus } from '../../models/create-vm-request-status.emun';
import { CreateVmWizardConstants } from '../../models/create-vm-wizard.constants';

@Component({
    selector: 'app-create-vm-wizard-setup',
    imports: [ReactiveFormsModule, MediaSelectorComponent],
    templateUrl: './setup.component.html',
    styleUrls: ['../../create-vm-wizard-common.scss', './setup.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmWizardSetupComponent implements OnInit {

    public readonly store = inject(CreateVMWizardStore);
    private readonly destroyRef = inject(DestroyRef);
    private readonly formBuilder = inject(FormBuilder);

    protected readonly form = this.formBuilder.group<SetupStepForm>({
        zone: this.formBuilder.control<ZoneViewModel>(this.store.setupStep.form.zone(), Validators.required),
        template: this.formBuilder.control<TemplateViewModel>(this.store.setupStep.form.template(), Validators.required),
        osType: this.formBuilder.control<OsType>(this.store.setupStep.form.osType()),
        osTypeFilter: this.formBuilder.control<OsTypeFilter>(this.store.setupStep.form.osTypeFilter()),
        // From CI api inmplmentation: VM name can contain ASCII letters 'a' through 'z', the digits '0' through '9', and the hyphen ('-'), must be between 1 and 63 characters long, and can't start or end with \"-\" and can't start with digit"
        virtualMachineName: this.formBuilder.control<string>(this.store.setupStep.form.virtualMachineName(), [Validators.minLength(1), Validators.maxLength(63), Validators.pattern(/^[a-zA-Z]([a-zA-Z0-9-]*[a-zA-Z0-9])?$/)])
    });

    protected readonly label = CreateVmWizardConstants.SetupLabel;
    protected readonly osType = OsType;
    protected readonly osTypeFilter = OsTypeFilter;

    protected readonly filteredTemplates = computed(() => {
        const featuredTemplates = this.store.setupStep.templates.featuredTemplates();
        const publicTemplates = this.store.setupStep.templates.publicTemplates();
        const myTemplates = this.store.setupStep.templates.myTemplates();
        const featuredISOs = this.store.setupStep.templates.featuredISOs();
        const publicISOs = this.store.setupStep.templates.publicISOs();
        const myISOs = this.store.setupStep.templates.myISOs();

        if (this.store.setupStep.form.osType() === OsType.Template) {
            switch (this.store.setupStep.form.osTypeFilter()) {
                case OsTypeFilter.Featured:
                    return featuredTemplates;
                case OsTypeFilter.Public:
                    return publicTemplates;
                case OsTypeFilter.MyTemplates:
                    return myTemplates;
            }
        } else if (this.store.setupStep.form.osType() === OsType.ISO) {
            switch (this.store.setupStep.form.osTypeFilter()) {
                case OsTypeFilter.Featured:
                    return featuredISOs;
                case OsTypeFilter.Public:
                    return publicISOs;
                case OsTypeFilter.MyTemplates:
                    return myISOs;
            }
        }
        return [];
    });

    protected readonly createVmRequestStatus = CreateVmRequestStatus;

    ngOnInit(): void {

        this.form.controls.template.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.form.updateValueAndValidity();
                this.store.setTemplate(this.form.value, this.form.valid);
            });

        this.form.controls.zone.valueChanges.pipe(
            filter(zone => !!zone),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(value => {
            this.form.patchValue({
                osType: OsType.Template,
                osTypeFilter: OsTypeFilter.Featured,
                template: null
            });
            this.form.updateValueAndValidity();
            this.store.setZone(value);
        });

        this.form.controls.virtualMachineName.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.form.updateValueAndValidity({ emitEvent: false});
                this.store.setVirtualMachineName(value, this.form.valid);
            });

        this.form.controls.osType.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.store.setOsType(value, this.form.controls.osTypeFilter.value);
            });

        this.form.controls.osTypeFilter.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.store.setOsType(this.form.controls.osType.value, value);
            });
    }

}
