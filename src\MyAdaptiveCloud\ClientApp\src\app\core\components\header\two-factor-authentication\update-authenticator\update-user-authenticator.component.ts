import { ChangeDetectionStrategy, Component, DestroyRef, OnInit, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { UpdateUserAuthenticatorRequest } from '@app/core/requests/update-user-authenticator.request';
import { TwoFactorAuthenticationService } from '@app/core/services/two-factor-authentication.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UpdateUserAuthenticatorForm } from './update-user-authenticator.form';

@Component({
    selector: 'app-update-user-authenticator',
    imports: [ReactiveFormsModule, BtnSubmitComponent],
    templateUrl: './update-user-authenticator.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UpdateUserAuthenticatorComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly twoFactorAuthenticationService = inject(TwoFactorAuthenticationService);
    private readonly notificationService = inject(NotificationService);
    private readonly destroyRef = inject(DestroyRef);

    readonly userAuthenticatorId = signal<string>(null);
    readonly userAuthenticatorName = signal<string>(null);

    public readonly isSubmitted = signal(false);
    public readonly form = signal<FormGroup<UpdateUserAuthenticatorForm>>(null);

    ngOnInit(): void {
        this.form.set(this.formBuilder.group<UpdateUserAuthenticatorForm>({
            name: this.formBuilder.control(this.userAuthenticatorName(), [Validators.required, Validators.maxLength(100)])
        }));
    }

    submitForm(): void {
        this.isSubmitted.set(true);
        if (this.form().valid) {
            const request: UpdateUserAuthenticatorRequest = {
                name: this.form().controls.name.value
            };
            this.twoFactorAuthenticationService.updateUserAuthenticator(this.userAuthenticatorId(), request)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });
        }
    }
}
