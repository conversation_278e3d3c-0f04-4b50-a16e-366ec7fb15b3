import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { DomainAccountTreeComponent } from '@app/modules/cloud-infrastructure/components/domain-account-tree/domain-account-tree.component';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { STORAGE_ROUTE_SEGMENTS } from '../models/route.segments';

@Component({
    selector: 'app-storage-management',
    imports: [RouterLink, RouterOutlet, DomainAccountTreeComponent],
    templateUrl: './storage-management.component.html',
    styleUrl: './storage-management.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class StorageManagementComponent {
    protected readonly store = inject(ZoneDomainAccountStore);
    protected readonly routes = STORAGE_ROUTE_SEGMENTS;
}

