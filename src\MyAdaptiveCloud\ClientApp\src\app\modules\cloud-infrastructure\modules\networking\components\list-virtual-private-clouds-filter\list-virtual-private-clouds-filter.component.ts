import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { VirtualPrivateCloudListFilters } from '../../requests/virtual-private-cloud-list.filter';

@Component({
    selector: 'app-list-virtual-private-clouds-filter',
    imports: [ReactiveFormsModule],
    templateUrl: './list-virtual-private-clouds-filter.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListVirtualPrivateCloudsFilterComponent {
    protected readonly zones = input.required<ZoneViewModel[]>();
    protected readonly filters = input.required<VirtualPrivateCloudListFilters>();

    get form(): FormGroup {
        return this.filters().form;
    }

    get filterGroupOptions() {
        return this.filters().filterGroupOptions;
    }
}
