<div class="content-heading">
    <div class="header-title">Tasks</div>
</div>

<div class="content-sub-heading">
    <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap no-detail-row" />
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #dateCellTemplate let-value="value">
    <span>
        {{ (value) ? (value | date: 'yyyy-MM-dd HH:mm:ss') : '-' }}
    </span>
</ng-template>

<ng-template #statusTemplate let-row="row">
    <span class="ps-3">
        @if (toItem(row); as row) {
            @switch (row.status) {
                @case ('WARNING') {
                    <i [title]="taskStatus.Warning" class="fa fa-warning text-warning"></i>
                }
                @case ('FAILED') {
                    <i [title]="taskStatus.Failed" class="fa fa-warning text-danger"></i>
                }
                @case ('SUCCESS') {
                    <i [title]="taskStatus.Successful" class="fa fa-circle-check text-success"></i>
                }
            }
        }
    </span>
</ng-template>

<ng-template #messageTemplate let-row="row">
    @if (toItem(row); as row) {
        @let title = row.messages?.slice(-3).reverse().join('\n');
        <span [title]="title">{{ row.messages?.length ? row.messages[row.messages.length - 1] : ''  }}</span>
    }
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        <app-table-action (clickHandler)="viewMessages(row)" [icon]="'icon-viewDetails'" [title]="'View Messages'" />
    }
</ng-template>
