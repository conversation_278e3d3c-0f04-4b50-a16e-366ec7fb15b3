import { JobQueueStatus } from '@app/shared/models/job-queue/job-queue-status.enum';
import { JobQueueType } from '@app/shared/models/job-queue/job-queue-type.enum';

export interface NotificationTrayItem {
    id: string;
    title: string;
    elapsedTime: string;
    event: string;
    eventDescription: string;
    isRead: boolean;
    status: JobQueueStatus;
    type: JobQueueType;
    createdDateUtcIsoString: string;
}
