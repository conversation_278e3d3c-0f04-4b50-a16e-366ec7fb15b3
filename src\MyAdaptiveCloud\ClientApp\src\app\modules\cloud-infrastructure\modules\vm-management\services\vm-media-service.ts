import { inject, Injectable } from '@angular/core';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { forkJoin, map, Observable } from 'rxjs';
import { TemplateFilterEnum } from '../models/template-filter.enum';
import { TemplateModel } from '../models/template.model';
import { TemplateViewModel } from '../models/template.view.model';
import { AttachIsoResponse } from '../responses/attach-iso.response';
import { CreateVolumeResponse } from '../responses/create-volume.response';
import { DetachIsoResponse } from '../responses/detach-iso.response';
import { ListIsoResponse } from '../responses/list-iso.response';
import { ListTemplateResponse } from '../responses/list-template.response';
import { byteToGB } from '../utils/functions';

@Injectable({
    providedIn: 'root'
})
export class VmMediaService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getFeaturedISOsByZoneId(zoneId: string): Observable<TemplateViewModel[]> {
        // Featured ISOs should not include domainId and accountId in the request, even though it is supported

        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listIsos,
            listall: 'true',
            details: 'min',
            bootable: 'true',
            zoneId,
            isoFilter: TemplateFilterEnum.Featured.toString()
        };

        return this.cloudInfraApiService.get<ListIsoResponse>(params)
            .pipe(map((response: ListIsoResponse) => (response.listisosresponse?.iso ?? [])
                .filter(t => !!t)
                .sort(sortByProperty('name'))
                .map(t => this.mapTemplateToViewModel(t))));
    }

    getPublicISOsByZoneId(zoneId: string, domainId: string, account: string): Observable<TemplateViewModel[]> {
        const paramsCommunity: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listIsos,
            listall: 'true',
            details: 'min',
            bootable: 'true',
            zoneId,
            isoFilter: TemplateFilterEnum.Community.toString()
        };

        const paramsSharedExecutable: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listIsos,
            listall: 'true',
            details: 'min',
            bootable: 'true',
            zoneId,
            isoFilter: TemplateFilterEnum.SharedExecutable.toString()
        };

        paramsCommunity[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        paramsSharedExecutable[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        paramsCommunity[CloudInfraParamsEnum.ACCOUNT] = account;
        paramsSharedExecutable[CloudInfraParamsEnum.ACCOUNT] = account;

        return forkJoin([
            this.cloudInfraApiService.get<ListIsoResponse>(paramsCommunity),
            this.cloudInfraApiService.get<ListIsoResponse>(paramsSharedExecutable)
        ])
            .pipe(map((response: ListIsoResponse[]) => (response[0].listisosresponse?.iso ?? [])
                .concat((response[1].listisosresponse?.iso ?? []))
                .filter(t => !!t)
                .sort(sortByProperty('name'))
                .map(t => this.mapTemplateToViewModel(t))));
    }

    getMyISOsByZoneId(zoneId: string, domainId: string, account: string): Observable<TemplateViewModel[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listIsos,
            listall: 'true',
            details: 'min',
            bootable: 'true',
            zoneId,
            isoFilter: TemplateFilterEnum.SelfExecutable.toString()
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListIsoResponse>(params)
            .pipe(map((response: ListIsoResponse) => (response.listisosresponse?.iso ?? [])
                .filter(t => !!t)
                .sort(sortByProperty('name'))
                .map(t => this.mapTemplateToViewModel(t))));
    }

    getFeaturedTemplatesByZoneId(zoneId: string): Observable<TemplateViewModel[]> {
        // Featured templates should not include domainId and accountId in the request, even though it is supported

        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listTemplates,
            listall: 'true',
            details: 'min',
            zoneId,
            templatefilter: TemplateFilterEnum.Featured.toString()
        };

        return this.cloudInfraApiService.get<ListTemplateResponse>(params)
            .pipe(map((response: ListTemplateResponse) => (response.listtemplatesresponse?.template ?? [])
                .filter(t => !!t)
                .sort(sortByProperty('name'))
                .map(t => this.mapTemplateToViewModel(t))));
    }

    getPublicTemplatesByZoneId(zoneId: string, domainId: string, account: string): Observable<TemplateViewModel[]> {
        const paramsCommunity: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listTemplates,
            listall: 'true',
            details: 'min',
            zoneId,
            templatefilter: TemplateFilterEnum.Community.toString()
        };

        const paramsSharedExecutable: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listTemplates,
            listall: 'true',
            details: 'min',
            zoneId,
            templatefilter: TemplateFilterEnum.SharedExecutable.toString()
        };

        if (domainId) {
            paramsCommunity[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
            paramsSharedExecutable[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
            paramsCommunity[CloudInfraParamsEnum.ACCOUNT] = account;
            paramsSharedExecutable[CloudInfraParamsEnum.ACCOUNT] = account;
        }

        return forkJoin([
            this.cloudInfraApiService.get<ListTemplateResponse>(paramsCommunity),
            this.cloudInfraApiService.get<ListTemplateResponse>(paramsSharedExecutable)
        ])
            .pipe(map((response: ListTemplateResponse[]) => (response[0].listtemplatesresponse?.template ?? [])
                .concat((response[1].listtemplatesresponse?.template))
                .filter(t => !!t)
                .sort(sortByProperty('name'))
                .map(t => this.mapTemplateToViewModel(t))));
    }

    getMyTemplatesByZoneId(zoneId: string, domainId: string, account: string): Observable<TemplateViewModel[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listTemplates,
            listall: 'true',
            details: 'min',
            zoneId,
            templatefilter: TemplateFilterEnum.SelfExecutable.toString()
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListTemplateResponse>(params)
            .pipe(map((response: ListTemplateResponse) => (response.listtemplatesresponse?.template ?? [])
                .filter(t => !!t)
                .sort(sortByProperty('name'))
                .map(t => this.mapTemplateToViewModel(t))));
    }

    attachIso(id: string, isoId: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.attachIso,
            virtualmachineid: id,
            id: isoId
        };

        return this.cloudInfraApiService.get<AttachIsoResponse>(params).pipe(map(response => response.attachisoresponse?.jobid));
    }

    detachIso(id: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.ejectIso,
            virtualmachineid: id
        };

        return this.cloudInfraApiService.get<DetachIsoResponse>(params).pipe(map(response => response.detachisoresponse?.jobid));
    }

    createVolume(id: string, diskOfferingId: string, zoneId: string, domainId: string, account: string, size: number | null): Observable<string> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createVolume,
            response: 'json',
            virtualmachineid: id,
            diskofferingid: diskOfferingId,
            zoneid: zoneId
        };

        if (size) {
            params['size'] = size.toString();
        }

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<CreateVolumeResponse>(params)
            .pipe(map(res => res.createvolumeresponse?.jobid));
    }

    private mapTemplateToViewModel(template: TemplateModel): TemplateViewModel {
        return {
            description: template.displaytext,
            id: template.id,
            name: template.name,
            size: parseFloat(byteToGB(template.size).toFixed(2))
        };
    }

}
