import { inject, Injectable } from '@angular/core';
import { UserContextService } from '@app/shared/services/user-context.service';

@Injectable({
    providedIn: 'root'
})
export class CloudInfraPermissionService {

    private readonly userContextService = inject(UserContextService);

    public isAdmin() {
        return this.userContextService.currentUser.cloudInfraUserContext.roleType !== 'User';
    }

    public isRootAdmin() {
        return this.isAdmin() && this.userContextService.currentUser.cloudInfraUserContext.roleName === 'Root Admin';
    }

}
