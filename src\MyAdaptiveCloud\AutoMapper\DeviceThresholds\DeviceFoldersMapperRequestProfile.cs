﻿using AutoMapper;

namespace MyAdaptiveCloud.Api.AutoMapper.DeviceThresholds
{
    public class DeviceFoldersMapperRequestProfile : Profile
    {
        public DeviceFoldersMapperRequestProfile()
        {
            CreateMap<Requests.DeviceThresholds.UpdateDeviceThresholdsRequest, Services.Requests.DeviceThresholds.UpdateDeviceThresholdsRequest>();
            CreateMap<Requests.DeviceThresholds.UpdateDeviceComponentThresholdsRequest, Services.Requests.DeviceThresholds.UpdateDeviceComponentThresholdsRequest>();
            CreateMap<Requests.DeviceThresholds.UpdateDeviceThresholdIntervalsRequest, Services.Requests.DeviceThresholds.DeviceThresholdIntervalsRequest>();
            CreateMap<Requests.DeviceThresholds.UpdateDeviceThresholdsMetricsRequest, Services.Requests.DeviceThresholds.UpdateDeviceThresholdsMetricsRequest>();
        }
    }
}