import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { CPU_CUSTOM_OFFERING_MIN_VALUE, MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';

export function customOfferMemoryValidator(customComputeMaxAllowedMemory: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {

        if ((customComputeMaxAllowedMemory && control.value > customComputeMaxAllowedMemory) || control.value < MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB) {
            return { memory: true };
        }
        return null;

    };
}

export function customOfferCpuNumberValidator(customComputeMaxAllowedCpuCores: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {

        if ((customComputeMaxAllowedCpuCores && control.value > customComputeMaxAllowedCpuCores) || control.value < CPU_CUSTOM_OFFERING_MIN_VALUE) {
            return { cpuNumber: true };
        }

        return null;

    };
}

