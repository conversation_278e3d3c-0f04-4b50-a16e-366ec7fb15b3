import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { filter, startWith } from 'rxjs';
import { CreateVirtualPrivateCloudForm } from '../../forms/create-virtual-private.form';
import { VirtualPrivateCloudOfferingViewModel } from '../../models/virtual-private-cloud-offering.view-model';
import { CreateVirtualPrivateCloudRequest } from '../../requests/create-virtual-private-cloud.request';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';
import { validateCidr } from '@app/modules/cloud-infrastructure/validators/network-validators';

@Component({
    selector: 'app-create-virtual-private-cloud',
    imports: [BtnSubmitComponent, NgSelectComponent, ReactiveFormsModule, NgbPopover],
    templateUrl: './create-virtual-private-cloud.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVirtualPrivateCloudComponent implements OnInit {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);
    private readonly virtualPrivateCloudService = inject(VirtualPrivateCloudService);

    readonly zones = signal<ZoneViewModel[]>([]);
    readonly domainId = signal<string>(null);
    readonly account = signal<string>(null);

    protected readonly vpcOfferings = signal<VirtualPrivateCloudOfferingViewModel[]>([]);
    protected readonly form = signal<FormGroup<CreateVirtualPrivateCloudForm>>(null);

    ngOnInit(): void {

        const form = this.formBuilder.group<CreateVirtualPrivateCloudForm>({
            cidr: this.formBuilder.control<string>('', [Validators.required, validateCidr()]),
            description: this.formBuilder.control<string | null>(null, Validators.maxLength(255)),
            name: this.formBuilder.control<string>('', [Validators.required, Validators.maxLength(255)]),
            networkDomain: this.formBuilder.control('', Validators.maxLength(100)),
            vpcOffering: this.formBuilder.control(null, Validators.required),
            zone: this.formBuilder.control<string>(this.zones()[0].id, Validators.required)
        });

        form.controls.zone.valueChanges
            .pipe(
                startWith(form.controls.zone.value),
                filter(zone => !!zone),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(zone => {
                this.virtualPrivateCloudService.getVpcOfferings(zone)
                    .subscribe(vpcOfferings => this.vpcOfferings.set(vpcOfferings));
            });

        this.form.set(form);
    }

    protected submit() {
        if (this.form().valid) {
            const formValue = this.form().value;
            const request: CreateVirtualPrivateCloudRequest = {
                cidr: formValue.cidr,
                description: formValue.description,
                name: formValue.name,
                networkDomain: formValue.networkDomain,
                vpcOfferingId: formValue.vpcOffering,
                zoneId: formValue.zone
            };
            this.virtualPrivateCloudService.createVirtualPrivateCloud(request, this.domainId(), this.account())
                .subscribe(() => {
                    this.activeModal.close();
                });
        }
    }

}
