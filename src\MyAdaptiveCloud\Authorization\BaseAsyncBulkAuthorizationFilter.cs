using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Core.Permissions;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    ///     Verifies that the caller is authorized to exectute the action.
    ///     It inspects the request body property that matches the supplied <see cref="_name"/> and checks if the caller has permissions on each element.
    ///     The request body parameter must be named "request".
    /// </summary>
    public abstract class BaseAsyncBulkAuthorizationFilter : ActionFilterAttribute, IAsyncActionFilter
    {
        protected readonly string _name;
        protected readonly int _distance;
        protected readonly Perms[] _perms;
        protected const string _requestName = "request";

        public BaseAsyncBulkAuthorizationFilter(Perms[] perms, int distance = 0, string name = null)
        {
            _name = name;
            _perms = perms;
            _distance = distance;
        }

    }

}