﻿using AutoMapper;
using MyAdaptiveCloud.Api.Requests.Ontap;
using MyAdaptiveCloud.Services.DTOs.Ontap;

namespace MyAdaptiveCloud.Api.AutoMapper.OnTap
{
    public class OnTapMappingProfile : Profile
    {
        public OnTapMappingProfile()
        {
            CreateMap<CreateFileServerRequest, CreateFileServerDTO>();
            CreateMap<UpdateFileServerRequest, UpdateFileServerDTO>();
            CreateMap<JoinWorkgroupRequest, JoinWorkgroupDTO>();
            CreateMap<EditWorkgroupRequest, EditWorkgroupDTO>();
            CreateMap<ConfigureFileServerForAdRequest, ConfigureFileServerForAdDTO>();
            CreateMap<AddUserOrGroupToShareRequest, UserOrGroupToShareDTO>();

        }
    }
}
