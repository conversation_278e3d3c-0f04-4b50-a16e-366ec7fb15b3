@use "sass:color";
@import "custom-variables.scss";

$sidebar-bg: #FFFFFF;
$sidebar-bg-active: #eeeeee;
$sidebar-item-color: #515253;
$sidebar-item-color-active: $primary;
$sidebar-item-bg-active: color.adjust($sidebar-bg, $lightness: 1%);

$sidebar-icon-color: inherits;
$sidebar-icon-color-active: $primary;

$sidebar-bullet-color: rgba(0, 0, 0, 0.25);
$sidebar-bullet-color-active: $sidebar-icon-color-active;

$sidebar-heading-color: #919da8;
$sidebar-label-color: $sidebar-item-color;
$sidebar-item-border-wd: 3px;
$nav-horizontal-padding: 15px;
$nav-collapsed-horizontal-padding: 10px;

$icon-total-tech--filter: invert(43%) sepia(20%) saturate(1373%) hue-rotate(160deg) brightness(93%) contrast(95%);

.aside-inner {
    overflow-y: auto;
    width: 280px;
    height: 100%;
    background-color: $sidebar-bg;

    .toggle-nav-btn {
        border: none;
        background-color: transparent;
    }

    &.collapsed {
        width: max-content;

        .sidebar-nav {
            padding-right: 0.5rem;
        }
       
        .user-initial {
            margin: 1rem 0;
            border-radius: 50%;
            background-color: $secondary;
            aspect-ratio: 1;
            width: 1.6rem;

            &>h3 {
                color: $sidebar-bg;
                font-size: 1rem;
            }
        }

        .sidebar {
            background-color: $secondary-25;
            padding-left: $nav-collapsed-horizontal-padding;
            padding-right: $nav-collapsed-horizontal-padding;
            width: min-content;
            height: 100%;
        }

        ul.sidebar-nav>li {
            background-color: transparent;
        }

        .collapsed-nav-item-icon {
            font-size: large;

            &.active-icon {
                color: $primary;
            }

            &.active-icon.icon-total-tech-menu-item {
                filter: $icon-total-tech--filter;
            }
        }

        .collapsed .icon-total-tech-menu-item {
            width: 1.7em;
            margin-left: 7px;
        }
    }

    .sidebar {
        background-color: $sidebar-bg;
        overflow-x: hidden;
        height: min-content;
        border-right: 1px solid #e4eaec;
        padding-left: $nav-horizontal-padding;
        padding-right: $nav-horizontal-padding;
        padding-bottom: 10px;

        .nav-heading {
            padding: 12px 15px;
            color: $sidebar-heading-color;
            font-size: 0.8125rem;
            letter-spacing: 0.035em;
            pointer-events: none;
            cursor: default;
        }
    }

    // Items
    // First main level
    .sidebar-nav {
        position: relative;
        font-size: 0.875rem;
        margin-bottom: 0;
        padding-left: 0;
        list-style: none;

        >li {
            background-color: $sidebar-bg;

            >a {
                position: relative;
                display: block;
                padding: 10px 0px;
                color: $secondary;
                letter-spacing: 0.025em;
                font-weight: normal;

                &:focus,
                &:hover {
                    text-decoration: none;
                    outline: none;
                    color: $primary;

                    >em {
                        color: $primary;
                    }
                }

                >em {
                    width: 1.8em;
                    color: $secondary;
                }
            }

            // Active item state

            &.active {

                >a {
                    font-weight: bold !important;
                    color: $primary;

                    >em {
                        color: $primary;
                    }
                }

                .icon-total-tech-menu-item {
                    filter: $icon-total-tech--filter;
                }
            }

            &:hover {
                .icon-total-tech-menu-item {
                    filter: $icon-total-tech--filter;
                }

            }
        }
    }

    // Sub Levels
    .sidebar-subnav {

        >li {
            >a {
                padding: 10px 20px 10px 26px;
                color: $secondary;
            }
        }
    }

    .reminder-icon {
        font-size: 0.625rem;
        color: $error;
    }

    .active-submenu {
        color: $primary;
        background-color: $primary-10;
        font-weight: bold !important;
        border-radius: 10px;
    }
}

.aside-inner:not(.collapsed) .sidebar-nav > li > a {
    cursor: pointer;
}