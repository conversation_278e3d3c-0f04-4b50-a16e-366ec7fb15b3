import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NetworkListFilters } from '../../requests/network-list.filter';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';

@Component({
    selector: 'app-list-networks-filter',
    imports: [ReactiveFormsModule],
    templateUrl: './list-networks-filter.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListNetworksFilterComponent {
    readonly filters = input.required<NetworkListFilters>();
    readonly zones = input.required<ZoneViewModel[]>();

    get form(): FormGroup {
        return this.filters().form;
    }

    get filterGroupOptions() {
        return this.filters().filterGroupOptions;
    }
}
