import { DynamicListFilterGroupOptions } from '@app/shared/models/datatable/filters/dynamic-list-filter-group-options';
import { FilterGroupAndRequest } from '@app/shared/models/datatable/filters/filter-group-and-request';
import { VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS } from '../models/virtual-private-cloud-list.constants';

export class VirtualPrivateCloudListFilters extends FilterGroupAndRequest {

    constructor() {
        super();
        this.orderBy = 'name';
        this.orderDir = 'asc';
        this.filterGroupOptions.push(new DynamicListFilterGroupOptions('Zone', VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.zoneIdKey));
    }
}

