import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UpdateReleaseTagConfirmationModel } from '../../../models/update-release-tag-confirmation.model';

@Component({
    selector: 'app-update-release-tags-confirmation-modal',
    imports: [DatePipe],
    templateUrl: './update-release-tags-confirmation-modal.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UpdateReleaseTagsModalComponent {

    protected readonly activeModal = inject(NgbActiveModal);

    readonly releaseTags = signal<UpdateReleaseTagConfirmationModel[]>([]);
}
