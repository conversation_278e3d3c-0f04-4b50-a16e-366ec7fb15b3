﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class DeviceAlertRulesAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public DeviceAlertRulesAuthorizeFilter(
            IIdentityService identityService,
            IUserContextService userContextService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int alertRulesId);

            var deviceAlertRuleOrganizationId = await _entityAuthorizationService.GetDeviceAlertRuleOrganizationId(alertRulesId);

            if (deviceAlertRuleOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, deviceAlertRuleOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, deviceAlertRuleOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class DeviceAlertRulesAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public DeviceAlertRulesAuthorizeAttribute(params Perms[] perms) : base(typeof(DeviceAlertRulesAuthorizeFilter), perms)
        {
            Name = "alertRuleId";
        }
    }
}