import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmMediaService } from '../../services/vm-media-service';
import { AttachIsoComponent } from './attach-iso.component';

describe('AttachIsoComponent', () => {

    let component: AttachIsoComponent;
    let fixture: ComponentFixture<AttachIsoComponent>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [AttachIsoComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmMediaService),
                FormBuilder
            ]
        })
            .compileComponents();

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.attachIso.and.returnValue(of('jobId1'));
        mockVmMediaService.getFeaturedISOsByZoneId.and.returnValue(of([{ name: 'Featured ISO', id: 'iso1', size: 10000, description: 'Featured ISO' }]));
        mockVmMediaService.getPublicISOsByZoneId.and.returnValue(of([{ name: 'Public ISO', id: 'iso2', size: 10000, description: 'Public ISO' }]));
        mockVmMediaService.getMyISOsByZoneId.and.returnValue(of([{ name: 'My ISO', id: 'iso3', size: 10000, description: 'My ISO' }]));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        fixture = TestBed.createComponent(AttachIsoComponent);
        component = fixture.componentInstance;
        component.inputData.set({
            virtualMachineId: 'test-id',
            virtualMachineZoneId: 'zone-id',
            domainId: 'domain',
            account: 'account'
        });

        fixture.detectChanges();
    });

    describe('Submit', () => {

        it('should close modal on cancel', () => {
            const cancelButton = fixture.debugElement.queryAll(By.css('.btn.btn-outline-secondary'))[1].nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit the form with the first featured iso', () => {

            const firstItem = fixture.debugElement.queryAll(By.css('.form-check-input'))[0].nativeElement as HTMLInputElement;
            firstItem.checked = true;
            firstItem.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmMediaService.getFeaturedISOsByZoneId).toHaveBeenCalledOnceWith(component.inputData().virtualMachineZoneId);
            expect(mockVmMediaService.getMyISOsByZoneId).not.toHaveBeenCalled();
            expect(mockVmMediaService.getPublicISOsByZoneId).not.toHaveBeenCalled();
            expect(mockVmMediaService.attachIso).toHaveBeenCalledOnceWith('test-id', 'iso1');
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit the form with the first public iso', () => {

            const publicIso = fixture.debugElement.queryAll(By.css('.btn-check'))[1].nativeElement as HTMLInputElement;
            publicIso.checked = true;
            publicIso.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const firstItem = fixture.debugElement.queryAll(By.css('.form-check-input'))[0].nativeElement as HTMLInputElement;
            firstItem.checked = true;
            firstItem.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(mockVmMediaService.getFeaturedISOsByZoneId).toHaveBeenCalledOnceWith(component.inputData().virtualMachineZoneId);
            expect(mockVmMediaService.getPublicISOsByZoneId).toHaveBeenCalledWith(component.inputData().virtualMachineZoneId, component.inputData().domainId, component.inputData().account);

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmMediaService.getMyISOsByZoneId).not.toHaveBeenCalled();
            expect(mockVmMediaService.attachIso).toHaveBeenCalledOnceWith('test-id', 'iso2');
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit the form with the first my iso', () => {

            const publicIso = fixture.debugElement.queryAll(By.css('.btn-check'))[2].nativeElement as HTMLInputElement;
            publicIso.checked = true;
            publicIso.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const firstItem = fixture.debugElement.queryAll(By.css('.form-check-input'))[0].nativeElement as HTMLInputElement;
            firstItem.checked = true;
            firstItem.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(mockVmMediaService.getFeaturedISOsByZoneId).toHaveBeenCalledOnceWith(component.inputData().virtualMachineZoneId);
            expect(mockVmMediaService.getMyISOsByZoneId).toHaveBeenCalledOnceWith(component.inputData().virtualMachineZoneId, component.inputData().domainId, component.inputData().account);

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmMediaService.getPublicISOsByZoneId).not.toHaveBeenCalled();
            expect(mockVmMediaService.attachIso).toHaveBeenCalledOnceWith('test-id', 'iso3');
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

    });

});

