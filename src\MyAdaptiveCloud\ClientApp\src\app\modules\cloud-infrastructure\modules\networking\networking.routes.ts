import { Routes } from '@angular/router';
import { cloudInfrastructureCredentialsGuard } from '../../guards/cloud-infrastructure-credentials.guard';
import { listNetworkGuard } from './guards/list-network.guard';
import { listRemoteVpnGatewayGuard } from './guards/list-remote-vpn-gateway.guard';
import { listVirtualPrivateCloudGuard } from './guards/list-virtual-private-cloud.guard';
import { listVpnUserGuard } from './guards/list-vpn-user.guard';
import { NETWORKING_ROUTE_SEGMENTS } from './models/route.segments';
import { NetworkDetailResolverService } from './services/network-detail-resolver.service';

export const networkingRoutes: Routes = [
    {
        path: '',
        loadComponent: () => import('./components/networking-management/networking-management.component').then(m => m.NetworkingManagementComponent),
        canActivate: [cloudInfrastructureCredentialsGuard],
        canActivateChild: [cloudInfrastructureCredentialsGuard],
        children: [
            {
                path: '',
                redirectTo: NETWORKING_ROUTE_SEGMENTS.NETWORKS,
                pathMatch: 'full'
            },
            {
                path: NETWORKING_ROUTE_SEGMENTS.NETWORKS,
                data: { breadcrumb: 'Networks' },

                children: [
                    {
                        path: '',
                        redirectTo: 'list',
                        pathMatch: 'full'
                    },
                    {
                        path: 'list',
                        canActivate: [listNetworkGuard],
                        data: { breadcrumb: 'List' },
                        loadComponent: () => import('./components/list-networks/list-networks.component').then(m => m.ListNetworksComponent),
                    },
                    {
                        path: `${NETWORKING_ROUTE_SEGMENTS.NETWORK}/:id`,
                        resolve: { selectedNetwork: NetworkDetailResolverService },
                        data: {
                            tabsVisible: false,
                            breadcrumb: data => `${data.selectedNetwork}`,
                        },
                        canActivate: [listNetworkGuard],
                        loadComponent: () => import('./components/network-detail-container/network-detail-container.component').then(m => m.NetworkDetailContainerComponent),
                        children: [
                            {
                                path: '',
                                redirectTo: NETWORKING_ROUTE_SEGMENTS.NETWORK_DETAIL,
                                pathMatch: 'full'
                            },
                            {
                                path: NETWORKING_ROUTE_SEGMENTS.NETWORK_DETAIL,
                                data: { breadcrumb: 'Details' },
                                loadComponent: () => import('./components/network-detail/network-detail.component').then(m => m.NetworkDetailComponent),
                            }
                        ]
                    }
                ]
            },
            {
                path: NETWORKING_ROUTE_SEGMENTS.VIRTUAL_PRIVATE_CLOUDS,
                canActivate: [listVirtualPrivateCloudGuard],
                data: { breadcrumb: 'Virtual Private Clouds' },
                loadComponent: () => import('./components/virtual-private-clouds-container/virtual-private-clouds-container.component').then(m => m.VirtualPrivateCloudsContainerComponent),

                children: [
                    {
                        path: '',
                        redirectTo: 'list',
                        pathMatch: 'full'
                    },
                    {
                        path: 'list',
                        data: { breadcrumb: 'List' },
                        loadComponent: () => import('./components/list-virtual-private-clouds/list-virtual-private-clouds.component').then(m => m.ListVirtualPrivateCloudsComponent),
                    }

                ]
            },
            {
                path: NETWORKING_ROUTE_SEGMENTS.REMOTE_VPN_GATEWAYS,
                canActivate: [listRemoteVpnGatewayGuard],
                data: { breadcrumb: 'Remote VPN Gateways' },
                loadComponent: () => import('./components/remote-vpngateways-container/remote-vpngateways-container.component').then(m => m.RemoteVpngatewaysContainerComponent),

                children: [
                    {
                        path: '',
                        redirectTo: 'list',
                        pathMatch: 'full'
                    },
                    {
                        path: 'list',
                        data: { breadcrumb: 'List' },
                        loadComponent: () => import('./components/list-remote-vpn-gateways/list-remote-vpn-gateways.component').then(m => m.ListRemoteVpnGatewaysComponent),
                    }
                ]
            },
            {
                path: NETWORKING_ROUTE_SEGMENTS.REMOTE_ACCESS_VPN_USERS,
                canActivate: [listVpnUserGuard],
                data: { breadcrumb: 'Remote Access VPN Users' },
                loadComponent: () => import('./components/remote-access-vpn-users-container/remote-access-vpn-users-container.component').then(m => m.RemoteAccessVpnUsersContainerComponent),

                children: [
                    {
                        path: '',
                        redirectTo: 'list',
                        pathMatch: 'full'
                    },
                    {
                        path: 'list',
                        data: { breadcrumb: 'List' },
                        loadComponent: () => import('./components/list-remote-access-vpn-users/list-remote-access-vpn-users.component').then(m => m.ListRemoteAccessVpnUsersComponent),
                    }
                ]
            }
        ]
    }
];
