import { ChangeDetectionStrategy, Component, DestroyRef, OnInit, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NotificationItem } from '@app/shared/models/notification-item';
import { NotificationType } from '@app/shared/models/notification-type';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgbToast } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'app-notification',
    imports: [NgbToast],
    templateUrl: './notification.component.html',
    styleUrl: './notification.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class NotificationComponent implements OnInit {
    protected readonly notificationService = inject(NotificationService);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly notifications = signal<NotificationItem[]>([]);

    public ngOnInit(): void {
        this.notificationService.notifications$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
                this.notifications.update(n => [...n, res]);
            });
    }

    public remove(notification: NotificationItem): void {
        this.notifications.update(value => value.filter(n => n !== notification));
    }

    public delay(type: NotificationType) {
        switch (type) {
            case NotificationType.Error:
            case NotificationType.Warning:
                return 10000;
            default:
                return 5000;
        }
    }

}
