import { FormControl } from '@angular/forms';
import { NetworkOfferingViewModel } from '../models/network-offering.view-model';

export interface SharedNetworkForm {
    name: FormControl<string>;
    description: FormControl<string>;
    vlanVni: FormControl<string>;
    hideIpAddressUsage: FormControl<boolean>;
    ipv4Gateway: FormControl<string | null>;
    ipv4Netmask: FormControl<string | null>;
    ipv4StartIp: FormControl<string | null>;
    ipv4EndIp: FormControl<string | null>;
    ipv6Gateway: FormControl<string | null>;
    ipv6StartIp: FormControl<string | null>;
    ipv6EndIp: FormControl<string | null>;
    ipv6CIDR: FormControl<string | null>;
    networkOffering: FormControl<NetworkOfferingViewModel>;
    networkDomain: FormControl<string | null>;
}
