import { TestBed } from '@angular/core/testing';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { CreateAcCwVmMappingComponent } from './create-ac-cw-vm-mapping.component';

describe('CreateAcCwVmMappingComponent', () => {
    let component: CreateAcCwVmMappingComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                CreateAcCwVmMappingComponent
            ],
            providers: [
                provideMock(AcToCwMappingService),
                provideMock(NgbActiveModal),
                provideMock(AdaptiveCloudUsageService),
                provideMock(NotificationService),
                provideMock(UserContextService)
            ]
        });

        component = TestBed.createComponent(CreateAcCwVmMappingComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
