import { TestBed } from '@angular/core/testing';
import { ApiService } from '@app/shared/services/api.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { SelectableOrganizationService } from './selectable-organization.service';

describe('SelectableOrganizationService', () => {
    let service: SelectableOrganizationService;
    let mockApiService: jasmine.SpyObj<ApiService>;
    const endpoint = 'organization';

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ApiService),
                SelectableOrganizationService
            ]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(SelectableOrganizationService);
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(service).toBeTruthy();
        });
    });

    describe('API', () => {

        it('should use the right endpoint for getSelectable', () => {
            service.getSelectable();
            expect(mockApiService.get).toHaveBeenCalledOnceWith(`${endpoint}/selectable`, null);
        });

    });
});
