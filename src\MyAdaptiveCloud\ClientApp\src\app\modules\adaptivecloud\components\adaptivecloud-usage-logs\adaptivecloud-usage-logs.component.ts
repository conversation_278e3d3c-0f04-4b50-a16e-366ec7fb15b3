import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { AdaptiveCloudUsageLogModel } from '../../models/adaptivecloud-usage-log.model';
import { AdaptiveCloudUsageLogsListRequest } from '../../requests/adaptivecloud-usage-logs.request';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';

@Component({
    selector: 'app-adaptivecloud-usage-logs.component',
    imports: [NgxDatatableModule, AutoSearchBoxComponent, TableActionComponent, DatePipe],
    templateUrl: './adaptivecloud-usage-logs.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdaptiveCloudUsageLogsComponent extends BaseListComponent<AdaptiveCloudUsageLogModel> implements OnInit {
    private readonly usageService = inject(AdaptiveCloudUsageService);
    private readonly router = inject(Router);

    private readonly dateCell = viewChild<TemplateRef<never>>('dateCellTemplate');
    private readonly errorCount = viewChild<TemplateRef<never>>('errorCount');

    constructor() {
        super();
        this.pagination = new AdaptiveCloudUsageLogsListRequest();
    }

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Command',
                prop: 'command',
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Error Count',
                headerTemplate: this.headerTemplateSortable(),
                cellTemplate: this.errorCount(),
                sortable: true,
                width: 60,
                canAutoResize: true,
            },
            {
                name: 'Start Time',
                prop: 'startTime',
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'End Time',
                prop: 'endTime',
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Period',
                prop: 'month',
                headerTemplate: this.headerTemplateSortable(),
                cellTemplate: this.dateCell(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },

            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 40
            },
        ];

        super.initialize(this.usageService.getAcUsageRunLogs.bind(this.usageService), columns);
    }

    protected viewDetails(id: number): void {
        this.router.navigate([`/cloud-infrastructure/AcUsageDetails/${id}`]);
    }

}
