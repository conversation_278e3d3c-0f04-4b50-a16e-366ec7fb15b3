import { TestBed } from '@angular/core/testing';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NETWORKING_ENDPOINT_NAMES } from '../models/networking.constants';
import { NetworkingPermissionService } from './networking-permission.service';

describe('NetworkingPermissionService', () => {
    let service: NetworkingPermissionService;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    const createCloudInfraUserContext = (permissions: string[]): CloudInfraUserContext => ({
        accountId: 'test-account-id',
        accountName: 'Test Account',
        apiKey: 'test-api-key',
        apiUrl: 'https://test-api.com',
        apiVersion: '4.19',
        cpuCustomOfferingMaxValue: 16,
        diskSizeCustomOfferingMaxValue: 2000,
        diskSizeCustomOfferingMinValue: 1,
        domainId: 'test-domain-id',
        hasMappedDomain: true,
        memoryCustomOfferingMaxValue: 32768,
        permissions,
        roleName: 'Test Role',
        roleType: 'User',
        secretKey: 'test-secret-key'
    });

    const createUserContext = (cloudInfraUserContext: CloudInfraUserContext): UserContext => ({
        cloudInfraUserContext
    }) as UserContext;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                NetworkingPermissionService,
                provideMock(UserContextService)
            ]
        });

        service = TestBed.inject(NetworkingPermissionService);
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
    });

    describe('canViewNetworkList', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewNetworkList();

            expect(result).toBeTrue();
        });

        it('should return true when user has listNetworks permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listNetworks]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewNetworkList();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewNetworkList();

            expect(result).toBeFalse();
        });

        it('should return true when user has multiple permissions including listNetworks', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                'someOtherPermission',
                NETWORKING_ENDPOINT_NAMES.listNetworks,
                'anotherPermission'
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewNetworkList();

            expect(result).toBeTrue();
        });
    });

    describe('canViewVpnUserList', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewVpnUserList();

            expect(result).toBeTrue();
        });

        it('should return true when user has listVpnUsers permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listVpnUsers]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewVpnUserList();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewVpnUserList();

            expect(result).toBeFalse();
        });
    });

    describe('canViewVirtualPrivateCloudList', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewVirtualPrivateCloudList();

            expect(result).toBeTrue();
        });

        it('should return true when user has listVirtualPrivateClouds permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listVirtualPrivateClouds]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewVirtualPrivateCloudList();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewVirtualPrivateCloudList();

            expect(result).toBeFalse();
        });
    });

    describe('canViewRemoteVpnGatewaysList', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewRemoteVpnGatewaysList();

            expect(result).toBeTrue();
        });

        it('should return true when user has listRemoteVpnGateways permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listRemoteVpnGateways]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewRemoteVpnGatewaysList();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canViewRemoteVpnGatewaysList();

            expect(result).toBeFalse();
        });
    });

    describe('canRestartNetwork', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canRestartNetwork();

            expect(result).toBeTrue();
        });

        it('should return true when user has restartNetwork permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.restartNetwork]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canRestartNetwork();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canRestartNetwork();

            expect(result).toBeFalse();
        });
    });

    describe('canCreateNetwork', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateNetwork();

            expect(result).toBeTrue();
        });

        it('should return true when user has createNetwork permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.createNetwork]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateNetwork();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateNetwork();

            expect(result).toBeFalse();
        });
    });

    describe('canDeleteNetwork', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteNetwork();

            expect(result).toBeTrue();
        });

        it('should return true when user has deleteNetwork permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.deleteNetwork]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteNetwork();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteNetwork();

            expect(result).toBeFalse();
        });
    });

    describe('canEditNetwork', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditNetwork();

            expect(result).toBeTrue();
        });

        it('should return true when user has editNetwork permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.editNetwork]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditNetwork();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditNetwork();

            expect(result).toBeFalse();
        });
    });

    describe('canAddVpnUser', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canAddVpnUser();

            expect(result).toBeTrue();
        });

        it('should return true when user has addVpnUser permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.addVpnUser]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canAddVpnUser();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canAddVpnUser();

            expect(result).toBeFalse();
        });
    });

    describe('canDeleteVpnUser', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteVpnUser();

            expect(result).toBeTrue();
        });

        it('should return true when user has deleteVpnUser permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.deleteVpnUser]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteVpnUser();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteVpnUser();

            expect(result).toBeFalse();
        });
    });

    describe('canRestartVirtualPrivateCloud', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canRestartVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return true when user has restartNetwork permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.restartNetwork]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canRestartVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canRestartVirtualPrivateCloud();

            expect(result).toBeFalse();
        });
    });

    describe('canDeleteVirtualPrivateCloud', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return true when user has deleteVirtualPrivateCloud permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.deleteVirtualPrivateCloud]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteVirtualPrivateCloud();

            expect(result).toBeFalse();
        });
    });

    describe('canCreateVirtualPrivateCloud', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return true when user has both createNetwork and listVirtualPrivateCloudOfferings permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                NETWORKING_ENDPOINT_NAMES.createNetwork,
                NETWORKING_ENDPOINT_NAMES.listVirtualPrivateCloudOfferings
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return false when user has only createNetwork permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.createNetwork]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateVirtualPrivateCloud();

            expect(result).toBeFalse();
        });

        it('should return false when user has only listVirtualPrivateCloudOfferings permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listVirtualPrivateCloudOfferings]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateVirtualPrivateCloud();

            expect(result).toBeFalse();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateVirtualPrivateCloud();

            expect(result).toBeFalse();
        });

        it('should return true when user has multiple permissions including both required ones', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                'someOtherPermission',
                NETWORKING_ENDPOINT_NAMES.createNetwork,
                NETWORKING_ENDPOINT_NAMES.listVirtualPrivateCloudOfferings,
                'anotherPermission'
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateVirtualPrivateCloud();

            expect(result).toBeTrue();
        });
    });

    describe('canEditVirtualPrivateCloud', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return true when user has editVirtualPrivateCloud permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.editVirtualPrivateCloud]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditVirtualPrivateCloud();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditVirtualPrivateCloud();

            expect(result).toBeFalse();
        });
    });

    describe('canCreateRemoteVpnGateway', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateRemoteVpnGateway();

            expect(result).toBeTrue();
        });

        it('should return true when user has createRemoteVpnGateway permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.createRemoteVpnGateway]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateRemoteVpnGateway();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canCreateRemoteVpnGateway();

            expect(result).toBeFalse();
        });
    });

    describe('canEditRemoteVpnGateway', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditRemoteVpnGateway();

            expect(result).toBeTrue();
        });

        it('should return true when user has editRemoteVpnGateway permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.editRemoteVpnGateway]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditRemoteVpnGateway();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canEditRemoteVpnGateway();

            expect(result).toBeFalse();
        });
    });

    describe('canDeleteRemoteVpnGateway', () => {
        it('should return true when user has all permissions (*)', () => {
            const cloudInfraContext = createCloudInfraUserContext(['*']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteRemoteVpnGateway();

            expect(result).toBeTrue();
        });

        it('should return true when user has deleteRemoteVpnGateway permission', () => {
            const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.deleteRemoteVpnGateway]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteRemoteVpnGateway();

            expect(result).toBeTrue();
        });

        it('should return false when user does not have required permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext(['someOtherPermission']);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            const result = service.canDeleteRemoteVpnGateway();

            expect(result).toBeFalse();
        });
    });

    describe('hasAllPermissions (private method behavior)', () => {
        describe('Different role types with all permissions', () => {
            it('should return true for Admin role with all permissions', () => {
                const cloudInfraContext = createCloudInfraUserContext(['*']);
                cloudInfraContext.roleType = 'Admin';
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });

            it('should return true for DomainAdmin role with all permissions', () => {
                const cloudInfraContext = createCloudInfraUserContext(['*']);
                cloudInfraContext.roleType = 'DomainAdmin';
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });

            it('should return true for ResourceAdmin role with all permissions', () => {
                const cloudInfraContext = createCloudInfraUserContext(['*']);
                cloudInfraContext.roleType = 'ResourceAdmin';
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });

            it('should return true for User role with all permissions', () => {
                const cloudInfraContext = createCloudInfraUserContext(['*']);
                cloudInfraContext.roleType = 'User';
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });
        });

        describe('Edge cases for hasAllPermissions', () => {
            it('should return false when permissions array has multiple items including *', () => {
                const cloudInfraContext = createCloudInfraUserContext(['*', 'someOtherPermission']);
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                // This should return false because hasAllPermissions checks for exactly one permission that equals '*'
                const result = service.canViewNetworkList();

                // Should still return true because it has the specific permission
                expect(result).toBeFalse();
            });

            it('should return false when permissions array is empty', () => {
                const cloudInfraContext = createCloudInfraUserContext([]);
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeFalse();
            });
        });

        describe('API Version variations', () => {
            it('should work correctly with API version 4.15', () => {
                const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listNetworks]);
                cloudInfraContext.apiVersion = '4.15';
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });

            it('should work correctly with API version 4.19', () => {
                const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listNetworks]);
                cloudInfraContext.apiVersion = '4.19';
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });
        });

        describe('Domain mapping scenarios', () => {
            it('should work correctly when domain is mapped', () => {
                const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listNetworks]);
                cloudInfraContext.hasMappedDomain = true;
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });

            it('should work correctly when domain is not mapped', () => {
                const cloudInfraContext = createCloudInfraUserContext([NETWORKING_ENDPOINT_NAMES.listNetworks]);
                cloudInfraContext.hasMappedDomain = false;
                mockUserContextService.currentUser = createUserContext(cloudInfraContext);

                const result = service.canViewNetworkList();

                expect(result).toBeTrue();
            });
        });
    });

    describe('Integration tests with multiple permissions combinations', () => {
        it('should handle user with comprehensive network permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                NETWORKING_ENDPOINT_NAMES.listNetworks,
                NETWORKING_ENDPOINT_NAMES.createNetwork,
                NETWORKING_ENDPOINT_NAMES.editNetwork,
                NETWORKING_ENDPOINT_NAMES.deleteNetwork,
                NETWORKING_ENDPOINT_NAMES.restartNetwork
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            expect(service.canViewNetworkList()).toBeTrue();
            expect(service.canCreateNetwork()).toBeTrue();
            expect(service.canEditNetwork()).toBeTrue();
            expect(service.canDeleteNetwork()).toBeTrue();
            expect(service.canRestartNetwork()).toBeTrue();
        });

        it('should handle user with comprehensive VPN permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                NETWORKING_ENDPOINT_NAMES.listVpnUsers,
                NETWORKING_ENDPOINT_NAMES.addVpnUser,
                NETWORKING_ENDPOINT_NAMES.deleteVpnUser
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            expect(service.canViewVpnUserList()).toBeTrue();
            expect(service.canAddVpnUser()).toBeTrue();
            expect(service.canDeleteVpnUser()).toBeTrue();
        });

        it('should handle user with comprehensive VPC permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                NETWORKING_ENDPOINT_NAMES.listVirtualPrivateClouds,
                NETWORKING_ENDPOINT_NAMES.createNetwork,
                NETWORKING_ENDPOINT_NAMES.listVirtualPrivateCloudOfferings,
                NETWORKING_ENDPOINT_NAMES.editVirtualPrivateCloud,
                NETWORKING_ENDPOINT_NAMES.deleteVirtualPrivateCloud,
                NETWORKING_ENDPOINT_NAMES.restartNetwork
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            expect(service.canViewVirtualPrivateCloudList()).toBeTrue();
            expect(service.canCreateVirtualPrivateCloud()).toBeTrue();
            expect(service.canEditVirtualPrivateCloud()).toBeTrue();
            expect(service.canDeleteVirtualPrivateCloud()).toBeTrue();
            expect(service.canRestartVirtualPrivateCloud()).toBeTrue();
        });

        it('should handle user with comprehensive remote VPN gateway permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                NETWORKING_ENDPOINT_NAMES.listRemoteVpnGateways,
                NETWORKING_ENDPOINT_NAMES.createRemoteVpnGateway,
                NETWORKING_ENDPOINT_NAMES.editRemoteVpnGateway,
                NETWORKING_ENDPOINT_NAMES.deleteRemoteVpnGateway
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            expect(service.canViewRemoteVpnGatewaysList()).toBeTrue();
            expect(service.canCreateRemoteVpnGateway()).toBeTrue();
            expect(service.canEditRemoteVpnGateway()).toBeTrue();
            expect(service.canDeleteRemoteVpnGateway()).toBeTrue();
        });

        it('should handle user with no networking permissions', () => {
            const cloudInfraContext = createCloudInfraUserContext([
                'someUnrelatedPermission',
                'anotherUnrelatedPermission'
            ]);
            mockUserContextService.currentUser = createUserContext(cloudInfraContext);

            expect(service.canViewNetworkList()).toBeFalse();
            expect(service.canViewVpnUserList()).toBeFalse();
            expect(service.canViewVirtualPrivateCloudList()).toBeFalse();
            expect(service.canViewRemoteVpnGatewaysList()).toBeFalse();
            expect(service.canRestartNetwork()).toBeFalse();
            expect(service.canCreateNetwork()).toBeFalse();
            expect(service.canDeleteNetwork()).toBeFalse();
            expect(service.canEditNetwork()).toBeFalse();
            expect(service.canAddVpnUser()).toBeFalse();
            expect(service.canDeleteVpnUser()).toBeFalse();
            expect(service.canRestartVirtualPrivateCloud()).toBeFalse();
            expect(service.canDeleteVirtualPrivateCloud()).toBeFalse();
            expect(service.canCreateVirtualPrivateCloud()).toBeFalse();
            expect(service.canEditVirtualPrivateCloud()).toBeFalse();
            expect(service.canCreateRemoteVpnGateway()).toBeFalse();
            expect(service.canEditRemoteVpnGateway()).toBeFalse();
            expect(service.canDeleteRemoteVpnGateway()).toBeFalse();
        });
    });
});
