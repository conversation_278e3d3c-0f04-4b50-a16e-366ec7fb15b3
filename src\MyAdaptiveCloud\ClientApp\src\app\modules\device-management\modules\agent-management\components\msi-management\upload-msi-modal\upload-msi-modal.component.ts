import { HttpEventType } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgOptionComponent, NgSelectComponent } from '@ng-select/ng-select';
import { catchError, EMPTY } from 'rxjs';
import { UploadMsiForm } from '../../../forms/upload-msi.form';
import { ServiceTypeEnum } from '../../../models/service-type.enum';
import { UploadMsiRequest } from '../../../requests/upload-msi.request';
import { MsiManagementService } from '../../../services/msi-management.service';

@Component({
    selector: 'app-upload-msi-modal.component',
    imports: [ReactiveFormsModule, NgSelectComponent, NgOptionComponent],
    templateUrl: './upload-msi-modal.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UploadMsiModalComponent {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly formBuilder = inject(FormBuilder);
    private readonly msiManagementService = inject(MsiManagementService);
    private readonly notificationService = inject(NotificationService);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly serviceType = ServiceTypeEnum;
    protected readonly fileSelected = signal<File>(null);
    protected readonly isUploading = signal(false);
    protected readonly uploadProgressMessage = signal('');

    protected readonly form = this.formBuilder.group<UploadMsiForm>({
        majorVersion: this.formBuilder.control({ value: null, disabled: true }, [Validators.required, Validators.min(1)]),
        minorVersion: this.formBuilder.control({ value: null, disabled: true }, [Validators.required, Validators.min(1)]),
        build: this.formBuilder.control({ value: null, disabled: true }, [Validators.required, Validators.min(1)]),
        serviceType: this.formBuilder.control({ value: null, disabled: true }, Validators.required),
    });

    protected selectFile(event: Event) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const files = (event.target as any)?.files as FileList;
        if (files?.length) {
            const file = files[0] as File;
            const split = file.name.split('_');
            const version = split.length > 1 ? split[1].split('.') : '';

            this.form.patchValue({
                build: version.length > 2 ? parseInt(version[2], 10) : null,
                majorVersion: version.length ? parseInt(version[0], 10) : null,
                minorVersion: version.length > 1 ? parseInt(version[1], 10) : null,
                serviceType: split.length ? split[0].toLowerCase().includes('watchdog') ? ServiceTypeEnum.Watchdog : split[0].toLowerCase().includes('agent') ? ServiceTypeEnum.Agent : null : null
            });

            this.fileSelected.set(file);
            this.form.enable();

            this.form.updateValueAndValidity();
        }

    }

    protected upload() {
        if (this.form.valid && this.fileSelected()) {
            this.isUploading.set(true);
            this.form.disable();
            const request: UploadMsiRequest = {
                majorVersion: this.form.value.majorVersion,
                minorVersion: this.form.value.minorVersion,
                build: this.form.value.build,
                serviceType: this.form.value.serviceType
            };

            this.msiManagementService.upload(request, this.fileSelected())
                .pipe(
                    catchError(() => {
                        this.isUploading.set(false);
                        this.form.enable();
                        this.uploadProgressMessage.set('');
                        return EMPTY;
                    }),
                    takeUntilDestroyed(this.destroyRef)
                )
                .subscribe(res => {
                    switch (res.type) {
                        case HttpEventType.UploadProgress:
                            this.uploadProgressMessage.set(`${res.uploadProgressPercentage}% uploaded`);
                            break;
                        case HttpEventType.Response:
                            this.notificationService.notify(res.message);
                            this.activeModal.close(true);
                            break;
                    }
                });
        }
    }
}

