export interface HostForMigration {
    id: string;
    name: string;
    state: string;
    disconnected: string;
    type: string;
    ipaddress: string;
    zoneid: string;
    zonename: string;
    podid: string;
    podname: string;
    version: string;
    hypervisor: string;
    cpunumber: number;
    cpuspeed: number;
    cpuallocated: string;
    cpuallocatedvalue: number;
    cpuallocatedpercentage: string;
    cpuallocatedwithoverprovisioning: string;
    cpuused: string;
    cpuwithoverprovisioning: string;
    memorytotal: number;
    memorywithoverprovisioning: string;
    networkkbsread: number;
    networkkbswrite: number;
    memoryallocated: string;
    memoryallocatedpercentage: string;
    memoryallocatedbytes: number;
    memoryused: number;
    capabilities: string;
    lastpinged: string;
    managementserverid: number;
    clusterid: string;
    clustername: string;
    clustertype: string;
    islocalstorageactive: boolean;
    created: string;
    events: string;
    suitableformigration: boolean;
    requiresStorageMotion: boolean;
    resourcestate: string;
    hahost: boolean;
    jobstatus: number;
}
