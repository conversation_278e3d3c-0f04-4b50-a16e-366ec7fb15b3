import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { of } from 'rxjs';
import { CreateNetworkService } from '../../../../../../services/create-network.service';
import { AffinityGroup } from '../../../../models/affinity-group.model';
import { SSHKeyPair } from '../../../../models/ssh-key-pair';
import { VmAffinityGroupsService } from '../../../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../../../services/vm-management.service';
import { VmMediaService } from '../../../../services/vm-media-service';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { DiskOffering } from '../../models/disk-offering.model';
import { SshKeyPairViewModel } from '../../models/ssh-keypair.view-model';
import { CreateVmComputeService } from '../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../services/create-vm-network.service';
import { CreateVmService } from '../../services/create-vm-service';
import { CreateVmStorageService } from '../../services/create-vm-storage.service';
import { CreateVmWizardAdvancedSettingsComponent } from './advanced-settings.component';
import { AVAILABLE_KEYBOARD_LANG_VALUES, DEFAULT_KEYBOARD_LANG } from './vm-available-keyboard-langs';

describe('CreateVMWizardAdvancedSettingsComponent', () => {
    let component: CreateVmWizardAdvancedSettingsComponent;
    let fixture: ComponentFixture<CreateVmWizardAdvancedSettingsComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;

    const mockGetAffinityGroupsResponse: AffinityGroup[] = [
        {
            id: '1',
            account: 'admin',
            description: 'test group 1',
            domain: 'ROOT',
            domainid: 'domain1',
            name: 'group 1',
            project: 'project1',
            projectid: 'projectid1',
            type: 'type1',
            virtualmachineIds: 'vm1'
        },
        {
            id: '2',
            account: 'admin',
            description: 'test group 2',
            domain: 'ROOT',
            domainid: 'domain2',
            name: 'group 2',
            project: 'project2',
            projectid: 'projectid2',
            type: 'type2',
            virtualmachineIds: 'vm2'
        },
        {
            id: '3',
            account: 'admin',
            description: 'test group 3',
            domain: 'ROOT',
            domainid: 'domain3',
            name: 'group 3',
            project: 'project3',
            projectid: 'projectid3',
            type: 'type3',
            virtualmachineIds: 'vm3'
        }
    ];

    const mockGetSshKeyPairsResponse: SSHKeyPair[] = [
        {
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain1',
            fingerprint: 'fingerprint1',
            name: 'keypair 1'
        },
        {
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain2',
            fingerprint: 'fingerprint2',
            name: 'keypair 2'
        },
        {
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain3',
            fingerprint: 'fingerprint3',
            name: 'keypair 3'
        }
    ];

    const mockParsedSshKeyPairsResponse: SshKeyPairViewModel[] = [
        {
            id: 'keypair-1',
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain1',
            fingerprint: 'fingerprint1',
            name: 'keypair 1'
        },
        {
            id: 'keypair-2',
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain2',
            fingerprint: 'fingerprint2',
            name: 'keypair 2'
        },
        {
            id: 'keypair-3',
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain3',
            fingerprint: 'fingerprint3',
            name: 'keypair 3'
        }
    ];

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateVmWizardAdvancedSettingsComponent],
            providers: [
                CreateVMWizardStore,
                provideMock(CreateVmComputeService),
                provideMock(CreateNetworkService),
                provideMock(CreateVmNetworkService),
                provideMock(VmMediaService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(UserContextService),
                provideMock(CreateVmStorageService),
                provideMock(CreateVmService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: getMockZoneDomainAccountStore(),
                },
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'test-account',
                domainId: 'test-domain-id'
            }
        } as UserContext;

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of(mockGetAffinityGroupsResponse));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of(mockGetSshKeyPairsResponse));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        fixture = TestBed.createComponent(CreateVmWizardAdvancedSettingsComponent);
        component = fixture.componentInstance;

        component.store.loadAffinityGroups();
        component.store.loadSSHKeyPairs();

        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('Initially', () => {

        it('Should add the options to the affinity groups adn SSH keys tables when the Show advanced settings is true', () => {
            component.store.toggleAdvanceSettingsDisplay(true);
            fixture.detectChanges();

            const advanceSettingsTables = fixture.debugElement.queryAll(By.css('table.options-table'));

            const affinityGroupsTable = advanceSettingsTables[0];
            const sshKeyPairsTable = advanceSettingsTables[1];

            const affinityGroupsRows = affinityGroupsTable.queryAll(By.css('tr'));
            const sshKeyPairsRows = sshKeyPairsTable.queryAll(By.css('tr'));

            expect(affinityGroupsRows.length).toEqual(mockGetAffinityGroupsResponse.length);
            expect(sshKeyPairsRows.length).toEqual(mockParsedSshKeyPairsResponse.length);
        });
    });

    describe('Changes to the form', () => {
        beforeEach(() => {
            component.store.reset();

            component.store.loadAffinityGroups();
            component.store.loadSSHKeyPairs();
            component.store.toggleAdvanceSettingsDisplay(true);

            fixture.detectChanges();
        });

        describe('Affinity Groups', () => {
            it('Should save the options correctly in the store', () => {

                const affinityGroups = component.store.advancedSettingsStep.affinityGroups();

                const optionsTables = fixture.debugElement.queryAll(By.css('table.options-table'));
                const affinityGroupsTable = optionsTables[0];

                const affinityGroupsRows = affinityGroupsTable.queryAll(By.css('tr'));

                affinityGroupsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    const groupId = checkbox.nativeElement.id.split('-')[1];
                    const selectedGroup = affinityGroups.find(_group => _group.id === groupId);

                    checkbox.nativeElement.click();
                    expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.options()).toContain({ id: selectedGroup.id, name: selectedGroup.name });
                    checkbox.nativeElement.click();
                    expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.options()).not.toContain({ id: selectedGroup.id, name: selectedGroup.name });
                });
            });

            it('should set the value in the store correctly when clicking on the select all checkbox', () => {
                const affinityGroupsTable = fixture.debugElement.queryAll(By.css('table.options-table'))[0];
                const tableHeader = affinityGroupsTable.query(By.css('thead'));
                const selectAllCheckbox = tableHeader.query(By.css('input[type="checkbox"]'));
                const affinityGroupsRows = affinityGroupsTable.queryAll(By.css('tr'));

                const allAffinityGroupsSelected = mockGetAffinityGroupsResponse.map(group => ({ id: group.id, name: group.name }));

                expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.options()).toEqual([]);

                selectAllCheckbox.nativeElement.click();
                expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.selectAll()).toBeTruthy();
                expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.options()).toEqual(allAffinityGroupsSelected);

                affinityGroupsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    expect(checkbox.nativeElement.checked).toBeTruthy();
                });

                selectAllCheckbox.nativeElement.click();
                expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.selectAll()).toBeFalsy();
                expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.options()).toEqual([]);

                affinityGroupsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    expect(checkbox.nativeElement.checked).toBeFalsy();
                });
            });

            it('should set the value of the select all control to TRUE when all options are selected', () => {
                const affinityGroupsTable = fixture.debugElement.queryAll(By.css('table.options-table'))[0];
                const tableHeader = affinityGroupsTable.query(By.css('thead'));
                const selectAllCheckbox = tableHeader.query(By.css('input[type="checkbox"]'));
                const affinityGroupsRows = affinityGroupsTable.queryAll(By.css('tr'));

                affinityGroupsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    checkbox.nativeElement.click();
                });

                expect(selectAllCheckbox.nativeElement.checked).toBeTruthy();
                expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.selectAll()).toBeTruthy();
            });

            it('should set the value of the select all control to FALSE when an option is false', () => {
                const affinityGroupsTable = fixture.debugElement.queryAll(By.css('table.options-table'))[0];
                const tableHeader = affinityGroupsTable.query(By.css('thead'));
                const selectAllCheckbox = tableHeader.query(By.css('input[type="checkbox"]'));
                const affinityGroupsRows = affinityGroupsTable.queryAll(By.css('tr'));

                affinityGroupsRows.forEach(row => {
                    selectAllCheckbox.nativeElement.click();
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    expect(checkbox.nativeElement.checked).toBeTruthy();
                    expect(selectAllCheckbox.nativeElement.checked).toBeTruthy();
                    expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.selectAll()).toBeTruthy();

                    checkbox.nativeElement.click();

                    expect(checkbox.nativeElement.checked).toBeFalsy();
                    expect(selectAllCheckbox.nativeElement.checked).toBeFalsy();
                    expect(component.store.advancedSettingsStep.selectedSettings.affinityGroups.selectAll()).toBeFalsy();
                });
            });
        });

        describe('SSH Key Pairs', () => {

            beforeEach(() => {
                component.store.reset();

                component.store.loadAffinityGroups();
                component.store.loadSSHKeyPairs();
                component.store.toggleAdvanceSettingsDisplay(true);

                fixture.detectChanges();
            });

            it('Should save the options correctly in the store', () => {

                const sshKeyPairs = component.store.advancedSettingsStep.sshKeyPairs();

                const optionsTables = fixture.debugElement.queryAll(By.css('table.options-table'));
                const sshKeyPairsTable = optionsTables[1];

                const sshKeyPairsRows = sshKeyPairsTable.queryAll(By.css('tr'));

                sshKeyPairsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    const keyPairId = checkbox.nativeElement.id.replace('key-pair', '');
                    const selectedKeyPair = sshKeyPairs.find(_keyPair => _keyPair.id === keyPairId);

                    checkbox.nativeElement.click();
                    expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.options()).toContain({ id: selectedKeyPair.id, name: selectedKeyPair.name });
                    checkbox.nativeElement.click();
                    expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.options()).not.toContain({ id: selectedKeyPair.id, name: selectedKeyPair.name });
                });
            });

            it('should set the value in the store correctly when clicking on the select all checkbox', () => {
                const sshKeyPairsTable = fixture.debugElement.queryAll(By.css('table.options-table'))[1];
                const tableHeader = sshKeyPairsTable.query(By.css('thead'));
                const selectAllCheckbox = tableHeader.query(By.css('input[type="checkbox"]'));
                const sshKeyPairsRows = sshKeyPairsTable.queryAll(By.css('tr'));

                const allSshKeyPairsSelected = mockParsedSshKeyPairsResponse.map(keyPair => ({ id: keyPair.id, name: keyPair.name }));

                expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.options()).toEqual([]);

                selectAllCheckbox.nativeElement.click();
                expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.selectAll()).toBeTruthy();
                expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.options()).toEqual(allSshKeyPairsSelected);

                sshKeyPairsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    expect(checkbox.nativeElement.checked).toBeTruthy();
                });

                selectAllCheckbox.nativeElement.click();
                expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.selectAll()).toBeFalsy();
                expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.options()).toEqual([]);

                sshKeyPairsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    expect(checkbox.nativeElement.checked).toBeFalsy();
                });
            });

            it('should set the value of the select all control to TRUE when all options are selected', () => {
                const sshKeyPairsTable = fixture.debugElement.queryAll(By.css('table.options-table'))[1];
                const tableHeader = sshKeyPairsTable.query(By.css('thead'));
                const selectAllCheckbox = tableHeader.query(By.css('input[type="checkbox"]'));
                const sshKeyPairsRows = sshKeyPairsTable.queryAll(By.css('tr'));

                sshKeyPairsRows.forEach(row => {
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    checkbox.nativeElement.click();
                });

                expect(selectAllCheckbox.nativeElement.checked).toBeTruthy();
                expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.selectAll()).toBeTruthy();
            });

            it('should set the value of the select all control to FALSE when an option is false', () => {
                const sshKeyPairsTable = fixture.debugElement.queryAll(By.css('table.options-table'))[1];
                const tableHeader = sshKeyPairsTable.query(By.css('thead'));
                const selectAllCheckbox = tableHeader.query(By.css('input[type="checkbox"]'));
                const sshKeyPairsRows = sshKeyPairsTable.queryAll(By.css('tr'));

                sshKeyPairsRows.forEach(row => {
                    selectAllCheckbox.nativeElement.click();
                    const checkbox = row.query(By.css('input[type="checkbox"]'));
                    expect(checkbox.nativeElement.checked).toBeTruthy();
                    expect(selectAllCheckbox.nativeElement.checked).toBeTruthy();
                    expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.selectAll()).toBeTruthy();

                    checkbox.nativeElement.click();

                    expect(checkbox.nativeElement.checked).toBeFalsy();
                    expect(selectAllCheckbox.nativeElement.checked).toBeFalsy();
                    expect(component.store.advancedSettingsStep.selectedSettings.sshKeyPairs.selectAll()).toBeFalsy();
                });
            });
        });

        describe('Userdata control', () => {

            beforeEach(() => {
                component.store.reset();

                component.store.loadAffinityGroups();
                component.store.loadSSHKeyPairs();
                component.store.toggleAdvanceSettingsDisplay(true);

                fixture.detectChanges();
            });

            it('Should save the settings correctly when the value in the Userdata field changes', () => {
                const expectedValue = 'new value';
                const userdataControl = fixture.debugElement.query(By.css('textarea[name="userData"]'));

                expect(component.store.advancedSettingsStep.selectedSettings.userdata()).toEqual('');

                userdataControl.nativeElement.value = expectedValue;
                userdataControl.nativeElement.dispatchEvent(new Event('input'));
                expect(component.store.advancedSettingsStep.selectedSettings.userdata()).toEqual(expectedValue);
            });
        });

        describe('Keyboard language control', () => {
            beforeEach(() => {
                component.store.reset();

                component.store.loadAffinityGroups();
                component.store.loadSSHKeyPairs();
                component.store.toggleAdvanceSettingsDisplay(true);

                fixture.detectChanges();
            });

            it('Should set the correct value in the store when the value in the Keyboard language control changes', fakeAsync(() => {
                const availableLangsKeys = Object.keys(AVAILABLE_KEYBOARD_LANG_VALUES);

                selectOption(fixture, 'ng-select', 0, true, 0);
                expect(component.store.advancedSettingsStep.selectedSettings.keyboardLanguage()).toEqual(DEFAULT_KEYBOARD_LANG);

                availableLangsKeys.forEach(key => {
                    selectOption(fixture, 'ng-select', 1, false, 0);
                    expect(component.store.advancedSettingsStep.selectedSettings.keyboardLanguage()).toEqual(key);
                });
            }));
        });
    });
});
