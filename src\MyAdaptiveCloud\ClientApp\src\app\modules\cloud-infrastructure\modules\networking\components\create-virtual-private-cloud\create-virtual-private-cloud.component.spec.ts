import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VirtualPrivateCloudOfferingViewModel } from '../../models/virtual-private-cloud-offering.view-model';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';
import { CreateVirtualPrivateCloudComponent } from './create-virtual-private-cloud.component';

describe('CreateVirtualPrivateCloudComponent', () => {

    let fixture: ComponentFixture<CreateVirtualPrivateCloudComponent>;
    let component: CreateVirtualPrivateCloudComponent;
    let mockVirtualPrivateCloudService: jasmine.SpyObj<VirtualPrivateCloudService>;

    let zones: ZoneViewModel[];
    let zone1VpcOfferings: VirtualPrivateCloudOfferingViewModel[];
    let zone2VpcOfferings: VirtualPrivateCloudOfferingViewModel[];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateVirtualPrivateCloudComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VirtualPrivateCloudService)
            ]
        });

        zone1VpcOfferings = [
            { id: 'vpc-offering-1', description: 'VPC Offering 1', name: 'VPC Offering 1' },
            { id: 'vpc-offering-2', description: 'VPC Offering 2', name: 'VPC Offering 2' }
        ];
        zone2VpcOfferings = [
            { id: 'vpc-offering-3', description: 'VPC Offering 3', name: 'VPC Offering 3' },
            { id: 'vpc-offering-4', description: 'VPC Offering 4', name: 'VPC Offering 4' }
        ];

        zones = [{ id: 'zone-id', name: 'Zone 1' }, { id: 'zone-id-2', name: 'Zone 2' }];

        mockVirtualPrivateCloudService = TestBed.inject(VirtualPrivateCloudService) as jasmine.SpyObj<VirtualPrivateCloudService>;
        mockVirtualPrivateCloudService.getVpcOfferings.withArgs(zones[0].id).and.returnValue(of(zone1VpcOfferings));
        mockVirtualPrivateCloudService.getVpcOfferings.withArgs(zones[1].id).and.returnValue(of(zone2VpcOfferings));
        mockVirtualPrivateCloudService.createVirtualPrivateCloud.and.returnValue(of('job-id'));

        fixture = TestBed.createComponent(CreateVirtualPrivateCloudComponent);
        component = fixture.componentInstance;

        component.account.set('account');
        component.domainId.set('domain-id');
        component.zones.set(zones);

    });

    describe('Initialization', () => {

        it('should load VPC for default zone', () => {
            fixture.detectChanges();
            expect(mockVirtualPrivateCloudService.getVpcOfferings).toHaveBeenCalledOnceWith(zones[0].id);
        });
    });

    describe('Form Interaction', () => {

        it('should update VPC offerings when zone changes', fakeAsync(() => {
            fixture.detectChanges();
            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();
            expect(mockVirtualPrivateCloudService.getVpcOfferings).toHaveBeenCalledWith(zones[1].id);
        }));

        it('should not submit when form is invalid', fakeAsync(() => {
            fixture.detectChanges();
            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();

            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).not.toHaveBeenCalled();

            const nameInput = fixture.debugElement.query(By.css('[data-testid="name-input"]')).nativeElement as HTMLInputElement;
            nameInput.value = 'Name';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();
            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).not.toHaveBeenCalled();

            const descriptionInput = fixture.debugElement.query(By.css('[data-testid="description-input"]')).nativeElement as HTMLInputElement;
            descriptionInput.value = 'Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();
            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).not.toHaveBeenCalled();

            selectOption(fixture, 'ng-select', 0, true, 1);
            fixture.detectChanges();

            const cidrInput = fixture.debugElement.query(By.css('[data-testid="cidr-input"]')).nativeElement as HTMLInputElement;
            cidrInput.value = 'Invalid CIDR';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();
            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).not.toHaveBeenCalled();

            const networkDomainInput = fixture.debugElement.query(By.css('[data-testid="network-domain-input"]')).nativeElement as HTMLInputElement;
            networkDomainInput.value = 'domain';
            networkDomainInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            cidrInput.value = '10.0.0.0/16';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();
            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).toHaveBeenCalledOnceWith({
                zoneId: zones[1].id,
                name: 'Name',
                description: 'Description',
                cidr: '10.0.0.0/16',
                vpcOfferingId: zone2VpcOfferings[0].id,
                networkDomain: 'domain'
            }, 'domain-id', 'account');
        }));

        it('should submit when form is valid', fakeAsync(() => {
            fixture.detectChanges();

            // Fill out all required fields with valid data
            const nameInput = fixture.debugElement.query(By.css('[data-testid="name-input"]')).nativeElement as HTMLInputElement;
            nameInput.value = 'Test VPC';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const descriptionInput = fixture.debugElement.query(By.css('[data-testid="description-input"]')).nativeElement as HTMLInputElement;
            descriptionInput.value = 'Test VPC Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const cidrInput = fixture.debugElement.query(By.css('[data-testid="cidr-input"]')).nativeElement as HTMLInputElement;
            cidrInput.value = '10.0.0.0/16';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const networkDomainInput = fixture.debugElement.query(By.css('[data-testid="network-domain-input"]')).nativeElement as HTMLInputElement;
            networkDomainInput.value = 'test.domain';
            networkDomainInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            // Select VPC offering
            selectOption(fixture, 'ng-select', 0, true, 1);
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();

            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).toHaveBeenCalledOnceWith({
                zoneId: zones[0].id,
                name: 'Test VPC',
                description: 'Test VPC Description',
                cidr: '10.0.0.0/16',
                vpcOfferingId: zone1VpcOfferings[0].id,
                networkDomain: 'test.domain'
            }, 'domain-id', 'account');
        }));

        it('should submit with minimal required fields', fakeAsync(() => {
            fixture.detectChanges();

            // Fill out only required fields
            const nameInput = fixture.debugElement.query(By.css('[data-testid="name-input"]')).nativeElement as HTMLInputElement;
            nameInput.value = 'Minimal VPC';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const descriptionInput = fixture.debugElement.query(By.css('[data-testid="description-input"]')).nativeElement as HTMLInputElement;
            descriptionInput.value = 'Minimal Description';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const cidrInput = fixture.debugElement.query(By.css('[data-testid="cidr-input"]')).nativeElement as HTMLInputElement;
            cidrInput.value = '***********/24';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            // Select VPC offering (assuming it's required)
            selectOption(fixture, 'ng-select', 1, true, 1);
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();

            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).toHaveBeenCalledOnceWith({
                zoneId: zones[0].id,
                name: 'Minimal VPC',
                description: 'Minimal Description',
                cidr: '***********/24',
                vpcOfferingId: zone1VpcOfferings[1].id,
                networkDomain: ''
            }, 'domain-id', 'account');
        }));

        it('should submit with different zone selected', fakeAsync(() => {
            fixture.detectChanges();

            // Change zone first
            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            // Fill out form fields
            const nameInput = fixture.debugElement.query(By.css('[data-testid="name-input"]')).nativeElement as HTMLInputElement;
            nameInput.value = 'Zone2 VPC';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const descriptionInput = fixture.debugElement.query(By.css('[data-testid="description-input"]')).nativeElement as HTMLInputElement;
            descriptionInput.value = 'VPC in Zone 2';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const cidrInput = fixture.debugElement.query(By.css('[data-testid="cidr-input"]')).nativeElement as HTMLInputElement;
            cidrInput.value = '**********/12';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const networkDomainInput = fixture.debugElement.query(By.css('[data-testid="network-domain-input"]')).nativeElement as HTMLInputElement;
            networkDomainInput.value = 'zone2.domain';
            networkDomainInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            // Select VPC offering for zone 2
            selectOption(fixture, 'ng-select', 0, true, 1);
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();

            expect(mockVirtualPrivateCloudService.createVirtualPrivateCloud).toHaveBeenCalledOnceWith({
                zoneId: zones[1].id,
                name: 'Zone2 VPC',
                description: 'VPC in Zone 2',
                cidr: '**********/12',
                vpcOfferingId: zone2VpcOfferings[0].id,
                networkDomain: 'zone2.domain'
            }, 'domain-id', 'account');
        }));
    });

});
