import { NgComponentOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, OnInit, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { ScheduleType } from '@app/shared/models/schedule-type.enum';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbActiveModal, NgbNav, NgbNavContent, NgbNavItem, NgbNavLink, NgbNavOutlet } from '@ng-bootstrap/ng-bootstrap';
import { provideComponentStore } from '@ngrx/component-store';
import { combineLatest, forkJoin, map, mergeMap } from 'rxjs';
import { ScheduleMonthsType } from '../../models/schedule/schedule-months-type.enum';
import { CreateEditScheduleRequest } from '../../modules/policy/requests/create-edit-schedule-request';
import { CreatePolicyRequest } from '../../modules/policy/requests/create-policy.request';
import { EditPolicyRequest } from '../../modules/policy/requests/edit-policy.request';
import { OrganizationScheduleService } from '../../modules/policy/services/organization-schedules.service';
import { ScheduleService } from '../../modules/policy/services/schedule.service';
import { OrganizationPolicyService } from '../../services/organization-policy.service';
import { PolicyService } from '../../services/policy.service';
import { ScheduleInfoComponent } from './schedule-info/schedule-info.component';
import { ScheduleTypeComponent } from './schedule-type/schedule-type.component';
import { ScheduleInfoStore, ScheduleState, ScheduleStore, ScheduleTypeStore } from './schedule.component.store';

@Component({
    selector: 'app-schedule-form',
    imports: [
        BtnSubmitComponent,
        NgbNav,
        NgbNavOutlet,
        NgbNavItem,
        NgbNavLink,
        NgbNavContent,
        NgComponentOutlet
    ],
    templateUrl: './schedule-form.component.html',
    providers: [
        provideComponentStore(ScheduleStore)
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ScheduleFormComponent implements OnInit {
    private readonly activeModal = inject(NgbActiveModal);
    private readonly policyService = inject(PolicyService);
    private readonly organizationPoliciesService = inject(OrganizationPolicyService);
    private readonly userContextService = inject(UserContextService);
    private readonly notificationService = inject(NotificationService);
    private readonly scheduleService = inject(ScheduleService);
    private readonly organizationScheduleService = inject(OrganizationScheduleService);
    readonly scheduleStore = inject(ScheduleStore);
    private readonly destroyRef = inject(DestroyRef);

    public readonly scheduleId = signal<number | null>(null);
    public readonly policyId = signal<number | null>(null);
    public readonly isReadOnly = signal<boolean>(false);
    public readonly releaseTagName = signal<string>('');

    protected activeTab = 1;
    protected readonly isInfoFormValid = signal<boolean>(false);
    protected readonly isFormSubmittable = signal<boolean>(false);
    protected readonly hasMicrosoftUpdates = signal<boolean>(false);
    private scheduleState: ScheduleState;

    protected readonly submitButtonLabel = computed(() => (!this.policyId() ? 'Create' : 'Save'));
    public readonly dialogTitle = computed(() => (!this.policyId() ? 'Create Policy' : (this.isReadOnly() ? 'View Policy' : 'Edit Policy')));

    public readonly scheduleInfoTab = ScheduleInfoComponent;
    public readonly scheduleTypeTab = ScheduleTypeComponent;

    ngOnInit(): void {
        if (this.policyId()) {
            this.scheduleStore.updateIsEditingState(true);
            this.setFormForScheduleEdit();
        }

        this.scheduleStore.getScheduleInfoFormValid$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(valid => this.isInfoFormValid.set(valid));
        this.scheduleStore.getScheduleHasMicrosoftUpdates$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(value => this.hasMicrosoftUpdates.set(value));

        this.scheduleStore.getScheduleState$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(state => this.scheduleState = state);
        combineLatest([this.scheduleStore.getScheduleInfoFormValid$, this.scheduleStore.getScheduleTypeFormState$])
            .subscribe(([infoFormValid, scheduleTypeFormState]) => {
                switch (scheduleTypeFormState.scheduleType) {
                    case ScheduleType.Daily:
                        this.isFormSubmittable.set(infoFormValid && scheduleTypeFormState.dailySchedule.isValid && !scheduleTypeFormState.isReadOnly);
                        break;
                    case ScheduleType.Weekly:
                        this.isFormSubmittable.set(infoFormValid && scheduleTypeFormState.weeklySchedule.isValid && !scheduleTypeFormState.isReadOnly);
                        break;
                    case ScheduleType.Monthly:
                        this.isFormSubmittable.set(infoFormValid && scheduleTypeFormState.monthlySchedule.isValid && !scheduleTypeFormState.isReadOnly);
                        break;
                    default:
                        this.isFormSubmittable.set(false);
                }
            });
    }

    private setFormForScheduleEdit() {
        const minVersion$ = this.policyService.getMinAgentVersionImmediateUpdateInstall(this.userContextService.currentUser.organizationId);
        if (this.scheduleId()) {
            const schedule$ = this.organizationScheduleService.getScheduleFromAncestorOrganization(this.userContextService.currentUser.organizationId, this.scheduleId());

            forkJoin([schedule$, minVersion$]).subscribe(([res, minVersionRes]) => {
                const scheduleTypeForm: ScheduleTypeStore = {
                    scheduleType: (res.data.scheduleType === ScheduleType.MonthlyRelative) ? ScheduleType.Monthly : res.data.scheduleType,
                    dailySchedule: {
                        freqRecurrenceFactor: (res.data.scheduleType === ScheduleType.Daily) ? res.data.freqRecurrenceFactor : null,
                        isValid: res.data.scheduleType === ScheduleType.Daily
                    },
                    weeklySchedule: {
                        freqRecurrenceFactor: (res.data.scheduleType === ScheduleType.Weekly) ? res.data.freqRecurrenceFactor : null,
                        weeklyDays: res.data.weeklyDays,
                        isValid: res.data.scheduleType === ScheduleType.Weekly
                    },
                    monthlySchedule: {
                        scheduleMonthsType: (res.data.scheduleType === ScheduleType.MonthlyRelative) ? ScheduleMonthsType.On : ScheduleMonthsType.Days,
                        months: res.data.months,
                        monthDays: res.data.monthDays,
                        monthWeeks: res.data.monthOnWeek,
                        monthWeeksDays: res.data.monthOnWeekNumber,
                        isValid: (res.data.scheduleType === ScheduleType.MonthlyRelative || res.data.scheduleType === ScheduleType.Monthly)
                    },
                    autoApprovalUpdateCategoryInfo: { autoApprovalUpdateCategories: res.data.updateCategoriesAutoApproval, isReadOnly: this.isReadOnly() },
                    startDateLocalized: new Date(res.data.startDateTimeLocalized),
                    isReadOnly: this.isReadOnly()
                };

                this.scheduleStore.updateScheduleTypeFormState(scheduleTypeForm);
                this.scheduleStore.updateUpdateCategoriesFormState(scheduleTypeForm.autoApprovalUpdateCategoryInfo);

                const scheduleInfoForm: ScheduleInfoStore = {
                    name: res.data.name,
                    description: res.data.description,
                    isScheduleInfoFormValid: true,
                    isReadOnly: this.isReadOnly(),
                    hasMicrosoftUpdates: res.data.isEnabled,
                    minAgentVersionImmediateUpdateInstall: minVersionRes.data,
                    releaseTagName: this.releaseTagName()
                };

                this.scheduleStore.updateScheduleInfoFormState(scheduleInfoForm);
            });
        } else {
            const schedule$ = this.organizationPoliciesService.getPolicyFromAncestorOrganization(this.userContextService.currentUser.organizationId, this.policyId());

            forkJoin([schedule$, minVersion$]).subscribe(([res, minVersionRes]) => {
                const scheduleInfoForm: ScheduleInfoStore = {
                    name: res.data.name,
                    description: res.data.description,
                    isScheduleInfoFormValid: true,
                    isReadOnly: this.isReadOnly(),
                    hasMicrosoftUpdates: res.data.isEnabled,
                    minAgentVersionImmediateUpdateInstall: minVersionRes.data,
                    releaseTagName: this.releaseTagName()
                };

                this.scheduleStore.updateScheduleInfoFormState(scheduleInfoForm);
            });

        }
    }

    submitForm() {
        const createEditScheduleRequest: CreateEditScheduleRequest = {
            name: this.scheduleState.scheduleInfoForm.name,
            description: this.scheduleState.scheduleInfoForm.description,
            startDate: this.scheduleState.scheduleTypeForm.startDateLocalized,
            scheduleType: this.scheduleState.scheduleTypeForm.scheduleType,
            scheduleTypeMonthsType: this.scheduleState.scheduleTypeForm.monthlySchedule.scheduleMonthsType,
            freqRecurrenceFactor: this.scheduleState.scheduleTypeForm.dailySchedule.freqRecurrenceFactor,
            weeklyDays: this.scheduleState.scheduleTypeForm.weeklySchedule.weeklyDays,
            months: this.scheduleState.scheduleTypeForm.monthlySchedule.months,
            monthDays: this.scheduleState.scheduleTypeForm.monthlySchedule.monthDays,
            monthOnWeek: this.scheduleState.scheduleTypeForm.monthlySchedule.monthWeeks,
            monthOnWeekNumber: this.scheduleState.scheduleTypeForm.monthlySchedule.monthWeeksDays,
            isEnabled: this.scheduleState.scheduleInfoForm.hasMicrosoftUpdates
        };

        const createPolicyRequest: CreatePolicyRequest = {
            name: this.scheduleState.scheduleInfoForm.name,
            description: this.scheduleState.scheduleInfoForm.description,
            isEnabled: this.scheduleState.scheduleInfoForm.hasMicrosoftUpdates,
            scheduleId: this.scheduleId(),
            autoApprovalUpdateCategories: this.scheduleState.autoApprovalState.autoApprovalUpdateCategories,
            releaseTagName: this.scheduleState.scheduleInfoForm.releaseTagName
        };

        if (createEditScheduleRequest.scheduleType === ScheduleType.Weekly) {
            createEditScheduleRequest.freqRecurrenceFactor = this.scheduleState.scheduleTypeForm.weeklySchedule.freqRecurrenceFactor;
        }
        if (this.scheduleState.isEditing) {
            this.submitEditedSchedule(createEditScheduleRequest, createPolicyRequest);
        } else {
            this.submitNewSchedule(createPolicyRequest, createEditScheduleRequest);
        }
    }

    private submitEditedSchedule(scheduleRequest: CreateEditScheduleRequest, createRequest: CreatePolicyRequest) {
        const editScheduleRequest: EditPolicyRequest = {
            name: createRequest.name,
            description: createRequest.description,
            isEnabled: createRequest.isEnabled,
            schedule: scheduleRequest,
            scheduleId: this.scheduleId(),
            autoApprovalUpdateCategories: this.scheduleState.autoApprovalState.autoApprovalUpdateCategories,
            releaseTagName: createRequest.releaseTagName
        };

        if (this.scheduleId()) {
            forkJoin([
                this.scheduleService.editSchedule(this.scheduleId(), scheduleRequest),
                this.policyService.editPolicy(this.policyId(), editScheduleRequest)
            ]).pipe(map(([, editPolicy]) => editPolicy))
                .subscribe(editPolicy => {
                    this.notificationService.notify(editPolicy.message);
                    this.activeModal.close(editPolicy);
                });
        } else if (this.policyId() && !scheduleRequest.isEnabled) {
            this.policyService.editPolicy(this.policyId(), editScheduleRequest)
                .subscribe(r => {
                    this.notificationService.notify(r.message);
                    this.activeModal.close(r);
                });
        } else {
            this.organizationScheduleService.createSchedule(this.userContextService.currentUser.organizationId, scheduleRequest)
                .pipe(
                    map(j => editScheduleRequest.scheduleId = j?.data),
                    mergeMap(() => this.policyService.editPolicy(this.policyId(), editScheduleRequest))
                )
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });
        }
    }

    private submitNewSchedule(request: CreatePolicyRequest, requestEdit: CreateEditScheduleRequest) {
        if (!request.scheduleId && !request.isEnabled) {
            this.policyService.createPolicy(request, this.userContextService.currentUser.organizationId)
                .subscribe(response => {
                    this.notificationService.notify(response.message);
                    this.activeModal.close(response);
                });
        } else {
            this.organizationScheduleService.createSchedule(this.userContextService.currentUser.organizationId, requestEdit)
                .pipe(
                    map(j => request.scheduleId = j?.data),
                    mergeMap(() => this.policyService.createPolicy(request, this.userContextService.currentUser.organizationId))
                )
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });
        }
    }

    public cancel(): void {
        this.activeModal.close(false);
    }
}
