import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { ResetSSHKeyPairForm } from '../../forms/reset-ssh-key-pair.form';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-reset-ssh-key-pair',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgSelectComponent, AsyncPipe],
    templateUrl: './reset-ssh-key-pair.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ResetSSHKeyPairComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);

    readonly inputData = signal<{
        virtualMachineId: string;
        domainId: string;
        account: string;
    }>(null);

    protected keyPairs$: Observable<{ name: string }[]>;

    protected form = this.formBuilder.group<ResetSSHKeyPairForm>({
        keyPair: this.formBuilder.control<string | null>(null, Validators.required),
    });

    protected readonly isSubmitting = signal<boolean>(false);

    ngOnInit(): void {
        this.keyPairs$ = this.vmManagementService.getKeyPairList(this.inputData().domainId, this.inputData().account);
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected resetSSHKeyPair() {
        this.isSubmitting.set(true);
        if (this.form.valid) {

            this.vmManagementService.resetSSHKeyPairForVirtualMachine(
                this.inputData().virtualMachineId,
                this.form.controls.keyPair.value,
                this.inputData().domainId,
                this.inputData().account
            )
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
