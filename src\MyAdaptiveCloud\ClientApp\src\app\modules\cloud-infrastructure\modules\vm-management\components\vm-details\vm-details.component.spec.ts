/* eslint-disable @typescript-eslint/no-explicit-any */
import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { VmStateEnum } from '@app/shared/models/cloud-infra/vm-state.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { VmDetails } from '../../models/vm-detail.model';
import { CloudInfrastructureTagsService } from '../../services/cloud-infra-tags.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmDetailsComponent } from './vm-details.component';

describe('VmDetailsComponent', () => {
    let fixture: ComponentFixture<VmDetailsComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let vmDetailsStateService: VmDetailsStateService;
    let mockCloudInfraTagsService: jasmine.SpyObj<CloudInfrastructureTagsService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let el: DebugElement;
    let component: VmDetailsComponent;

    let mockVmRunning: VmDetails;
    let mockVmStopped: VmDetails;

    beforeEach(() => {

        mockVmRunning = {
            affinitygroup: [],
            id: 'vm-001',
            name: '1-Running',
            displayname: 'web-server-01',
            account: 'account',
            domainid: 'id',
            domain: 'example.com',
            state: VmStateEnum.Running,
            zoneid: 'zone-101',
            zonename: 'Zone A',
            hostid: 'host-202',
            isoid: 'iso-404',
            nic: [],
            osdisplayname: 'Linux',
            passwordenabled: true,
            created: new Date(2025, 10, 10).toISOString(),
            cpunumber: 2,
            memory: 4096,
            cpuused: '50%',
            memorykbs: 2048000,
            templatename: 'Ubuntu 20.04',
            isAgentInstalled: true,
            isdynamicallyscalable: true,
            keypairs: [{ key: 'ssh-key', value: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...' }],
            tags: [{ key: 'env', value: 'prod' }],
            vgpu: 'NVIDIA vGPU',
            cpuUsagePercentage: 50,
            memoryUsagePercentage: 25,
            memoryUsagePercentageString: '25%',
        };

        mockVmStopped = {
            ...mockVmRunning,
            id: 'vm-002',
            name: '2-Stopped',
            displayname: 'db-server-02',
            state: VmStateEnum.Stopped,
            isAgentInstalled: false,
            vgpu: null,
            keypairs: [],
        };

        TestBed.configureTestingModule({
            imports: [VmDetailsComponent],
            providers: [
                VmDetailsStateService,
                provideMock(UserContextService),
                provideMock(CloudInfrastructureTagsService),
                provideMock(CloudInfraPermissionService)
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        vmDetailsStateService = TestBed.inject(VmDetailsStateService);
        mockCloudInfraTagsService = TestBed.inject(CloudInfrastructureTagsService) as jasmine.SpyObj<CloudInfrastructureTagsService>;
        mockCloudInfraTagsService.createTags.and.returnValue(of('jobId'));
        mockCloudInfraTagsService.deleteTags.and.returnValue(of('jobId'));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                roleType: 'User',
            } as CloudInfraUserContext
        } as UserContext;

        mockUserContextService.currentUser = {
            organizationId: 0,
        } as UserContext;

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;

        fixture = TestBed.createComponent(VmDetailsComponent);
        component = fixture.componentInstance;
        el = fixture.debugElement;
    });

    describe('Compute Offering', () => {

        it('should display CPU number', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);

            fixture.detectChanges();
            const cpuNumber = el.query(By.css('.cpu .text-secondary:nth-child(2)'));
            expect(cpuNumber).toBeTruthy();
            expect(cpuNumber.nativeElement.textContent).toContain(mockVmRunning.cpunumber.toString());
        });

        it('should display CPU usage percentage string', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();
            const cpuUsedText = el.query(By.css('.cpu .ms-3'));
            expect(cpuUsedText).toBeTruthy();
            expect(cpuUsedText.nativeElement.textContent).toContain(`${mockVmRunning.cpuused} Used`);
        });

        it('should set CPU progress bar width', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();
            const progressBar = el.query(By.css('.cpu .progress-bar'));
            expect(progressBar).toBeTruthy();
            expect(progressBar.nativeElement.style.width).toBe(mockVmRunning.cpuused);
            expect(progressBar.nativeElement.getAttribute('aria-valuenow')).toBe(mockVmRunning.cpuUsagePercentage.toString());
        });

        it('should display Memory (in MB)', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();
            const memory = el.query(By.css('.memory .text-secondary:nth-child(2)'));
            expect(memory).toBeTruthy();
            expect(memory.nativeElement.textContent).toContain(mockVmRunning.memory.toString());
        });

        it('should display Memory usage percentage', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();
            const memoryUsedText = el.query(By.css('.memory .ms-3'));
            expect(memoryUsedText).toBeTruthy();
            expect(memoryUsedText.nativeElement.textContent).toContain(`${mockVmRunning.memoryUsagePercentage}% Used`);
        });

        it('should set Memory progress bar width', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();
            const progressBar = el.query(By.css('.memory .progress-bar'));
            expect(progressBar).toBeTruthy();
            expect(progressBar.nativeElement.style.width).toBe(mockVmRunning.memoryUsagePercentageString);
            expect(progressBar.nativeElement.getAttribute('aria-valuenow')).toBe(mockVmRunning.memoryUsagePercentage.toString());
        });
    });

    describe('Location & OS', () => {

        it('should display Zone Name', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();
            const zoneName = el.query(By.css('.zone .d-flex.flex-column div:last-child'));
            expect(zoneName).toBeTruthy();
            expect(zoneName.nativeElement.textContent).toContain(mockVmRunning.zonename);
        });

        it('should display OS Type', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);

            fixture.detectChanges();
            const osType = el.query(By.css('.os .d-flex.flex-column div:last-child'));
            expect(osType).toBeTruthy();
            expect(osType.nativeElement.textContent).toContain(mockVmRunning.osdisplayname);
        });

        it('should display SSH Key Pair if available', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();
            const keyPair = el.query(By.css('.key .d-flex.flex-column div:last-child'));
            expect(keyPair).toBeTruthy();
            expect(keyPair.nativeElement.textContent).toContain(mockVmRunning.keypairs[0].value);
        });

        it('should display "N/A" for SSH Key Pair if not available', () => {
            const vmNoKeypairs = { ...mockVmRunning, keypairs: [] };
            vmDetailsStateService.selectedVM.set(vmNoKeypairs);
            fixture.detectChanges();
            const keyPair = el.query(By.css('.key .d-flex.flex-column div:last-child'));
            expect(keyPair).toBeTruthy();
            expect(keyPair.nativeElement.textContent).toContain('N/A');
        });

        it('should display vGPU if available', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);

            fixture.detectChanges();
            const vgpu = el.query(By.css('.gpu .d-flex.flex-column div:last-child'));
            expect(vgpu).toBeTruthy();
            expect(vgpu.nativeElement.textContent).toContain(mockVmRunning.vgpu);
        });

        it('should display "N/A" for vGPU if not available', () => {
            vmDetailsStateService.selectedVM.set(mockVmStopped);

            fixture.detectChanges();
            const vgpu = el.query(By.css('.gpu .d-flex.flex-column div:last-child'));
            expect(vgpu).toBeTruthy();
            expect(vgpu.nativeElement.textContent).toContain('N/A');
        });

        it('should display Dynamically Scalable as "Yes"', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);

            fixture.detectChanges();
            const scalable = el.query(By.css('.scalable .d-flex.flex-column div:last-child'));
            expect(scalable).toBeTruthy();
            expect(scalable.nativeElement.textContent).toContain('Yes');
        });

        it('should display Dynamically Scalable as "No"', () => {
            const vmNotScalable = { ...mockVmRunning, isdynamicallyscalable: false };
            vmDetailsStateService.selectedVM.set(vmNotScalable);

            fixture.detectChanges();
            const scalable = el.query(By.css('.scalable .d-flex.flex-column div:last-child'));
            expect(scalable).toBeTruthy();
            expect(scalable.nativeElement.textContent).toContain('No');
        });

        it('should display Template name', () => {
            vmDetailsStateService.selectedVM.set(mockVmRunning);

            fixture.detectChanges();
            const template = el.query(By.css('.template .d-flex.flex-column div:last-child'));
            expect(template).toBeTruthy();
            expect(template.nativeElement.textContent).toContain(mockVmRunning.templatename);
        });
    });

    describe('Tags Management', () => {

        it('should not display the section when the user is not root admin', () => {

            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();

            const addBtn = fixture.debugElement.query(By.css('[data-testid="add-tag"]'));
            const deleteBtn = fixture.debugElement.query(By.css('[data-testid="remove-tag"]'));

            expect(addBtn).toBeNull();
            expect(deleteBtn).toBeNull();

        });

        it('should call createTags() and reset form when clicking add button', () => {

            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            vmDetailsStateService.selectedVM.set(mockVmRunning);

            (component as any).tagsForm.setValue({ tagKey: 'region', tagValue: 'us-east' });
            fixture.detectChanges();

            const addBtn = fixture.debugElement.query(By.css('[data-testid="add-tag"]'));
            addBtn.nativeElement.click();
            fixture.detectChanges();

            expect(mockCloudInfraTagsService.createTags).toHaveBeenCalledWith('vm-001', [{ key: 'region', value: 'us-east' }]);
        });

        it('should call deleteTags() and remove tag when clicking delete button', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            vmDetailsStateService.selectedVM.set(mockVmRunning);
            fixture.detectChanges();

            const deleteBtn = fixture.debugElement.query(By.css('[data-testid="remove-tag"]'));
            (deleteBtn.nativeElement as HTMLButtonElement).click();
            fixture.detectChanges();

            expect(mockCloudInfraTagsService.deleteTags).toHaveBeenCalledWith('vm-001', [{ key: 'env', value: 'prod' }]);
        });

    });
});
