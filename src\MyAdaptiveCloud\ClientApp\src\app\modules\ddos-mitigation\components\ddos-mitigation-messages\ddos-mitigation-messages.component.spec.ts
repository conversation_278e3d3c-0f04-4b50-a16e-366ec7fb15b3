import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DdosMitigationMessagesComponent } from './ddos-mitigation-messages.component';

describe('DdosMitigationMessagesComponent', () => {
    let component: DdosMitigationMessagesComponent;
    let fixture: ComponentFixture<DdosMitigationMessagesComponent>;

    const messages = [
        'Mitigation requested',
        'Task created',
        'Mitigation successfully initiated.'
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationMessagesComponent],
            providers: [provideMock(NgbActiveModal)]
        })
            .compileComponents();

        fixture = TestBed.createComponent(DdosMitigationMessagesComponent);
        component = fixture.componentInstance;
    });

    it('should display all the messages', () => {
        component.messages.set(messages);
        fixture.detectChanges();
        const listItems = fixture.debugElement.queryAll(By.css('.list-group-item'));
        expect(listItems.length).toBe(3);
        expect(listItems[0].nativeElement.textContent.trim()).toBe(component.messages()[0]);
        expect(listItems[1].nativeElement.textContent.trim()).toBe(component.messages()[1]);
        expect(listItems[2].nativeElement.textContent.trim()).toBe(component.messages()[2]);
    });

    it('should display no messages', () => {
        component.messages.set([]);
        fixture.detectChanges();
        const listItems = fixture.debugElement.queryAll(By.css('.list-group-item'));
        expect(listItems.length).toBe(1);
        expect(listItems[0].nativeElement.textContent.trim()).toBe('No messages to display.');
    });

});
