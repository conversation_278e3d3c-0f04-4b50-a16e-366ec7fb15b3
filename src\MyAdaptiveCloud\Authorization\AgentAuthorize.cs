using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class AgentAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public AgentAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService, IUserContextService userContextService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int agentId);

            var agentOrganizationId = await _entityAuthorizationService.GetAgentOrganizationId(agentId);
            if (agentOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, agentOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, agentOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class AgentAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public AgentAuthorizeAttribute(params Perms[] perms) : base(typeof(AgentAuthorizeFilter), perms)
        {
            Name = "agentId";
        }
    }
}