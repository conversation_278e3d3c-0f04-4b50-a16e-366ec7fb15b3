export interface NetworkListViewModel {
    account: string;
    cidr: string;
    description: string | null;
    domain: string;
    domainId: string;
    id: string;
    isSystem: boolean;
    networkDomain: string | null;
    networkOfferingId: string;
    name: string;
    state: 'Implemented' | 'Implementing' | 'Allocated' | 'Setup';
    type: 'Isolated' | 'Shared' | 'L2';
    vpc: string;
    zoneName: string;
    zoneId: string;
}
