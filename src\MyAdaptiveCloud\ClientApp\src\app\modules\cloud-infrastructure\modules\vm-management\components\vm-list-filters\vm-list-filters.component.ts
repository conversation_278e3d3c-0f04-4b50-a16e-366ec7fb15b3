import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { FilterGroupAndRequest } from '@app/shared/models/datatable/filters/filter-group-and-request';
import { IDynamicFilterOption } from '@app/shared/models/datatable/filters/idynamic-filter-option';

@Component({
    selector: 'app-vm-list-filters',
    imports: [ReactiveFormsModule],
    templateUrl: './vm-list-filters.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmListFiltersComponent {

    readonly filters = input.required<FilterGroupAndRequest>();
    readonly zones = input.required<ZoneViewModel[]>();

    protected readonly zoneOptions = computed(() => this.zones()?.map(zone => {
        const z: IDynamicFilterOption = {
            id: zone.id,
            label: zone.name
        };
        return z;
    }));
}
