<div class="content-heading">
    <div class="header-title">Mitigation</div>
</div>

<div class="content-sub-heading">
    <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
    <div class="action-buttons">
            <div class="d-inline-block" ngbDropdown>
                <button type="button" class="btn btn-primary"
                    ngbDropdownToggle>Start Mitigation</button>
                <div ngbDropdownMenu>
                    <button ngbDropdownItem
                        (click)="openStartMitigationModal(mitigationTypeEnum.Blackhole)">Blackhole</button>
                    <button ngbDropdownItem
                        (click)="openStartMitigationModal(mitigationTypeEnum.Scrub)">Scrub</button>
                </div>
        </div>
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap no-detail-row" />
    </div>
</div>

<ng-template #statusTemplate let-row="row">
    @if (toItem(row); as row) {
        <div class="d-flex align-items-center justify-content-space">
            <div class="col-6 d-flex">
                @let taskStatusMessage = row.taskStatusMessage;
                @switch (row.taskStatus) {
                    @case (TaskStatusEnum.Pending) {
                        <div [title]="taskStatusMessage" class="spinner-border spinner-border-sm text-secondary me-1"
                            role="taskStatus">
                        </div>
                    }
                    @case (TaskStatusEnum.Failed) {
                        <i [title]="taskStatusMessage" class="fa-solid fa-warning text-danger"></i>
                    }
                    @case (TaskStatusEnum.Warning) {
                        <i [title]="taskStatusMessage" class="fa fa-warning text-warning"></i>
                    }
                }
            </div>
            <div class="col-6 d-flex">
                @let status = row.status;
                @switch (status) {
                    @case (StatusEnum.StopComplete) {
                        <div class="circle text-bg-secondary" title="Inactive">
                        </div>
                    }
                    @case (StatusEnum.Phase1StopComplete) {
                        <div class="circle text-bg-secondary blinking" title="Stopping"></div>
                    }
                    @case (StatusEnum.Phase2StartComplete) {
                        <div class="circle text-bg-success blinking" title="Mitigating (Propagating)"></div>
                    }
                    @case (StatusEnum.StartComplete) {
                        <div class="circle text-bg-success" title="Mitigating"></div>
                    }
                }
            </div>
        </div>
    }

</ng-template>

<ng-template #dateCellTemplate let-value="value">
    <span>
        {{ (value) ? (value | date: 'yyyy-MM-dd HH:mm:ss') : '-' }}
    </span>
</ng-template>

<ng-template #messageTemplate let-row="row">
    @if (toItem(row); as row) {
        <!-- Tooltip shows the last three messages in reverse order, while the displayed message shows the most recent message -->
        @let title = row.messages?.slice(-3).reverse().join('\n');
        <span [title]="title" class="text-break">{{ row.messages?.length ? row.messages[row.messages.length - 1] : '' }}</span>
    }
</ng-template>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        @let preventAction = row.taskStatus === TaskStatusEnum.Pending || row.taskStatus === TaskStatusEnum.Running;
        <div class="d-flex justify-content-center align-items-center">
            @if (row.active) {
                <app-table-action [enabled]="!preventAction" (clickHandler)="stop(row)"
                    [icon]="'fa-solid fa-circle-stop text-danger fs-5'" [title]="'Stop Mitigation'" />
            }
            @else {
                <app-table-action [enabled]="!preventAction" (clickHandler)="restart(row)"
                    [icon]="'fa-solid fa-rotate-right text-primary fs-5'" [title]="'Restart Mitigation'" />
            }
            <app-table-action (clickHandler)="viewMessages(row)" [icon]="'icon-viewDetails'" [title]="'View Messages'" class="ms-1" />
        </div>
    }
</ng-template>
