import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-agent-management-container',
    imports: [RouterOutlet, RouterLink, RouterLinkActive],
    templateUrl: './agent-management-container.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class AgentManagementContainerComponent {

}
