export interface NetworkOffering {
    availability: string;
    conservemode: boolean;
    displaytext: string;
    egressdefaultpolicy: boolean;
    forvpc: boolean
    guestiptype: 'Isolated' | 'Shared';
    id: string
    isdefault: boolean;
    ispersistent: boolean;
    name: string
    networkrate: number;
    serviceofferingid: string;
    specifyipranges: boolean;
    specifyvlan: boolean;
    state: 'Enabled' | 'Disabled' | 'Inactive';
    supportspublicaccess: boolean;
    supportsstrechedl2subnet: boolean;
    traffictype: 'Public' | 'Management' | 'Control' | 'Guest' | 'Vlan' | 'Storage';
}
