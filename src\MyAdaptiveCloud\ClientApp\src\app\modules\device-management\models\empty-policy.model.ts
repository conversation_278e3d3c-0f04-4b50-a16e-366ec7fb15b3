import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';
import { Policy } from './policy.model';

export const EMPTY_POLICY_ID = -1;
export class EmptyPolicy extends Policy {

    constructor() {
        super();
        this.policyId = EMPTY_POLICY_ID;
        this.name = 'No Policy';
        this.canEdit = UserActionState.Forbidden;
        this.canDelete = UserActionState.Forbidden;
        this.canCreate = UserActionState.Forbidden;
        this.canView = UserActionState.Allowed;
    }
}
