import { ValidatorFn, AbstractControl, ValidationErrors } from '@angular/forms';
import { isValidCidr } from '@app/shared/utils/helpers';

export function networkDomainValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value as string;
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;

        if (!value) {
            return null;
        }

        // Must be 1 to 63 characters
        if (value.length < 1 || value.length > 63) {
            return { invalidLength: true };
        }

        // Must match the regex (allowed characters and no leading/trailing hyphen)
        if (!domainRegex.test(value)) {
            return { invalidFormat: true };
        }

        // If used as hostname, cannot start with digit (optional extra check)
        if (/^[0-9]/.test(value)) {
            return { startsWithDigit: true };
        }

        return null;
    };
}

export function validateCidr(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => (!isValidCidr(control.value) ? { cidr: true } : null);
}
