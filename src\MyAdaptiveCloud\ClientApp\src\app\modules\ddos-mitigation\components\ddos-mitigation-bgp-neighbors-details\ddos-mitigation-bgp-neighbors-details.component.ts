import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Neighbor } from '../../models/neighbor.model';

@Component({
    selector: 'app-ddos-mitigation-bgp-neighbors-details',
    imports: [],
    templateUrl: './ddos-mitigation-bgp-neighbors-details.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DdosMitigationBgpNeighborsDetailsComponent {
    protected readonly activeModal = inject(NgbActiveModal);
    readonly neighborDetails = signal<Neighbor>(null);
}
