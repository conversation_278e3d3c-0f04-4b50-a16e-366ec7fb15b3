import { TaskStatusEnum } from '@app/shared/models/ddos-mitigation/task-status.enum';
import { StatusEnum } from './status.enum';

export interface MitigationViewModel {
    cidr: string;
    mitigationType: string;
    taskStatus: TaskStatusEnum;
    taskStatusMessage: string;
    status: StatusEnum;
    startTime: Date | null;
    stopTime: Date | null;
    messages: string[];
    taskId: string;
    simulate: boolean;
    active: boolean;
}
