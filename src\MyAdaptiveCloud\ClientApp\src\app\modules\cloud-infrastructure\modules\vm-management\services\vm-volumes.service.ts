import { inject, Injectable } from '@angular/core';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { map, Observable } from 'rxjs';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '../models/vm.constants';
import { Volume } from '../models/volume';
import { ListVolumeResponse } from '../responses/list-volume.response';

@Injectable({
    providedIn: 'root'
})
export class VmVolumesService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getVolumeMetricsByVirtualMachine(virtualMachineId: string): Observable<Volume[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listVolumes,
            details: 'all',
            listall: 'true',
            virtualmachineid: virtualMachineId
        };

        return this.cloudInfraApiService.get<ListVolumeResponse>(params)
            .pipe(map((response: ListVolumeResponse) => response.listvolumesresponse?.volume || []));
    }
}
