import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { Organization } from '@app/shared/models/organization.model';
import { UserContext } from '@app/shared/models/user-context.model';
import { DeviceManagementRoutingService } from '@app/shared/services/device-management-routing.service';
import { ModalService } from '@app/shared/services/modal.service';
import { OrganizationSharedService } from '@app/shared/services/organization-shared.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideComponentStore } from '@ngrx/component-store';
import { of } from 'rxjs';
import { DeviceFolder } from '../../models/device-folder';
import { DeviceFolderTreeNodeDto } from '../../models/device-folder-tree-node-dto';
import { OrganizationFolder } from '../../models/organization-folder.model';
import { DeviceFolderService } from '../../services/device-folder.service';
import { DevicesService } from '../../services/devices.service';
import { OrganizationFoldersService } from '../../services/organization-folders.service';
import { FoldersTreeStore } from '../../store/folders-tree.store';
import { OrganizationThresholdsService } from '../device-thresholds/services/device-thresholds.service';
import { BrowseFoldersDevicesModalComponent } from './browse-folders-devices-modal.component';

describe('BrowseFoldersDevicesModalComponent', () => {
    let component: BrowseFoldersDevicesModalComponent;
    let fixture: ComponentFixture<BrowseFoldersDevicesModalComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockDeviceFolderService: jasmine.SpyObj<DeviceFolderService>;
    let mockOrganizationService: jasmine.SpyObj<OrganizationSharedService>;
    let mockFoldersService: jasmine.SpyObj<OrganizationFoldersService>;

    const deviceChildrenFolders: DeviceFolderTreeNodeDto[] = [
        {
            folderId: 4,
            name: 'D',
            description: 'D desc',
            deviceCount: 0,
            hasSubfolders: false,
            parentFolderId: 1,
            deviceCountCurrentFolder: 0
        },
        {
            folderId: 5,
            name: 'E',
            description: 'E desc',
            deviceCount: 0,
            hasSubfolders: false,
            parentFolderId: 1,
            deviceCountCurrentFolder: 0
        }
    ];

    const deviceRootFolders: DeviceFolder[] = [
        {
            folderId: 1,
            name: 'A',
            description: 'A desc',
            deviceCount: 0,
            hasSubfolders: true,
            parentFolderId: null,
            deviceCountCurrentFolder: 0,
            policyId: null,
            inheritsPolicy: false,
            policyInheritedFrom: ''
        },
        {
            folderId: 2,
            name: 'B',
            description: 'B desc',
            deviceCount: 0,
            hasSubfolders: false,
            parentFolderId: null,
            deviceCountCurrentFolder: 0,
            policyId: null,
            inheritsPolicy: false,
            policyInheritedFrom: ''
        },
        {
            folderId: null,
            name: 'C',
            description: 'C desc',
            deviceCount: 0,
            hasSubfolders: true,
            parentFolderId: null,
            deviceCountCurrentFolder: 0,
            policyId: null,
            inheritsPolicy: false,
            policyInheritedFrom: ''
        }
    ];

    const organization: Organization = {
        organizationId: 1,
        name: 'Root',
        allowSubOrg: true,
        parentOrganizationId: null,
        parentOrganizationName: null,
        allowWhiteLabel: false,
        isPartner: false,
        organizationParentFullPath: 'Root'
    };

    const organizationFolder: OrganizationFolder = {
        organizationId: 1,
        name: 'Root',
        parentOrganizationId: null,
        isPartner: false,
        deviceCount: 0,
        deviceCountCurrentFolder: 0,
        hasSubfolders: true
    };

    const getOrganizationResult: ApiDataResult<Organization> = {
        data: organization,
        message: 'success'
    };

    const getOrganizationFolderResult: ApiDataResult<OrganizationFolder> = {
        data: organizationFolder,
        message: 'success'
    };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [BtnSubmitComponent],
            providers: [
                provideMock(UserContextService),
                provideMock(DeviceFolderService),
                provideMock(NgbActiveModal),
                provideMock(ModalService),
                provideMock(OrganizationThresholdsService),
                provideMock(DevicesService),
                provideMock(OrganizationSharedService),
                provideMock(OrganizationFoldersService),
                provideMock(DeviceManagementRoutingService),
                provideMock(ActivatedRoute),
                provideComponentStore(FoldersTreeStore),
                ChangeDetectorRef
            ]
        });

        mockOrganizationService = TestBed.inject(OrganizationSharedService) as jasmine.SpyObj<OrganizationSharedService>;
        mockOrganizationService.getOrganizationById.and.returnValue(of(getOrganizationResult));

        mockFoldersService = TestBed.inject(OrganizationFoldersService) as jasmine.SpyObj<OrganizationFoldersService>;
        mockFoldersService.getOrganizationFolderById.and.returnValue(of(getOrganizationFolderResult));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1
        } as UserContext;
        mockDeviceFolderService = TestBed.inject(DeviceFolderService) as jasmine.SpyObj<DeviceFolderService>;
        mockDeviceFolderService.getOrganizationFolders.and.returnValue(of({ data: deviceRootFolders, message: 'OK' }));
        mockDeviceFolderService.getChildrenFolders.and.returnValue(of({ data: deviceChildrenFolders, message: 'OK' }));
        mockDeviceFolderService.deleteDeviceFolder.and.returnValue(of());

        fixture = TestBed.createComponent(BrowseFoldersDevicesModalComponent);
        component = fixture.componentInstance;

        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
