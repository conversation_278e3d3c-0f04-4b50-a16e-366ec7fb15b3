<div class="content-heading">
    Cloud Infrastructure - ConnectWise Mapping List
</div>

<div class="content-sub-heading">
    <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" [dataItemName]="'searchTerm'" />
    <div class="action-buttons">
        <button class="btn btn-primary" type="button" (click)="addMap()">
            Add mapping
        </button>
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap no-detail-row" (sort)="onSorting($event)"
            (page)="onPageChanged($event)" />
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #billingTemplate let-row="row">
    @if (toItem(row); as row) {
        @switch (row.billingStatus) {
            @case (BillingStatus.NewDomain)
            {
                <div class="billing-icon text-center"
                    [ngbPopover]="'New Cloud Infrastructure Account/Domain, please Edit, review Billing status, and save.'"
                    placement="top-left bottom-left auto" triggers="hover" container="body">
                    <i class="fa fa-lg fa-exclamation text-danger"></i>
                </div>
            }
            @case (BillingStatus.BillingFuture)
            {
                <div class="billing-icon text-center"
                    [ngbPopover]="'We have a mapping for this, but the billing start date in ConnectWise is in the future.'"
                    placement="top-left bottom-left auto" triggers="hover" container="body">
                    <i class="fa-solid fa-clock text-warning"></i>
                </div>
            }
            @case (BillingStatus.NoBilling)
            {
                <div class="billing-icon text-center"
                    [ngbPopover]="'We have a mapping for this, and we are not billing for this account/domain.'"
                    placement="top-left bottom-left auto" triggers="hover" container="body">
                    <i class="fas fa-lg fa-dollar-sign text-secondary"></i>
                </div>
            }
            @case (BillingStatus.Billing)
            {
                <div class="billing-icon text-center"
                    [ngbPopover]="'We have a mapping for this, and we are billing for this account/domain.'"
                    placement="top-left bottom-left auto" triggers="hover" container="body">
                    <i class="fas fa-lg fa-dollar-sign text-success"></i>
                </div>
            }
        }
    }
</ng-template>

<ng-template #acNameTemplate let-row="row">
    @if (toItem(row); as row) {
        @if (!row.acEntityExists) {
            <div [ngbPopover]="'This account/domain no longer exists in Cloud Infrastructure. You may remove it once final billing is ensured.'"
                placement="top-left bottom-left auto text-danger" triggers="hover" container="body">
                {{ row.acName }}
            </div>
        } @else {
            {{ row.acName }}
        }
    }
</ng-template>

<ng-template #cwCompanyNameTemplate let-row="row">
    @if (toItem(row); as row) {
        {{ (row.cwCompanyName) ? row.cwCompanyName : '-' }}
    }
</ng-template>

<ng-template #cwAgreementNameTemplate let-row="row">
    @if (toItem(row); as row) {
        {{ (row.cwAgreementName) ? row.cwAgreementName : '-' }}
    }
</ng-template>

<ng-template #nextInvoiceDateTemplate let-row="row">
    @if (toItem(row); as row) {
        {{ (row.nextInvoiceDate) ? (row.nextInvoiceDate | date: 'yyyy-MM-dd') : '-' }}
    }
</ng-template>

<ng-template #billingDateTemplate let-row="row">
    @if (toItem(row); as row) {
        @switch (row.billingStartDateStatus) {
            @case (BillingStartDateStatus.LocalEarlierThanCW) {
                <ng-template #PriorDate>
                    Saved billing start date is prior to the ConnectWise billing start date of <br />{{
                    row.cwBillingStartDate | date: 'yyy-MM-dd':'UTC' }}<br />Please remedy here or in
                    ConnectWise!
                </ng-template>
                <div [ngbPopover]="PriorDate" triggers="hover"
                    container="body" class="text-danger">
                    {{ (row.billingStartDate) ? (row.billingStartDate | date: 'yyyy-MM-dd':'UTC') : '-' }}
                </div>
            }
            @case (BillingStartDateStatus.LocalInTheFuture) {
                <div
                    [ngbPopover]="'The billing start date is in the future. This might be okay, but should be verified.'"
                    placement="top-left bottom-left" class="text-error" triggers="hover" container="body">
                    {{ (row.billingStartDate) ? (row.billingStartDate | date: 'yyyy-MM-dd':'UTC') : '-' }}
                </div>
            }
            @default {
                <div class="text-success">
                    {{ (row.billingStartDate) ? (row.billingStartDate | date: 'yyyy-MM-dd':'UTC') : '-' }}
                </div>
            }
        }
    }
</ng-template>

<ng-template #actionsTemplate let-row=" row">
    @if (toItem(row); as row) {
        <app-table-action [icon]="'far fa-edit'" [enabled]="true" [title]="'Mapping'" (clickHandler)="onMap(row)" />
        <app-table-action [icon]="'far fa-trash-alt'" [enabled]="!!row.id" [title]="'Mapping'"
            (clickHandler)="onDelete(row)" />
    }
</ng-template>
