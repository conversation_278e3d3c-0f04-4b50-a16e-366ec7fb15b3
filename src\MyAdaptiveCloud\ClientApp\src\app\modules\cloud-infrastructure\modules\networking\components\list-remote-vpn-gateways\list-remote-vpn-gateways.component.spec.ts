import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { CloudInfraAccountService } from '@app/modules/cloud-infrastructure/services/cloud-infra-account.service';
import { CloudInfraDomainService } from '@app/modules/cloud-infrastructure/services/cloud-infra-domain.service';
import { CloudInfraZoneService } from '@app/modules/cloud-infrastructure/services/cloud-infra-zone.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { RemoteVpnGateway } from '../../models/remote-vpn-gateway';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { RemoteVpnGatewayService } from '../../services/remote-vpn-gateway.service';
import { ListRemoteVpnGatewaysComponent } from './list-remote-vpn-gateways.component';

describe('ListRemoteVpnGatewaysComponent', () => {
    let fixture: ComponentFixture<ListRemoteVpnGatewaysComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockRemoteVpnGatewayService: jasmine.SpyObj<RemoteVpnGatewayService>;
    let mockNetworkingPermissionService: jasmine.SpyObj<NetworkingPermissionService>;
    let mockCloudInfraZoneService: jasmine.SpyObj<CloudInfraZoneService>;

    const remoteVpnGatewayList: RemoteVpnGateway[] = [
        {
            account: 'Gorilla',
            cidrlist: '***********/24',
            domain: 'ROOT',
            domainid: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
            dpd: true,
            esplifetime: 3600,
            esppolicy: 'aes256-sha512',
            forceencap: false,
            gateway: '***********',
            hasannotations: false,
            id: '0c2d6a77-6099-4682-9cc6-c5c1dab777b1',
            ikelifetime: 86400,
            ikepolicy: 'aes256-sha512;modp2048',
            ikeversion: 'ikev2',
            ipsecpsk: 'g39lLp8yw0oTY3Ti',
            name: 'TestVPN',
            remoteid: '',
            remoteidtype: 'auto',
            splitconnections: true,
        },
        {
            account: 'Gorilla L2',
            cidrlist: '***********/30',
            domain: 'ROOT',
            domainid: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
            dpd: true,
            esplifetime: 3600,
            esppolicy: 'aes256-sha512',
            forceencap: false,
            gateway: '***********',
            hasannotations: false,
            id: '0c2d6a77-6099-4682-9cc6-c5c1dab777b1',
            ikelifetime: 86400,
            ikepolicy: 'aes256-sha512;modp2048',
            ikeversion: 'ikev2',
            ipsecpsk: 'g39lasdsas3Ti',
            name: 'TestVPN 2',
            remoteid: '',
            remoteidtype: 'auto',
            splitconnections: true,
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                ListRemoteVpnGatewaysComponent
            ],
            providers: [
                provideMock(UserContextService),
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraAccountService),
                provideMock(CloudInfraDomainService),
                provideMock(NetworkingPermissionService),
                provideMock(RemoteVpnGatewayService),
                provideMock(CloudInfraZoneService),
                ZoneDomainAccountStore
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        mockUserContextService.currentUser = {
            organizationId: 1,
            cloudInfraUserContext: {
                accountName: 'account',
                domainId: 'domainId',
                hasMappedDomain: true,
            } as CloudInfraUserContext
        } as UserContext;

        mockNetworkingPermissionService = TestBed.inject(NetworkingPermissionService) as jasmine.SpyObj<NetworkingPermissionService>;
        mockNetworkingPermissionService.canViewVirtualPrivateCloudList.and.returnValue(true);

        mockCloudInfraZoneService = TestBed.inject(CloudInfraZoneService) as jasmine.SpyObj<CloudInfraZoneService>;
        mockCloudInfraZoneService.getZones.and.returnValue(of([{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }]));

        mockRemoteVpnGatewayService = TestBed.inject(RemoteVpnGatewayService) as jasmine.SpyObj<RemoteVpnGatewayService>;
        mockRemoteVpnGatewayService.getRemoteVpnGatewayList.and.returnValue(of(remoteVpnGatewayList));

        fixture = TestBed.createComponent(ListRemoteVpnGatewaysComponent);
    });

    describe('Initialization', () => {

        it('should call getNetworks without account when context have a mapped domain', () => {
            mockUserContextService.currentUser = {
                organizationId: 1,
                cloudInfraUserContext: {
                    accountName: 'account',
                    domainId: 'domainId',
                    hasMappedDomain: true,
                } as CloudInfraUserContext
            } as UserContext;

            fixture.detectChanges();

            expect(mockRemoteVpnGatewayService.getRemoteVpnGatewayList).toHaveBeenCalledTimes(1);

        });
    });

    describe('Data Binding', () => {

        it('should display the remote vpn gateway name in the second column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(1)').textContent.trim()).toEqual(remoteVpnGatewayList[0].name);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(1)').textContent.trim()).toEqual(remoteVpnGatewayList[1].name);
        });

        it('should display the account name in the third column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(remoteVpnGatewayList[0].account);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(remoteVpnGatewayList[1].account);
        });

        it('should display the cidr in the third column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(remoteVpnGatewayList[0].cidrlist);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(remoteVpnGatewayList[1].cidrlist);
        });

        it('should display the gateway in the fourth column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(remoteVpnGatewayList[0].gateway);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(remoteVpnGatewayList[1].gateway);
        });

        it('should display the ipsecpsk in the fifth column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(5)').textContent.trim()).toEqual(remoteVpnGatewayList[0].ipsecpsk);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(5)').textContent.trim()).toEqual(remoteVpnGatewayList[1].ipsecpsk);
        });

    });
});
