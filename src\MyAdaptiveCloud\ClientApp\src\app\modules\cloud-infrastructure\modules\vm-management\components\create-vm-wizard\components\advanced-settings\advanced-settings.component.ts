import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSwitchComponent } from '@app/shared/components/btn-switch/btn-switch.component';
import { NgSelectComponent } from '@ng-select/ng-select';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { AdvanceSettingsForm, AffinityGroupsForm, SshKeyPairsForm } from '../../forms/advance-settings-step.form';
import { OptionsTable } from '../../models/advanced-settings.model';
import { CreateVmWizardConstants } from '../../models/create-vm-wizard.constants';
import { AVAILABLE_KEYBOARD_LANG_VALUES } from './vm-available-keyboard-langs';

@Component({
    selector: 'app-create-vm-wizard-advanced-settings',
    imports: [
        BtnSwitchComponent,
        ReactiveFormsModule,
        NgSelectComponent,
    ],
    templateUrl: './advanced-settings.component.html',
    styleUrls: ['../../create-vm-wizard-common.scss', './advanced-settings.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmWizardAdvancedSettingsComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);
    public readonly store = inject(CreateVMWizardStore);

    protected readonly label = CreateVmWizardConstants.AdvancedSettingsLabel;
    protected readonly affinityGroupsRes = this.store.advancedSettingsStep.affinityGroups;
    protected readonly sshKeyPairsRes = this.store.advancedSettingsStep.sshKeyPairs;

    protected readonly availableKeyboardValues = Object.keys(AVAILABLE_KEYBOARD_LANG_VALUES);

    protected affinityGroupsForm: FormGroup<AffinityGroupsForm>;
    protected affinityGroupsOptions = new FormGroup({});
    protected sshKeyPairsForm: FormGroup<SshKeyPairsForm>;
    protected sshKeyPairsOptions: FormGroup = new FormGroup({});
    protected advancedSettingsForm: FormGroup<AdvanceSettingsForm>;

    ngOnInit(): void {
        this.initializeForm();
    }

    private setFormGroupsChangesHandlers(): void {
        this.affinityGroupsForm.controls.selectAllGroups.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.setOptionsValuesOnSelectAllChange(value, this.affinityGroupsOptions);
            });

        this.sshKeyPairsForm.controls.selectAllKeyPairs.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.setOptionsValuesOnSelectAllChange(value, this.sshKeyPairsOptions);
            });

        this.affinityGroupsOptions.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.setSelectAllControlValue(this.affinityGroupsForm.controls.selectAllGroups, value);
            });

        this.sshKeyPairsOptions.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.setSelectAllControlValue(this.sshKeyPairsForm.controls.selectAllKeyPairs, value);
            });

        this.advancedSettingsForm.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.saveAdvancedSettings();
            });
    }

    private setOptionsValuesOnSelectAllChange(value: boolean, optionsGroup: FormGroup<Record<string, FormControl<boolean>>>) {
        Object.keys(optionsGroup.controls)
            .forEach(key => {
                optionsGroup.controls[key].setValue(value, { emitEvent: false });
            });

        this.saveAdvancedSettings();
    }

    private setSelectAllControlValue(control: FormControl<boolean>, value: Record<string, boolean>): void {
        const options = Object.keys(value);
        const areAllSelected = options.every(key => value[key]);
        control.setValue(areAllSelected, { emitEvent: false });
    }

    private initializeForm(): void {
        const affinityGroupsIds = this.store.advancedSettingsStep.affinityGroups().map(ag => ag.id);
        const sshKeyPairsIds = this.store.advancedSettingsStep.sshKeyPairs().map(kp => kp.id);

        const selectedAffinityGroups = this.store.advancedSettingsStep.selectedSettings.affinityGroups.options().map(ag => ag.id);
        const selectedSshKeyPairsIds = this.store.advancedSettingsStep.selectedSettings.sshKeyPairs.options().map(kp => kp.id);

        const isAllAffinityGroupsSelected = this.store.advancedSettingsStep.selectedSettings.affinityGroups.selectAll();
        const isAllSshKeyPairsSelected = this.store.advancedSettingsStep.selectedSettings.sshKeyPairs.selectAll();

        this.addTableRowsControls(
            this.affinityGroupsOptions,
            affinityGroupsIds,
            selectedAffinityGroups,
            isAllAffinityGroupsSelected
        );

        this.addTableRowsControls(
            this.sshKeyPairsOptions,
            sshKeyPairsIds,
            selectedSshKeyPairsIds,
            isAllSshKeyPairsSelected
        );

        this.affinityGroupsForm = this.formBuilder.group({
            selectAllGroups: this.formBuilder.control<boolean>(isAllAffinityGroupsSelected),
            affinityGroupsOptions: this.affinityGroupsOptions
        });

        this.sshKeyPairsForm = this.formBuilder.group({
            selectAllKeyPairs: this.formBuilder.control<boolean>(isAllSshKeyPairsSelected),
            sshKeyPairsOptions: this.sshKeyPairsOptions
        });

        this.advancedSettingsForm = this.formBuilder.group({
            affinityGroupsForm: this.affinityGroupsForm,
            sshKeyPairsForm: this.sshKeyPairsForm,
            userData: this.formBuilder.control(this.store.advancedSettingsStep.selectedSettings.userdata()),
            keyboardLanguage: this.formBuilder.control(this.store.advancedSettingsStep.selectedSettings.keyboardLanguage(), Validators.required)
        });

        this.setFormGroupsChangesHandlers();
    }

    private addTableRowsControls(group: FormGroup<Record<string, FormControl<boolean>>>, rows: string[], selectedOptions: string[], allSelected: boolean): void {
        rows.forEach(row => {
            const isOptionSelected = selectedOptions.includes(row);
            group.addControl(row, new FormControl(allSelected || isOptionSelected), { emitEvent: false });
        });
    }

    protected toggleAdvancedSettings(event: boolean) {
        this.store.toggleAdvanceSettingsDisplay(event);
    }

    private saveAdvancedSettings(): void {
        const selectedAffinityGroupsOptions = Object.keys(this.affinityGroupsOptions.controls)
            .filter(key => this.affinityGroupsOptions.controls[key].value)
            .map(key => {
                const group = this.store.advancedSettingsStep.affinityGroups().find(g => g.id === key);
                return { id: group.id, name: group.name };
            });

        const affinityGroups: OptionsTable = {
            selectAll: this.affinityGroupsForm.controls.selectAllGroups.value,
            options: selectedAffinityGroupsOptions
        };

        const selectedSshKeyPairsOptions = Object.keys(this.sshKeyPairsOptions.controls)
            .filter(key => this.sshKeyPairsOptions.controls[key].value)
            .map(key => {
                const keyPair = this.store.advancedSettingsStep.sshKeyPairs().find(k => k.id === key);
                return { id: keyPair.id, name: keyPair.name };
            });

        const sshKeyPairs: OptionsTable = {
            selectAll: this.sshKeyPairsForm.controls.selectAllKeyPairs.value,
            options: selectedSshKeyPairsOptions
        };

        const userdata = this.advancedSettingsForm.controls.userData.value;
        const keyboardLanguage = this.advancedSettingsForm.controls.keyboardLanguage.value;

        this.store.setAdvancedSettings({
            affinityGroups,
            sshKeyPairs,
            userdata,
            keyboardLanguage
        }, this.advancedSettingsForm.valid);
    }
}
