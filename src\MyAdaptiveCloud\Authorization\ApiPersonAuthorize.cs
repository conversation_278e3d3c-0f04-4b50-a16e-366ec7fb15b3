﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    ///     Verifies that the current logged in user has permission to execute the operation in all of the target person's organizations,
    ///     or, when the <see cref="_requirePermissionOnAllOrganizations"/> parameter is false, in at least one of the target person's organizations.
    ///     This attribute won't set the organization id in the context, so the use of [FromAuth] won't be available.
    /// </summary>
    public class ApiPersonAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        private readonly bool _requirePermissionOnAllOrganizations;

        public ApiPersonAuthorizeFilter(IUserContextService userContextService, IIdentityService identityService, IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name, bool requirePermissionOnAllOrganizations) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _requirePermissionOnAllOrganizations = requirePermissionOnAllOrganizations;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int targetPersonId);

            var targetPerson = await _entityAuthorizationService.GetPerson(targetPersonId);

            if (targetPerson == null)
            {
                context.Result = new BadRequestResult();
                return;
            }

            if (targetPersonId != 0 && targetPersonId == userId)
            {
                // This should be allowed, as it is a regular User operating on themself
                return;
            }
            else
            {
                // This should not be allowed, users can only operate on other API Users if they are not the target person
                // Only self can create API Clients for regular Users
                if (!targetPerson.IsApiUser)
                {
                    context.Result = new ForbidResult();
                    return;
                }
                // Person is ApiUser, so proceed with authorization
                else
                {
                    // Gets the active organization ids where the target person in a contact, and
                    // check if the caller user has permissions in at least one of them
                    var organizationIds = await _entityAuthorizationService.GetPersonOrganizationIds(targetPersonId);

                    // If the target person is not a member of any active organization, then the caller user is not authorized
                    if (!organizationIds.Any())
                    {
                        context.Result = new ForbidResult();
                        return;
                    }

                    var isAuthorized = false;

                    // The caller user should have permissions in at least one if the organizations the target user is member of
                    if (!_requirePermissionOnAllOrganizations)
                    {
                        foreach (var organizationId in organizationIds)
                        {
                            if (_perms != null && _userContextService.HasPermission(userId, organizationId, _distance, _perms))
                            {
                                isAuthorized = true;
                                break;
                            }
                        }
                    }
                    // The caller user should have permissions in all the organizations the target user is member of
                    else
                    {
                        isAuthorized = true;
                        foreach (var organizationId in organizationIds)
                        {
                            if (_perms != null && !_userContextService.HasPermission(userId, organizationId, _distance, _perms))
                            {
                                isAuthorized = false;
                                break;
                            }
                        }
                    }

                    if (!isAuthorized)
                    {
                        context.Result = new ForbidResult();
                        return;
                    }
                }
            }


        }

        /// <summary>
        /// Specifies what minimum Role is required within the target Organization to access this endpoint.
        /// </summary>
        /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
        [AttributeUsage(AttributeTargets.Method, Inherited = false)]
        public class ApiPersonAuthorizeAttribute : BaseAuthorizeAttribute
        {
            private bool _requirePermissionOnAllOrganizations = true;

            /// <summary>
            ///     This flag indicates if the caller should have permissions in all the organizations the target user is member of. Default is true.
            /// </summary>
            public bool RequirePermissionOnAllOrganizations
            {
                get { return _requirePermissionOnAllOrganizations; }
                set
                {
                    _requirePermissionOnAllOrganizations = value;
                    Arguments = new object[] { _perms, _name, _distance, _requirePermissionOnAllOrganizations };
                }
            }

            public ApiPersonAuthorizeAttribute(params Perms[] perms) : base(typeof(ApiPersonAuthorizeFilter), perms)
            {
                Name = "userId";
                Arguments = new object[] { _perms, _name, _distance, _requirePermissionOnAllOrganizations };
            }
        }
    }
}
