import { ChangeDetectionStrategy, Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ConfirmationRequiredDialogComponent } from '@app/shared/components/confirmation-required-dialog/confirmation-required-dialog.component';
import { ConfirmationRequiredDialogData } from '@app/shared/models/confirmation-required-dialog.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { forkJoin, map } from 'rxjs';
import { DevicesService } from '../../services/devices.service';

@Component({
    selector: 'app-download-agent-installer',
    imports: [],
    templateUrl: './download-agent-installer.component.html',
    styleUrl: './download-agent-installer.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DownloadAgentInstallerComponent {
    private readonly agentService = inject(DevicesService);
    private readonly userContextService = inject(UserContextService);
    private readonly modalService = inject(ModalService);
    private readonly destroyRef = inject(DestroyRef);

    public downloadAgentInstaller(): void {
        const organizationId = this.userContextService.currentUser.organizationId;
        forkJoin([this.agentService.getAgentInstallerAgreement(organizationId), this.agentService.getAgentAgreement(organizationId)])
            .pipe(map(([res1, res2]) => ({ installerAgreement: res1.data, agentAgreement: res2.data })))
            .subscribe(res => {
                const modalRef = this.modalService.openModalComponent(ConfirmationRequiredDialogComponent, { backdropClass: 'backdrop-class', size: 'lg', centered: true });
                const dialogData: ConfirmationRequiredDialogData = {
                    title: 'Software License Agreement Authorization',
                    scrollableContent: res.agentAgreement,
                    fixedContent: res.installerAgreement,
                    acceptText: 'Accept',
                    cancelText: 'Cancel',
                    agreeText: 'I agree to the terms of this Software License Agreement'
                };
                (modalRef.componentInstance as ConfirmationRequiredDialogComponent).dialogData.set(dialogData);

                modalRef.closed.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(modalResponse => {
                    if (modalResponse) {
                        this.agentService.getAgentInstaller(this.userContextService.currentUser.organizationId);
                    }
                });

            });
    }
}
