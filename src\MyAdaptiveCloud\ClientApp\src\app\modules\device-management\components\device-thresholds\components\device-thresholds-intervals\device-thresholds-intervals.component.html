<form [formGroup]="form" class="container">
  <div class="row">
    @if (!useIntervalsForMetrics()) {
      <div class="col-6 p-0">
        <label class="col-form-label d-inline">Trigger<span class="required-asterisk">*</span></label>
        <select class="form-select ms-1 d-inline w-auto" formControlName="trigger">
          @for (interval of intervals(); track interval) {
            <option [value]="interval.value">{{ interval.name }}
            </option>
          }
        </select>
      </div>
    }
    <div class="col-6 p-0">
      <label class="col-form-label d-inline">Clear<span class="required-asterisk">*</span></label>
      <select class="form-select ms-1 d-inline w-auto" formControlName="clear">
        @for (interval of intervals(); track interval) {
          <option [value]="interval.value">{{ interval.name }}
          </option>
        }
      </select>
    </div>
  </div>
  @if (form.invalid) {
    <div class="row">
      <small class="text-danger">
        @if (form.controls.trigger?.errors?.['required']) {
          <div> Trigger {{
          form.controls.trigger?.errors?.['required']}} </div>
        }
        @if (form.controls['clear'].errors?.['required']) {
          <div> Clear
          {{form.controls['clear'].errors?.['required']}}</div>
        }
      </small>
      <small class="text-danger">
        @if (form.controls.trigger?.errors?.['max']) {
          <div> Trigger {{ form.controls.trigger?.errors?.['max']}}
          </div>
        }
        @if (form.controls['clear'].errors?.['max']) {
          <div> Clear {{form.controls['clear'].errors?.['max']}}</div>
        }
      </small>
      <small class="text-danger">
        @if (form.controls.trigger?.errors?.['min']) {
          <div> Trigger {{ form.controls.trigger?.errors?.['min']}}
          </div>
        }
        @if (form.controls['clear'].errors?.['min']) {
          <div> Clear {{form.controls['clear'].errors?.['min']}}</div>
        }
      </small>
    </div>
  }
</form>