import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { UserPreferencesTabComponent } from './user-preferences-tab.component';

describe('UserPreferencesTabComponent', () => {
    let component: UserPreferencesTabComponent;
    let fixture: ComponentFixture<UserPreferencesTabComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                UserPreferencesTabComponent
            ],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(UserContextService)
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 10,
            userId: 2
        } as UserContext;

        fixture = TestBed.createComponent(UserPreferencesTabComponent);
        component = fixture.componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
