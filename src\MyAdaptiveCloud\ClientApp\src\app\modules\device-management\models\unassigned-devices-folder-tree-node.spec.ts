import { TestBed } from '@angular/core/testing';
import { UnassignedDevicesFolderTreeNode } from './unassigned-devices-folder-tree-node';

describe('UnassignedDevicesTreeNode', () => {

    beforeEach(() => {

        TestBed.configureTestingModule({
            providers: [UnassignedDevicesFolderTreeNode]
        });
    });

    describe('folder class', () => {
        let unassignedDevicesFolder: UnassignedDevicesFolderTreeNode;

        beforeEach(() => {
            unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(0);
        });

        it('should return false when isOrganizationFolder() is called', () => {
            const result = unassignedDevicesFolder.isOrganizationFolder();
            expect(result).toBeFalse();
        });

        it('should return true when isUnassignedDevicesFolder() is called', () => {
            const result = unassignedDevicesFolder.isUnassignedDevicesFolder();
            expect(result).toBeTrue();
        });
    });

    describe('lazy load', () => {
        let unassignedDevicesFolder: UnassignedDevicesFolderTreeNode;

        beforeEach(() => {
            unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(0);
        });

        it('should return false when shouldLoadDevices is called', () => {
            const result = unassignedDevicesFolder.shouldLoadDevices();
            expect(result).toBeFalse();
        });

        it('should return false when shouldLoadSubfolders is called', () => {
            const result = unassignedDevicesFolder.shouldLoadSubfolders();
            expect(result).toBeFalse();
        });
    });
});
