import { TestBed } from '@angular/core/testing';
import { ActivationKeysService } from './activation-keys.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { ApiService } from '@app/shared/services/api.service';

describe('ActivationKeysService', () => {
    let service: ActivationKeysService;
    let mockApiService: jasmine.SpyObj<ApiService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ApiService)
            ]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(ActivationKeysService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('getActivationKeysForOrgs', () => {

        it('should call the get method from the ApiService with the correct url', () => {
            service.getActivationKeysForOrg(0);
            const expectedUrl = 'activationkeys/organization/0';

            expect(mockApiService.get).toHaveBeenCalledOnceWith(expectedUrl);
        });
    });

    describe('createActivationKey', () => {

        it('should call the post method from the ApiService with the correct parameters', () => {
            service.createActivationKey(0);
            const expectedUrl = 'activationkeys/0';

            expect(mockApiService.post).toHaveBeenCalledOnceWith(expectedUrl, null);
        });
    });

    describe('toggleActivationKey', () => {

        it('should call the post method from the ApiService with the correct parameters', () => {
            service.toggleActivationKey(1);
            const expectedUrl = 'activationkeys/1/toggle';

            expect(mockApiService.post).toHaveBeenCalledOnceWith(expectedUrl, null);
        });
    });
});
