using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    /// Verifies that the current user's Role is authorized to access the target Organization's information
    /// </summary>
    public class OrgAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly bool _includeInactiveOrganizations;

        public OrgAuthorizeFilter(
            IUserContextService userContextService,
            IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name, bool includeInactiveOrganizations = false) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
            _includeInactiveOrganizations = includeInactiveOrganizations;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {

                string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
                _ = int.TryParse(val, out int organizationId);

                var organizationExists = await _entityAuthorizationService.OrganizationExists(organizationId, _includeInactiveOrganizations);
                if (organizationExists)
                {
                    if (_perms != null && _perms.Count() > 0)
                    {
                        if (!_userContextService.HasPermission(userId, organizationId, _distance, _perms))
                        {
                            context.Result = new ForbidResult();
                        }
                        else
                        {
                            AuthorizeFilterHelpers.SetOrganizationId(context, organizationId);
                        }
                    }
                    else
                    {
                        context.Result = new ForbidResult();
                    }
                }
                else
                {
                    context.Result = new BadRequestResult();
                }
            }

            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Specifies what minimum Role is required within the target Organization to access this endpoint.
    /// The target Organization is determined via organizationId or parentOrganizationId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = true)]
    public class OrgAuthorizeAttribute : BaseAuthorizeAttribute
    {
        private bool _includeInactiveOrganizations = false;

        public bool IncludeInactiveOrganizations
        {
            get { return _includeInactiveOrganizations; }
            set
            {
                _includeInactiveOrganizations = value;
                Arguments = new object[] { _perms, _name, _distance, _includeInactiveOrganizations };
            }
        }

        public OrgAuthorizeAttribute(params Perms[] perms) : base(typeof(OrgAuthorizeFilter), perms)
        {
            Name = "organizationId";
            Arguments = new object[] { _perms, _name, _distance, _includeInactiveOrganizations };
        }
    }
}