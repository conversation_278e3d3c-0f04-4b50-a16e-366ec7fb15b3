services:
  api:
    user: "${MY_UID}:${MY_GID}"
    container_name: "${COMPOSE_PROJECT_NAME}-api"
    build:
      context: ./
      dockerfile: ./Dockerfile
    working_dir: /home/<USER>/myadaptivecloud
    command: dotnet MyAdaptiveCloud.dll
    restart: always
    logging:
      driver: syslog
      options:
        tag: "{{.Name}}"
    volumes:
      # Mount the entire home directory for this user, which gives access to things like $HOME/.aspnet, etc
      - type: bind
        source: /home/<USER>
        target: /home/<USER>
      - ./docker_passwd:/etc/passwd
      - ./docker_group:/etc/group
      # Mount the NFS volume that is used by AdaptiveCloud Drive
      - /mnt/dsm1_adaptivecloud_drive01:/mnt/dsm1_adaptivecloud_drive01
      # DVOP-49 Fix issues connecting to MS Sql Server 2014 billing db due to DotNet 8 container changes
      - ./openssl.cnf:/etc/ssl/openssl.cnf
    ports:
      - 8080:8080/tcp
    extra_hosts:
      - "cloud.adaptivecloud.com:************"
      - "sso.adaptivecloud.com:************"
      - "remote-api.adaptivecloud.com:************"
      - "imap.ippathways.com:**********"
    environment:
      - ASPNETCORE_URLS=http://+:8080
