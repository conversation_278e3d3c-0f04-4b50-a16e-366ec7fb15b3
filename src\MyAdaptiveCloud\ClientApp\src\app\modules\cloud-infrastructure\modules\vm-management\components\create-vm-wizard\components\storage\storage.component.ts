import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { debounceTime } from 'rxjs';
import { OsType } from '../../../../models/os-type.enum';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { CreateVmWizardConstants } from '../../models/create-vm-wizard.constants';
import { DiskOffering } from '../../models/disk-offering.model';
import { OfferingSelectForm } from '../../models/disk-offerings-select.form';

@Component({
    selector: 'app-create-vm-wizard-storage',
    imports: [
        NgSelectModule,
        ReactiveFormsModule,
        CdkDropList,
        CdkDrag,
        NgbPopover
    ],
    templateUrl: './storage.component.html',
    styleUrls: ['../../create-vm-wizard-common.scss', './storage.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmWizardStorageComponent implements OnInit {

    private readonly diskCustomSizeDebounceTime = 250;

    protected readonly destroyRef = inject(DestroyRef);
    public readonly store = inject(CreateVMWizardStore);
    private readonly userContextService = inject(UserContextService);
    private readonly formBuilder = inject(FormBuilder);

    protected readonly diskSizeCustomOfferingMinValue = this.userContextService.currentUser.cloudInfraUserContext.diskSizeCustomOfferingMinValue;
    protected readonly diskSizeCustomOfferingMaxValue = this.userContextService.currentUser.cloudInfraUserContext.diskSizeCustomOfferingMaxValue;

    protected readonly label = CreateVmWizardConstants.StorageLabel;
    public readonly diskOptions = this.store.storageStep.diskOfferings;
    protected readonly customOfferingMinimumSize = computed(() => (this.store.setupStep.form.template()?.size ? Math.round(this.store.setupStep.form.template.size()) : this.diskSizeCustomOfferingMinValue));
    private readonly osType = OsType;

    protected readonly isIso = computed(() => this.store.setupStep.form.osType() === this.osType.ISO);

    protected selectRootDiskForm: FormGroup<OfferingSelectForm>;

    protected readonly selectDataDisksForm = this.formBuilder.group<OfferingSelectForm>({
        offeringSelect: this.formBuilder.control<DiskOffering | null>(null, Validators.required),
        diskCustomSize: this.formBuilder.control<number | null>(null, [
            Validators.min(this.diskSizeCustomOfferingMinValue),
            Validators.max(this.diskSizeCustomOfferingMaxValue),
            Validators.pattern('^[0-9]*$')])
    });

    ngOnInit(): void {
        this.selectRootDiskForm = this.buildRootDiskForm();

        this.selectRootDiskForm.controls.diskCustomSize.valueChanges
            .pipe(
                debounceTime(this.diskCustomSizeDebounceTime),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
                this.setRootDisk();
            });

        this.selectDataDisksForm.controls.offeringSelect.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                this.setDiskSizeControlState(this.selectDataDisksForm.controls.diskCustomSize, value.id);
            });

        this.selectDataDisksForm.controls.diskCustomSize.valueChanges.pipe(
            debounceTime(this.diskCustomSizeDebounceTime),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(() => {
            this.selectDataDisksForm.controls.diskCustomSize.updateValueAndValidity({ emitEvent: false });
        });
    }

    private setRootDisk(): void {
        if (this.selectRootDiskForm.valid) {
            if (this.selectRootDiskForm.controls.offeringSelect.value.isCustomized || !this.isIso()) {
                this.store.setRootDisk({
                    ...this.selectRootDiskForm.controls.offeringSelect.value,
                    diskSize: this.selectRootDiskForm.controls.diskCustomSize.value
                });
            } else {
                this.store.setRootDisk(this.selectRootDiskForm.controls.offeringSelect.value);
            }
        }
        this.store.setStorageStepValidity(this.selectRootDiskForm.valid);
    }

    private setDiskSizeControlState(diskSizeFormControl: FormControl<number>, diskOfferingId: string): void {
        const disk: DiskOffering = this.diskOptions()
            .find((diskOffering: DiskOffering) => diskOffering.id === diskOfferingId);
        if (disk.isCustomized) {
            diskSizeFormControl.enable({ emitEvent: false });
            diskSizeFormControl.addValidators(Validators.required);

        } else {
            diskSizeFormControl.disable({ emitEvent: false });
            diskSizeFormControl.removeValidators(Validators.required);
        }
        diskSizeFormControl.setValue(disk.diskSize, { emitEvent: false });
        diskSizeFormControl.updateValueAndValidity({ emitEvent: false });
    }

    protected removeDisk(index: number): void {
        this.store.removeDataDisk(index);
    }

    protected addDisk(disk: DiskOffering): void {
        if (this.selectDataDisksForm.valid) {
            if (disk.isCustomized) {
                this.store.addDataDisk({ ...disk, diskSize: this.selectDataDisksForm.value.diskCustomSize });
            } else {
                this.store.addDataDisk(disk);
            }
            this.store.setStorageStepValidity(this.selectRootDiskForm.valid && this.selectDataDisksForm.valid);
            this.selectDataDisksForm.reset({}, { emitEvent: false });
        }
    }

    protected getDiskSize(): string {
        return this.selectDataDisksForm.value.offeringSelect ? '-- GB' : `${this.selectDataDisksForm.value.offeringSelect.diskSize} GB`;
    }

    protected drop(event: CdkDragDrop<string[]>) {
        const movedDisks = this.store.storageStep.selectedDataDisks();
        const updatedDisks = [...movedDisks];
        moveItemInArray(updatedDisks, event.previousIndex, event.currentIndex);
        this.store.setSelectedDataDisk(updatedDisks);
    }

    private buildRootDiskForm(): FormGroup<OfferingSelectForm> {
        let form: FormGroup<OfferingSelectForm>;

        // ISO offerings require the user to select a disk offering as the root disk.
        // If the disk offering is customized, the user must set a disk size.
        if (this.isIso()) {
            form = this.formBuilder.group<OfferingSelectForm>({
                offeringSelect: this.formBuilder.control<DiskOffering | null>(this.store.storageStep.rootDisk(), Validators.required),
                diskCustomSize: this.formBuilder.control<number | null>(
                    this.store.storageStep.rootDisk()?.diskSize,
                    [
                        Validators.max(this.diskSizeCustomOfferingMaxValue),
                        Validators.min(this.customOfferingMinimumSize()),
                        Validators.pattern('^[0-9]*$')
                    ]
                )
            });

            form.controls.offeringSelect.valueChanges
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(value => {
                    this.setDiskSizeControlState(form.controls.diskCustomSize, value.id);
                    this.setRootDisk();
                });

        } else {
            // Template offerings have a default root disk offering that cannot be changed, and it is set when the template is selected in the setup step.
            // Only the disk size can be customized. If a custom size is not set, the default size is used.
            form = this.formBuilder.group<OfferingSelectForm>({
                offeringSelect: this.formBuilder.control<DiskOffering>({ value: this.store.storageStep.rootDisk(), disabled: true }),
                diskCustomSize: this.formBuilder.control<number | null>(
                    this.store.storageStep.rootDisk().diskSize,
                    [
                        Validators.max(this.diskSizeCustomOfferingMaxValue),
                        Validators.min(this.customOfferingMinimumSize()),
                        Validators.pattern('^[0-9]*$')
                    ]
                )
            });
        }

        return form;
    }
}
