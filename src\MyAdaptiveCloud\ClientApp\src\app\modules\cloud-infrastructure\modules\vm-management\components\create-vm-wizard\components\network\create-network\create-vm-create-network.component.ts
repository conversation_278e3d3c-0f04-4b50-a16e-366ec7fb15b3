import { ChangeDetectionStrategy, Component, effect, inject, viewChild } from '@angular/core';
import { IsolatedNetworkComponent } from '@app/modules/cloud-infrastructure/components/isolated-network/isolated-network.component';
import { Layer2NetworkComponent } from '@app/modules/cloud-infrastructure/components/layer2-network/layer2-network.component';
import { SharedNetworkComponent } from '@app/modules/cloud-infrastructure/components/shared-network/shared-network.component';
import { AddNetworkTabs } from '@app/modules/cloud-infrastructure/models/add-network-tabs.enum';
import { CreateIsolatedNetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-isolated-network.request';
import { CreateLayer2NetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-layer2-network.request';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ink, Ng<PERSON><PERSON>avOutlet } from '@ng-bootstrap/ng-bootstrap';
import { CreateVMWizardStore } from '../../../create-vm-wizard-store';
import { CreateVmRequestStatus } from '../../../models/create-vm-request-status.emun';
import { SharedNetworkRequest } from '@app/modules/cloud-infrastructure/requests/create-shared-network';

@Component({
    selector: 'app-create-vm-create-network',
    imports: [BtnSubmitComponent, NgbNav, NgbNavItem, NgbNavOutlet, NgbNavLink, NgbNavContent, IsolatedNetworkComponent, Layer2NetworkComponent, SharedNetworkComponent],
    templateUrl: './create-vm-create-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmCreateNetworkComponent {

    protected readonly activeModal = inject(NgbActiveModal);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);
    public readonly store = inject(CreateVMWizardStore);

    protected readonly isolatedNetworkComponent = viewChild(IsolatedNetworkComponent);
    protected readonly layer2NetworkComponent = viewChild(Layer2NetworkComponent);
    protected readonly sharedNetworkComponent = viewChild(SharedNetworkComponent);

    protected readonly addNetworkTabs = AddNetworkTabs;
    protected activeTab = AddNetworkTabs.Isolated;
    protected readonly createVmRequestStatus = CreateVmRequestStatus;

    constructor() {
        effect(() => {
            if (this.store.networkStep.requestStatus() === CreateVmRequestStatus.Success) {
                this.activeModal.close();
            }
        });
    }

    protected submitForm() {
        switch (this.activeTab) {
            case AddNetworkTabs.Isolated:
                if (this.isolatedNetworkComponent().form.valid) {
                    const form = this.isolatedNetworkComponent().form.value;
                    const request: CreateIsolatedNetworkRequest = {
                        description: form.description,
                        gateway: form.gateway,
                        name: form.name,
                        netmask: form.netmask,
                        networkDomain: form.networkDomain,
                        networkOfferingId: form.networkOffering.id,
                        vpc: form.vpc
                    };
                    this.store.createIsolatedNetwork(request);
                }
                break;
            case AddNetworkTabs.Layer2:
                if (this.layer2NetworkComponent().form.valid) {
                    const form = this.layer2NetworkComponent().form.value;
                    const request: CreateLayer2NetworkRequest = {
                        description: form.description,
                        name: form.name,
                        networkOfferingId: form.networkOffering.id,
                        vLan: form.vlan,
                        bypassVLanId: form.bypassVLanId,
                        secondaryVLanID: form.secondaryVLanId,
                        secondaryVLanType: form.secondaryVLanType
                    };
                    this.store.createLayer2Network(request);
                }
                break;
            case AddNetworkTabs.Shared:
                if (this.sharedNetworkComponent().form.valid) {
                    const form = this.sharedNetworkComponent().form.value;

                    const request : SharedNetworkRequest = {
                        name: form.name,
                        displaytext: form.description,
                        networkofferingid: form.networkOffering?.id,
                        hideipaddressusage: form.hideIpAddressUsage,
                        gateway: form.ipv4Gateway,
                        netmask: form.ipv4Netmask,
                        startip: form.ipv4StartIp,
                        endip: form.ipv4EndIp,
                        gatewayv6: form.ipv6Gateway,
                        cidr: form.ipv6CIDR,
                        startipv6: form.ipv6StartIp,
                        endipv6: form.ipv6EndIp,
                        networkdomain: form.networkDomain,
                        vlan: form.vlanVni
                    };

                    this.store.createSharedNetwork(request);
                }

                break;
        }
    }

}
