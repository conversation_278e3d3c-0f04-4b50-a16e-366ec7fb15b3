using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class PartnerResourceHubAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public PartnerResourceHubAuthorizeFilter(
            IEntityAuthorizationService entityAuthorizationService,
            IIdentityService identityService)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _identityService = identityService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int currentUserId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (currentUserId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, "organizationId");
            _ = int.TryParse(val, out int organizationId);

            var organizationIsPartner = await _entityAuthorizationService.OrganizationIsPartner(organizationId);
            if (!organizationIsPartner)
            {
                context.Result = new ForbidResult();
            }
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class PartnerResourceHubAuthorizeAttribute : TypeFilterAttribute
    {
        public PartnerResourceHubAuthorizeAttribute() : base(typeof(PartnerResourceHubAuthorizeFilter))
        {
        }
    }
}