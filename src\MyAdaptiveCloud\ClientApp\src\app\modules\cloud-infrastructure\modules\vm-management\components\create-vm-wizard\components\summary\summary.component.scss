@import "custom-variables.scss";

.summary-container {

    border-width: 1px 1px 1px 0;
    border-style: solid;
    border-color: $secondary-25;
    color: $secondary;

    .header {
        background-color: white;
        display: flex;
        padding: 18px;
        border-bottom: 1px solid $secondary-25;

        .counter {
            border: 1px solid $secondary-25;
            color: $secondary;
            margin-left: auto;
            padding: 10px 15px 10px 15px;

        }
    }

    .content {
        background-color: white;
        border-bottom: 1px solid $secondary-25;
        padding: 1rem;

        &.selected {
            background-color: $primary-05;
            border-left: 2px solid $primary;

            .title {
                color: $primary;
            }
        }

        &.step-disabled {
            opacity: 0.65;
            background-color: $secondary-05;
            cursor: auto;
        }
    }

    .footer {
        background-color: white;
        display: flex;
        padding: 1rem;
    }

    .storage-step {
        $DISK_INDEX_SIZE: 1.5em;

        &-disk-index {
            width: $DISK_INDEX_SIZE;
            height: $DISK_INDEX_SIZE;
            line-height: $DISK_INDEX_SIZE;
            font-size: 0.7em;
            background-color: white;
        }

        &-disk {
            line-height: normal;
        }
    }
}
