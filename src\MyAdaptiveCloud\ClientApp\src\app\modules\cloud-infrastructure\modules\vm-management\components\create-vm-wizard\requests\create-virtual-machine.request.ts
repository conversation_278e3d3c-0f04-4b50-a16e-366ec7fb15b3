import { ServiceOfferingViewModel } from '../models/service-offering.view-model';

export interface CreateVirtualMachineRequest {
    affinityGroups: string[];
    // This should only be populated with the selected disk offering for Root Disk when selecting ISO
    diskOfferingId: string;
    // Only KVM is supported at this stage
    hypervisor: string;
    keyboardLanguage: string;
    keypairs: string[];
    name: string;
    networkIds: string[];
    rootDiskSize: number | null;
    serviceOffering: ServiceOfferingViewModel;
    // Custom ISO offering disk size
    size: number | null;
    templateId: string;
    userdata: string;
    zoneId: string;
}
