﻿using AutoMapper;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Notifications;

namespace MyAdaptiveCloud.Api.AutoMapper
{
    public class NotificationsMapperProfile : Profile
    {
        public NotificationsMapperProfile()
        {
            CreateMap<Requests.Notifications.NotificationsListRequest, Services.Requests.Notifications.NotificationsListRequest>();
            CreateMap<Requests.Notifications.CreateNotificationRequest, Services.Requests.Notifications.CreateNotificationRequest>();
            CreateMap<Requests.Notifications.CreateNotificationMetadataRequest, Services.Requests.Notifications.CreateNotificationMetadataRequest>();
            CreateMap<Services.Requests.Notifications.CreateNotificationMetadataRequest, NotificationMetadata>();
            CreateMap<Requests.Notifications.CreateNotificationPreviewRequest, Services.Requests.Notifications.CreateNotificationPreviewRequest>();
        }
    }
}