import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { CreateVmWizardStepEnum } from '../../models/create-vm-wizard-steps.enum';

@Component({
    selector: 'app-create-vm-wizard-stepper',
    templateUrl: './stepper.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmWizardStepperComponent {
    public readonly store = inject(CreateVMWizardStore);

    protected readonly hiddenBtn = computed(() => (this.store.isLastStep() ? 'visibility: hidden' : ''));

    protected readonly canNavigateToPreviousStep = computed(() => {
        switch (this.store.currentStep()) {
            case CreateVmWizardStepEnum.Compute:
                return this.store.isSetupAccessible();
            case CreateVmWizardStepEnum.Storage:
                return this.store.isComputeAccessible();
            case CreateVmWizardStepEnum.Network:
                return this.store.isStorageAccessible();
            case CreateVmWizardStepEnum.AdvancedSettings:
                return this.store.isNetworkAccessible();
            default:
                return false;
        }
    });

    protected readonly canNavigateToNextStep = computed(() => {
        switch (this.store.currentStep()) {
            case CreateVmWizardStepEnum.Setup:
                return this.store.isComputeAccessible();
            case CreateVmWizardStepEnum.Compute:
                return this.store.isStorageAccessible();
            case CreateVmWizardStepEnum.Storage:
                return this.store.isNetworkAccessible();
            case CreateVmWizardStepEnum.Network:
                return this.store.isAdvancedSettingsAccessible();
            default:
                return false;
        }
    });

}
