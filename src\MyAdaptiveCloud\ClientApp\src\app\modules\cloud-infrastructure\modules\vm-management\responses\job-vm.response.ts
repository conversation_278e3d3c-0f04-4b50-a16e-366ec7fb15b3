/**
* Model representing the response from https://cloudstack.apache.org/api/apidocs-4.15/apis/queryAsyncJobResult.html
*/
export interface JobResponse {
    accountid: string;
    cmd: string;
    completed: boolean;
    created: Date;
    jobinstanceid: string;
    jobInstancetype: string;
    jobprocstatus: string;
    jobresult: string;
    jobresultcode: number;
    jobresulttype: string;
    jobstatus: number;
    userid: string;
    jobid: string;
}

export interface JobQueryResponse {
    queryasyncjobresultresponse: JobResponse;
}
