import { inject, Injectable } from '@angular/core';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { map, Observable } from 'rxjs';
import { AffinityGroup } from '../models/affinity-group.model';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '../models/vm.constants';
import { ListAffinityGroupTypesResponse } from '../responses/list-affinity-group-types.response';
import { ListAffinityGroupsResponse } from '../responses/list-affinity-groups.response';
import { UpdateVirtualMachineAffinityGroupResponse } from '../responses/update-virtual-machine-affinity-group.response';
import { CreateAffinityGroupResponse } from '../responses/create-affinity-group.response';

@Injectable({
    providedIn: 'root'
})
export class VmAffinityGroupsService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getAffinityGroups(domainId: string, account: string): Observable<AffinityGroup[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listAffinityGroups,
            listall: 'true',
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListAffinityGroupsResponse>(params)
            .pipe(map((response: ListAffinityGroupsResponse) => (response?.listaffinitygroupsresponse?.affinitygroup ?? [])));
    }

    getAffinityGroupsByVirtualMachine(virtualMachineId: string): Observable<AffinityGroup[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listAffinityGroups,
            listall: 'true',
            virtualmachineid: virtualMachineId
        };

        return this.cloudInfraApiService.get<ListAffinityGroupsResponse>(params)
            .pipe(map((response: ListAffinityGroupsResponse) => response.listaffinitygroupsresponse?.affinitygroup || []));
    }

    updateAffinityGroupsForVirtualMachine(virtualMachineId: string, affinityGroupIds: string[]): Observable<string> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.updateVirtualMachineAffinityGroups,
            id: virtualMachineId,
            affinitygroupids: affinityGroupIds?.filter(id => id).join(',') || ''
        };

        return this.cloudInfraApiService.get<UpdateVirtualMachineAffinityGroupResponse>(params)
            .pipe(map(response => response.updatevirtualmachineresponse?.jobid));
    }

    getAffinityGroupTypes(): Observable<{ type: string }[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listAffinityGroupTypes
        };

        return this.cloudInfraApiService.get<ListAffinityGroupTypesResponse>(params)
            .pipe(map(response => response.listaffinitygrouptypesresponse?.affinityGroupType || []));
    }

    createAffinityGroup(name: string, description: string, type: string, domainId: string, account: string): Observable<string> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createAffinityGroup,
            name: name.trim(),
            type,
            domainid: domainId,
            account
        };

        if (description) {
            params['description'] = description.trim();
        }

        return this.cloudInfraApiService.get<CreateAffinityGroupResponse>(params)
            .pipe(map(response => response.createaffinitygroupresponse?.id));
    }

}
