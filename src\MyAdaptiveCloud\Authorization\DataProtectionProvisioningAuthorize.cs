using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class DataProtectionProvisioningAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public DataProtectionProvisioningAuthorizeFilter(IUserContextService userContextService, IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public override async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int organizationId);

            if (_perms != null && _perms.Count() > 0)
            {
                // TODO: Check if this condition actually works for all cases where we want to allow an organization to use DataProtection API 

                if (!_userContextService.HasPermission(userId, organizationId, 1, _perms) &&
                    !(await _entityAuthorizationService.OrganizationIsPartner(organizationId) &&
                      _userContextService.HasPermission(userId, organizationId, _distance, _perms)))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, organizationId);
                }
            }
            else
            {
                context.Result = new ForbidResult();
            }

            return;
        }
    }

    /// <summary>
    ///     Specifies what minimum Role is required within the target Organization to access this endpoint.
    ///     The target Organization is determined via organizationId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class DataProtectionProvisioningAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public DataProtectionProvisioningAuthorizeAttribute(params Perms[] perms) : base(typeof(DataProtectionProvisioningAuthorizeFilter), perms)
        {
            Name = "organizationId";
        }
    }
}