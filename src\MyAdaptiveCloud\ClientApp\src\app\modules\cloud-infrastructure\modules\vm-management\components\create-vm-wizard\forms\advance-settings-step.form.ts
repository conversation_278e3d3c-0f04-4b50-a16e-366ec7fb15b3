import { FormGroup, FormControl } from '@angular/forms';

export interface AdvanceSettingsForm {
    affinityGroupsForm: FormGroup<AffinityGroupsForm>;
    sshKeyPairsForm: FormGroup<SshKeyPairsForm>;
    userData: FormControl<string>;
    keyboardLanguage: FormControl<string>;
}

export interface AffinityGroupsForm {
    selectAllGroups: FormControl<boolean>;
    affinityGroupsOptions: FormGroup<Record<string, FormControl<boolean>>>;
}

export interface SshKeyPairsForm {
    selectAllKeyPairs: FormControl<boolean>;
    sshKeyPairsOptions: FormGroup<Record<string, FormControl<boolean>>>;
}
