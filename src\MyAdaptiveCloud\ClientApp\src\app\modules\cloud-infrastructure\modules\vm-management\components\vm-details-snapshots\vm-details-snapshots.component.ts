import { As<PERSON><PERSON><PERSON><PERSON>, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { VmStateEnum } from '@app/shared/models/cloud-infra/vm-state.enum';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { map, Observable } from 'rxjs';
import { SnapshotState } from '../../models/snapshot-state.enum';
import { SnapshotViewModel } from '../../models/snapshot.view-model';
import { VmActionsService } from '../../services/vm-actions.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-vm-details-snapshots',
    imports: [NgxDatatableModule, AutoSearchBoxComponent, TableActionComponent, AsyncPipe, DatePipe],
    templateUrl: './vm-details-snapshots.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmDetailsSnapshotsComponent extends BaseListClientComponent<SnapshotViewModel> implements OnInit {

    protected readonly vmDetailsStateService = inject(VmDetailsStateService);
    private readonly vmManagementService = inject(VmManagementService);
    protected readonly vmManagementPermissionService = inject(VmManagementPermissionService);
    private readonly vmActionsService = inject(VmActionsService);
    protected readonly VmStateEnum = VmStateEnum;
    protected readonly SnapshotState = SnapshotState;

    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');
    private readonly isCurrentTemplate = viewChild<TemplateRef<never>>('isCurrentTemplate');
    private readonly dateCellTemplate = viewChild<TemplateRef<never>>('dateCellTemplate');
    private readonly stateTemplate = viewChild<TemplateRef<never>>('stateTemplate');

    ngOnInit(): void {
        const columns: TableColumn[] = [
            {
                name: 'State',
                prop: 'state',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.stateTemplate(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 150
            },
            {
                name: 'Name',
                prop: 'name',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200
            },
            {
                name: 'Type',
                prop: 'type',
                headerTemplate: this.headerTemplate(),
                sortable: false,
                resizeable: true,
                canAutoResize: true,
                width: 200
            },
            {
                name: 'Current',
                prop: 'current',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.isCurrentTemplate(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 100
            },
            {
                name: 'Parent',
                prop: 'parent',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200
            },
            {
                name: 'Created Date',
                prop: 'created',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.dateCellTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 100
            },
        ];

        if (this.vmManagementPermissionService.canDeleteVirtualMachineSnapshot() ||
            this.vmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot() ||
            this.vmManagementPermissionService.canRevertToVirtualMachineSnapshot()
        ) {
            columns.push({
                name: 'Actions',
                prop: '',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 100
            });
        }

        super.initialize(() => this.getVirtualMachineSnapshots$(), columns);
        this.table().sorts = [{ prop: 'current', dir: 'desc' }];
    }

    private getVirtualMachineSnapshots$(): Observable<ApiDataResult<SnapshotViewModel[]>> {
        return this.vmManagementService.getVirtualMachineSnapshots(this.vmDetailsStateService.selectedVM().id)
            .pipe(map(res => ({
                data: res.map(snapshot => {
                    const snapshotViewModel: SnapshotViewModel = {
                        id: snapshot.id,
                        created: new Date(snapshot.created),
                        current: snapshot.current,
                        name: snapshot.displayname?.trim() ?? snapshot.name?.trim() ?? '',
                        parent: snapshot.parent,
                        parentName: snapshot.parentName,
                        state: snapshot.state,
                        type: snapshot.type
                    };
                    return snapshotViewModel;
                }),
                total: res.length,
                message: ''
            })));
    }

    protected openSnapshotVmModal(): void {
        this.vmActionsService.openSnapshotVmModal(this.vmDetailsStateService.selectedVM().id, this.vmDetailsStateService.selectedVM().name);
    }

    protected openCreateSnapshotFromSnapshotModal(snapshotId: string, snapshotName: string, snapshotCreatedDate: Date): void {
        const selectedVm = this.vmDetailsStateService.selectedVM();
        this.vmActionsService.openCreateSnapshotFromVirtualMachineSnapshotModal(snapshotId, selectedVm.id, selectedVm.name, selectedVm.domainid, selectedVm.account, snapshotName, snapshotCreatedDate);
    }

    protected openDeleteSnapshotModal(snapshotId: string): void {
        this.vmActionsService.openDeleteSnapshotModal(snapshotId);
    }

    protected openRevertToVirtualMachineSnapshotModal(snapshotId: string) {
        this.vmActionsService.openRevertToVirtualMachineSnapshotModal(snapshotId, this.vmDetailsStateService.selectedVM().id, this.vmDetailsStateService.selectedVM().name);
    }

}
