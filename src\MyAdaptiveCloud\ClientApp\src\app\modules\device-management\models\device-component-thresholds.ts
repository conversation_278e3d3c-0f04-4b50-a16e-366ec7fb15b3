import { DeviceAlertThresholdType } from '../components/device-thresholds/models/device-alert-threshold-type';
import { DeviceThresholdInheritanceTypeEnum } from '../components/device-thresholds/models/device-threshold-inheritance-type.enum';
import { DeviceThresholdIntervals } from '../components/device-thresholds/models/device-threshold-intervals';
import { DeviceThresholdMetrics } from '../components/device-thresholds/models/device-threshold-metrics';
import { DeviceAlertTypeEnum } from './device-alert-type.enum';

export class DeviceComponentThresholds {
    public inheritanceType: DeviceThresholdInheritanceTypeEnum;
    public inheritFrom: string;
    public inheritedFromPath: string;
    public metrics: DeviceThresholdMetrics;
    public intervals: DeviceThresholdIntervals;
    public deviceAlertType: DeviceAlertTypeEnum;
    public deviceAlertThresholdTypes: DeviceAlertThresholdType[];
    public deviceAlertThresholdType: number;
    public name: string;
}
