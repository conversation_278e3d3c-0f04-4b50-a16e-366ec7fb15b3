<div class="modal-header">
    <h4 class="modal-title">Details</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body message-list-body">
    @if (bgpDetails()) {
        <div class="container">
            <div class="d-flex pb-2">
                <span class="text-secondary fw-bold me-2">Neighbor IP:</span>
                <span class="content">{{ bgpDetails().neighborIp ?? '-' }}</span>
            </div>
            <div class="d-flex border-bottom pb-2 mb-3">
                <span class="text-secondary fw-bold me-2">Prefix:</span>
                <span class="content">{{ bgpDetails().prefix ?? '-' }}</span>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Remote AS</span>
                    <span class="content">{{ bgpDetails().neighborRemoteAs ?? '-' }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Aggregator</span>
                    <span class="content">{{ bgpDetails().aggregator ?? '-' }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Next Hop</span>
                    <span class="content">{{ bgpDetails().nextHop ?? '-' }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Atomic Aggregate</span>
                    <span class="content">{{ bgpDetails().atomicAggregate ? 'True' : 'False' }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">AS Path</span>
                    <span class="content">{{ bgpDetails().asPath?.length ? bgpDetails().asPath.join(', ') : '-'}}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Extended Community</span>
                    <span class="content">{{ bgpDetails().extendedCommunities | communities }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Communities</span>
                    <span class="content">{{ bgpDetails().communities | communities }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Large Community</span>
                    <span class="content">{{ bgpDetails().largeCommunities | communities}}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Med</span>
                    <span class="content">{{ bgpDetails().med ?? '-' }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Cluster List</span>
                    <span class="content">{{ bgpDetails().clusterList?.length ? bgpDetails().clusterList?.join(', ') : '-'
                        }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Local Pref</span>
                    <span class="content">{{ bgpDetails().localPref ?? '-' }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Originator ID</span>
                    <span class="content">{{ bgpDetails().originatorId ?? '-' }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col-6 d-flex flex-column">
                    <span class="text-secondary fw-bold">Origin</span>
                    <span class="content">{{ bgpDetails().origin ?? '-' }}</span>
                </div>
            </div>
        </div>
    }
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-primary" (click)="activeModal.dismiss()">Close</button>
</div>
