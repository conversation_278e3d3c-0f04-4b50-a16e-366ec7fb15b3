using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class PartnerResourceHubDocumentAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IConfigurationService _configurationService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public PartnerResourceHubDocumentAuthorizeFilter(
            IEntityAuthorizationService entityAuthorizationService,
            IIdentityService identityService,
            IConfigurationService configurationService)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _configurationService = configurationService;
            _identityService = identityService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            var configuration = await _configurationService.GetPartnerResourceHubConfiguration();

            string val = AuthorizeFilterHelpers.GetEntityValue(context, "documentId");
            _ = int.TryParse(val, out int documentId);

            var documentIsInPartnerResourceHubFolder = await _entityAuthorizationService.DocumentIsInPartnerResourceHubFolderHierarchy(documentId, configuration.FolderId);
            if (!documentIsInPartnerResourceHubFolder)
            {
                context.Result = new ForbidResult();
            }
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class PartnerResourceHubDocumentAuthorizeAttribute : TypeFilterAttribute
    {
        public PartnerResourceHubDocumentAuthorizeAttribute() : base(typeof(PartnerResourceHubDocumentAuthorizeFilter))
        {
        }
    }
}