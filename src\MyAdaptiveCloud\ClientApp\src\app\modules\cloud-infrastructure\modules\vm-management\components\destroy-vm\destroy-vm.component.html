<div class="modal-header">
  <h4 class="modal-title">Destroy VM</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
  <p>Please confirm that you want to destroy this VM</p>
  @if (form) {
    <form [formGroup]="form">
      @if (cloudInfraPermissionService.isAdmin()) {
        <div class="mb-3 row">
          <div class="col-6 mb-2">
            <input id="expunge" formControlName="expunge" class="form-check-input me-2" type="checkbox" />
            <label for="expunge" class="form-check-label">
              Expunge
            </label>
          </div>
        </div>
      }
      @if (volumes().length) {
        <div class="mb-3 row">
          <div>
            <label class="form-label">
              Data Volumes to be deleted
            </label>
          </div>
          <div class="row" formArrayName="volumes">
            @for (volume of form.controls.volumes.controls; track volume; let index = $index) {
              <div class="mb-2 col-4">
                <input id="delete-volumes-{{ volumes()[index].id }}" formControlName="{{ index }}"
                  class="form-check-input me-2" type="checkbox" />
                <label for="delete-volumes-{{ volumes()[index].id }}" class="form-check-label">
                  {{volumes()[index].name }}
                </label>
              </div>
            }
          </div>
        </div>
      }
    </form>
  }
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
  <app-btn-submit [disabled]="form?.invalid || isSubmitting()" [btnClasses]="'btn-primary'"
  (submitClickEvent)="destroyVirtualMachine()">OK</app-btn-submit>
</div>
