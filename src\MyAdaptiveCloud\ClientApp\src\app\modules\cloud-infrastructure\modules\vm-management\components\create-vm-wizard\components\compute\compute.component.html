<p class="title">{{ label }}<span class="required-asterisk">*</span></p>

<div [formGroup]="form">
    <table class="w-100 options-table">
        <thead>
            <th>Compute Offering</th>
            <th>vCPU(s)</th>
            <th>Memory</th>
        </thead>
        <tbody>
            @for(service of presetServiceOfferings(); track service; let even = $even) {
            <tr>
                <td>
                    <input
                        class="form-check-input me-3"
                        type="radio"
                        [formControl]="form.controls.serviceOffering"
                        [value]="service.id"
                        [id]="'service_' + service.id">
                    <label
                        class="form-check-label"
                        [for]="'service_' + service.id">
                        {{service.name}}</label>
                </td>
                <td>
                    <label class="form-check-label" [for]="'service_' + service.id">{{service.cpuNumber}}</label>
                </td>
                <td>
                    <label class="form-check-label" [for]="'service_' + service.id">{{service.memory}}
                        MB</label>
                </td>
            </tr>
            }
            @if (customServiceOffering()) {
            <tr formGroupName="customServiceOffering">
                <td class="col align-content-center">
                    <input class="form-check-input me-3" type="radio" [formControl]="form.controls.serviceOffering"
                        [value]="customServiceOffering().id" [id]="'service_' + customServiceOffering().id"> <label
                        class="form-check-label"
                        [for]="'service_' + customServiceOffering().id">{{customServiceOffering().name}}</label>
                </td>
                <td class="col align-content-center">
                    <input class="form-control cpu-input" type="number" step="1" formControlName="cpuNumber"
                        [class]="{'is-invalid': form.controls.customServiceOffering.controls.cpuNumber.invalid && form.controls.customServiceOffering.controls.cpuNumber.dirty}">
                    @if (form.controls.customServiceOffering.controls.cpuNumber.invalid &&
                    form.controls.customServiceOffering.controls.cpuNumber.dirty) {
                    <div class="invalid-feedback">
                        @if (cpuCoresCustomOfferingMaxValue) {
                        vCPU count must be between {{ cpuCoresCustomOfferingMinValue }} and {{
                        cpuCoresCustomOfferingMaxValue }}
                        } @else {
                        vCPU count must be at least {{ cpuCoresCustomOfferingMinValue }}
                        }
                    </div>
                    }
                </td>
                <td class="col align-content-center">
                    <input class="form-control d-inline memory-input" type="number" step="1" formControlName="memory"
                        [class]="{'is-invalid': form.controls.customServiceOffering.controls.memory.invalid && form.controls.customServiceOffering.controls.memory.dirty}">
                    MB
                    @if (form.controls.customServiceOffering.controls.memory.invalid &&
                    form.controls.customServiceOffering.controls.memory.dirty) {
                    <div class="invalid-feedback">
                        @if (memoryCustomOfferingMaxValue) {
                        Memory must be between {{ memoryCustomOfferingMinValue }} and {{ memoryCustomOfferingMaxValue }}
                        MB
                        } @else {
                        Memory must be at least {{ memoryCustomOfferingMinValue }} MB
                        }
                    </div>
                    }
                </td>
            </tr>
            }
        </tbody>
    </table>
</div>
