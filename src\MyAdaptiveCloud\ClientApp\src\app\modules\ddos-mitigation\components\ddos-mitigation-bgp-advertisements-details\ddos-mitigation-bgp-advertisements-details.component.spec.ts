import { ComponentFixture, TestBed } from '@angular/core/testing';

import { By } from '@angular/platform-browser';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { DdosMitigationBgpAdvertisementsDetailsComponent } from './ddos-mitigation-bgp-advertisements-details.component';

describe('DdosMitigationBgpAdvertisementsDetailsComponent', () => {
    let component: DdosMitigationBgpAdvertisementsDetailsComponent;
    let fixture: ComponentFixture<DdosMitigationBgpAdvertisementsDetailsComponent>;
    let activeModalSpy: jasmine.SpyObj<NgbActiveModal>;

    const mockBgp = {
        neighborIp: '**************',
        neighborRemoteAs: 393775,
        prefix: '************/29',
        nextHop: '**************',
        asPath: [],
        communities: [],
        med: 0,
        localPref: 100,
        origin: 0,
        aggregator: null,
        atomicAggregate: false,
        extendedCommunities: [],
        largeCommunities: [],
        clusterList: [
            '**************',
            '**************'
        ],
        originatorId: '**************',
        simulate: false
    };

    beforeEach(() => {
        activeModalSpy = jasmine.createSpyObj('NgbActiveModal', ['dismiss']);

        TestBed.configureTestingModule({
            imports: [DdosMitigationBgpAdvertisementsDetailsComponent, NgbPopover],
            providers: [{ provide: NgbActiveModal, useValue: activeModalSpy }],
        })
            .compileComponents();

        fixture = TestBed.createComponent(DdosMitigationBgpAdvertisementsDetailsComponent);
        component = fixture.componentInstance;
        component.bgpDetails.set(mockBgp);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should render modal title correctly', () => {
        const title = fixture.debugElement.query(By.css('.modal-title')).nativeElement;
        expect(title.textContent).toBe('Details');
    });

    it('should render neighbor IP correctly', () => {
        const ipRow = fixture.debugElement.query(By.css('.ip-row-value')).nativeElement;
        expect(ipRow.textContent).toBe('**************');
    });

    it('should render Local AS and Remote AS', () => {
        const cols = fixture.debugElement.queryAll(By.css('.details .col'));

        expect(cols[0].nativeElement.textContent).toContain('Remote AS');
        expect(cols[0].nativeElement.textContent).toContain('393775');

        expect(cols[2].nativeElement.textContent).toContain('Next Hop');
        expect(cols[2].nativeElement.textContent).toContain('**************');
    });

    it('should call activeModal.dismiss when close button is clicked', () => {
        const closeButton = fixture.debugElement.query(By.css('.btn-close'));
        closeButton.nativeElement.click();

        expect(activeModalSpy.dismiss).toHaveBeenCalled();
    });

    it('should render empty body if neighborDetails is null', () => {
        component.bgpDetails.set(null);
        fixture.detectChanges();

        const body = fixture.debugElement.query(By.css('.modal-body'));
        expect(body.nativeElement.textContent.trim()).toBe('');
    });
});
