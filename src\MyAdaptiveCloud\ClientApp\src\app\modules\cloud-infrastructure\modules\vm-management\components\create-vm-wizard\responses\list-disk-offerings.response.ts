export interface ListDiskOfferingsResponse {
  listdiskofferingsresponse: ListDiskOfferings;
}

export interface ListDiskOfferings {
  count: number;
  diskoffering: DiskOfferingResponseItem[];
}

export interface DiskOfferingResponseItem {
  id: string;
  name: string;
  displaytext: string;
  disksize: number;
  created: string;
  iscustomized: boolean;
  storagetype: string;
  provisioningtype: string;
  displayoffering: boolean;
  cacheMode?: string;
}

