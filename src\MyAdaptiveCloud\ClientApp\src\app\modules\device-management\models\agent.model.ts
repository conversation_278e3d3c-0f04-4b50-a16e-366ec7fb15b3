import { DeviceAlertThresholdTypeNameEnum } from '../components/device-thresholds/models/device-alert-threshold-type-name.enum';
import { DeviceThresholdInheritanceTypeEnum } from '../components/device-thresholds/models/device-threshold-inheritance-type.enum';
import { AgentStatus } from './agent-status.enum';
import { DeviceAlerts } from './device-alerts';
import { Disk } from './disk.model';

export class Agent {
    public agentId: number;
    public agentUuid: string;
    public hostname: string;
    public description: string;
    public status: AgentStatus;
    public ip: string;
    public operatingSystem: string;
    public heartbeatTs?: Date;
    public healthStatTs?: Date;
    public cpuUsage?: number;
    public ramTotal?: number;
    public ramUsed?: number;
    public ramUsage?: number;
    public agentVersionMajor: number;
    public agentVersionMinor: number;
    public agentVersionBuild: number;
    public canRemoteDesktop: boolean;
    public canRemoteCommands: boolean;
    public canDecommissionDevice: boolean;
    public diskName: string;
    public sizeTotal?: number;
    public sizeUsed?: number;
    public diskUsage?: number;
    public disks: Disk[] = [];
    public organizationId: number;
    public folderId?: number;
    public uptimeLastStartTimeUtc?: Date;
    public uptimeForDisplayDaysHoursMinutes?: string;
    public cloudStackVM?: boolean;

    public ramUsageWarningThreshold: number;
    public ramUsageErrorThreshold: number;
    public ramUsageCriticalThreshold: number;
    public ramUsageAlertThresholdTypeName: DeviceAlertThresholdTypeNameEnum;
    public ramUsageInheritanceType: DeviceThresholdInheritanceTypeEnum;

    public cpuUsageWarningThreshold: number;
    public cpuUsageErrorThreshold: number;
    public cpuUsageCriticalThreshold: number;
    public cpuUsageInheritanceType: DeviceThresholdInheritanceTypeEnum;

    public heartbeatWarningThreshold: number;
    public heartbeatErrorThreshold: number;
    public heartbeatCriticalThreshold: number;
    public heartbeatInheritanceType: DeviceThresholdInheritanceTypeEnum;

    public activeAlerts: DeviceAlerts[] = [];
    public inScheduleDowntime = false;
    public scheduleDowntimeEndDate: Date;
    public acknowledgedBy: string;
    public acknowledgedDate: Date;

    public markedForUninstall: boolean;
    public markedForReRegister: boolean;

    public agentVersionText: string;
    public folderIdsPathFromOrganization: string;
}
