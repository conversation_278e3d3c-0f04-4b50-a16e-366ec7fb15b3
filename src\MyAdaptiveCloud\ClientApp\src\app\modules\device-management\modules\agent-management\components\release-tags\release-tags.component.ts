import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { RELEASE_TAG_ORDER } from '@app/shared/constants/release-tags-constants';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgOptionComponent, NgSelectComponent } from '@ng-select/ng-select';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { filter, map, take } from 'rxjs';
import { ServiceTypeForm } from '../../forms/service-type.form';
import { ReleaseTag } from '../../models/release-tag.model';
import { ServiceTypeEnum } from '../../models/service-type.enum';
import { UpdateReleaseTagConfirmationModel } from '../../models/update-release-tag-confirmation.model';
import { UpdateReleaseTagRequest } from '../../requests/update-release-tag.request';
import { ReleaseTagsService } from '../../services/release-tags.service';
import { UpdateReleaseTagsModalComponent } from './update-release-tags-confirmation-modal/update-release-tags-confirmation-modal.component';

@Component({
    selector: 'app-release-tags',
    imports: [NgxDatatableModule, NgSelectComponent, ReactiveFormsModule, NgOptionComponent, NgbPopover, DatePipe],
    templateUrl: './release-tags.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ReleaseTagsComponent implements OnInit {

    private readonly releaseTagsService = inject(ReleaseTagsService);
    private readonly notificationService = inject(NotificationService);
    private readonly formBuilder = inject(FormBuilder);
    private readonly modalService = inject(ModalService);

    protected readonly agentVersions = toSignal(this.releaseTagsService.getAgents().pipe(map(res => res.data)), { initialValue: [] });
    protected readonly watchdogVersions = toSignal(this.releaseTagsService.getWatchdogs().pipe(map(res => res.data)), { initialValue: [] });
    protected readonly releaseTags = signal<ReleaseTag[]>([]);

    protected readonly form = signal<FormArray<FormGroup<ServiceTypeForm>>>(null);

    ngOnInit(): void {
        this.releaseTagsService.getList()
            .pipe(map(res => this.sortByReleaseTagOrder(res.data, RELEASE_TAG_ORDER)))
            .subscribe(res => {
                this.releaseTags.set(res);
                const formGroups: FormGroup<ServiceTypeForm>[] = [];
                res.forEach(releaseTag => {
                    formGroups.push(this.formBuilder.group({
                        id: this.formBuilder.control(releaseTag.id),
                        name: this.formBuilder.control(releaseTag.releaseTagName),
                        agentVersion: this.formBuilder.control({ value: releaseTag.agentServiceId, disabled: !releaseTag.agentVisible }, { validators: releaseTag.agentMandatory ? Validators.required : null }),
                        watchdogVersion: this.formBuilder.control({ value: releaseTag.watchdogServiceId, disabled: !releaseTag.watchdogVisible }, { validators: releaseTag.watchdogMandatory ? Validators.required : null }),
                        agentMandatory: this.formBuilder.control(releaseTag.agentMandatory),
                        watchdogMandatory: this.formBuilder.control(releaseTag.watchdogMandatory)
                    }));
                });
                this.form.set(this.formBuilder.array(formGroups));
            });
    }

    protected update() {
        if (this.form().valid && this.form().dirty) {
            const modal = this.modalService.openModalComponent(UpdateReleaseTagsModalComponent);
            (modal.componentInstance as UpdateReleaseTagsModalComponent).releaseTags.set(this.mapConfirmationReleaseTags());

            modal.closed
                .pipe(
                    take(1),
                    filter(res => !!res)
                ).subscribe(() => {
                    this.releaseTagsService.update(this.mapRequest()).subscribe(res => {
                        this.notificationService.notify(res.message);
                        this.form().markAsPristine();
                    });
                });
        }
    }

    private mapConfirmationReleaseTags(): UpdateReleaseTagConfirmationModel[] {
        const updatedReleaseTags: UpdateReleaseTagConfirmationModel[] = [];
        this.form().controls.forEach(formGroup => {
            if (formGroup.controls.agentVersion.dirty || formGroup.controls.watchdogVersion.dirty) {
                const updatedReleaseTag: UpdateReleaseTagConfirmationModel = {
                    releaseTagName: formGroup.controls.name.value
                };

                if (formGroup.controls.agentVersion.dirty) {
                    const agent = this.agentVersions().find(a => a.serviceId === formGroup.controls.agentVersion.value);
                    updatedReleaseTag.agent = {
                        serviceName: agent?.name ?? 'No specific version',
                        serviceReleaseDate: agent?.releaseDate ?? null,
                        serviceVersion: agent?.version ?? null
                    };
                }

                if (formGroup.controls.watchdogVersion.dirty) {
                    const watchdog = this.watchdogVersions().find(w => w.serviceId === formGroup.controls.watchdogVersion.value);
                    updatedReleaseTag.watchdog = {
                        serviceName: watchdog?.name ?? 'No specific version',
                        serviceReleaseDate: watchdog?.releaseDate ?? null,
                        serviceVersion: watchdog?.version ?? null
                    };
                }

                updatedReleaseTags.push(updatedReleaseTag);
            }
        });
        return updatedReleaseTags;
    }

    private mapRequest(): UpdateReleaseTagRequest[] {
        const request: UpdateReleaseTagRequest[] = [];
        this.form().controls.forEach(formGroup => {
            if (formGroup.controls.agentVersion.dirty) {
                request.push({
                    id: formGroup.controls.id.value,
                    serviceId: formGroup.controls.agentVersion.value,
                    serviceType: ServiceTypeEnum.Agent
                });
            }
            if (formGroup.controls.watchdogVersion.dirty) {
                request.push({
                    id: formGroup.controls.id.value,
                    serviceId: formGroup.controls.watchdogVersion.value,
                    serviceType: ServiceTypeEnum.Watchdog
                });
            }
        });
        return request;
    }

    private sortByReleaseTagOrder(
        data: ReleaseTag[],
        releaseTagOrder: string[]
    ): ReleaseTag[] {
        return data.sort((a, b) => {
            const aIndex = releaseTagOrder.indexOf(a.releaseTagName.toLowerCase());
            const bIndex = releaseTagOrder.indexOf(b.releaseTagName.toLowerCase());

            if (aIndex !== -1 && bIndex !== -1) {
                return aIndex - bIndex;
            } else if (aIndex !== -1) {
                return 1;
            } else if (bIndex !== -1) {
                return -1;
            }

            return a.releaseTagName.localeCompare(b.releaseTagName);
        });
    }
}
