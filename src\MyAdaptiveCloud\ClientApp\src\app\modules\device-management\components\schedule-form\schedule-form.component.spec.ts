import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ScheduleType } from '@app/shared/models/schedule-type.enum';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { Policy } from '../../models/policy.model';
import { Schedule } from '../../models/schedule/schedule.model';
import { ReleaseTagId } from '../../modules/agent-management/models/release-tag-id.model';
import { ReleaseTagsService } from '../../modules/agent-management/services/release-tags.service';
import { OrganizationScheduleService } from '../../modules/policy/services/organization-schedules.service';
import { ScheduleService } from '../../modules/policy/services/schedule.service';
import { OrganizationPolicyService } from '../../services/organization-policy.service';
import { PolicyService } from '../../services/policy.service';
import { ScheduleFormComponent } from './schedule-form.component';
import { ScheduleStore } from './schedule.component.store';

describe('ScheduleFormComponent', () => {
    let component: ScheduleFormComponent;
    let fixture: ComponentFixture<ScheduleFormComponent>;
    let mockScheduleService: jasmine.SpyObj<ScheduleService>;
    let mockOrganizationScheduleService: jasmine.SpyObj<OrganizationScheduleService>;
    let mockPolicyService: jasmine.SpyObj<PolicyService>;
    let mockOrganizationPolicyService: jasmine.SpyObj<OrganizationPolicyService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockReleaseTagsService: jasmine.SpyObj<ReleaseTagsService>;

    const data: Schedule = {
        organizationId: 1,
        canEdit: UserActionState.Allowed,
        canDelete: UserActionState.Allowed,
        canCreate: UserActionState.Allowed,
        canView: UserActionState.Allowed,
        scheduleId: 2,
        name: 'Name',
        description: 'something',
        startDateTimeLocalized: '',
        scheduleType: ScheduleType.Daily,
        isEnabled: true,
        freqRecurrenceFactor: 0,
        months: [],
        monthDays: [],
        monthOnWeek: [],
        monthOnWeekNumber: [],
        weeklyDays: [],
        updateCategoriesAutoApproval: []
    };

    const dataPolicy: Policy = {
        canEdit: UserActionState.Disabled,
        canCreate: UserActionState.Disabled,
        canDelete: UserActionState.Disabled,
        canView: UserActionState.Disabled,
        policyId: 1,
        organizationId: 0,
        name: 'Policy 1',
        description: '1',
        isEnabled: false,
        scheduleId: 2,
        releaseTagName: ''
    };

    const RELEASE_TAGS: ReleaseTagId[] = [
        {
            id: 1,
            releaseTagName: 'Stable',
            description: 'Recommended for production environments with thoroughly tested features.',
            agentVersion: ''
        },
        {
            id: 2,
            releaseTagName: 'Beta',
            description: 'Includes upcoming features that are mostly stable but may still have minor bugs.',
            agentVersion: ''
        },
        {
            id: 3,
            releaseTagName: 'Alpha',
            description: 'Early access to new features for testing; not recommended for production.',
            agentVersion: ''
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                ScheduleFormComponent
            ],
            providers: [
                NgbActiveModal,
                provideMock(UserContextService),
                provideMock(PolicyService),
                provideMock(OrganizationPolicyService),
                provideMock(NotificationService),
                provideMock(ScheduleService),
                provideMock(OrganizationScheduleService),
                provideMock(ReleaseTagsService),
                ScheduleStore,
                provideMock(UserContextService)
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(ScheduleFormComponent);
        mockScheduleService = TestBed.inject(ScheduleService) as jasmine.SpyObj<ScheduleService>;
        mockScheduleService.getById.and.returnValue(of({ data, message: 'success' }));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            userId: 1,
            organizationId: 50
        } as UserContext;

        mockPolicyService = TestBed.inject(PolicyService) as jasmine.SpyObj<PolicyService>;
        mockPolicyService.getById.and.returnValue(of({ data: dataPolicy, message: 'success' }));
        mockPolicyService.getMinAgentVersionImmediateUpdateInstall.and.returnValue(of({ data: 'v1.27', message: 'success' }));

        mockOrganizationPolicyService = TestBed.inject(OrganizationPolicyService) as jasmine.SpyObj<OrganizationPolicyService>;
        mockOrganizationPolicyService.getPolicyFromAncestorOrganization.and.returnValue(of({ data: dataPolicy, message: 'success' }));

        mockOrganizationScheduleService = TestBed.inject(OrganizationScheduleService) as jasmine.SpyObj<OrganizationScheduleService>;
        mockOrganizationScheduleService.getScheduleFromAncestorOrganization.and.returnValue(of({ data, message: 'success' }));

        mockReleaseTagsService = TestBed.inject(ReleaseTagsService) as jasmine.SpyObj<ReleaseTagsService>;
        mockReleaseTagsService.getIdList.and.returnValue(of({ data: RELEASE_TAGS, message: 'success' }));

        component = fixture.componentInstance;
    });

    describe('ngOnInit', () => {
        it('should set dialogTitle to "Create Schedule" if policyId is undefined', () => {
            component.policyId.set(null);
            fixture.detectChanges();
            expect(component.dialogTitle()).toEqual('Create Policy');
        });

        it('should set dialogTitle to "Edit Schedule" if policyId is not undefined', () => {
            component.policyId.set(1);
            component.scheduleId.set(1);
            fixture.detectChanges();
            expect(component.dialogTitle()).toEqual('Edit Policy');
        });

        it('should call the scheduleService if PolicyId and ScheduleId is not undefined', () => {
            component.policyId.set(1);
            component.scheduleId.set(1);
            fixture.detectChanges();
            expect(mockOrganizationScheduleService.getScheduleFromAncestorOrganization).toHaveBeenCalled();
        });
    });

    describe('setFormInitialState', () => {
        it('should call setFormForScheduleEdit if scheduleID is not NaN and PolicyId is not NaN', () => {
            spyOn(component.scheduleStore, 'updateScheduleTypeFormState').and.callThrough();
            spyOn(component.scheduleStore, 'updateScheduleInfoFormState').and.callThrough();
            component.policyId.set(1);
            component.scheduleId.set(1);
            fixture.detectChanges();
            expect(mockOrganizationScheduleService.getScheduleFromAncestorOrganization).toHaveBeenCalledOnceWith(50, 1);
            expect(component.dialogTitle()).toEqual('Edit Policy');
            expect(component.scheduleStore.updateScheduleTypeFormState).toHaveBeenCalledTimes(1);
            expect(component.scheduleStore.updateScheduleInfoFormState).toHaveBeenCalledTimes(1);
        });

        it('should call setFormForScheduleEdit if scheduleID is NOT NaN and policyId is NaN', () => {
            spyOn(component.scheduleStore, 'updateScheduleInfoFormState').and.callThrough();
            component.policyId.set(1);
            component.scheduleId.set(NaN);
            fixture.detectChanges();
            expect(component.dialogTitle()).toEqual('Edit Policy');
            expect(component.scheduleStore.updateScheduleInfoFormState).toHaveBeenCalledTimes(1);
            expect(mockOrganizationPolicyService.getPolicyFromAncestorOrganization).toHaveBeenCalledWith(50, 1);
        });

        it('should not call setFormForScheduleCreate if scheduleID is NaN and PolicyId is NaN', () => {
            component.scheduleId.set(NaN);
            component.policyId.set(NaN);
            spyOn(component.scheduleStore, 'updateScheduleTypeFormState').and.callThrough();
            spyOn(component.scheduleStore, 'updateScheduleInfoFormState').and.callThrough();
            fixture.detectChanges();
            expect(component.dialogTitle()).toEqual('Create Policy');
            expect(component.scheduleStore.updateScheduleInfoFormState).not.toHaveBeenCalled();
        });
    });
});
