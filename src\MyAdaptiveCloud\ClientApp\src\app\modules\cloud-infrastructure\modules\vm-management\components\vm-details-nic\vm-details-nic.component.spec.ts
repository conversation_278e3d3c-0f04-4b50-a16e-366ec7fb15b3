import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { Nic } from '@app/shared/models/cloud-infra/nic.model';
import { NgbAccordionBody, NgbAccordionItem } from '@ng-bootstrap/ng-bootstrap';
import { VmDetails } from '../../models/vm-detail.model';
import { VmAffinityGroupsService } from '../../services/vm-affinity-groups.service';
import { VmDetailsNicComponent } from './vm-details-nic.component';
import { VmDetailsStateService } from '../../services/vm-details.state.service';

describe('VmDetailsNicComponent', () => {
    let fixture: ComponentFixture<VmDetailsNicComponent>;

    let vmDetailsStateService: VmDetailsStateService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [VmDetailsNicComponent],
            providers: [
                VmAffinityGroupsService
            ]
        });

        vmDetailsStateService = TestBed.inject(VmDetailsStateService);

        fixture = TestBed.createComponent(VmDetailsNicComponent);
    });

    describe('Nics', () => {

        it('should return the correct nics', () => {

            const vm = {
                nic: [
                    {
                        id: '8d9499ac-8b60-4fc0-a52c-5bb31cd74537',
                        networkid: 'c0c69ac7-4fcd-4f11-90f6-8dc4962767ee',
                        networkname: 'Network 1',
                        netmask: '*************',
                        gateway: '***********',
                        ipaddress: '************',
                        isolationuri: 'vlan://384',
                        broadcasturi: 'vlan://384',
                        traffictype: 'Guest',
                        type: 'Isolated',
                        macaddress: '00:11:22:33:44:55'
                    } as Nic,
                    {
                        id: '2d9499ac-8b60-4fc0-a52c-5bb31cd74538',
                        networkid: 'c0c69ac7-4fcd-4f11-90f6-8dc4962767e2',
                        networkname: 'Network 2',
                        netmask: '*************',
                        gateway: '***********',
                        ipaddress: '************',
                        isolationuri: 'vlan://387',
                        broadcasturi: 'vlan://388',
                        traffictype: 'Guest',
                        type: 'L2',
                        macaddress: '00:11:22:33:44:55'
                    } as Nic
                ]
            } as VmDetails;

            vmDetailsStateService.selectedVM.set(vm);

            fixture.detectChanges();

            const nics = fixture.debugElement.queryAll(By.directive(NgbAccordionItem));
            const fieldsNic1 = nics[0].query(By.directive(NgbAccordionBody)).queryAll(By.css('p'));
            const fieldsNic2 = nics[1].query(By.directive(NgbAccordionBody)).queryAll(By.css('p'));

            expect(nics.length).toBe(2);
            expect(fieldsNic1[0].nativeElement.textContent).toContain('Type: Isolated');
            expect(fieldsNic1[1].nativeElement.textContent).toContain('Traffic Type: Guest');
            expect(fieldsNic1[2].nativeElement.textContent).toContain('Network Name: Network 1');
            expect(fieldsNic1[3].nativeElement.textContent).toContain('Netmask: *************');
            expect(fieldsNic1[4].nativeElement.textContent).toContain('IP Address: ************');
            expect(fieldsNic1[5].nativeElement.textContent).toContain('MAC Address: 00:11:22:33:44:55');
            expect(fieldsNic1[6].nativeElement.textContent).toContain('ID: 8d9499ac-8b60-4fc0-a52c-5bb31cd74537');
            expect(fieldsNic1[7].nativeElement.textContent).toContain('Network ID: c0c69ac7-4fcd-4f11-90f6-8dc4962767ee');
            expect(fieldsNic1[8].nativeElement.textContent).toContain('Isolation URI: vlan://384');
            expect(fieldsNic1[9].nativeElement.textContent).toContain('Broadcast URI: vlan://384');

            expect(fieldsNic2[0].nativeElement.textContent).toContain('Type: L2');
            expect(fieldsNic2[1].nativeElement.textContent).toContain('Traffic Type: Guest');
            expect(fieldsNic2[2].nativeElement.textContent).toContain('Network Name: Network 2');
            expect(fieldsNic2[3].nativeElement.textContent).toContain('Netmask: *************');
            expect(fieldsNic2[4].nativeElement.textContent).toContain('IP Address: ************');
            expect(fieldsNic2[5].nativeElement.textContent).toContain('MAC Address: 00:11:22:33:44:55');
            expect(fieldsNic2[6].nativeElement.textContent).toContain('ID: 2d9499ac-8b60-4fc0-a52c-5bb31cd74538');
            expect(fieldsNic2[7].nativeElement.textContent).toContain('Network ID: c0c69ac7-4fcd-4f11-90f6-8dc4962767e2');
            expect(fieldsNic2[8].nativeElement.textContent).toContain('Isolation URI: vlan://387');
            expect(fieldsNic2[9].nativeElement.textContent).toContain('Broadcast URI: vlan://388');
        });

    });

});
