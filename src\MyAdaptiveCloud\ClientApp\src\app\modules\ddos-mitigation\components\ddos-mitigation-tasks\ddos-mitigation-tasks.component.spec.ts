import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { TaskDetail } from '@app/shared/models/ddos-mitigation/task-detail.model';
import { UserContext } from '@app/shared/models/user-context.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { DdosMitigationMessagesComponent } from '../ddos-mitigation-messages/ddos-mitigation-messages.component';
import { DdosMitigationTasksComponent } from './ddos-mitigation-tasks.component';
import { DDoSMitigationTasksService } from '../../services/ddos-mitigation-tasks.service';

describe('DdosMitigationTasksComponent', () => {

    let fixture: ComponentFixture<DdosMitigationTasksComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockDDoSMitigationTasksService: jasmine.SpyObj<DDoSMitigationTasksService>;
    let mockModalService: jasmine.SpyObj<ModalService>;

    const data: TaskDetail[] = [
        {
            endTime: '2023-10-01T12:00:00Z',
            id: '1',
            messages: ['Task completed successfully', 'No issues detected'],
            name: 'Task',
            result: 'Task 1',
            startTime: '2023-10-01T10:00:00Z',
            status: 'SUCCESS',
        },
        {
            endTime: '2023-10-01T12:00:00Z',
            id: '2',
            messages: ['No issues detected'],
            name: 'Task 2',
            result: 'Task 2',
            startTime: '2024-10-01T10:00:00Z',
            status: 'SUCCESS',
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationTasksComponent],
            providers: [
                provideMock(DDoSMitigationTasksService),
                provideMock(UserContextService),
                provideMock(ModalService),
                provideMock(NgbActiveModal),
                DdosMitigationMessagesComponent
            ]
        })
            .compileComponents();

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1,
            name: 'Test User',
            email: ''
        } as UserContext;

        mockDDoSMitigationTasksService = TestBed.inject(DDoSMitigationTasksService) as jasmine.SpyObj<DDoSMitigationTasksService>;
        mockDDoSMitigationTasksService.getList.and.returnValue(of({ data }));

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;

        fixture = TestBed.createComponent(DdosMitigationTasksComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            fixture.detectChanges();
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
        });

        it('should call getNeighborsList', () => {
            expect(mockDDoSMitigationTasksService.getList).toHaveBeenCalledTimes(1);
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });
    });

    describe('Actions', () => {

        let dataTableDebugElement: DebugElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
        });

        it('should call open messages modal when clicking on the action', () => {
            const modalRef = {
                closed: of(true),
                componentInstance: TestBed.inject(DdosMitigationMessagesComponent)
            } as NgbModalRef;
            mockModalService.openModalComponent.and.returnValue(modalRef);

            const action = dataTableDebugElement.queryAll(By.directive(TableActionComponent))[1];
            action.query(By.css('.table-action-container')).nativeElement.click();
            fixture.detectChanges();

            expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            expect((modalRef.componentInstance as DdosMitigationMessagesComponent).messages()).toEqual(data[0].messages);

        });

    });

});
