import { TestBed } from '@angular/core/testing';
import { UpdateUserAuthenticatorRequest } from '@app/core/requests/update-user-authenticator.request';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { ApiService } from '@app/shared/services/api.service';
import { TwoFactorAuthenticationService } from './two-factor-authentication.service';

describe('TwoFactorAuthenticationService', () => {
    let service: TwoFactorAuthenticationService;
    let mockApiService: jasmine.SpyObj<ApiService>;
    const endpoint = 'twofactorauthentication';

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [provideMock(ApiService)]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(TwoFactorAuthenticationService);
    });

    describe('API', () => {
        it('should use the right endpoint for getUserAuthenticatorsList', () => {
            service.getUserAuthenticatorsList();
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/authenticators`);
        });

        it('should use the right endpoint for deleteAuthenticator', () => {
            const credentialId = '3';
            service.deleteAuthenticator(credentialId);
            expect(mockApiService.delete).toHaveBeenCalledWith(`${endpoint}`, credentialId);
        });
    });

    describe('API', () => {
        it('should use the right endpoint for updateUserAuthenticator', () => {

            const credentialId = '1';

            const request: UpdateUserAuthenticatorRequest = {
                name: '1'
            };

            service.updateUserAuthenticator(credentialId, request);
            expect(mockApiService.put).toHaveBeenCalledWith(`${endpoint}`, credentialId, request);
        });
    });
});
