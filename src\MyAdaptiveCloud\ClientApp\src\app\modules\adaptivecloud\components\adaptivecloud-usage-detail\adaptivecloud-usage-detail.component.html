@if (isDataLoaded()) {
  <div class="content-sub-heading">
    <h5>Virtual Machine Services</h5>
  </div>
  @if (virtualMachineServices()?.length) {
    @for (virtualMachineService of virtualMachineServices(); track virtualMachineService) {
      <div class="card card-default">
        <div class="card-body">
          <h5 class="card-subtitle mb-3">{{ virtualMachineService.name }}</h5>
          <app-adaptivecloud-usage-detail-service [data]="virtualMachineService.services"
            descriptionLabel="Virtual Machine Total ({{ virtualMachineService.startDate | date: 'M/d' }} - {{ virtualMachineService.endDate | date: 'M/d' }})" />
        </div>
      </div>
    }
  } @else {
    <div class="card card-default">
      <div class="card-body">
        <p>No Data to Display</p>
      </div>
    </div>
  }
  <div class="content-sub-heading">
    <h5>Network Services</h5>
  </div>
  @if (networkServices()?.length) {
    <div class="card card-default">
      <div class="card-body">
        <app-adaptivecloud-usage-detail-service [data]="networkServices()" [descriptionLabel]="'Network Total'" />
      </div>
    </div>
  } @else {
    <div class="card card-default">
      <div class="card-body">
        <p>No Data to Display</p>
      </div>
    </div>
  }
  <div class="content-sub-heading">
    <h5>Storage Services</h5>
  </div>
  @if (storageServices()?.length) {
    <div class="card card-default">
      <div class="card-body">
        <app-adaptivecloud-usage-detail-service [data]="storageServices()" [descriptionLabel]="'Storage Total'" />
      </div>
    </div>
  } @else {
    <div class="card card-default">
      <div class="card-body">
        <p>No Data to Display</p>
      </div>
    </div>
  }
}
