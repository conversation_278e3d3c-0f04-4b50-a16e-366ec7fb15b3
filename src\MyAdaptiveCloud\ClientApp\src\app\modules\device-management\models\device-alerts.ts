import { AlertThresholdLevelEnum } from './alert-rule-threshold-level.model';

export class DeviceAlerts {
    public agentId: number;
    public deviceAlertId: number;
    public alertType: string;
    public alertStatus: string;
    public acknowledgedBy: string;
    public acknowledgedDate: Date;
    public notes: string;
    public alertThresholdLevel: AlertThresholdLevelEnum;
    public alertTypeId: number;
    public inScheduledDownTime: boolean;
    public scheduleDowntimeEndDate: Date;

    // this two props must be moved to DeviceAlertsUI
    public selected = false;
    public selectable = true;
}
