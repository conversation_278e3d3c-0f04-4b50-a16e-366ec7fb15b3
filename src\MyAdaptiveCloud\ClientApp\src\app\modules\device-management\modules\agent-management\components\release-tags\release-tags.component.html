<div class="content-sub-heading">
    <div class="action-buttons">
        <button type="button" class="btn btn-primary" (click)="update()"
            [disabled]="!form()?.valid || form()?.pristine">Save</button>
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        @if (form()) {
        <div form="form">
            <div class="row g-5 p-2">
                <div class="col fw-bold">Release Type
                    <i class="ms-1 fa fa-info-circle" [ngbPopover]="releaseTypeMessageTemplate" triggers="hover"
                        container="body"></i>
                    <ng-template #releaseTypeMessageTemplate>
                        @for (releaseTag of releaseTags(); track releaseTag.id) {
                        <p>
                            <span class="fw-bold">{{ releaseTag.releaseTagName }}</span><br /> {{
                            releaseTag.description }}
                        </p>
                        }
                    </ng-template>
                </div>
                <div class="col fw-bold">Agent Version</div>
                <div class="col fw-bold">Watchdog Version</div>
            </div>
            <div class="row g-5 p-2">
                <hr />
            </div>
            @for (formGroup of form().controls; track formGroup) {
            <div class="row g-5 p-2">
                <div class="col-xl-2">
                    <div class="d-flex align-items-center">
                        {{ formGroup.controls.name.value }}
                    </div>
                </div>
                <div class="col-xl-5">
                    <div class="d-flex align-items-center">
                        <ng-select [formControl]="formGroup.controls.agentVersion" [searchable]="false"
                            [clearable]="true">
                            @for (agentVersion of agentVersions(); track agentVersion.serviceId) {
                            <ng-option [value]="agentVersion.serviceId">
                                {{ agentVersion.name }} {{ agentVersion.version }} ({{ agentVersion.releaseDate | date:
                                'yyyy-MM-dd':'UTC' }})
                            </ng-option>
                            }
                        </ng-select>
                        @if (formGroup.controls.agentMandatory.value) {
                            <span class="required-asterisk">*</span>
                        }
                    </div>
                </div>
                <div class="col-xl-5">
                    <div class="d-flex align-items-center">
                        <ng-select [formControl]="formGroup.controls.watchdogVersion" [searchable]="false"
                            [clearable]="true">
                            @for (watchdogVersion of watchdogVersions(); track watchdogVersion.serviceId) {
                            <ng-option [value]="watchdogVersion.serviceId">
                                {{ watchdogVersion.name }} {{ watchdogVersion.version }} ({{ watchdogVersion.releaseDate
                                |
                                date: 'yyyy-MM-dd':'UTC' }})
                            </ng-option>
                            }
                        </ng-select>
                        @if (formGroup.controls.watchdogMandatory.value) {
                            <span class="required-asterisk">*</span>
                        }
                    </div>
                </div>
            </div>
            }
        </div>
        }
    </div>
</div>
