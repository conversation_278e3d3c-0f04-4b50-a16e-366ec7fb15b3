@if (scheduleTypeForm) {
    <form class="form-horizontal" [formGroup]="scheduleTypeForm" novalidate>
        <div class="mb-3 row">
            <div class="col-xl-12">
                <label class="form-label">Start Date
                    <span><i title="The selected date and time will be in each target device’s local time zone."
                            class="text-secondary fa fa-info-circle"></i></span>
                </label>
                <div class="input-group">
                    <app-date-time-picker [id]="'startDate'" [placeholder]="'MM/DD/yyyy h:mm a'"
                        formControlName="startDate" />
                </div>
            </div>
        </div>
        <div class="mb-3 row">
            <div class="btn-group" role="group" aria-label="Schedule Type">
                <input type="radio" class="btn-check" formControlName="scheduleType" [value]="scheduleTypeEnum.Daily"
                    name="scheduleType" id="scheduleTypeDailyId" autocomplete="off">
                <label class="btn btn-outline-secondary"
                    for="scheduleTypeDailyId">{{scheduleTypeEnum[scheduleTypeEnum.Daily]}}</label>
                <input type="radio" class="btn-check" formControlName="scheduleType" [value]="scheduleTypeEnum.Weekly"
                    name="scheduleType" id="scheduleTypeWeeklyId" autocomplete="off">
                <label class="btn btn-outline-secondary"
                    for="scheduleTypeWeeklyId">{{scheduleTypeEnum[scheduleTypeEnum.Weekly]}}</label>
                <input type="radio" class="btn-check" formControlName="scheduleType" [value]="scheduleTypeEnum.Monthly"
                    name="scheduleType" id="scheduleTypeMonthlyId" autocomplete="off">
                <label class="btn btn-outline-secondary"
                    for="scheduleTypeMonthlyId">{{scheduleTypeEnum[scheduleTypeEnum.Monthly]}}</label>
            </div>
        </div>
        <div class="mb-3 row">
            <hr class="hr hr-blurry" />
        </div>
    </form>
}

@if (scheduleTypeForm.controls.scheduleType.value === scheduleTypeEnum.Daily) {
    <app-daily-schedule />
}

@if (scheduleTypeForm.controls.scheduleType.value === scheduleTypeEnum.Weekly) {
    <app-weekly-schedule />
}

@if (scheduleTypeForm.controls.scheduleType.value === scheduleTypeEnum.Monthly) {
    <app-monthly-schedule />
}

@if (isPolicyAutoApprovalFlagEnabled) {
    <app-update-category-autoapproval />
}
