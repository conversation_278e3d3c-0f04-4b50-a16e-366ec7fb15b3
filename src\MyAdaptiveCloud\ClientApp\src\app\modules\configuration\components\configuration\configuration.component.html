<div class="content-heading">
    Configuration
</div>

@if (form()) {
<form [formGroup]="form()" novalidate>
    @if (permissionService.canManageConfiguration()) {
    <div class="content-sub-heading">
        <div class="action-buttons">
            <button class="btn btn-primary" type="button" (click)="submitForm()"
                [disabled]="!form().valid || !form().dirty">Save</button>
        </div>
    </div>
    }
    <div class="card card-default">
        <div class="card-body">
            <div ngbAccordion [closeOthers]="true">
                @for (configuration of configurationList(); track configuration) {
                <div ngbAccordionItem>
                    <div ngbAccordionHeader>
                        <button ngbAccordionButton>{{ configuration.category }}</button>
                    </div>
                    <div ngbAccordionCollapse>
                        <div ngbAccordionBody>
                            <ng-template>
                                @for (configurationValue of configuration.configurationValues; track configurationValue)
                                {
                                <div class="mb-3 row">
                                    <label class="col-3 col-form-label">{{ configurationValue.name }}</label>
                                    <div class="col-9">
                                        @if (configurationValue.inputType === inputType.Input) {
                                        <input [type]="configurationValue.isSecret ? 'password' : 'text'"
                                            class="form-control" formControlName="{{ configurationValue.id }}"
                                            [class]="{ 'is-invalid': form().controls[configurationValue.id].invalid }" />
                                        }
                                        @if (configurationValue.inputType === inputType.TextArea) {
                                        <textarea rows="10" class="form-control"
                                            formControlName="{{ configurationValue.id }}"
                                            [class]="{ 'is-invalid': form().controls[configurationValue.id].invalid }">
                                                        </textarea>
                                        }
                                        @if (configurationValue.inputType === inputType.Toggle) {
                                        <input type="checkbox" class="form-control form-control-check-input clickable"
                                            formControlName="{{ configurationValue.id }}" />
                                        }
                                    </div>
                                </div>
                                }
                            </ng-template>
                        </div>
                    </div>
                </div>
                }
            </div>
        </div>
    </div>
</form>
}
