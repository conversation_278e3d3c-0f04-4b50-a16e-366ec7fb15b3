@if(form) {
    <div class="general-details-middle card p-4" [formGroup]="form">
        <div class="row gy-2">
            <div class="col-md-6">
                <div class="mb-2">
                    <label class="text-secondary fw-bold">CIDR</label>
                    <input class="form-control" formControlName="cidr" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Network Domain</label>
                    <input class="form-control" formControlName="networkdomain" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Zone</label>
                    <input class="form-control" formControlName="zonename" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Domain</label>
                    <input class="form-control" formControlName="domain" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Account</label>
                    <input class="form-control" formControlName="account" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Type</label>
                    <input class="form-control" formControlName="type" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">ID</label>
                    <input class="form-control" formControlName="id" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Network Offering</label>
                    <input class="form-control" formControlName="networkofferingname" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Restart Required</label>
                    <input class="form-control" formControlName="restartrequired" />
                </div>
                <div class="mb-2">
                    <label class="text-secondary fw-bold">Redundant VPC</label>
                    <input class="form-control" formControlName="redundantrouter" />
                </div>
            </div>
        </div>
    </div>
}
