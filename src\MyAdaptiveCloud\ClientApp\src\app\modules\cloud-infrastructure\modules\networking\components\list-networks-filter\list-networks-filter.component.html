@if (filters()?.form) {
    <form [formGroup]="filters().form">
        <div id="vpc-group">
            <h6 id="vpc-group-title" class="card-title mb-3 fw-normal h5">{{ filters().filterGroupOptions[0].groupName }}</h6>
            <div class="card-body">
                <div class="form-check mb-2">
                    <label class="form-check-label clickable">{{
                        filters().filterGroupOptions[0].options[0].label }}
                        <input [checked]="form.get(filters().filterGroupOptions[0].key)?.value" [attr.data-testid]="filters().filterGroupOptions[0].key" type="checkbox" class="form-check-input clickable"
                            [formControlName]="filters().filterGroupOptions[0].key" />
                    </label>
                </div>
                <div class="form-check">
                    <label class="form-check-label clickable">{{
                        filters().filterGroupOptions[1].options[0].label }}
                        <input  [checked]="form.get(filters().filterGroupOptions[1].key)?.value" [attr.data-testid]="filters().filterGroupOptions[1].key" type="checkbox" class="form-check-input clickable"
                            [formControlName]="filters().filterGroupOptions[1].key" />
                    </label>
                </div>
            </div>
            <hr>
        </div>
        <div id="type-group">
            <h6 class="card-title mb-3 fw-normal h5">{{ filters().filterGroupOptions[2].groupName }}</h6>
            <div class="card-body">
                <div class="form-check mb-2">
                    <label class="form-check-label clickable">{{
                        filters().filterGroupOptions[2].options[0].label }}
                        <input  [checked]="form.get(filters().filterGroupOptions[2].key)?.value" [attr.data-testid]="filters().filterGroupOptions[2].key" type="checkbox" class="form-check-input clickable"
                            [formControlName]="filters().filterGroupOptions[2].key" /></label>
                </div>
                <div class="form-check mb-2">
                    <label class="form-check-label clickable">{{
                        filters().filterGroupOptions[3].options[0].label }}
                        <input [checked]="form.get(filters().filterGroupOptions[3].key)?.value" [attr.data-testid]="filters().filterGroupOptions[3].key" type="checkbox" class="form-check-input clickable"
                            [formControlName]="filters().filterGroupOptions[3].key" />
                    </label>
                </div>
                <div class="form-check">
                    <label class="form-check-label clickable">{{
                        filters().filterGroupOptions[4].options[0].label }}
                        <input [checked]="form.get(filters().filterGroupOptions[4].key)?.value" [attr.data-testid]="filters().filterGroupOptions[3].key" type="checkbox" class="form-check-input clickable"
                            [formControlName]="filters().filterGroupOptions[4].key" />
                    </label>
                </div>
            </div>
            <hr>
        </div>
      <div id="zone-group">
        @if (filters().filterGroupOptions[5]) {
            <h6 class="card-title mb-3 fw-normal h5">{{filters().filterGroupOptions[5].groupName}}</h6>
            <div class="card-body">
                @for (zone of zones(); track $index) {
                    @if (filters().filterGroupOptions[$index + 5]) {
                        <div class="form-check mb-2">
                            <label class="form-check-label clickable" for="{{zone.id}}">
                                {{filters().filterGroupOptions[$index + 5].options[0].label}}
                                <input type="checkbox"
                                    [checked]="true"
                                    class="form-check-input clickable"
                                    id="{{zone.id}}"
                                    [formControlName]="filters().filterGroupOptions[$index + 5].key" />
                            </label>
                        </div>
                    }
                }
            </div>
            <hr>
        }
      </div>
    </form>
}
