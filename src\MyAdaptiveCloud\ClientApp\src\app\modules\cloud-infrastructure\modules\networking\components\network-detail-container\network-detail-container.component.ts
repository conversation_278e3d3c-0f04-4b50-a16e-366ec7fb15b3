import { DatePipe } from '@angular/common';
import { NetworkingDetailService } from './../../services/networking-detail.service';
import { Component, ChangeDetectionStrategy, inject } from '@angular/core';
import { RouterLink, RouterOutlet, RouterLinkActive } from '@angular/router';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';

@Component({
    selector: 'app-network-detail-container',
    imports: [RouterOutlet, RouterLink, RouterLinkActive, DatePipe],
    templateUrl: './network-detail-container.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class NetworkDetailContainerComponent {
    protected readonly networkingDetailService = inject(NetworkingDetailService);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);
}
