import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { UserContextService } from '@app/shared/services/user-context.service';

export const virtualMachinesDetailsGuard: CanActivateFn = () => {
    const service = inject(UserContextService);
    return service.getFeatureFlagState(FeatureFlag.FeatureFlagVirtualMachineDetails);
};
