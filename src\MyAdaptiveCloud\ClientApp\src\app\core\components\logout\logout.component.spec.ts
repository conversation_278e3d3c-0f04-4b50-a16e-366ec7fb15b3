import { TestBed } from '@angular/core/testing';
import { AuthService } from '@app/shared/services/auth.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { LogoutComponent } from './logout.component';

describe('LogoutComponent', () => {
    let component: LogoutComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(AuthService),
                provideMock(UserContextService)
            ],
            imports: [
                LogoutComponent
            ]
        });

        component = TestBed.createComponent(LogoutComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
