<div class="content-heading">
    Storage
</div>
<div class="d-flex">
    <div class="w-25">
        <div class="card card-default tree-container overflow-auto ms-0 rounded-0 me-4 pt-2">
            <app-domain-account-tree [domain]="store.mainDomain()" />
        </div>
    </div>
    <div class="w-75">
        <div class="mx-4">
            <ul class="manage-nav nav nav-underline">
                <li class="nav-item">
                    <a class="nav-link px-5" aria-current="page" routerLinkActive="active"
                        [routerLink]="[routes.VOLUMES]">Volumes</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link px-5" [routerLink]="[routes.SNAPSHOTS]">Snapshots</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link px-5" [routerLink]="[routes.VM_SNAPSHOTS]">VM Snapshots</a>
                </li>
            </ul>
            @if(store.zones()?.length) {
                <router-outlet />
            }
        </div>
    </div>
</div>
