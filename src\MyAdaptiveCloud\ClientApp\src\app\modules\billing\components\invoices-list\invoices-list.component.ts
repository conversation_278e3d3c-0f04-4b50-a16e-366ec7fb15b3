import { <PERSON><PERSON><PERSON>cy<PERSON>ip<PERSON>, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { Invoice } from '../../models/invoice.model';
import { BillingService } from '../../services/billing.service';

@Component({
    selector: 'app-invoices-list',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent, CurrencyPipe, DatePipe],
    templateUrl: './invoices-list.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class InvoicesListComponent extends BaseListClientComponent<Invoice> implements OnInit {
    private readonly billingService = inject(BillingService);
    private readonly userContextService = inject(UserContextService);

    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');
    private readonly dateCell = viewChild<TemplateRef<never>>('dateCellTemplate');
    private readonly currencyCellTemplate = viewChild<TemplateRef<never>>('currencyCellTemplate');

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Invoice',
                prop: 'invoiceNumber',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 50,
            },
            {
                name: 'Date',
                prop: 'invoiceDate',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 75,
                cellTemplate: this.dateCell()
            },
            {
                name: 'Type',
                prop: 'invoiceType',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 100,
            },
            {
                name: 'Description',
                prop: 'description',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 350
            },
            {
                name: 'Due Date',
                prop: 'dueDate',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 75,
                cellTemplate: this.dateCell()
            },
            {
                name: 'Amount',
                prop: 'invoiceAmount',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.currencyCellTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 75,
            },
            {
                name: 'Balance',
                prop: 'invoiceBalance',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.currencyCellTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 75,
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                width: 100,
                sortable: false,
                resizeable: false,
                canAutoResize: false
            },
        ];

        super.initialize(this.billingService.getInvoicesByOrganization.bind(this.billingService, this.userContextService.currentUser.organizationId), columns);
    }

    public downloadInvoice(row: Invoice): void {
        this.billingService.downloadInvoice(row.invoiceId);
    }

}
