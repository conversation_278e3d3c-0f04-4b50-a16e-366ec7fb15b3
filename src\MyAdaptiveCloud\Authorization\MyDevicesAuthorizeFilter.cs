﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class MyDevicesAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        private readonly string _name;
        public readonly DeviceControlRoleEnum[] _deviceControlRoles;

        public MyDevicesAuthorizeFilter(
            IEntityAuthorizationService entityAuthorizationService,
            IIdentityService identityService,
            DeviceControlRoleEnum[] deviceControlRoles, string name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _identityService = identityService;
            _name = name;
            _deviceControlRoles = deviceControlRoles;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);

            if (userId == 0)
                context.Result = new UnauthorizedResult();
            else
            {
                string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
                int.TryParse(val, out int agentId);

                var isDeviceControlledByUser = await _entityAuthorizationService.IsDeviceControlledByUserWithRoles(agentId, userId, _deviceControlRoles);
                if (!isDeviceControlledByUser)
                    context.Result = new BadRequestResult();
            }
        }
    }

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class MyDevicesAuthorizeFilterAttribute : TypeFilterAttribute
    {
        public MyDevicesAuthorizeFilterAttribute(params DeviceControlRoleEnum[] deviceControlRoles) : base(typeof(MyDevicesAuthorizeFilter))
        {
            Arguments = new object[] { deviceControlRoles, "agentId" };
        }
    }
}