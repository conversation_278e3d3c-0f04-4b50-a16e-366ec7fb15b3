﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services.PreSigned;
using Microsoft.AspNetCore.Http.Extensions;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class PreSignedAuthorize(
        IEntityAuthorizationService entityAuthorizationService,
        IIdentityService identityService,
        Perms[] perms,
        int distance,
        string name,
        IOrganizationPreSignedService preSignedService) : BaseAsyncAuthorizationFilter(perms, distance, name)
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService = entityAuthorizationService;
        private readonly IIdentityService _identityService = identityService;
        private readonly IOrganizationPreSignedService _organizationPreSignedService = preSignedService;

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var request = context.HttpContext.Request;

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int fileAdministrationId);

            var adaptiveCloudDriveFileOrganizationId = await _entityAuthorizationService.GetAdaptiveCloudDriveFileOrganizationId(fileAdministrationId);

            if (!adaptiveCloudDriveFileOrganizationId.HasValue)
            {
                context.Result = new BadRequestResult();
                return;
            }

            var uriPath = new Uri(request.GetDisplayUrl()).GetLeftPart(UriPartial.Path);
            var signature = await _organizationPreSignedService.GetSignature(HttpMethod.Get.Method, uriPath, [fileAdministrationId.ToString(), request.Query["d"]],
                (int)adaptiveCloudDriveFileOrganizationId);

            if (request.Query["s"] != signature)
            {
                context.Result = new BadRequestResult();
                return;
            }

            var canParseDate = DateTime.TryParse(request.Query["d"], out var date);

            if (date < DateTime.UtcNow)
            {
                context.Result = new BadRequestResult();
                return;
            }
        }
    }


    public class PreSignedAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public PreSignedAuthorizeAttribute(params Perms[] perms) : base(typeof(PreSignedAuthorize), perms)
        {
            Name = "fileAdministrationId";
        }
    }
}