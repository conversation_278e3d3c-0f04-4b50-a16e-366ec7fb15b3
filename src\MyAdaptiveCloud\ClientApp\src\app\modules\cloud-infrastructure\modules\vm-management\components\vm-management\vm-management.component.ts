import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { DomainAccountTreeComponent } from '@app/modules/cloud-infrastructure/components/domain-account-tree/domain-account-tree.component';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { BreadcrumbsComponent } from '@app/shared/components/breadcrumbs/breadcrumbs.component';
import { InformationBannerComponent } from '@app/shared/components/information-banner/information-banner.component';
import { filter } from 'rxjs';
import { VM_ROUTE_SEGMENTS } from '../../constants/vm-management-route-segments';
import { VmDetailsStateService } from '../../services/vm-details.state.service';

@Component({
    selector: 'app-vm-management',
    imports: [RouterOutlet, InformationBannerComponent, DomainAccountTreeComponent, BreadcrumbsComponent],
    templateUrl: './vm-management.component.html',
    styleUrl: './vm-management.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmManagementComponent implements OnInit {

    private readonly vmDetailsStateService = inject(VmDetailsStateService);
    protected readonly store = inject(ZoneDomainAccountStore);
    private readonly domainAccountSelected$ = toObservable(this.store.domainOrAccountSelected);
    private readonly router = inject(Router);
    private readonly activatedRoute = inject(ActivatedRoute);
    private readonly destroyRef = inject(DestroyRef);

    ngOnInit() {
        this.domainAccountSelected$
            .pipe(
                filter(domainAccount => !!domainAccount && !!this.vmDetailsStateService.selectedVM()),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => this.router.navigate([VM_ROUTE_SEGMENTS.LIST], { relativeTo: this.activatedRoute.parent }));
    }
}

