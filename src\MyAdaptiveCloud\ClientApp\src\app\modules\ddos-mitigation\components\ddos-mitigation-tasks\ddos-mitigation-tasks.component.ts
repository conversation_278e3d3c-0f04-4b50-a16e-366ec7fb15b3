import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { TaskDetail } from '@app/shared/models/ddos-mitigation/task-detail.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { TaskStatusEnum } from '../../models/task-status.enum';
import { DDosMitigationValuePipe } from '../../pipes/ddos-mitigation-value.pipe';
import { DDoSMitigationTasksService } from '../../services/ddos-mitigation-tasks.service';
import { DdosMitigationMessagesComponent } from '../ddos-mitigation-messages/ddos-mitigation-messages.component';

@Component({
    selector: 'app-ddos-mitigation-tasks',
    imports: [NgxDatatableModule, AutoSearchBoxComponent, DatePipe, TableActionComponent],
    templateUrl: './ddos-mitigation-tasks.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})

export class DdosMitigationTasksComponent extends BaseListClientComponent<TaskDetail> implements OnInit {

    private readonly ddosMitigationTasksService = inject(DDoSMitigationTasksService);
    private readonly userContextService = inject(UserContextService);
    private readonly modalService = inject(ModalService);

    private readonly dateCellTemplate = viewChild.required<TemplateRef<never>>('dateCellTemplate');
    private readonly headerTemplate = viewChild.required<TemplateRef<never>>('headerTemplate');
    private readonly statusTemplate = viewChild.required<TemplateRef<never>>('statusTemplate');
    private readonly messageTemplate = viewChild.required<TemplateRef<never>>('messageTemplate');
    private readonly actionsTemplate = viewChild.required<TemplateRef<never>>('actionsTemplate');

    protected readonly taskStatus = TaskStatusEnum;

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Status',
                prop: 'status',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.statusTemplate(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 100
            },
            {
                name: 'Task',
                prop: 'name',
                headerTemplate: this.headerTemplate(),
                width: 300,
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Start Time',
                prop: 'startTime',
                cellTemplate: this.dateCellTemplate(),
                width: 200,
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: false,
                canAutoResize: false
            },
            {
                name: 'End Time',
                prop: 'endTime',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.dateCellTemplate(),
                width: 200,
                sortable: true,
                resizeable: false,
                canAutoResize: false
            },
            {
                name: 'Last Message',
                cellTemplate: this.messageTemplate(),
                width: 300,
                sortable: false,
                resizeable: false,
                canAutoResize: true
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false
            }
        ];

        super.initialize(this.ddosMitigationTasksService.getList.bind(this.ddosMitigationTasksService, this.userContextService.currentUser.organizationId), columns);
        this.table().sorts = [{ prop: 'startTime', dir: 'desc' }];
    }

    protected viewMessages(item: TaskDetail): void {
        const modalRef = this.modalService.openModalComponent(DdosMitigationMessagesComponent);
        (modalRef.componentInstance as DdosMitigationMessagesComponent).messages.set(item.messages);
    }

}
