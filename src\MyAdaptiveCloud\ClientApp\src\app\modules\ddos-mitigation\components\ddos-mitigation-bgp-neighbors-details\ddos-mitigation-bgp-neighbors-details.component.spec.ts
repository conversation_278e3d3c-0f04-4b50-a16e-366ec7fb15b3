import { ComponentFixture, TestBed } from '@angular/core/testing';

import { By } from '@angular/platform-browser';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Neighbor } from '../../models/neighbor.model';
import { DdosMitigationBgpNeighborsDetailsComponent } from './ddos-mitigation-bgp-neighbors-details.component';

const mockBGPStatus1: Neighbor = {
    ip: '***********',
    localAs: 64512,
    remoteAs: 64513,
    state: 'Established',
    updown: '00:01:23',
    prefixesReceived: 100,
    prefixesSent: 50,
    prefixesSentSimulated: 5,
    holdTime: 180,
    retryInterval: 30,
    scrubPhase1Community: 'community-1',
    scrubPhase2Community: 'community-2',
    scrubPostCommunity: 'community-post',
    blackholeCommunity: 'blackhole-community',
};

describe('DdosMitigationBgpNeighborsDetailsComponent', () => {
    let component: DdosMitigationBgpNeighborsDetailsComponent;
    let fixture: ComponentFixture<DdosMitigationBgpNeighborsDetailsComponent>;
    let activeModalSpy: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        activeModalSpy = jasmine.createSpyObj('NgbActiveModal', ['dismiss']);

        TestBed.configureTestingModule({
            imports: [DdosMitigationBgpNeighborsDetailsComponent],
            providers: [{ provide: NgbActiveModal, useValue: activeModalSpy }],

        })
            .compileComponents();

        fixture = TestBed.createComponent(DdosMitigationBgpNeighborsDetailsComponent);
        component = fixture.componentInstance;
        component.neighborDetails.set(mockBGPStatus1);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should render modal title correctly', () => {
        const title = fixture.debugElement.query(By.css('.modal-title')).nativeElement;
        expect(title.textContent).toBe('Details');
    });

    it('should render neighbor IP correctly', () => {
        const ipRow = fixture.debugElement.query(By.css('.ip-row-value')).nativeElement;
        expect(ipRow.textContent).toBe('***********');
    });

    it('should render Local AS and Remote AS', () => {
        const cols = fixture.debugElement.queryAll(By.css('.details .col'));

        expect(cols[0].nativeElement.textContent).toContain('Local AS');
        expect(cols[0].nativeElement.textContent).toContain('64512');

        expect(cols[2].nativeElement.textContent).toContain('Remote AS');
        expect(cols[2].nativeElement.textContent).toContain('64513');
    });

    it('should display Prefixes Sent and Simulated correctly', () => {
        const cols = fixture.debugElement.queryAll(By.css('.details .col'));
        const prefixesSentCol = cols.find(col => col.nativeElement.textContent.includes('Prefixes Sent'));

        expect(prefixesSentCol).toBeTruthy();
        expect(prefixesSentCol.nativeElement.textContent).toContain('50 (5 Simulated)');
    });

    it('should call activeModal.dismiss when close button is clicked', () => {
        const closeButton = fixture.debugElement.query(By.css('.btn-close'));
        closeButton.nativeElement.click();

        expect(activeModalSpy.dismiss).toHaveBeenCalled();
    });

    it('should render empty body if neighborDetails is null', () => {
        component.neighborDetails.set(null);
        fixture.detectChanges();

        const body = fixture.debugElement.query(By.css('.modal-body'));
        expect(body.nativeElement.textContent.trim()).toBe('');
    });
});
