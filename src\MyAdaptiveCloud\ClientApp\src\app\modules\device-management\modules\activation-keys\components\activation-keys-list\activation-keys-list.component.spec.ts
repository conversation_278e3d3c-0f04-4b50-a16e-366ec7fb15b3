import { Clipboard } from '@angular/cdk/clipboard';
import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ActivationKey } from '@app/modules/device-management/modules/activation-keys/models/activation-key.model';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { NotificationService } from '@app/shared/services/notification.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { ActivationKeysService } from '../../services/activation-keys.service';
import { ActivationKeysListComponent } from './activation-keys-list.component';

describe('ActivationKeysListComponent', () => {
    let component: ActivationKeysListComponent;
    let fixture: ComponentFixture<ActivationKeysListComponent>;
    let mockActivationKeysService: jasmine.SpyObj<ActivationKeysService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockClipboard: jasmine.SpyObj<Clipboard>;
    let mockNotificationsService: jasmine.SpyObj<NotificationService>;

    const data: ActivationKey[] = [
        {
            activationKeyId: 1,
            key: 'something',
            creationDate: new Date(2024, 2, 2),
            deactivationDate: new Date(),
            isActive: true,
            canView: UserActionState.Allowed,
            canCreate: UserActionState.Disabled,
            canEdit: UserActionState.Allowed,
            canDelete: UserActionState.Disabled,
            organizationId: 4,
        },
        {
            activationKeyId: 2,
            key: 'pizza',
            creationDate: new Date(2024, 2, 10),
            deactivationDate: new Date(),
            isActive: true,
            canView: UserActionState.Allowed,
            canCreate: UserActionState.Disabled,
            canEdit: UserActionState.Allowed,
            canDelete: UserActionState.Disabled,
            organizationId: 4,
        },
        {
            activationKeyId: 3,
            key: 'beef',
            creationDate: new Date(2024, 3, 1),
            deactivationDate: new Date(),
            isActive: true,
            canView: UserActionState.Allowed,
            canCreate: UserActionState.Disabled,
            canEdit: UserActionState.Allowed,
            canDelete: UserActionState.Disabled,
            organizationId: 4,
        },
        {
            activationKeyId: 4,
            key: 'hamburguer',
            creationDate: new Date(2023, 10, 1),
            deactivationDate: new Date(),
            isActive: false,
            canView: UserActionState.Allowed,
            canCreate: UserActionState.Allowed,
            canEdit: UserActionState.Allowed,
            canDelete: UserActionState.Allowed,
            organizationId: 4,
        },
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                ActivationKeysListComponent
            ],
            providers: [
                provideMock(ActivationKeysService),
                provideMock(UserContextService),
                provideMock(PermissionService),
                provideMock(Clipboard),
                provideMock(NotificationService)
            ]
        });

        mockNotificationsService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
        mockClipboard = TestBed.inject(Clipboard) as jasmine.SpyObj<Clipboard>;
        mockClipboard.copy.and.returnValue(true);
        mockActivationKeysService = TestBed.inject(ActivationKeysService) as jasmine.SpyObj<ActivationKeysService>;
        mockActivationKeysService.getActivationKeysForOrg.and.returnValue(of({ data, message: 'success', totalCount: data.length }));
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1
        } as UserContext;
        fixture = TestBed.createComponent(ActivationKeysListComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(component).toBeTruthy();
        });
    });

    describe('OnInit', () => {

        it('should call notificationService', () => {
            expect(mockActivationKeysService.getActivationKeysForOrg).toHaveBeenCalled();
        });

        it('should have the right amount of data', () => {
            expect(component.table().count).toEqual(4);
            expect(component.table().rows).toEqual(data);
        });
    });

    describe('Component Interaction', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
            fixture.detectChanges();
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });

        it('should display the correct data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRowWrapper = rows[0].querySelectorAll<HTMLElement>('datatable-body-cell span');
            const secondRowWrapper = rows[1].querySelectorAll<HTMLElement>('datatable-body-cell span');
            const thirdRowWrapper = rows[2].querySelectorAll<HTMLElement>('datatable-body-cell span');
            const fourthRowWrapper = rows[3].querySelectorAll<HTMLElement>('datatable-body-cell span');
            expect(firstRowWrapper[1].innerText).toEqual(data[2].key);
            expect(secondRowWrapper[1].innerText).toEqual(data[1].key);
            expect(thirdRowWrapper[1].innerText).toEqual(data[0].key);
            expect(fourthRowWrapper[1].innerText).toEqual(data[3].key);
        });

        it('should copy the activation key', () => {
            const firstRow = dataTable.querySelectorAll('datatable-row-wrapper')[0];
            const rowWrapper = firstRow.querySelectorAll<HTMLElement>('.datatable-body-cell-label span i');
            rowWrapper[0].click();
            expect(mockClipboard.copy).toHaveBeenCalledWith('beef');
        });

        it('should not show message if the activation key copy failed', () => {
            mockClipboard.copy.and.returnValue(false);
            const firstRow = dataTable.querySelectorAll('datatable-row-wrapper')[0];
            const rowWrapper = firstRow.querySelectorAll<HTMLElement>('.datatable-body-cell-label span i');
            rowWrapper[0].click();
            expect(mockNotificationsService.notify).not.toHaveBeenCalled();
        });
    });
});
