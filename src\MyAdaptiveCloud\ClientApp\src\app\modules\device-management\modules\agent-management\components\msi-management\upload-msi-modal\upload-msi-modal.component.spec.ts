import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { MsiManagementService } from '../../../services/msi-management.service';
import { UploadMsiModalComponent } from './upload-msi-modal.component';

describe('UploadMsiModalComponent', () => {
    let component: UploadMsiModalComponent;
    let fixture: ComponentFixture<UploadMsiModalComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [UploadMsiModalComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(MsiManagementService)
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(UploadMsiModalComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
