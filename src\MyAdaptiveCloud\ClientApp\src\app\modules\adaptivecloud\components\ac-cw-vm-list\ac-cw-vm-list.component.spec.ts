import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';
import { ModalService } from '@app/shared/services/modal.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';
import { AcCwVmListComponent } from './ac-cw-vm-list.component';

describe('AcCwVmListComponent', () => {
    let component: AcCwVmListComponent;
    let mockAcToCwMappingService: jasmine.SpyObj<AcToCwMappingService>;
    let fixture: ComponentFixture<AcCwVmListComponent>;
    const mappingData = [
        {
            organizationId: 1,
            canEdit: UserActionState.Allowed,
            canDelete: UserActionState.Allowed,
            canCreate: UserActionState.Allowed,
            canView: UserActionState.Allowed,
            id: 1,
            acId: '1',
            acName: 'test',
            acType: 'one',
            priority: 1,
            quantityValue: '1',
            isFormula: true,
            productMap: {
                id: 1,
                usageType: '',
                cwProductId: 3,
                cwProductName: '',
                label: '',
                description: '',
                prorate: true,
                bandwidthGb: 10,
                tiered: true,
                valueFn: ''
            },
            cwProductName: 'sd',
            account: 'test1',
            domain: 'test2',
            startDate: new Date(),
            endDate: new Date(),
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AcCwVmListComponent
            ],
            providers: [
                provideMock(AcToCwMappingService),
                provideMock(ModalService),
                provideMock(PermissionService)
            ]
        });

        mockAcToCwMappingService = TestBed.inject(AcToCwMappingService) as jasmine.SpyObj<AcToCwMappingService>;
        mockAcToCwMappingService.getVmMappingPeriods.and.returnValue(of({
            data: [new Date(), new Date(), new Date()],
            message: 'success'
        }));
        mockAcToCwMappingService.getVmMappings.and.returnValue(of({
            data: mappingData,
            message: 'success',
            totalCount: mappingData.length
        }));

        fixture = TestBed.createComponent(AcCwVmListComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(component).toBeTruthy();
        });
    });
});
