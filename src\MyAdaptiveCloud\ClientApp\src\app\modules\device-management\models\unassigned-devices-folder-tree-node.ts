import { computed, signal } from '@angular/core';
import { UNASSIGNED_DEVICES_FOLDER_ID, UNASSIGNED_DEVICES_FOLDER_ID_PREFIX } from '@app/shared/constants/shared-folder-devices-constants';
import { Device } from './device';
import { DeviceFolderTreeNode } from './device-folder-tree-node';
import { ItemsSelection } from './items-selection';

export class UnassignedDevicesFolderTreeNode extends DeviceFolderTreeNode {

    override readonly name = signal<string>('Unmonitored New Devices');

    constructor(private organizationId: number, devices?: Device[]) {
        super(null, 1, null, []);
        this.folderId = UNASSIGNED_DEVICES_FOLDER_ID;
        if (devices) {
            this.setDevices(devices);

            if (devices?.length) {
                this._deviceCountOnlySelf.set(devices.length);
                this._deviceCountSelfAndChildren.set(devices.length);
            }
        }
    }

    // Since the ids of folders and organizations are not unique between them, we add a prefix to generate an unique id.
    override getUniqueStringId(): string {
        return `${UNASSIGNED_DEVICES_FOLDER_ID_PREFIX}_O${this.organizationId.toString()}_${this.getId().toString()}`;
    }

    override isOrganizationFolder(): boolean {
        return false;
    }

    override isUnassignedDevicesFolder(): boolean {
        return true;
    }

    override readonly shouldLoadDevices = computed(() => false);
    override readonly shouldLoadSubfolders = computed(() => false);

    override canReceiveItems(movingItems: ItemsSelection): boolean {
        // folders cannot be moved into unassigned folder
        return !movingItems.hasFolders() && super.canReceiveItems(movingItems);
    }
}
