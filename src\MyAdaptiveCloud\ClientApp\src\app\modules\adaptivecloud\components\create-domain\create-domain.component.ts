import { Component, OnInit, inject, viewChild } from '@angular/core';
import { CloudInfrastructureDomainComponent } from '@app/shared/components/cloud-infrastructure-domain/cloud-infrastructure-domain.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { RootOrganizationId } from '@app/shared/models/constants';
import { RegistrationCloudInfrastructureValidationAndMappingResponse } from '@app/shared/models/registration/registration-cloud-infrastructure-validation-and-mapping.response';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-create-domain',
    imports: [CloudInfrastructureDomainComponent],
    templateUrl: './create-domain.component.html'
})
export class CreateDomainComponent implements OnInit {
    private readonly userContextService = inject(UserContextService);
    private readonly notificationService = inject(NotificationService);

    protected readonly cloudInfrastructureDomainComponent = viewChild(CloudInfrastructureDomainComponent);

    get isValid(): boolean {
        const cloudInfrastructureDomainComponent = this.cloudInfrastructureDomainComponent();
        return cloudInfrastructureDomainComponent && cloudInfrastructureDomainComponent.isValid;
    }

    public organizationId: number;
    public readonly rootOrganizationId = RootOrganizationId;

    ngOnInit(): void {
        this.organizationId = this.userContextService.currentUser.organizationId;
    }

    public onDomainCreated(result: ApiDataResult<RegistrationCloudInfrastructureValidationAndMappingResponse>): void {
        this.notificationService.notify(result.message);
    }

    public submit(): void {
        this.cloudInfrastructureDomainComponent().submit();
    }

}
