<div class="content-sub-heading d-flex justify-content-between">
    <div class="search-bar">
        <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
    </div>
    @if (vmManagementPermissionService.canSnapshotVirtualMachine()) {
        <div class="action-buttons">
            <div class="d-inline">
                <button class="btn btn-primary"
                    [disabled]="vmDetailsStateService.selectedVM().state !== VmStateEnum.Running || (vmDetailsStateService.isLoading$ | async)"
                    data-testid="create-snapshot-button" (click)="openSnapshotVmModal()">Create Snapshot</button>
            </div>
        </div>
    }
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class='table bootstrap no-detail-row'>
        </ngx-datatable>
    </div>
</div>

<ng-template #stateTemplate let-row="row">
    @if (toItem(row); as row) {
        @if (row.state === SnapshotState.Allocated || row.state === SnapshotState.Ready) {
            <div>
                <i class="fa-solid fa-circle text-success me-1 status-icon"></i>
                {{ row.state }}
            </div>
        }
        @else if (row.state === SnapshotState.Error) {
            <div>
                <i class="fa-solid fa-circle text-danger me-1 status-icon"></i>
                {{ row.state }}
            </div>
        }
        @else if (row.state === SnapshotState.Expunging) {
            <div>
                <i class="fa-solid fa-circle text-secondary me-1 status-icon"></i>
                {{ row.state }}
            </div>
        }
        @else if (row.state === SnapshotState.Creating || row.state === SnapshotState.Reverting) {
            <div class="spinner-border spinner-border-sm text-secondary" role="status">
                <span class="visually-hidden">{{ row.state }}</span>
            </div>
            {{ row.state }}
        }
    }
</ng-template>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #isCurrentTemplate let-row="row">
    @if (toItem(row); as row) {
        <span>
            {{ row.current ? 'Yes': 'No' }}
        </span>
    }
</ng-template>

<ng-template #dateCellTemplate let-value="value">
    <span>
        {{ (value) ? (value | date: 'yyyy-MM-dd HH:mm:ss') : '-' }}
    </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        @if (vmManagementPermissionService.canCreateSnapshotFromVirtualMachineSnapshot()) {
            <app-table-action [icon]="'fa-solid fa-camera'"
                (clickHandler)="openCreateSnapshotFromSnapshotModal(row.id, row.name, row.created)"
                [enabled]="(vmDetailsStateService.isLoading$ | async) === false" [title]="'Use to create volume snapshot'" />
        }
        @if (vmManagementPermissionService.canRevertToVirtualMachineSnapshot()) {
            <app-table-action [icon]="'fa-solid fa-arrow-rotate-left'"
                (clickHandler)="openRevertToVirtualMachineSnapshotModal(row.id)"
                [enabled]="(vmDetailsStateService.isLoading$ | async) === false && (vmDetailsStateService.selectedVM().state === VmStateEnum.Stopped || vmDetailsStateService.selectedVM().state === VmStateEnum.Running)"
                [title]="'Revert to snapshot'" />
            }
        @if (vmManagementPermissionService.canDeleteVirtualMachineSnapshot()) {
            <app-table-action [icon]="'far fa-trash-can'" (clickHandler)="openDeleteSnapshotModal(row.id)"
                [enabled]="(vmDetailsStateService.isLoading$ | async) === false && ( row.state === SnapshotState.Ready || row.state === SnapshotState.Expunging || row.state === SnapshotState.Error)"
                [title]="'Delete snapshot'" />
            }
    }
</ng-template>
