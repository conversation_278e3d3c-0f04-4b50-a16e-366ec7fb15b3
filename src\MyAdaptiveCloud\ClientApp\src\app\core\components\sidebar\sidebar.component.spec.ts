import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { MenuService } from '@app/shared/services/menu.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { SidebarComponent } from './sidebar.component';

describe('SidebarComponent', () => {

    let component: SidebarComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(Router),
                provideMock(MenuService)
            ],
            imports: [
                SidebarComponent
            ]
        });

        component = TestBed.createComponent(SidebarComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
