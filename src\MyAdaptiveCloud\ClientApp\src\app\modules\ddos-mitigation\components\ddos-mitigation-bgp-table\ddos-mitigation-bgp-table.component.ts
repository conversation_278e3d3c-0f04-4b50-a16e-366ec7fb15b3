import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { Bgp } from '../../models/bgp.model';
import { TaskStatusEnum } from '../../models/task-status.enum';
import { CommunitiesPipe } from '../../pipes/communities.pipe';
import { DDosMitigationValuePipe } from '../../pipes/ddos-mitigation-value.pipe';
import { DDoSMitigationBgpService } from '../../services/ddos-mitigation-bgp.service';
import { DdosMitigationBgpTableDetailsComponent } from '../ddos-mitigation-bgp-table-details/ddos-mitigation-bgp-table-details.component';

@Component({
    selector: 'app-ddos-mitigation-bgp-table',
    imports: [NgxDatatableModule, AutoSearchBoxComponent, TableActionComponent],
    templateUrl: './ddos-mitigation-bgp-table.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})

export class DdosMitigationBgpTableComponent extends BaseListClientComponent<Bgp> implements OnInit {

    private readonly ddosMitigationBgpService = inject(DDoSMitigationBgpService);
    private readonly modalService = inject(ModalService);

    private readonly headerTemplate = viewChild.required<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild.required<TemplateRef<never>>('actionsTemplate');

    protected readonly taskStatus = TaskStatusEnum;

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Prefix',
                prop: 'prefix',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Next Hop',
                prop: 'nextHop',
                headerTemplate: this.headerTemplate(),
                width: 200,
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Neighbor IP',
                prop: 'neighborIp',
                width: 200,
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Communities',
                prop: 'communities',
                headerTemplate: this.headerTemplate(),
                width: 200,
                sortable: true,
                resizeable: true,
                pipe: new CommunitiesPipe(),
                canAutoResize: true
            },
            {
                name: 'Local Preference',
                prop: 'localPref',
                headerTemplate: this.headerTemplate(),
                width: 200,
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'AS path',
                prop: 'asPath',
                headerTemplate: this.headerTemplate(),
                width: 200,
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false
            }
        ];

        super.initialize(this.ddosMitigationBgpService.getList.bind(this.ddosMitigationBgpService), columns);
    }

    protected viewDetails(bgp: Bgp): void {
        const modal = this.modalService.openModalComponent(DdosMitigationBgpTableDetailsComponent);
        (modal.componentInstance as DdosMitigationBgpTableDetailsComponent).bgpDetails.set(bgp);
    }

}
