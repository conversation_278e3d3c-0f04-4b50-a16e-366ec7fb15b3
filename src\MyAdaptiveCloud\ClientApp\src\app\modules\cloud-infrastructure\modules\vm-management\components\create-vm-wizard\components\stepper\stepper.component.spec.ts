import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { VmAffinityGroupsService } from '../../../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../../../services/vm-management.service';
import { VmMediaService } from '../../../../services/vm-media-service';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { CreateVmWizardStepEnum } from '../../models/create-vm-wizard-steps.enum';
import { DiskOffering } from '../../models/disk-offering.model';
import { CreateVmComputeService } from '../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../services/create-vm-network.service';
import { CreateVmService } from '../../services/create-vm-service';
import { CreateVmStorageService } from '../../services/create-vm-storage.service';
import { CreateVmWizardStepperComponent } from './stepper.component';

describe('CreateVmWizardStepperComponent', () => {
    let component: CreateVmWizardStepperComponent;
    let fixture: ComponentFixture<CreateVmWizardStepperComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateVmWizardStepperComponent],
            providers: [
                CreateVMWizardStore,
                provideMock(CreateVmComputeService),
                provideMock(VmMediaService),
                provideMock(CreateVmNetworkService),
                provideMock(CreateNetworkService),
                provideMock(UserContextService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(CloudInfraPermissionService),
                provideMock(CreateVmStorageService),
                provideMock(CreateVmService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: getMockZoneDomainAccountStore(),
                },
            ]
        });

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of([]));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of([]));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'test-account',
                domainId: 'test-domain-id'
            }
        } as UserContext;

        fixture = TestBed.createComponent(CreateVmWizardStepperComponent);
        component = fixture.componentInstance;

        fixture.detectChanges();
    });

    describe('previous button', () => {
        it('should not be present if current step equals 0', () => {
            component.store.navigateToStep(CreateVmWizardStepEnum.Setup);
            const previousButton = fixture.nativeElement.querySelector('[title="Previous"]');
            expect(previousButton).toBeNull();
        });
    });

    describe('next button', () => {

        it('should be disabled if current step is Compute and there is no template selected in first step', () => {
            component.store.navigateToStep(CreateVmWizardStepEnum.Compute);
            fixture.detectChanges();

            const nextButton = fixture.nativeElement.querySelector('[title="Next"]');
            expect(nextButton.disabled).toBeTrue();
        });

        it('should be disabled if current step is Network and affinityGroups and ssh keys are not populated', () => {
            component.store.navigateToStep(CreateVmWizardStepEnum.AdvancedSettings);
            fixture.detectChanges();

            const nextButton = fixture.nativeElement.querySelector('[title="Next"]');
            expect(nextButton.disabled).toBeTrue();
        });

        it('should not be present if current step equals max step', () => {
            component.store.navigateToStep(component['store'].totalSteps() - 1);
            fixture.detectChanges();

            const nextButton = fixture.nativeElement.querySelector('[title="Next"]');
            expect(nextButton.style.visibility).toEqual('hidden');
        });

    });
});
