<div class="card card-default">
    <div class="card-body">
        <div class="card-body">
            <ngx-datatable #table class='table bootstrap' [selectionType]="selectionType.checkbox"
                [selectAllRowsOnPage]="false" [selected]="selectedAlerts()" />
        </div>
    </div>

    <ng-template #expandedTemplate let-expanded="expanded" let-row="row">
        @let alertVM = toItem(row);
        <div class="d-inline">
            @if (selectable()) {
                <input type="checkbox" class="form-check-input" [title]="alertVM.iconText"
                    [disabled]="alertVM.acknowledged || !alertVM.selectable" [checked]="alertVM.selected"
                    (click)="toggleAlertSelection(alertVM)" />
            }
        </div>
        <div [class]="alertVM.alertThresholdLevelClass" [title]="alertVM.iconText"></div>
        <span>{{alertVM.alertType}}</span>
    </ng-template>

    <ng-template #dateCellTemplate let-value="value">
        <span>{{ formatDate(value) }}</span>
    </ng-template>

    <ng-template #actionsTemplate let-row="row">
        @let alertVM = toItem(row);
        @if(alertVM?.notes){
            <app-table-action [icon]="'fa-solid fa-message'" [ngbTooltip]="alertVM.notes" />
        }
    </ng-template>
