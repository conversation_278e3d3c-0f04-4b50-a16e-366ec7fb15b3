import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NetworkDetailComponent } from './network-detail.component';
import { NetworkingDetailService } from '../../services/networking-detail.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { FormBuilder } from '@angular/forms';

describe('NetworkDetailComponent', () => {
    let component: NetworkDetailComponent;
    let fixture: ComponentFixture<NetworkDetailComponent>;
    let mockNetworkingService: NetworkingDetailService;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [NetworkDetailComponent],
            providers: [
                NetworkingDetailService,
                provideMock(FormBuilder)
            ],
        }).compileComponents();

        mockNetworkingService = TestBed.inject(NetworkingDetailService);

        mockNetworkingService.selectedNetwork.set({
            id: 'test-id-123',
            account: 'TestAccount',
            acltype: '',
            canusefordeploy: false,
            created: '2025-05-28T14:27:55+0000',
            displaynetwork: false,
            displaytext: 'Test Description',
            dns1: '',
            dns2: '',
            domain: 'TestDomain',
            domainid: '',
            hasannotations: false,
            ip6dns1: '',
            ip6dns2: '',
            ispersistent: false,
            issystem: false,
            name: 'Test Network',
            networkofferingavailability: '',
            networkofferingconservemode: false,
            networkofferingdisplaytext: '',
            networkofferingid: '',
            networkofferingname: 'Test Offering',
            physicalnetworkid: '',
            privatemtu: 0,
            publicmtu: 0,
            receivedbytes: 0,
            redundantrouter: true,
            related: '',
            restartrequired: true,
            sentbytes: 0,
            specifyipranges: false,
            state: 'Implemented',
            strechedl2subnet: false,
            supportsvmautoscaling: false,
            tags: [],
            traffictype: '',
            type: 'Isolated',
            zoneid: '',
            zonename: 'TestZone',
            cidr: '10.0.0.0/24',
            vpcname: ''
        });

        fixture = TestBed.createComponent(NetworkDetailComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
