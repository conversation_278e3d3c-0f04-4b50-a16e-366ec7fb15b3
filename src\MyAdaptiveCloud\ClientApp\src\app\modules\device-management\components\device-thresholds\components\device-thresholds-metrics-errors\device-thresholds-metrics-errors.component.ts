import { ChangeDetectionStrategy, Component, Input, input } from '@angular/core';
import { ValidationErrors } from '@angular/forms';

@Component({
    selector: 'app-device-thresholds-metrics-errors',
    imports: [],
    templateUrl: './device-thresholds-metrics-errors.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceThresholdsMetricsErrorsComponent {

    // TODO: Skipped for migration because:
    //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
    //  and migrating would break narrowing currently.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() errors: ValidationErrors;
    readonly propertyName = input('');
}
