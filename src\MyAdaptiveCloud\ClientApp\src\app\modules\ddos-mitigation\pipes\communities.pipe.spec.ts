import { CommunitiesPipe } from './communities.pipe';

describe('CommunitiesPipe', () => {
    let pipe: CommunitiesPipe;

    beforeEach(() => {
        pipe = new CommunitiesPipe();
    });

    it('should format array of arrays into "x:y" joined by comma', () => {
        const input = [[123, 66], [444, 55]];
        const result = pipe.transform(input);
        expect(result).toBe('123:66, 444:55');
    });

    it('should ignore invalid inner arrays', () => {
        const input = [[123, 66], [1], [], [999, 888]];
        const result = pipe.transform(input);
        expect(result).toBe('123:66, 999:888');
    });

    it('should return empty string for null input', () => {
        expect(pipe.transform(null)).toBe('-');
    });

    it('should return empty string for undefined input', () => {
        expect(pipe.transform(undefined)).toBe('-');
    });

});
