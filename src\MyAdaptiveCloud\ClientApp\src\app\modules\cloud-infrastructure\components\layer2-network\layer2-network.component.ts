import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Layer2NetworkForm } from '@app/modules/cloud-infrastructure/forms/layer2-network.form';
import { NetworkOfferingViewModel } from '@app/modules/cloud-infrastructure/models/network-offering.view-model';
import { SecondaryVlanType } from '@app/modules/cloud-infrastructure/models/secondary-vlan-type.enum';
import { NgSelectComponent } from '@ng-select/ng-select';
import { filter } from 'rxjs';

@Component({
    selector: 'app-add-network-layer2',
    imports: [ReactiveFormsModule, NgSelectComponent],
    templateUrl: './layer2-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class Layer2NetworkComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);

    readonly networkOfferings = input.required<NetworkOfferingViewModel[]>();

    protected readonly SecondaryVlanType = SecondaryVlanType;
    protected readonly secondaryVlanTypes = Object.keys(SecondaryVlanType);

    public form = this.formBuilder.group<Layer2NetworkForm>({
        description: this.formBuilder.control<string | null>(null, [Validators.required, Validators.maxLength(255)]),
        name: this.formBuilder.control<string | null>(null, [Validators.required, Validators.maxLength(255)]),
        networkOffering: this.formBuilder.control<NetworkOfferingViewModel | null>(null, Validators.required),
        bypassVLanId: this.formBuilder.control<boolean | null>(null),
        secondaryVLanType: this.formBuilder.control(null),
        secondaryVLanId: this.formBuilder.control(null),
        vlan: this.formBuilder.control(null)
    });

    ngOnInit(): void {
        this.form.controls.networkOffering.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(networkOffering => {
                if (networkOffering.specifyVLan) {
                    this.form.controls.secondaryVLanType.setValue(SecondaryVlanType.None);
                    this.form.controls.vlan.addValidators(Validators.required);
                } else {
                    this.form.controls.vlan.setValue(null);
                    this.form.controls.secondaryVLanId.setValue(null);
                    this.form.controls.secondaryVLanType.setValue(null);
                    this.form.controls.bypassVLanId.setValue(null);
                    this.form.controls.vlan.removeValidators(Validators.required);
                }
                this.form.controls.vlan.updateValueAndValidity({ emitEvent: false });
            });

        this.form.controls.secondaryVLanType.valueChanges
            .pipe(
                filter(() => !!this.form.controls.networkOffering.value?.specifyVLan),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(type => {
                if (type === SecondaryVlanType.Community || type === SecondaryVlanType.Isolated) {
                    this.form.controls.secondaryVLanId.setValidators(Validators.required);
                } else {
                    this.form.controls.secondaryVLanId.setValue(null);
                    this.form.controls.secondaryVLanId.removeValidators(Validators.required);
                }
                this.form.controls.secondaryVLanId.updateValueAndValidity({ emitEvent: false });
            });

    }

}
