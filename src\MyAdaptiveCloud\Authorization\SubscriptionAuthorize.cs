﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class SubscriptionAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public SubscriptionAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService)
        {
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string organizationId = AuthorizeFilterHelpers.GetEntityValue(context, "organizationId");
            _ = int.TryParse(organizationId, out int parsedOrganizationId);

            string serviceId = AuthorizeFilterHelpers.GetEntityValue(context, "serviceId");
            _ = int.TryParse(serviceId, out int parsedServiceId);

            var serviceOrganizationId = await _entityAuthorizationService.HasPersonAccessToService(parsedOrganizationId, parsedServiceId, userId);
            if (!serviceOrganizationId)
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class SubscriptionAuthorizeAttribute : TypeFilterAttribute
    {
        public SubscriptionAuthorizeAttribute() : base(typeof(SubscriptionAuthorizeFilter))
        {
        }
    }
}