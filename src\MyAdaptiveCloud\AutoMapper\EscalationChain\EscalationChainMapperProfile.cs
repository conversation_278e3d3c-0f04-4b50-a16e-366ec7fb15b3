﻿using AutoMapper;
using MyAdaptiveCloud.Api.Requests.EscalationChain;
using MyAdaptiveCloud.Services.DTOs.EscalationChain;

namespace MyAdaptiveCloud.Api.AutoMapper.EscalationChain
{
    public class EscalationChainMapperProfile : Profile
    {
        public EscalationChainMapperProfile()
        {
            CreateMap<CreateEscalationChainRequest, EscalationChainDTO>()
             .ForMember(dest => dest.AlertIntervalMinutes, opt => opt.MapFrom(src => src.AlertIntervalMinutes.GetValueOrDefault()))
             .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details))
             .ForMember(dest => dest.EscalationChainId, opt => opt.Ignore());

            CreateMap<CreateEscalationChainDetailRequest, EscalationChainDetailDTO>()
                .ForMember(dest => dest.UserName, opt => opt.Ignore())
                .ForMember(dest => dest.EscalationChainDetailId, opt => opt.Ignore());

            CreateMap<EditEscalationChainRequest, EscalationChainDTO>()
              .ForMember(dest => dest.AlertIntervalMinutes, opt => opt.MapFrom(src => src.AlertIntervalMinutes.GetValueOrDefault()))
              .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.Details))
              .ForMember(dest => dest.EscalationChainId, opt => opt.Ignore());

            CreateMap<EditEscalationChainDetailRequest, EscalationChainDetailDTO>()
             .ForMember(dest => dest.UserName, opt => opt.Ignore());
        }
    }
}
