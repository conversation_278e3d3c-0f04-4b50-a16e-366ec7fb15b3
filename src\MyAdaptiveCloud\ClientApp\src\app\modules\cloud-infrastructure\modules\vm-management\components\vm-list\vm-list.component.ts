import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { VmManagementPermissionService } from '@app/modules/cloud-infrastructure/modules/vm-management/services/vm-management-permission.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { FiltersFormComponent } from '@app/shared/components/datatable/filters-form/filters-form.component';
import { PillFilterComponent } from '@app/shared/components/datatable/pill-filter/pill-filter.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { VmInstance } from '@app/shared/models/cloud-infra/vm-instance.model';
import { VmListViewModel } from '@app/shared/models/cloud-infra/vm-list.view-model';
import { VmStatusEnum } from '@app/shared/models/cloud-infra/vm-status.enum';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { NgbDropdown, NgbDropdownMenu, NgbDropdownToggle, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { Group, NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, map, Observable, of, skip, switchMap, take } from 'rxjs';
import { VmListFilters } from '../../requests/vm-list.filter';
import { VmActionsService } from '../../services/vm-actions.service';
import { VmManagementService } from '../../services/vm-management.service';
import { VmListFiltersComponent } from '../vm-list-filters/vm-list-filters.component';

@Component({
    selector: 'app-vm-list',
    imports: [
        NgxDatatableModule,
        AutoSearchBoxComponent,
        NgbPopover,
        NgbDropdown,
        NgbDropdownMenu,
        NgbDropdownToggle,
        TableActionComponent,
        FiltersFormComponent,
        VmListFiltersComponent,
        PillFilterComponent,
        AsyncPipe,
        RouterLink
    ],
    templateUrl: './vm-list.component.html',
    styleUrl: './vm-list.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmListComponent extends BaseListComponent<VmListViewModel> implements OnInit {
    private readonly statusRow = viewChild<TemplateRef<never>>('statusRow');
    private readonly nameRow = viewChild<TemplateRef<never>>('nameRow');

    protected readonly store = inject(ZoneDomainAccountStore);
    private readonly vmManagementService = inject(VmManagementService);
    private readonly router = inject(Router);
    private readonly cloudInfrastructureQueueService = inject(CloudInfrastructureJobQueueService);
    protected readonly vmPermissionService = inject(VmManagementPermissionService);
    protected readonly activatedRoute = inject(ActivatedRoute);
    private readonly userContextService = inject(UserContextService);
    protected readonly vmActionsService = inject(VmActionsService);

    private readonly virtualMachineList = signal<VmListViewModel[]>(null);
    protected readonly vmStateEnum = VmStatusEnum;
    private readonly columns = signal<TableColumn[]>(null);

    private readonly stateKey = 'state';
    private readonly nameKey = 'name';
    private readonly osDisplayNameKey = 'osDisplayName';
    private readonly accountKey = 'account';
    private readonly zoneKey = 'zone';
    private readonly domainKey = 'domain';

    private readonly selectedDomain$ = toObservable(this.store.selectedDomain);
    private readonly selectedAccount$ = toObservable(this.store.selectedAccount);

    protected readonly hasVirtualMachineDetailsFeatureFlag = this.userContextService.getFeatureFlagState(FeatureFlag.FeatureFlagVirtualMachineDetails);

    constructor() {
        super();
        this.filters = new VmListFilters();
        this.pagination = this.filters.request;

        this.cloudInfrastructureQueueService.updates$
            .pipe(
                filter(vmsIds => Array.isArray(vmsIds) && vmsIds.length > 0 && this.virtualMachineList()?.length > 0),
                switchMap(vmsIds => this.vmManagementService.getVirtualMachineListByIds(vmsIds)
                    .pipe(map(res => {
                        const rows = this.virtualMachineList();
                        const viewModel = this.mapVirtualMachineListResponse(res);
                        return rows
                            .map(row => {
                                const vm = viewModel.find(m => m.id === row.id);
                                if (vm) {
                                    return { ...row, state: this.vmStateEnum[vm.state], attachedIsoId: vm.attachedIsoId, passwordEnabled: vm.passwordEnabled };
                                }

                                // Keep row only if not targeted for update (not in vmsIds)
                                return vmsIds.includes(row.id) ? null : row;
                            })
                            .filter((r): r is VmListViewModel => !!r); // type guard for non-null
                    }))),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(updatedRows => {
                this.virtualMachineList.set(updatedRows);
                super.initialize(() => this.getVirtualMachineList$(), this.columns());
            });

        this.cloudInfrastructureQueueService.idsAndStatusLoading$
            .pipe(
                filter(vmsIdsAndStatus => Array.isArray(vmsIdsAndStatus) && vmsIdsAndStatus.length > 0 && this.virtualMachineList()?.length > 0),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(vmsIdsAndStatus => {
                vmsIdsAndStatus.forEach(vm => {
                    this.setStatusToRows(vm.id, vm.status as VmStatusEnum);
                });
            });

        this.vmActionsService.actionExecuted$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(action => this.setStatusToRows(action.virtualMachineId, action.state));
    }

    ngOnInit(): void {
        this.columns.set([
            {
                cellTemplate: this.statusRow(),
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 65,
                prop: this.stateKey,
                name: 'Status'
            },
            {
                cellTemplate: this.nameRow(),
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 65,
                prop: this.nameKey,
                name: 'Name'
            },
            {
                sortable: false,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: this.domainKey,
                name: 'Domain'
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: this.accountKey,
                name: 'Account',
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                prop: this.osDisplayNameKey,
                name: 'OS Type',
                width: 120
            },
            {
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 150,
                prop: 'actions',
                name: 'Actions',
            }
        ]);

        // When the user context has its own domain, do not pass account so the result includes all VMs in the domain
        const accountName = this.userContextService.currentUser.cloudInfraUserContext.hasMappedDomain ? null : this.userContextService.currentUser.cloudInfraUserContext.accountName;
        this.vmManagementService.getVirtualMachineList(this.userContextService.currentUser.cloudInfraUserContext.domainId, accountName)
            .pipe(take(1))
            .subscribe(res => {
                this.virtualMachineList.set(this.mapVirtualMachineListResponse(res));
                this.onDomainAccountChanged(this.userContextService.currentUser.cloudInfraUserContext.domainId, accountName);
            });

        this.selectedDomain$.pipe(
            skip(1),
            filter(domain => !!domain),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(domain => {
            this.onDomainAccountChanged(domain.id, this.store.getAccount());
        });

        this.selectedAccount$.pipe(
            skip(1),
            filter(account => !!account),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(account => {
            this.onDomainAccountChanged(this.store.getDomainId(), account.name);
        });
    }

    protected createVirtualMachine() {
        if (this.store.getAccount()) {
            this.router.navigate(['create'], { relativeTo: this.activatedRoute.parent });
        }
    }

    protected getDisabledRowClass(row: VmListViewModel | Group<VmListViewModel>): Record<string, boolean> {
        row = (row as VmListViewModel);
        return {
            'disabled-row': row.state === VmStatusEnum.Stopping
                || row.state === VmStatusEnum.Migrating
                || row.state === VmStatusEnum.Starting
        };
    }

    private getVirtualMachineList$(): Observable<ApiDatasetResult<VmListViewModel[]>> {
        // Apply filters, including selected domain and account, search term and zone and state filters
        const filteredVirtualMachineList = [...this.virtualMachineList().filter(vm => {
            const matchesDomain = this.store.selectedDomain() ? vm.domainId === this.store.selectedDomain().id : true;
            const matchesAccount = this.store.getAccount() ? vm.account === this.store.getAccount() : true;
            const matchesSearchTerm = !this.pagination.searchTerm ||
                vm.osDisplayName.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                vm.name.toLowerCase().includes(this.pagination.searchTerm.toLowerCase());

            const matchesZoneFilter = !this.pagination[this.zoneKey] || vm.zoneId === this.pagination[this.zoneKey];
            const matchesStatusFilter = !this.pagination[this.stateKey] || vm.state === this.pagination[this.stateKey];
            return matchesDomain && matchesAccount && matchesSearchTerm && matchesZoneFilter && matchesStatusFilter;
        })];

        // Apply sorting
        if (this.pagination.orderBy === this.stateKey) {
            filteredVirtualMachineList.sort(sortByProperty(this.stateKey, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === this.nameKey) {
            filteredVirtualMachineList.sort(sortByProperty(this.nameKey, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === this.accountKey) {
            filteredVirtualMachineList.sort(sortByProperty(this.accountKey, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === this.osDisplayNameKey) {
            filteredVirtualMachineList.sort(sortByProperty(this.osDisplayNameKey, this.pagination.orderDir === 'asc'));
        }

        // Apply pagination
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = startIndex + this.pagination.pageSize;
        const paginatedList = filteredVirtualMachineList.slice(startIndex, endIndex);

        return of({ data: paginatedList, totalCount: filteredVirtualMachineList.length });
    }

    private setStatusToRows(id: string, status: VmStatusEnum): void {
        const rows = this.table().rows as VmListViewModel[];
        const row = rows.find(r => r.id === id);
        if (row) {
            row.state = status;
            this.table().rows = [...rows];
        }
    }

    private onDomainAccountChanged(domainId: string, account: string): void {
        let columns = [...this.columns()];
        if (account) {
            columns = [...columns.filter(c => c.prop !== this.accountKey && c.prop !== this.domainKey)];
        } else if (domainId && !this.store.isRootDomainSelected()) {
            columns = [...columns.filter(c => c.prop !== this.domainKey)];
        }

        if (this.table().columns?.length) {
            this.table().columns = [...columns];
        }

        super.initialize(() => this.getVirtualMachineList$(), columns);
    }

    private mapVirtualMachineListResponse(vms: VmInstance[]): VmListViewModel[] {
        let vmsViewModelList: VmListViewModel[] = [];
        if (vms?.length > 0) {
            vmsViewModelList = vms.map(vm => {
                const viewModel: VmListViewModel = {
                    id: vm.id,
                    name: vm.name,
                    osDisplayName: vm.osdisplayname,
                    zoneName: vm.zonename,
                    isAgentInstalled: false,
                    state: this.vmStateEnum[vm.state],
                    zoneId: vm.zoneid,
                    attachedIsoId: vm.isoid,
                    domain: vm.domain.trim(),
                    account: vm.account.trim(),
                    passwordEnabled: !!vm.passwordenabled,
                    domainId: vm.domainid,
                    isNewInstance: false
                };
                return viewModel;
            });
        }
        return vmsViewModelList;
    }

}
