using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Requests.Unsubscribe;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Extensions;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class AnonymousUnsubscribeAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public AnonymousUnsubscribeAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService)
        {
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            if (!context.HttpContext.Request.Query.TryGetValue("messageInfo", out var rawMessage))
            {
                context.Result = new ForbidResult();
                return;
            }


            var messageBase64string = rawMessage.ToString().FromBase64String();
            var deserializedMessage = JsonSerializer.Deserialize<UnsubscribeMessageInfo>(messageBase64string);
            bool isValidMessage = await _entityAuthorizationService.ValidateUnsubscribeMessage(deserializedMessage.UserEmail, deserializedMessage.MessageQueueItemMessageId);
            if (!isValidMessage)
            {
                context.Result = new ForbidResult();
                return;
            }

            return;
        }
    }

    /// <summary>
    ///     Verifies that the encoded message is valid
    /// </summary>
    /// <remarks>
    ///     This attribute should not inherit from <see cref="BaseAuthorizeAttribute">, as it doesn't include permissions.
    /// </remarks>
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    public class UnsubscribeAuthorizeAttribute : TypeFilterAttribute
    {
        public UnsubscribeAuthorizeAttribute() : base(typeof(AnonymousUnsubscribeAuthorizeFilter))
        {
        }
    }
}