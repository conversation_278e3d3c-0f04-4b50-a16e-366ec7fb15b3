[Unit]
Description=MyAdaptiveCloud Web API
After=docker.service
Requires=docker.service

[Service]
WorkingDirectory=/home/<USER>/myadaptivecloud
ExecStart=docker compose --file /home/<USER>/myadaptivecloud/docker-compose.yaml up -d
ExecStop=docker compose --file /home/<USER>/myadaptivecloud/docker-compose.yaml down
Restart=no
RemainAfterExit=yes
SyslogIdentifier=myac
User=myac

[Install]
WantedBy=multi-user.target
