import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { StopVmComponent } from './stop-vm.component';

describe('StopVmComponent', () => {
    let component: StopVmComponent;
    let fixture: ComponentFixture<StopVmComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [StopVmComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(StopVmComponent);
        component = fixture.componentInstance;
        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.stopVirtualMachine.and.returnValue(of('jobId1'));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.virtualMachineId = 'test-id';
        fixture.detectChanges();
    });

    it('should close modal on cancel', () => {
        const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
        cancelButton.click();
        fixture.detectChanges();

        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    it('should close modal with response on successful stopVirtualMachine call', () => {
        const forceStopControl = fixture.debugElement.query(By.css('#force-stop')).nativeElement as HTMLInputElement;
        forceStopControl.checked = true;
        forceStopControl.dispatchEvent(new Event('change'));
        fixture.detectChanges();

        const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
        submit.click();
        fixture.detectChanges();

        expect(mockVmManagementService.stopVirtualMachine).toHaveBeenCalledOnceWith('test-id', true);
        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

});

