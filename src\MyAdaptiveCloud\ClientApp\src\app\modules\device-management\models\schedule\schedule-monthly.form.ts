import { FormControl, FormGroup } from '@angular/forms';
import { MonthDaysForm } from '../../modules/policy/models/month-days.form';
import { MonthWeekForm } from '../../modules/policy/models/month-week.form';

export interface ScheduleMonthlyForm {
    months: FormControl<number[]>,
    selectByDaysOrWeeksRadios: FormControl<number>,
    monthDaysGroup: FormGroup<MonthDaysForm>,
    weeksOnMonthGroup: FormGroup<MonthWeekForm>
}
