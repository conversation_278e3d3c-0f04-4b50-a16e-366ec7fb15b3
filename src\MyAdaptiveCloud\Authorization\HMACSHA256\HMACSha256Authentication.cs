﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using MyAdaptiveCloud.Core.Common.Cryptography.HMACSHA256;
using MyAdaptiveCloud.Services.Provider;
using MyAdaptiveCloud.Services.Services;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Web;

namespace MyAdaptiveCloud.Api.Authorization.HMACSHA256
{
    public class HMACAuthOptions : AuthenticationSchemeOptions
    {
        public string Key { get; set; }
    }

    public static class HMACAuthExtensions
    {
        public static AuthenticationBuilder AddHMACSigAuthAuthentication(
            this AuthenticationBuilder builder,
            string authScheme,
            Action<HMACAuthOptions> configureOptions)
        {
            return builder.AddScheme<HMACAuthOptions, HMACSigAuthHandler>(authScheme, configureOptions);
        }
    }

    public class HMACSigAuthHandler : AuthenticationHandler<HMACAuthOptions>
    {
        private readonly IConfigurationService _configurationService;
        private readonly IAuthenticationKeyService _authenticationKeyService;
        private readonly IDateTimeProvider _dateTimeProvider;

        public HMACSigAuthHandler(
            IOptionsMonitor<HMACAuthOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            IConfigurationService configurationService,
            IAuthenticationKeyService authenticationKeyService,
            IDateTimeProvider dateTimeProvider
        )
            : base(options, logger, encoder)
        {
            _configurationService = configurationService;
            _authenticationKeyService = authenticationKeyService;
            _dateTimeProvider = dateTimeProvider;
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var configurationValues = await _configurationService.GetAgentAuthenticationConfiguration();

            if (Request.Headers.Authorization.Any())
            {
                var rawAuthzHeader = Request.Headers.Authorization;
                var authorizationHeaderArray = GetAuthorizationHeaderValues(rawAuthzHeader);
                if (authorizationHeaderArray != null)
                {
                    var authkey = authorizationHeaderArray[0];
                    var receivedApiKey = Request.Headers[configurationValues.ApiKeyHeaderName];
                    var isValidRequest = IsValidRequest(configurationValues.RequestMaxAgeInSeconds,
                        authkey, configurationValues.AuthenticationMethodName, receivedApiKey,
                        configurationValues.TimeStampHeaderName, out var base64EncodedString);
                    var apiClient = await _authenticationKeyService.GetAuthenticationKeyByApiKey(receivedApiKey);

                    if (!isValidRequest)
                    {
                        return AuthenticateResult.Fail("Authentication failed");
                    }

                    var secret = await _authenticationKeyService.GetAuthenticationKeySecret(apiClient.Id);
                    var isRequestAuthorized = await IsRequestAuthorized(authorizationHeaderArray, base64EncodedString, secret, configurationValues.AuthenticationMethodName);

                    if (isRequestAuthorized)
                    {
                        Claim[] claims = new[] { new Claim(ClaimTypes.Name, apiClient.PersonId.ToString()) };

                        var identity = new ClaimsIdentity(claims, Scheme.Name);
                        var principal = new ClaimsPrincipal(identity);
                        var ticket = new AuthenticationTicket(principal, Scheme.Name);

                        return AuthenticateResult.Success(ticket);
                    }
                    else
                    {
                        return AuthenticateResult.Fail("Authentication failed");
                    }
                }
                else
                {
                    return AuthenticateResult.Fail("Authentication failed");
                }
            }
            else
            {
                return AuthenticateResult.Fail("Authentication failed");
            }
        }

        private async Task<bool> IsRequestAuthorized(string[] authorizationHeaderArray, string base64EncodedString, string secretKey, string authenticationMethodName)
        {
            var nonce = authorizationHeaderArray[1];
            var requestTimeStamp = authorizationHeaderArray[2];

            _ = long.TryParse(requestTimeStamp, out var requestTimeStampInt);

            var signatureRawData = await GetStringToSign(requestTimeStampInt, nonce, authenticationMethodName);
            var signingKey = MYACHMACSHA256Helper.GetSigningKey(authenticationMethodName, secretKey, requestTimeStamp.ToString(), nonce);
            var signature = MYACHMACSHA256Helper.GetSignature(signingKey, signatureRawData);

            return (base64EncodedString.Equals(signature, StringComparison.Ordinal));
        }

        private async Task<string> GetStringToSign(long requestTimeStamp, string nonce, string authenticationMethodName)
        {
            var requestUri = HttpUtility.UrlEncode(Request.Path.Value.ToLower());
            var requestHttpMethod = Request.Method;

            var requestContentHash = await GetBodyHash();

            var canonicalRequest = string.Format("{0}{1}{2}{3}{4}",
                requestHttpMethod, requestUri, requestTimeStamp,
                nonce, requestContentHash);

            return $"{authenticationMethodName}:{MYACHMACSHA256Helper.GetHashFromString(canonicalRequest)}";
        }

        private bool IsValidRequest(int requestMaxAgeInSeconds,
            string authkey, string authenticationMethodName,
            string receivedApiKey, string timeStampHeaderName,
            out string base64EncodedString)
        {
            var authorizationHeaders = Request.Headers;
            var receivedTS = authorizationHeaders[timeStampHeaderName];
            var authorization = authorizationHeaders.Authorization;

            base64EncodedString = string.Empty;

            if (string.IsNullOrEmpty(receivedTS.ToString()) ||
                string.IsNullOrEmpty(receivedApiKey.ToString()) ||
                string.IsNullOrEmpty(authorization.ToString()))
            {
                return false;
            }

            if (!IsDateValidated(receivedTS, requestMaxAgeInSeconds))
            {
                return false;
            }

            if (!IsAuthKeyValid(authkey, authenticationMethodName, out base64EncodedString))
            {
                return false;
            }

            return true;
        }

        private static string[] GetAuthorizationHeaderValues(string rawAuthzHeader)
        {
            var credArray = rawAuthzHeader.Split(':');
            if (credArray.Length == 3)
            {
                return credArray;
            }
            else
            {
                return null;
            }
        }

        private static bool IsAuthKeyValid(string authkey, string authenticationMethodName, out string base64EncodedString)
        {
            var authParts = authkey.Split(' ');
            if (authParts.Length != 2)
            {
                base64EncodedString = string.Empty;
                return false;
            }

            if (authParts[0] != authenticationMethodName)
            {
                base64EncodedString = string.Empty;
                return false;
            }

            base64EncodedString = authParts[1];
            return true;
        }

        private async Task<string> GetBodyHash()
        {
            using (var memoryStream = new MemoryStream())
            {
                await Request.Body.CopyToAsync(memoryStream);
                byte[] byteArray = memoryStream.ToArray();

                if (!byteArray.Any())
                {
                    return string.Empty;
                }

                return MYACHMACSHA256Helper.GetHashFromBytes(byteArray);
            }
        }

        public bool IsDateValidated(string timestampString, int requestMaxAgeInSeconds)
        {
            try
            {
                var isValidLong = long.TryParse(timestampString, out var timeStamp);
                if (!isValidLong)
                {
                    return false;
                }

                var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timeStamp);

                var now = _dateTimeProvider.UtcTime;

                if (timestamp < now.AddSeconds(-requestMaxAgeInSeconds))
                {
                    return false;
                }

                if (timestamp > now.AddSeconds(requestMaxAgeInSeconds))
                {
                    return false;
                }

                return true;
            }
            catch (ArgumentOutOfRangeException)
            {
                return false;
            }
        }
    }
}