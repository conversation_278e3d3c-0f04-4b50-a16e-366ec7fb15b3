<div class="device-detail-header d-flex">
    <div class="col-6">
        <h3 class="text-secondary">Software Inventory</h3>
        @if(hostName()) {
        <small>HostName : <strong>{{hostName()}}</strong></small>
        }
    </div>
</div>

<div class="content-sub-heading">
    <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" [dataItemName]="'searchTerm'" />

    <div class="action-buttons">
        <button type="button" class="btn btn-primary" (click)="exportData()">Export to CSV</button>
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap" (sort)="onSorting($event)" (page)="onPageChanged($event)" />
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #dateCellTemplate let-value="value">
    <span>
        {{ value ? (value | date: 'MM/dd/yy') : '-' }}
    </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
    @if ((permissionService.canManageDevices() || permissionService.canViewDevices()) && row.hasUninstall &&
    row.canViewUninstallSoftwareInventory) {
    <app-table-action [enabled]="row.softwareInventoryStatus !== softwareInventoryStatus.MarkedForDeleted"
        [icon]="'icon-software-inventory-uninstall'" (clickHandler)="uninstallSoftwareInventory(row)"
        [title]="row.softwareInventoryStatus === softwareInventoryStatus.MarkedForDeleted ? 'Marked for Deletion':'Uninstall Software' " />
    }
    }
</ng-template>