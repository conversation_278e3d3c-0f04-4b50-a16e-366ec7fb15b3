﻿using AutoMapper;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Api.ViewModel.Schedule;

namespace MyAdaptiveCloud.Api.AutoMapper.Schedule
{
    public class StartDateTimeLocalizedResolver : IValueResolver<Services.DTOs.Schedule.Schedule, ScheduleViewModel, string>
    {
        private readonly IContextTimeZoneService _timeZoneService;

        public StartDateTimeLocalizedResolver(IContextTimeZoneService timeZoneService)
        {
            _timeZoneService = timeZoneService;
        }

        public string Resolve(Services.DTOs.Schedule.Schedule source, ScheduleViewModel destination, string destMember, ResolutionContext context)
        {
            return _timeZoneService?.GetUIDateTimeLocalizedString((DateTimeOffset)source.StartDate, source.StartDateTimeZone) ?? string.Empty;
        }
    }
}
