import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { VmListFilters } from '../../requests/vm-list.filter';
import { VmListFiltersComponent } from './vm-list-filters.component';

describe('VmListFiltersComponent', () => {
    let component: VmListFiltersComponent;
    let fixture: ComponentFixture<VmListFiltersComponent>;

    const mockZones = [{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [VmListFiltersComponent]
        });

        fixture = TestBed.createComponent(VmListFiltersComponent);
        component = fixture.componentInstance;
        fixture.componentRef.setInput('filters', new VmListFilters());
        fixture.componentRef.setInput('zones', mockZones);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should not set initial form values', () => {
        expect(component.filters().form.get('state').value).toBeNull();
        expect(component.filters().form.get('zone').value).toBeNull();
    });

    it('should set the Zone filter options correctly in the view', () => {
        const groupElement = fixture.debugElement.query(By.css('#zone-group'));
        const inputs = groupElement.queryAll(By.css('.form-check-input'));
        const labels = groupElement.queryAll(By.css('.form-check-label'));
        const groupSectionTitle = groupElement.query(By.css('h6')).nativeElement.innerText;
        const zoneGroup = component.filters().filterGroupOptions.find(group => group.key === 'zone');

        expect(inputs.length).toEqual(mockZones.length);
        expect(groupSectionTitle).toEqual(zoneGroup.groupName);

        inputs.forEach((input, index) => {
            const radioInput = (input.nativeElement as HTMLInputElement).id;
            expect(radioInput).toEqual(mockZones[index].id);
        });

        labels.forEach((label, index) => {
            const labelText = (label.nativeElement as HTMLLabelElement).innerText;
            expect(labelText).toEqual(mockZones[index].name);
        });
    });

    it('should set the Status filter options correctly in the view', () => {
        const groupElement = fixture.debugElement.query(By.css('#status-group'));
        const inputs = groupElement.queryAll(By.css('.form-check'));
        const groupSectionTitle = groupElement.query(By.css('h6')).nativeElement.innerText;
        const statusGroup = component.filters().filterGroupOptions.find(group => group.key === 'state');

        expect(inputs.length).toEqual(statusGroup.options.length);
        expect(groupSectionTitle).toEqual(statusGroup.groupName);

        inputs.forEach((_optionElement, _index) => {
            const radioInput = _optionElement.nativeElement.children[0].id;
            const radioLabel = _optionElement.nativeElement.children[1].innerText;
            const expectedRadioId = `${statusGroup.options[_index].value}`;
            const expectedLabelText = statusGroup.options[_index].label;

            expect(radioInput).toEqual(expectedRadioId);
            expect(radioLabel).toEqual(expectedLabelText);
        });
    });

});
