import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { HubService } from '@app/shared/hubs/services/hub.service';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { NotificationService } from '@app/shared/services/notification.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbA<PERSON>rdionBody, NgbAccordionButton, NgbAccordionItem } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { ConfigurationValueInputType } from '../../models/configuration-value-input-type.enum';
import { Configuration } from '../../models/configuration.model';
import { ConfigurationService } from '../../services/configuration.service';
import { ConfigurationComponent } from './configuration.component';

describe('ConfigurationComponent', () => {

    let fixture: ComponentFixture<ConfigurationComponent>;
    let mockConfigurationService: jasmine.SpyObj<ConfigurationService>;
    let mockPermissionService: jasmine.SpyObj<PermissionService>;
    let mockHubService: jasmine.SpyObj<HubService>;

    const configurationData: Configuration[] = [
        {
            configurationId: 1,
            category: 'Test Configuration',
            configurationValues: [
                {
                    id: 10,
                    name: 'Test Configuration Value',
                    value: 'Test Value',
                    inputType: ConfigurationValueInputType.Input,
                    isSecret: false
                }
            ]
        },
        {
            configurationId: 2,
            category: 'Test Category',
            configurationValues: [
                {
                    id: 11,
                    name: 'Test Configuration Value 2',
                    value: 'Test Value 2',
                    inputType: ConfigurationValueInputType.TextArea,
                    isSecret: true
                },
                {
                    id: 12,
                    name: 'Test Configuration Value 2',
                    value: 'Test Value 2',
                    inputType: ConfigurationValueInputType.TextArea,
                    isSecret: true
                }
            ]
        },
        {
            configurationId: 3,
            category: 'Feature Flags',
            configurationValues: [
                {
                    id: 13,
                    name: 'Feature Flag',
                    value: 'true',
                    inputType: ConfigurationValueInputType.Toggle,
                    isSecret: false
                }
            ]
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ConfigurationService),
                provideMock(NotificationService),
                provideMock(PermissionService),
                provideMock(HubService)
            ],
            imports: [
                ConfigurationComponent
            ]
        });

        mockConfigurationService = TestBed.inject(ConfigurationService) as jasmine.SpyObj<ConfigurationService>;
        mockConfigurationService.getList.and.returnValue(of({ data: configurationData, message: 'success' } as ApiDataResult<Configuration[]>));
        mockConfigurationService.editConfigurationValue.and.returnValue(of({ message: 'success' } as ApiResult));

        mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;

        mockHubService = TestBed.inject(HubService) as jasmine.SpyObj<HubService>;
        // eslint-disable-next-line arrow-body-style
        mockHubService.updateFeatureFlags.and.callFake(() => {
            return;
        });

        fixture = TestBed.createComponent(ConfigurationComponent);
    });

    describe('Component Initialization', () => {

        it('should set form controls as disabled when the user does not have ManageConfiguration permission', () => {
            mockPermissionService.canManageConfiguration.and.returnValue(false);
            fixture.detectChanges();
            const accordionItems = fixture.debugElement.queryAll(By.directive(NgbAccordionItem));
            expect(accordionItems.length).toEqual(configurationData.length);
            accordionItems.forEach(item => {
                const accordionHeader = item.query(By.directive(NgbAccordionButton));
                accordionHeader.nativeElement.click();
                const accordionBody = item.query(By.directive(NgbAccordionBody));
                const controls = accordionBody.queryAll(By.css('.form-control'));
                expect(controls.every(control => (control.nativeElement as HTMLInputElement).disabled)).toBeTrue();
            });
        });

        it('should hide Update button when the user does not have ManageConfiguration permission', () => {
            mockPermissionService.canManageConfiguration.and.returnValue(false);
            fixture.detectChanges();
            const updateButton = fixture.debugElement.query(By.css('.btn.btn-primary'));
            expect(updateButton).toBeNull();
        });

        it('should set form controls as enabled when the user does have ManageConfiguration permission', () => {
            mockPermissionService.canManageConfiguration.and.returnValue(true);
            fixture.detectChanges();
            const accordionItems = fixture.debugElement.queryAll(By.directive(NgbAccordionItem));
            expect(accordionItems.length).toEqual(configurationData.length);
            accordionItems.forEach(item => {
                const accordionHeader = item.query(By.directive(NgbAccordionButton));
                accordionHeader.nativeElement.click();
                const accordionBody = item.query(By.directive(NgbAccordionBody));
                const controls = accordionBody.queryAll(By.css('.form-control'));
                expect(controls.every(control => (control.nativeElement as HTMLInputElement).disabled)).toBeFalse();
            });
        });

        it('should show Update button when the user does have ManageConfiguration permission', () => {
            mockPermissionService.canManageConfiguration.and.returnValue(true);
            fixture.detectChanges();
            const updateButton = fixture.debugElement.query(By.css('.btn.btn-primary'));
            expect(updateButton.nativeElement).toBeDefined();
        });

    });

    describe('Component Interaction', () => {

        it('should not submit form if no controls are in dirty state', () => {
            mockPermissionService.canManageConfiguration.and.returnValue(true);
            fixture.detectChanges();
            const updateButton = fixture.debugElement.query(By.css('.btn.btn-primary'));
            const updateButtonElement = updateButton.nativeElement as HTMLButtonElement;
            updateButtonElement.click();
            expect(mockConfigurationService.editConfigurationValue).not.toHaveBeenCalled();
        });

        it('should submit form if at least one control has changed', () => {
            mockPermissionService.canManageConfiguration.and.returnValue(true);
            fixture.detectChanges();

            const updateButton = fixture.debugElement.query(By.css('.btn.btn-primary'));
            const updateButtonElement = updateButton.nativeElement as HTMLButtonElement;

            const accordionItem = fixture.debugElement.query(By.directive(NgbAccordionItem));
            const accordionHeader = accordionItem.query(By.directive(NgbAccordionButton));
            accordionHeader.nativeElement.click();
            const accordionBody = accordionItem.query(By.directive(NgbAccordionBody));
            const control = accordionBody.query(By.css('.form-control'));
            control.nativeElement.value = 'New Value';
            control.nativeElement.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            updateButtonElement.click();
            fixture.detectChanges();

            expect(mockConfigurationService.editConfigurationValue).toHaveBeenCalledTimes(1);
        });

        it('should call hubService.updateFeatureFlags if a feature flag is updated', () => {
            mockPermissionService.canManageConfiguration.and.returnValue(true);
            fixture.detectChanges();

            const updateButton = fixture.debugElement.query(By.css('.btn.btn-primary'));
            const updateButtonElement = updateButton.nativeElement as HTMLButtonElement;

            const accordionItem = fixture.debugElement.queryAll(By.directive(NgbAccordionItem))[2];
            const accordionHeader = accordionItem.query(By.directive(NgbAccordionButton));
            accordionHeader.nativeElement.click();
            const accordionBody = accordionItem.query(By.directive(NgbAccordionBody));
            const control = accordionBody.query(By.css('.form-control-check-input'));
            control.nativeElement.checked = false;
            control.nativeElement.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            updateButtonElement.click();
            fixture.detectChanges();

            expect(mockHubService.updateFeatureFlags).toHaveBeenCalledTimes(1);
        });

    });

});
