import { ChangeDetectionStrategy, Component, Input, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { ElementsSelectorComponent } from '@app/shared/components/elements-selector/elements-selector.component';
import { ElementSelector } from '@app/shared/models/element-selector.model';
import { ReportFormat } from '@app/shared/models/report-format.enum';
import { ReportType } from '@app/shared/models/report-type.enum';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { UsageReportForm } from '../../forms/usage-report.form';
import { AdaptiveCloudUsageReportRequest } from '../../requests/adaptivecloud-usage-report.request';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';

@Component({
    selector: 'app-adaptivecloud-usage-report-modal',
    imports: [ReactiveFormsModule, NgSelectModule, BtnSubmitComponent, ElementsSelectorComponent],
    templateUrl: './adaptivecloud-usage-report-modal.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdaptiveCloudUsageReportModalComponent implements OnInit {
    private readonly usageService = inject(AdaptiveCloudUsageService);
    private readonly userContextService = inject(UserContextService);
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) accounts: ElementSelector[];
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) selectedAccounts: ElementSelector[];
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) period: string;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) reportType: ReportType;

    protected form: FormGroup<UsageReportForm>;
    protected readonly reportTypes = [ReportType.Summary, ReportType.Detail];
    protected readonly reportFormats = [ReportFormat.CSV];

    ngOnInit(): void {
        this.form = this.formBuilder.group<UsageReportForm>({
            period: this.formBuilder.control(this.period, [Validators.required]),
            reportType: this.formBuilder.control(this.reportType, [Validators.required]),
            reportFormat: this.formBuilder.control(ReportFormat.CSV, [Validators.required]),
            accounts: this.formBuilder.control(this.selectedAccounts?.map(e => e.id.toString()), [Validators.required])
        });
    }

    protected generateReport(): void {
        if (this.form.valid) {
            const request: AdaptiveCloudUsageReportRequest = {
                accounts: this.form.controls.accounts.value,
                period: this.form.controls.period.value,
                reportFormat: this.form.controls.reportFormat.value,
                reportType: this.form.controls.reportType.value
            };
            this.usageService.getUsageReport(this.userContextService.currentUser.organizationId, request);
        }
    }

    protected updateSelectedAccounts(event: ElementSelector[]) {
        this.form.controls.accounts.setValue(event.map(account => account.id.toString()));
    }
}
