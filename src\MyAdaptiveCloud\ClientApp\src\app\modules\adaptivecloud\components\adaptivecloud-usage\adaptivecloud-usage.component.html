<div class="content-heading">
  Cloud Infrastructure Usage@if (showDetailView()) {
  - {{ selectedAccountName() }}
}
</div>

<div class="content-sub-heading">
  <form [formGroup]="form" class="form-horizontal">
    <ng-select placeholder="Select period" [clearable]="false" [searchable]="false"
      formControlName="period">
      <ng-template ng-label-tmp let-item="item">
        <span>{{ item | period }}</span>
      </ng-template>
      @for (period of periods$ | async; track period) {
        <ng-option [value]="period">
          <span>{{ period | period }}</span>
        </ng-option>
      }
    </ng-select>
  </form>
  <div class="action-buttons">
    @if (accounts()?.length > 0) {
      <button class="btn btn-primary" type="button"
        (click)="openGenerateReportDialog()">
        Generate Report
      </button>
    }
    @if (showDetailView()) {
      <app-back [buttonText]="'Back to Summary'" [hasCustomBehavior]="true"
      (clickEvent)="onShowSummary()" />
    }
  </div>
</div>

@if (showViews()) {
  @if (!showDetailView()) {
    <app-adaptivecloud-usage-summary [period]="form.controls.period.value"
      (showDetails)="onShowDetails($event)" (dataLoaded)="onSummaryDataLoaded($event)" />
  }
  @if (showDetailView()) {
    <app-adaptivecloud-usage-detail [selectedPeriod]="form.controls.period.value"
      [selectedAccountId]="selectedAccountId()" />
  }
}
