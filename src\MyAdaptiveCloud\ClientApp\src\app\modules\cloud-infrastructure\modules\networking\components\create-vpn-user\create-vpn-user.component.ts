import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { CreateVpnUserForm } from '../../forms/create-vpn-user.form';
import { VpnUsersService } from '../../services/vpn-users.service';

@Component({
    selector: 'app-create-vpn-user',
    imports: [BtnSubmitComponent, ReactiveFormsModule, NgbPopover],
    templateUrl: './create-vpn-user.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVpnUserComponent {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vpnUsersService = inject(VpnUsersService);
    private readonly formBuilder = inject(FormBuilder);

    readonly domainId = signal<string>(null);
    readonly account = signal<string>(null);

    protected readonly form = this.formBuilder.group<CreateVpnUserForm>({
        userName: this.formBuilder.control<string>('', [Validators.required, Validators.pattern('^[a-zA-Z0-9][a-zA-Z0-9@._-]{2,63}$')]),
        password: this.formBuilder.control<string>('', [Validators.required, Validators.pattern('^[a-zA-Z0-9][a-zA-Z0-9@+=._-]{2,31}$')]),
    });

    protected submitForm() {
        if (this.form.valid) {
            this.vpnUsersService.createVpnUser(this.form.value.userName, this.form.value.password, this.domainId(), this.account())
                .subscribe(jobId => {
                    this.activeModal.close(jobId);
                });
        }
    }
}
