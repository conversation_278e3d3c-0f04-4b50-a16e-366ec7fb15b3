import { ComponentFixture, discardPeriodicTasks, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { SSHKeyPair } from '../../models/ssh-key-pair';
import { VmManagementService } from '../../services/vm-management.service';
import { ResetSSHKeyPairComponent } from './reset-ssh-key-pair.component';

describe('ResetSSHKeyPairComponent', () => {

    let component: ResetSSHKeyPairComponent;
    let fixture: ComponentFixture<ResetSSHKeyPairComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    const mockGetSshKeyPairsResponse: SSHKeyPair[] = [
        {
            account: 'test-account 1',
            domain: 'test-domain 1',
            domainid: 'test-domain-id 1',
            fingerprint: 'test-fingerprint 1',
            name: 'Key 1'
        },
        {
            account: 'test-account 2',
            domain: 'test-domain 2',
            domainid: 'test-domain-id 2',
            fingerprint: 'test-fingerprint 2',
            name: 'Key 2'
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ResetSSHKeyPairComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(ResetSSHKeyPairComponent);
        component = fixture.componentInstance;
        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.resetSSHKeyPairForVirtualMachine.and.returnValue(of('jobId1'));
        mockVmManagementService.getKeyPairList.and.returnValue(of(mockGetSshKeyPairsResponse));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.inputData.set({
            virtualMachineId: 'test-id',
            domainId: 'domain-id',
            account: 'account'
        });
    });

    describe('Initializtion', () => {

        it('should load options', () => {
            fixture.detectChanges();
            expect(mockVmManagementService.getKeyPairList).toHaveBeenCalledTimes(1);
        });

    });

    describe('Submit', () => {

        it('should close modal on cancel', () => {

            fixture.detectChanges();
            const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit the form when a ssh key value is selected', fakeAsync(() => {

            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.resetSSHKeyPairForVirtualMachine).toHaveBeenCalledOnceWith('test-id', 'Key 2', 'domain-id', 'account');
            expect(activeModal.close).toHaveBeenCalledTimes(1);

            flush();
            discardPeriodicTasks();
        }));

        it('should not submit the form when a ssh key value is not selected', () => {

            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.resetSSHKeyPairForVirtualMachine).not.toHaveBeenCalled();
            expect(activeModal.close).not.toHaveBeenCalled();
        });

    });

});

