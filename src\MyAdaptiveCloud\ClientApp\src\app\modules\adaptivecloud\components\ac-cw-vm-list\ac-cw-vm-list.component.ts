import { DatePipe } from '@angular/common';
import { Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MonthYearPickerComponent } from '@app/shared/components/month-year-picker/month-year-picker.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { AcCwVmMap } from '../../models/ac-cw-vm-map.model';
import { AcCwVmMappingListRequest } from '../../requests/ac-cw-vm-mapping-list.request';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';
import { CreateAcCwVmMappingComponent } from '../create-ac-cw-vm-mapping/create-ac-cw-vm-mapping.component';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-ac-cw-vm-list',
    imports: [MonthYearPickerComponent, NgxDatatableModule, TableActionComponent, DatePipe],
    templateUrl: './ac-cw-vm-list.component.html'
})

export class AcCwVmListComponent extends BaseListComponent<AcCwVmMap> implements OnInit {
    private readonly mappingService = inject(AcToCwMappingService);
    private readonly modalService = inject(ModalService);
    readonly permissionService = inject(PermissionService);

    readonly dashCell = viewChild<TemplateRef<never>>('dashCellTemplate');
    readonly dateCell = viewChild<TemplateRef<never>>('dateCellTemplate');
    readonly valueCell = viewChild<TemplateRef<never>>('valueCellTemplate');

    usagePeriods: Date[];

    constructor() {
        super();
        this.pagination = new AcCwVmMappingListRequest();
    }

    ngOnInit(): void {
        const columns: TableColumn[] = [
            {
                name: 'Domain',
                prop: 'domain',
                cellTemplate: this.dashCell(),
                sortable: false
            },
            {
                name: 'Account',
                prop: 'account',
                cellTemplate: this.dashCell(),
                sortable: false
            },
            {
                name: 'Name',
                prop: 'acName',
                sortable: false
            },
            {
                name: 'Priority',
                prop: 'priority',
                sortable: false,
            },
            {
                name: 'Type',
                prop: 'acType',
                sortable: false
            },
            {
                name: 'Start Date',
                prop: 'startDate',
                cellTemplate: this.dateCell(),
                sortable: false
            },
            {
                name: 'Value/Adjust',
                prop: 'quantityValue',
                cellTemplate: this.valueCell(),
                sortable: false
            },
            {
                name: 'Action',
                cellTemplate: this.actionsTemplate(),
                sortable: false
            }
        ];

        const table = this.table();
        table.groupRowsBy = 'cwProductName';
        table.groupExpansionDefault = true;

        this.mappingService.getVmMappingPeriods()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
                this.usagePeriods = res.data;
                this.setDefaultPeriod();
                super.initialize(this.mappingService.getVmMappings.bind(this.mappingService), columns);
            });
    }

    getVmMappingPeriods() {
        this.mappingService.getVmMappingPeriods()
            .subscribe(res => {
                this.usagePeriods = res.data;
                this.setDefaultPeriod();
                this.loadData();
            });
    }

    // Sets the default selected period to the first one in the list of periods that is either the current or a past month.
    private setDefaultPeriod() {
        const currentPeriod = new Date(new Date().getUTCFullYear(), new Date().getUTCMonth() + 1, 1, 0, 0);
        if (!this.usagePeriods) {
            (this.pagination as AcCwVmMappingListRequest).month = currentPeriod;
        } else if (this.usagePeriods.some(period => new Date(period).getTime() === currentPeriod.getTime())) {
            (this.pagination as AcCwVmMappingListRequest).month = currentPeriod;
        } else {
            const closestPastPeriod = this.usagePeriods.find(period => new Date(period).getTime() < currentPeriod.getTime());
            if (closestPastPeriod) {
                (this.pagination as AcCwVmMappingListRequest).month = closestPastPeriod;
            } else {
                (this.pagination as AcCwVmMappingListRequest).month = this.usagePeriods[0];
            }
        }

        if ((this.pagination as AcCwVmMappingListRequest).month === undefined) {
            (this.pagination as AcCwVmMappingListRequest).month = currentPeriod;
        }

    }

    createMapping(): void {
        const modalRef = this.modalService.openModalComponent(CreateAcCwVmMappingComponent);
        modalRef.result.then(res => {
            if (res) {
                this.getVmMappingPeriods();
            }
            // eslint-disable-next-line arrow-body-style
        }).catch(() => {
            return;
        });
    }

    editMapping(row: AcCwVmMap): void {
        const modalRef = this.modalService.openModalComponent(CreateAcCwVmMappingComponent);
        modalRef.componentInstance.mapping = row;
        modalRef.result.then(res => {
            if (res) {
                this.loadData();
            }
            // eslint-disable-next-line arrow-body-style
        }).catch(() => {
            return;
        });
    }

    deleteMapping(acCwVmMap: AcCwVmMap): void {
        this.modalService.openDeleteConfirmationDialog(`Delete ${acCwVmMap.acName} mapping`, 'Are you sure you want to delete this mapping?').closed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(result => {
                if (result) {
                    this.mappingService.deleteVmMapping(acCwVmMap.id)
                        .pipe(takeUntilDestroyed(this.destroyRef))
                        .subscribe(() => {
                            this.loadData();
                        });
                }
            });
    }

    onChangePeriod() {
        this.loadData();
    }

}
