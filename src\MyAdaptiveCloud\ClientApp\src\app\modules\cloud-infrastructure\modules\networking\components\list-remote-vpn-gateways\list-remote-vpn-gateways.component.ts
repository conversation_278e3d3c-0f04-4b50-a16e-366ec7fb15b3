import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, Observable, of, skip, switchMap, take } from 'rxjs';
import { RemoteVpnGateway } from '../../models/remote-vpn-gateway';
import { REMOTE_VPN_GATEWAY_LIST_CONSTANTS } from '../../models/remote-vpn-gateway-list.constants';
import { RemoteVpnGatewayViewModel } from '../../models/remote-vpn-gateway.view-model';
import { RemoteVpnGatewayListRequest } from '../../requests/remote-vpn-gateway-list.request';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { RemoteVpnGatewayService } from '../../services/remote-vpn-gateway.service';
import { CreateRemoteVpnGatewayComponent } from '../create-remote-vpn-gateway/create-remote-vpn-gateway.component';

@Component({
    selector: 'app-list-remote-vpn-gateways',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent],
    templateUrl: './list-remote-vpn-gateways.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListRemoteVpnGatewaysComponent extends BaseListComponent<RemoteVpnGatewayViewModel> implements OnInit {
    private readonly userContextService = inject(UserContextService);
    private readonly remoteVpnGatewayService = inject(RemoteVpnGatewayService);
    protected readonly domainAccountTreeStore = inject(ZoneDomainAccountStore);
    private readonly modalService = inject(ModalService);
    protected readonly networkingPermissionService = inject(NetworkingPermissionService);

    private readonly columns = signal<TableColumn[]>(null);
    private readonly remoteVpnGatewayList = signal<RemoteVpnGatewayViewModel[]>(null);

    private readonly selectedDomain$ = toObservable(this.domainAccountTreeStore.selectedDomain);
    private readonly selectedAccount$ = toObservable(this.domainAccountTreeStore.selectedAccount);

    constructor() {
        super();
        this.pagination = new RemoteVpnGatewayListRequest();
    }

    ngOnInit(): void {

        this.columns.set([
            {
                name: 'Name',
                prop: REMOTE_VPN_GATEWAY_LIST_CONSTANTS.nameKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                canAutoResize: true,
                resizeable: false,
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: REMOTE_VPN_GATEWAY_LIST_CONSTANTS.domainKey,
                name: 'Domain'
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: REMOTE_VPN_GATEWAY_LIST_CONSTANTS.accountKey,
                name: 'Account',
            },
            {
                name: 'CIDR',
                prop: REMOTE_VPN_GATEWAY_LIST_CONSTANTS.cidrKey,
                headerTemplate: this.headerTemplateSortable(),
                resizeable: false,
                canAutoResize: true,
                width: 100
            },
            {
                name: 'Remote IP Address',
                prop: REMOTE_VPN_GATEWAY_LIST_CONSTANTS.remoteIpAddressKey,
                headerTemplate: this.headerTemplateSortable(),
                resizeable: false,
                canAutoResize: true,
                width: 100
            },
            {
                name: 'IPsec Preshared-Key',
                prop: REMOTE_VPN_GATEWAY_LIST_CONSTANTS.ipsecPresharedKey,
                headerTemplate: this.headerTemplateSortable(),
                resizeable: false,
                canAutoResize: true,
                width: 100
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 120
            }
        ]);

        // When the user context has its own domain, do not pass account so the result includes all VMs in the domain
        const accountName = this.userContextService.currentUser.cloudInfraUserContext.hasMappedDomain ? null : this.userContextService.currentUser.cloudInfraUserContext.accountName;
        this.remoteVpnGatewayService.getRemoteVpnGatewayList()
            .pipe(take(1))
            .subscribe(res => {
                this.remoteVpnGatewayList.set(this.mapRemoteVpnGatewayListResponse(res));
                this.onDomainAccountChanged(this.userContextService.currentUser.cloudInfraUserContext.domainId, accountName);
            });

        this.selectedDomain$.pipe(
            skip(1),
            filter(domain => !!domain),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(domain => {
            this.onDomainAccountChanged(domain.id, this.domainAccountTreeStore.getAccount());
        });

        this.selectedAccount$.pipe(
            skip(1),
            filter(account => !!account),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(account => {
            this.onDomainAccountChanged(this.domainAccountTreeStore.getDomainId(), account.name);
        });

    }

    protected openCreateRemoteVpnGatewayModal(): void {
        if (this.networkingPermissionService.canCreateRemoteVpnGateway()) {
            const modalRef = this.modalService.openModalComponent(CreateRemoteVpnGatewayComponent, { size: 'lg' });
            (modalRef.componentInstance as CreateRemoteVpnGatewayComponent).domainId.set(this.domainAccountTreeStore.getDomainId());
            (modalRef.componentInstance as CreateRemoteVpnGatewayComponent).account.set(this.domainAccountTreeStore.getAccount());
            modalRef.closed
                .pipe(
                    take(1),
                    filter(jobId => !!jobId)
                )
                .subscribe();
        }
    }

    protected deleteRemoteVpnGateway(virtualPrivateCloudIdId: string): void {
        if (this.networkingPermissionService.canDeleteRemoteVpnGateway()) {
            this.modalService.openDeleteConfirmationDialog('Delete Remote VPN Gateway', 'Are you sure you want to delete this Remote VPN Gateway?', 'delete')
                .closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                    switchMap(() => this.remoteVpnGatewayService.deleteRemoteVpnGateway(virtualPrivateCloudIdId))
                ).subscribe();
        }
    }

    private onDomainAccountChanged(domainId: string, account: string): void {
        let columns = [...this.columns()];
        if (account) {
            columns = [...columns.filter(c => c.prop !== REMOTE_VPN_GATEWAY_LIST_CONSTANTS.accountKey && c.prop !== REMOTE_VPN_GATEWAY_LIST_CONSTANTS.domainKey)];
        } else if (domainId && !this.domainAccountTreeStore.isRootDomainSelected()) {
            columns = [...columns.filter(c => c.prop !== REMOTE_VPN_GATEWAY_LIST_CONSTANTS.domainKey)];
        }

        if (this.table().columns?.length) {
            this.table().columns = [...columns];
        }

        super.initialize(() => this.getRemoteVpnGatewayList$(), columns);
    }

    private mapRemoteVpnGatewayListResponse(remoteVpnGateways: RemoteVpnGateway[]): RemoteVpnGatewayViewModel[] {
        let remoteVpnGatewayViewModelList: RemoteVpnGatewayViewModel[] = [];
        if (remoteVpnGateways?.length > 0) {
            remoteVpnGatewayViewModelList = remoteVpnGateways.map(remoteVpnGateway => {
                const viewModel: RemoteVpnGatewayViewModel = {
                    account: remoteVpnGateway.account.trim(),
                    domain: remoteVpnGateway.domain.trim(),
                    domainId: remoteVpnGateway.domainid,
                    id: remoteVpnGateway.id,
                    cidr: remoteVpnGateway.cidrlist,
                    name: remoteVpnGateway.name?.trim() ?? '',
                    ipPresharedKey: remoteVpnGateway.ipsecpsk?.trim() ?? '',
                    remoteIpAddress: remoteVpnGateway.gateway?.trim() ?? '',
                };
                return viewModel;
            });
        }
        return remoteVpnGatewayViewModelList;
    }

    private getRemoteVpnGatewayList$(): Observable<ApiDatasetResult<RemoteVpnGatewayViewModel[]>> {
        // Apply filters, including selected domain and account and search term
        const filteredRemoteVpnGatewayList = [...this.remoteVpnGatewayList().filter(remoteVpnGateway => {
            const matchesDomain = this.domainAccountTreeStore.selectedDomain() ? remoteVpnGateway.domainId === this.domainAccountTreeStore.selectedDomain().id : true;
            const matchesAccount = this.domainAccountTreeStore.getAccount() ? remoteVpnGateway.account === this.domainAccountTreeStore.getAccount() : true;
            const matchesSearchTerm = !this.pagination.searchTerm ||
                remoteVpnGateway.name.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                remoteVpnGateway.cidr.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                remoteVpnGateway.ipPresharedKey.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                remoteVpnGateway.remoteIpAddress.toLowerCase().includes(this.pagination.searchTerm.toLowerCase());

            return matchesDomain && matchesAccount && matchesSearchTerm;
        })];

        // Apply sorting
        if (this.pagination.orderBy === REMOTE_VPN_GATEWAY_LIST_CONSTANTS.nameKey) {
            filteredRemoteVpnGatewayList.sort(sortByProperty(REMOTE_VPN_GATEWAY_LIST_CONSTANTS.nameKey as keyof RemoteVpnGatewayViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === REMOTE_VPN_GATEWAY_LIST_CONSTANTS.domainKey) {
            filteredRemoteVpnGatewayList.sort(sortByProperty(REMOTE_VPN_GATEWAY_LIST_CONSTANTS.domainKey as keyof RemoteVpnGatewayViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === REMOTE_VPN_GATEWAY_LIST_CONSTANTS.accountKey) {
            filteredRemoteVpnGatewayList.sort(sortByProperty(REMOTE_VPN_GATEWAY_LIST_CONSTANTS.accountKey as keyof RemoteVpnGatewayViewModel, this.pagination.orderDir === 'asc'));
        }

        // Apply pagination
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = startIndex + this.pagination.pageSize;
        const paginatedList = filteredRemoteVpnGatewayList.slice(startIndex, endIndex);

        return of({ data: paginatedList, totalCount: filteredRemoteVpnGatewayList.length });
    }

}

