import { FormControl } from '@angular/forms';
import { NetworkOfferingViewModel } from '../models/network-offering.view-model';

export interface IsolatedNetworkForm {
    name: FormControl<string>;
    description: FormControl<string>;
    networkOffering: FormControl<NetworkOfferingViewModel>;
    gateway: FormControl<string | null>;
    netmask: FormControl<string | null>;
    networkDomain: FormControl<string | null>;
    vpc: FormControl<string | null>;
}
