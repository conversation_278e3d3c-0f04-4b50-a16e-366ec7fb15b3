<div class="summary-container">
    <div class="header">
        <div class="title">Virtual Machine Summary</div>
        <div class="badge rounded-pill counter">{{ store.currentStep() + 1 }} / {{ store.totalSteps() }}</div>
    </div>

    <div class="content clickable" [class]="{'selected': store.currentStep() === steps.Setup}"
        (click)="navigateToStep(steps.Setup, store.isSetupAccessible())">
        <p class="title">
            {{ labels.SetupLabel }}
        </p>
        <p>
            <i class="fas fa-cloud me-2 text-secondary"></i>
            <span class="subtitle">Virtual Machine Name</span>
            - {{ store.setupStep.form.virtualMachineName() }}
        </p>
        <p>
            <i class="fas fa-building me-2 text-secondary"></i>
            <span class="subtitle">Zone<span class="required-asterisk">*</span></span>
            - {{ store.setupStep.form.zone()?.name }}
        </p>
        <p>
            <i class="fas fa-layer-group me-2 text-secondary"></i>
            <span class="subtitle">{{ store.setupStep.form.template () ? store.setupStep.form.osType() === osType.Template ? 'Template' : 'ISO' : 'Template or ISO' }}<span
                    class="required-asterisk">*</span></span>
            - {{ store.setupStep.form.template()?.name }}
    </div>

    <div class="content clickable" [class]="{
            'selected': store.currentStep() === steps.Compute,
            'step-disabled': !store.isComputeAccessible()
         }" (click)="navigateToStep(steps.Compute, store.isComputeAccessible())">
        <p class="title">
            {{ labels.ComputeLabel }}<span class="required-asterisk">*</span>
        </p>
        <p>
            <i class="fas fa-microchip me-2 text-secondary"></i>
            @if(store.computeStep.isValid()) {
            <span class="subtitle">{{ store.computeStep.form.serviceOffering().name }}</span>
            <span> - {{ store.computeStep.form.serviceOffering.cpuNumber() }} {{
                store.computeStep.form.serviceOffering.cpuNumber() > 1 ? 'vCPUs' : 'vCPU' }} / {{
                store.computeStep.form.serviceOffering.memory() }} MB</span>
            } @else {
            <span class="subtitle">No compute offering selected</span>
            }
        </p>
    </div>

    <div class="content clickable" [class]="{
            'selected': store.currentStep() === steps.Storage,
            'step-disabled': !store.isStorageAccessible()
         }" (click)="navigateToStep(steps.Storage, store.isStorageAccessible())">

        <p class="title">
            @if (store.setupStep.form.osType() === osType.ISO) {
                {{ labels.StorageLabel }}<span class="required-asterisk">*</span>
            } @else {
                {{ labels.StorageLabel }}
            }
        </p>

        <div class="mb-2 d-flex align-items-center">
            @if (store.storageStep.rootDisk()) {
                <span
                    class="border border-secondary rounded-circle me-2 d-inline-block text-center storage-step-disk-index">1</span>
                <span class="subtitle d-block storage-step-disk">
                    <i class="fas fa-server me-2"></i>{{ `${store.storageStep.rootDisk().offeringName} (Root Disk)${store.storageStep.rootDisk()?.diskSize ? ', ' + store.storageStep.rootDisk.diskSize() + ' GB' : '' } `}}
                </span>
            }
            @else {
                <span class="subtitle">No Storage Selected</span>
            }
        </div>

        @for (disk of store.storageStep.selectedDataDisks(); track $index) {
        <div class="mb-2 d-flex align-items-center">
            <span
                class="border border-secondary rounded-circle me-2 d-inline-block text-center storage-step-disk-index">{{$index
                + 2}}</span>
            <span class="subtitle d-block storage-step-disk">
                <i class="fas fa-server me-2"></i>
                {{disk.offeringName}}, {{ disk.diskSize }} GB
            </span>
        </div>
        }
    </div>

    <div class="content clickable" [class]="{
            'selected': store.currentStep() === steps.Network,
            'step-disabled': !store.isNetworkAccessible()
         }" (click)="navigateToStep(steps.Network, store.isNetworkAccessible())">
        <p class="title">
            {{ labels.NetworkLabel }}<span class="required-asterisk">*</span>
        </p>

        @for (disk of store.networkStep.selectedNetworks(); track $index) {
        <div class="mb-2 d-flex align-items-center">
            <span
                class="border border-secondary rounded-circle me-2 d-inline-block text-center storage-step-disk-index">{{$index
                + 1}}</span>
            <span class="subtitle d-block storage-step-disk">
                <i class="fas fa-network-wired me-2"></i>
                {{disk.name}} - {{disk.type}}
            </span>
        </div>
        } @empty {
        <div class="mb-2 d-flex align-items-center">
            <i class="fas fa-network-wired me-2 text-secondary"></i>
            <span class="subtitle">No network selected</span>
        </div>
        }
    </div>

    <div class="content clickable" [class]="{
            'selected': store.currentStep() === steps.AdvancedSettings,
            'step-disabled': !store.isAdvancedSettingsAccessible()
         }" (click)="navigateToStep(steps.AdvancedSettings, store.isAdvancedSettingsAccessible())">
        <p class="title">
            {{ labels.AdvancedSettingsLabel }}
        </p>

        <div>
            <p><span class="subtitle">Affinity Groups - </span>{{store.selectedAffinityGroupsNames().join(', ')}}</p>
            <p><span class="subtitle">SSH Key Pairs - </span>{{store.selectedSshKeyPairsNames().join(', ')}}</p>
            <p><span class="subtitle">Userdata - </span> {{store.advancedSettingsStep.selectedSettings.userdata()}}</p>
            <p><span class="subtitle">Keyboard Language -
                </span>{{store.advancedSettingsStep.selectedSettings.keyboardLanguage()}}</p>
        </div>
    </div>

    <div class="footer">
        <button type="button" id="reset-button" class="btn btn-outline-secondary" (click)="store.reset()">Clear</button>

        <div class="btn-group ms-2">
            <div class="btn-group" ngbDropdown role="group" #myDrop="ngbDropdown">
                <button [disabled]="!store.canCreateVirtualMachine()" type="button" class="btn btn-primary"
                    (click)="createVirtualMachine(true)">Create and power
                    on</button>
                <!-- eslint-disable-next-line @angular-eslint/template/elements-content -->
                <button [disabled]="!store.canCreateVirtualMachine()" type="button"
                    class="btn btn-primary dropdown-toggle-split" ngbDropdownToggle></button>
                <div class="dropdown-menu" ngbDropdownMenu>
                    <button [disabled]="!store.canCreateVirtualMachine()" class="btn" ngbDropdownItem
                        (click)="createVirtualMachine(true)">Create
                        and power on</button>
                    <button [disabled]="!store.canCreateVirtualMachine()" class="btn" ngbDropdownItem
                        (click)="createVirtualMachine(false)">Create
                        and do not power on</button>
                </div>
            </div>
        </div>


    </div>
</div>
