import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { OsTypeFilter } from '../../models/os-type-filter.enum';
import { TemplateViewModel } from '../../models/template.view.model';
import { OsType } from '../../models/os-type.enum';

@Component({
    selector: 'app-vm-media-selector',
    imports: [ReactiveFormsModule],
    templateUrl: './media-selector.component.html',
    styleUrl: './media-selector.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class MediaSelectorComponent {
    readonly mediaControl = input.required<FormControl<TemplateViewModel>>();
    readonly mediaTypeControl = input.required<FormControl<OsTypeFilter>>();
    readonly media = input<TemplateViewModel[]>([]);
    readonly selectedOsType = input<OsType>(OsType.Template);

    protected readonly osTypeFilter = OsTypeFilter;
    protected readonly osType = OsType;
}
