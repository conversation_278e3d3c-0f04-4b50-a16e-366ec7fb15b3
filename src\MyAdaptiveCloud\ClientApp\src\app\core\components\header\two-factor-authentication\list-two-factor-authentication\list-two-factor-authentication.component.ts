import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UserAuthenticator } from '@app/core/models/user-authenticator';
import { TwoFactorAuthenticationService } from '@app/core/services/two-factor-authentication.service';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { AuthService } from '@app/shared/services/auth.service';
import { ModalService } from '@app/shared/services/modal.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, take } from 'rxjs';
import { UpdateUserAuthenticatorComponent } from '../update-authenticator/update-user-authenticator.component';

@Component({
    selector: 'app-list-two-factor-authentication',
    imports: [NgxDatatableModule, TableActionComponent],
    templateUrl: './list-two-factor-authentication.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListTwoFactorAuthenticationComponent extends BaseListClientComponent<UserAuthenticator> implements OnInit {
    private readonly modalService = inject(ModalService);
    private readonly twoFactorAuthenticationService = inject(TwoFactorAuthenticationService);
    private readonly authService = inject(AuthService);

    protected readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Name',
                prop: 'name',
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false
            }
        ];

        super.initialize(this.twoFactorAuthenticationService.getUserAuthenticatorsList.bind(this.twoFactorAuthenticationService), columns);
    }

    protected updateAuthenticator(userAuthenticatorId: string, userAuthenticatorName: string): void {
        const modalRef = this.modalService.openModalComponent(UpdateUserAuthenticatorComponent);
        (modalRef.componentInstance as UpdateUserAuthenticatorComponent).userAuthenticatorId.set(userAuthenticatorId);
        (modalRef.componentInstance as UpdateUserAuthenticatorComponent).userAuthenticatorName.set(userAuthenticatorName);
        modalRef.closed.pipe(filter(res => !!res), take(1)).subscribe(() => {
            this.loadData();
        });
    }

    deleteAuthenticator(credentialId: string) {
        const isLastAuthenticator = this.rows.length === 1;

        const content = isLastAuthenticator ?
            'This is your only authenticator. If you delete this, you will have to re-validate your email address and set up a new method of two factor authentication the next time you login.' :
            'This will remove your method of two-factor authentication.';
        const userAcceptText = isLastAuthenticator ? 'I understand' : null;

        this.modalService.openDeleteConfirmationDialog('Delete authenticator', content, null, userAcceptText)
            .closed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(deleteConfirmed => {
                if (deleteConfirmed) {
                    if (!isLastAuthenticator) {
                        this.twoFactorAuthenticationService.deleteAuthenticator(credentialId).subscribe(() => {
                            this.loadData();
                        });
                    } else {
                        this.modalService.openConfirmationDialog({
                            title: 'Confirm',
                            content: 'Would you like to re-validate and set up your method of two factor authentication now?',
                            showCancelButton: true
                        }).closed
                            .pipe(takeUntilDestroyed(this.destroyRef))
                            .subscribe(revalidateCredentials => {
                                this.twoFactorAuthenticationService.deleteAuthenticator(credentialId).subscribe(() => {
                                    if (revalidateCredentials) {
                                        this.authService.logoutAndRedirectToLogin();
                                    } else {
                                        this.loadData();
                                    }
                                });
                            });
                    }
                }
            });
    }

    protected addAuthenticator() {
        this.modalService.openConfirmationDialog({
            title: 'Add authenticator',
            content: 'To add another authenticator to your account, you will need to log out and log back in.',
            confirmButtonText: 'Add authenticator',
            showCancelButton: true
        }).closed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(addConfirmed => {
                if (addConfirmed) {
                    this.twoFactorAuthenticationService.addAuthenticator().subscribe(() => {
                        this.modalService.openConfirmationDialog({
                            title: 'Confirm',
                            content: 'Would you like to log out now and add an authenticator? If not, this option will be available at your next login automatically.',
                            showCancelButton: true,
                            cancelButtonText: 'Later',
                            confirmButtonText: 'Now'
                        }).closed
                            .pipe(takeUntilDestroyed(this.destroyRef))
                            .subscribe(logoutConfirmed => {
                                if (logoutConfirmed) {
                                    this.authService.logoutAndRedirectToLogin();
                                }
                            });
                    });
                }
            });

    }

}
