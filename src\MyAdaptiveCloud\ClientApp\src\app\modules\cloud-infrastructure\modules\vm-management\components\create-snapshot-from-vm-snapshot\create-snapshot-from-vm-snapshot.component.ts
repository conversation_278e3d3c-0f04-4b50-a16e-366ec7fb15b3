import { Async<PERSON>ip<PERSON>, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { map, Observable } from 'rxjs';
import { CreateSnapshotFromVmSnapshotForm } from '../../forms/create-snapshot-from-vm-snapshot.form';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-create-snapshot-from-vm-snapshot',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgSelectComponent, AsyncPipe, DatePipe],
    templateUrl: './create-snapshot-from-vm-snapshot.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateSnapshotFromVmSnapshotComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);

    readonly virtualMachineId = signal<string>(null);
    readonly domainId = signal<string>(null);
    readonly account = signal<string>(null);
    readonly virtualMachineSnapshotId = signal<string>(null);
    readonly snapshotName = signal<string>(null);
    readonly snapshotCreatedDate = signal<Date>(null);

    protected volumes$: Observable<{ id: string, name: string }[]>;

    protected readonly isSubmitting = signal<boolean>(false);
    protected readonly form = this.formBuilder.group<CreateSnapshotFromVmSnapshotForm>({
        name: this.formBuilder.control<string>('', [Validators.required, Validators.maxLength(255)]),
        volumeId: this.formBuilder.control<string>('', Validators.required)
    });

    ngOnInit(): void {
        this.volumes$ = this.vmManagementService.getVolumesByVirtualMachine(this.virtualMachineId(), this.domainId(), this.account())
            .pipe(map(res => res?.map(volume => ({ id: volume.id, name: volume.name }))));
    }

    protected submit() {
        this.isSubmitting.set(true);
        if (this.form.valid) {
            this.vmManagementService.createSnapshotFromVirtualMachineSnapshot(this.virtualMachineSnapshotId(), this.form.value.volumeId, this.form.value.name).subscribe(jobId => {
                this.isSubmitting.set(false);
                this.activeModal.close(jobId);
            });
        }
    }

}
