import { AbstractControl, ValidationErrors } from '@angular/forms';
import { DeviceAlertThresholdTypeEnum } from '../models/device-alert-threshold-type.enum';

export function thresholdMetricValidator(deviceAlertThresholdType: number, maxValue: number, minValue = 1) {
    return (formGroup: AbstractControl): ValidationErrors | null => {
        const warning = +formGroup.get('warning')?.value;
        const error = +formGroup.get('error')?.value;
        const critical = +formGroup.get('critical')?.value;

        const thresholds = {
            warning,
            error,
            critical,
        };

        for (const [key, value] of Object.entries(thresholds)) {
            if (isNaN(value)) {
                return { validationError: 'All values must be a valid number.' };
            }

            if (!Number.isInteger(value)) {
                return { validationError: 'Please enter a whole number.' };
            }

            if (value > maxValue) {
                return { validationError: `${key.charAt(0).toUpperCase() + key.slice(1)} value must be smaller than ${maxValue}.` };
            }

            if (value < minValue && value !== 0) {
                return { validationError: `${key.charAt(0).toUpperCase() + key.slice(1)} value must be greater than ${minValue}.` };
            }
        }

        const isPercentageUsed = deviceAlertThresholdType === null || deviceAlertThresholdType === DeviceAlertThresholdTypeEnum.Percentage_Used;

        if (warning && error) {
            if (isPercentageUsed) {
                if (warning >= error) {
                    return { validationError: 'Warning must be less than Error value.' };
                }
            } else if (warning <= error) {
                return { validationError: 'Error must be less than Warning value.' };
            }
        }

        if (error && critical) {
            if (isPercentageUsed) {
                if (error >= critical) {
                    return { validationError: 'Error must be less than Critical value.' };
                }
            } else if (error <= critical) {
                return { validationError: 'Critical must be less than Error value.' };
            }
        }

        if (warning && critical) {
            if (isPercentageUsed) {
                if (warning >= critical) {
                    return { validationError: 'Warning must be less than Critical value.' };
                }
            } else if ((warning <= critical)) {
                return { validationError: 'Critical must be less than Warning value.' };
            }
        }

        if (!critical && !warning && !error) {
            return { validationError: 'At least one threshold is required.' };
        }

        return null;
    };
}

export function thresholdIntervalValidator(isRequired: boolean) {
    return (formControl: AbstractControl): ValidationErrors | null => {

        const value = +formControl.value;
        if (isRequired && value === 0) {
            return { required: 'value is required.' };
        }

        return null;
    };
}
