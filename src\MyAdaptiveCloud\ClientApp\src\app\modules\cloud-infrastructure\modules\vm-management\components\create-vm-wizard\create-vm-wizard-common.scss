@import "custom-variables.scss";

.title,
.subtitle {
    color: $secondary;
    font-weight: 600;
}

.title {
    font-size: 1rem;
}

.subtitle {
    font-size: 0.875rem;
}

table.options-table {
    border-collapse: separate;
    border-spacing: 0;
    border: var(--bs-border-width) solid var(--bs-border-color-translucent);
    border-radius: var(--bs-border-radius);
    overflow: hidden;

    tr:not(:last-child) td {
        border-bottom: 1px solid #ccc;
    }

    tr:nth-child(odd) {
        background-color: $datatable-row-details-bg;
    }

    tr:nth-child(even) {
        background-color: white;
    }

    th,
    td {
        font-size: 0.75rem;
        color: $secondary;
    }

    th {
        font-weight: 600;
        border-bottom: var(--bs-border-width) solid var(--bs-border-color-translucent);
        padding: 0.5rem 0.75rem;
        background-color: white;
    }

    td {
        padding: 0.5rem 0.75rem;
    }
}

// Lists
$description-size: 56px;
$description-radius: 15px;

.list-header {
    background-color: $secondary-05;
    border-radius: $description-radius;
    height: 41px;
}

.item-container {
    border-radius: $description-radius;
    height: $description-size;
    border: 1px solid $secondary-25;
}

.action-btn {
    width: $description-size;
    height: $description-size;
}

.item-index {
    height: 22px;
    width: 22px;
    border: 1px solid #ddd;
    border-radius: 10px;
    text-align: center;
}

.item-drag {
    cursor: grab;
    color: #9b9b9b;
}
