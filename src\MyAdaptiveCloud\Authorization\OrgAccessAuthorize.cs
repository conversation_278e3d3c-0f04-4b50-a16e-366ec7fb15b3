using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    ///     Verifies that the current user has a role is the supplied organization, either directly or via the organization hierarchy
    /// </summary>
    public class OrgAccessAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IIdentityService _identityService;
        private readonly bool _includeUnapprovedRoles;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public OrgAccessAuthorizeFilter(IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            bool includeUnapprovedRoles)
        {
            _identityService = identityService;
            _includeUnapprovedRoles = includeUnapprovedRoles;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                string val = AuthorizeFilterHelpers.GetEntityValue(context, "organizationId");
                _ = int.TryParse(val, out int organizationId);

                var hasUserRoles = _entityAuthorizationService.HasUserRolesByOrg(userId, organizationId, _includeUnapprovedRoles);
                if (hasUserRoles)
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, organizationId);
                }
                else
                {
                    context.Result = new ForbidResult();
                }
            }

            await Task.CompletedTask;
        }
    }


    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class OrgAccessAuthorizeAttribute : BaseAuthorizeAttribute
    {
        protected bool IncludeUnapprovedRoles;

        public OrgAccessAuthorizeAttribute(bool includeUnapprovedRoles)
            : base(typeof(OrgAccessAuthorizeFilter))
        {
            Arguments = new object[] { includeUnapprovedRoles };
        }
    }
}