import { inject, Injectable } from '@angular/core';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { map, Observable } from 'rxjs';
import { VmNetwork } from '../../../models/vm-network.model';
import { ListVmNetworkResponse } from '../responses/list-vm-network.response';

@Injectable({
    providedIn: 'root',
})
export class CreateVmNetworkService {
    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getNetworks(zoneId: string, domainId: string, account: string): Observable<VmNetwork[]> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listNetworks,
            listall: 'true',
            response: 'json',
            canuserfordeploy: 'true',
            zoneid: zoneId
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListVmNetworkResponse>(params)
            .pipe(map(response => (response?.listnetworksresponse?.network ?? [])
                .map(network => this.mapNetwork(network))));
    }

    private mapNetwork(network: VmNetwork): VmNetwork {
        return {
            id: network.id,
            name: network.name,
            cidr: network.cidr,
            vpcname: network.vpcname,
            type: network.type,
            gateway: network.gateway,
            ipaddress: '',
            macaddress: ''
        };
    }

}
