import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { AffinityGroup } from '../../models/affinity-group.model';
import { VmAffinityGroupsService } from '../../services/vm-affinity-groups.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { AddToAffinityGroupComponent } from './add-to-affinity-group.component';

describe('AddToAffinityGroupComponent', () => {

    let fixture: ComponentFixture<AddToAffinityGroupComponent>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;
    let mockVmManagementPermissionService: jasmine.SpyObj<VmManagementPermissionService>;

    const affinityGroups: AffinityGroup[] = [
        {
            account: 'account1',
            domain: 'domain1',
            id: 'group1',
            name: 'Affinity Group 1',
            type: 'host',
            domainid: 'domain1',
            description: 'Test Affinity Group 1',
            project: 'project1',
            projectid: 'project1',
            virtualmachineIds: 'vmId1'
        },
        {
            account: 'account1',
            domain: 'domain1',
            id: 'group2',
            name: 'Affinity Group 2',
            type: 'host',
            domainid: 'domain1',
            description: 'Test Affinity Group 2',
            project: 'project1',
            projectid: 'project1',
            virtualmachineIds: 'vmId2'
        }
    ];

    const affinityGroupTypes: { type: string }[] = [{ type: 'host' }, { type: 'zone' }, { type: 'pod' }];

    const affinityGroupsByVirtualMachine: AffinityGroup[] = [
        {
            account: 'account1',
            domain: 'domain1',
            id: 'group2',
            name: 'Affinity Group 2',
            type: 'host',
            domainid: 'domain1',
            description: 'Test Affinity Group 2',
            project: 'project1',
            projectid: 'project1',
            virtualmachineIds: 'vmId1'
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [AddToAffinityGroupComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementPermissionService)
            ]
        });

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;
        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmManagementPermissionService = TestBed.inject(VmManagementPermissionService) as jasmine.SpyObj<VmManagementPermissionService>;
        mockVmManagementPermissionService.canCreateAffinityGroup.and.returnValue(true);

        mockVmAffinityGroupsService.getAffinityGroupsByVirtualMachine.and.returnValue(of(affinityGroupsByVirtualMachine));
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of(affinityGroups));
        mockVmAffinityGroupsService.getAffinityGroupTypes.and.returnValue(of(affinityGroupTypes));
        mockVmAffinityGroupsService.createAffinityGroup.and.returnValue(of('group3'));
        mockVmAffinityGroupsService.updateAffinityGroupsForVirtualMachine.and.returnValue(of('jobId1'));

        fixture = TestBed.createComponent(AddToAffinityGroupComponent);
        fixture.componentInstance.domainId.set('domainId1');
        fixture.componentInstance.account.set('account1');
        fixture.componentInstance.virtualMachineId.set('vmId1');
    });

    describe('Initialization', () => {

        it('should initialize the component', () => {
            fixture.detectChanges();
            expect(mockVmAffinityGroupsService.getAffinityGroupsByVirtualMachine).toHaveBeenCalledOnceWith('vmId1');
            expect(mockVmAffinityGroupsService.getAffinityGroups).toHaveBeenCalledOnceWith('domainId1', 'account1');
            expect(mockVmAffinityGroupsService.getAffinityGroupTypes).toHaveBeenCalledTimes(1);
        });

        it('should only display the useExistingForm when canCreateAffinityGroup is false', () => {
            mockVmManagementPermissionService.canCreateAffinityGroup.and.returnValue(false);

            fixture.detectChanges();
            expect(fixture.debugElement.query(By.css('#useNewOrExistingForm'))).toBeNull();
            expect(fixture.debugElement.query(By.css('#createNewForm'))).toBeNull();
            expect(fixture.debugElement.query(By.css('#useExistingForm'))).not.toBeNull();
        });

        it('should display the create new form when canCreateAffinityGroup is true', () => {
            mockVmManagementPermissionService.canCreateAffinityGroup.and.returnValue(true);
            fixture.detectChanges();
            const useNewOrExistingForm = fixture.debugElement.query(By.css('#useNewOrExistingForm'));
            expect(useNewOrExistingForm).not.toBeNull();
            const createNewForm = fixture.debugElement.query(By.css('#createNewForm'));
            expect(createNewForm).not.toBeNull();
            const useExistingForm = fixture.debugElement.query(By.css('#useExistingForm'));
            expect(useExistingForm).toBeNull();
        });

    });

    describe('Component Interaction', () => {

        it('should close modal on cancel', () => {
            const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit a new affinity group', fakeAsync(() => {
            fixture.detectChanges();

            const nameInput = fixture.debugElement.query(By.css('#name'));
            nameInput.nativeElement.value = 'New Affinity Group';
            nameInput.nativeElement.dispatchEvent(new Event('input'));

            const descriptionInput = fixture.debugElement.query(By.css('#description'));
            descriptionInput.nativeElement.value = 'Description for new affinity group';
            descriptionInput.nativeElement.dispatchEvent(new Event('input'));

            selectOption(fixture, 'ng-select', 1, true, 0);

            fixture.detectChanges();

            const submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submitButton.click();
            fixture.detectChanges();

            expect(mockVmAffinityGroupsService.createAffinityGroup).toHaveBeenCalledOnceWith('New Affinity Group', 'Description for new affinity group', 'zone', 'domainId1', 'account1');
            expect(mockVmAffinityGroupsService.updateAffinityGroupsForVirtualMachine).toHaveBeenCalledOnceWith('vmId1', ['group2', 'group3']);
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        }));

        it('should submit a new affinity group', fakeAsync(() => {
            fixture.detectChanges();

            const useExistingRadio = fixture.debugElement.query(By.css('#useExisting'));
            useExistingRadio.nativeElement.click();
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 0);

            fixture.detectChanges();

            const submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submitButton.click();
            fixture.detectChanges();

            expect(mockVmAffinityGroupsService.createAffinityGroup).not.toHaveBeenCalled();
            expect(mockVmAffinityGroupsService.updateAffinityGroupsForVirtualMachine).toHaveBeenCalledOnceWith('vmId1', ['group2', 'group1']);
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        }));

    });

});

