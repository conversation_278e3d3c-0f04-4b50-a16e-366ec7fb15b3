import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { Neighbor } from '../../models/neighbor.model';
import { DDosMitigationValuePipe } from '../../pipes/ddos-mitigation-value.pipe';
import { DDoSMitigationNeighborsService } from '../../services/ddos-mitigation-neighbors.service';
import { DdosMitigationBgpNeighborsDetailsComponent } from '../ddos-mitigation-bgp-neighbors-details/ddos-mitigation-bgp-neighbors-details.component';

@Component({
    selector: 'app-ddos-mitigation-bgp-neighbors',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent],
    templateUrl: './ddos-mitigation-bgp-neighbors.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DdosMitigationBgpNeighborsComponent extends BaseListClientComponent<Neighbor> implements OnInit {
    private readonly ddosMitigationNeighborsService = inject(DDoSMitigationNeighborsService);
    private readonly modalService = inject(ModalService);

    private readonly headerTemplate = viewChild.required<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild.required<TemplateRef<never>>('actionsTemplate');
    private readonly prefixesSentTemplate = viewChild.required<TemplateRef<never>>('prefixesSentTemplate');

    ngOnInit(): void {

        const columns: TableColumn[] = ([
            {
                name: 'Neighbor IP',
                prop: 'ip',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Local AS',
                prop: 'localAs',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 100,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Remote AS',
                prop: 'remoteAs',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 100,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'State',
                prop: 'state',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Up/Down',
                prop: 'updown',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Prefixes Received',
                prop: 'prefixesReceived',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Prefixes Sent',
                prop: 'prefixesSent',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.prefixesSentTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false
            },
        ]);

        super.initialize(() => this.ddosMitigationNeighborsService.getList(), columns);
    }

    protected viewDetails(item: Neighbor): void {
        const modalRef = this.modalService.openModalComponent(DdosMitigationBgpNeighborsDetailsComponent);
        (modalRef.componentInstance as DdosMitigationBgpNeighborsDetailsComponent).neighborDetails.set(item);
    }

}
