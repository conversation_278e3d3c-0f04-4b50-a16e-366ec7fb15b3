<div>
    <p class="title">
        @if (isIso()) {
            {{ label }}<span class="required-asterisk">*</span>
        } @else {
            {{ label }}
        }
    </p>

    <div class="list-header storage-list-layout border border-1 px-3 mb-2 color-secondary">
        <div class="column-1">Disk</div>
        <div class="column-2">Disk Offering</div>
        <div class="column-3">Disk Size</div>
    </div>

    <ul class="list-unstyled">
        <li>
            <form [formGroup]="selectRootDiskForm">
                <div class="item-container storage-list-layout bg-white px-3 me-1 flex-grow-1">

                    <div class="p-0 d-flex align-items-center justify-content-between column-1">
                        <div class="item-index">1</div>
                        <span class="d-block p-0 item-drag"></span>
                    </div>

                    <div class="align-items-center column-2">
                        @if (isIso()) {
                            <ng-select [items]="diskOptions()" bindLabel="offeringName"
                                placeholder="Select Disk Offering" formControlName="offeringSelect" [clearable]="false">

                                <ng-template ng-label-tmp let-item="item">
                                    <span>{{item.offeringName}}</span>
                                </ng-template>

                                <ng-template ng-option-tmp let-item="item">
                                    <span>{{item.offeringName}} ({{item.description}})</span>
                                </ng-template>

                            </ng-select>
                        } @else {
                            <div class="column-2">
                                <input class="form-control" type="text" disabled="true"
                                    [value]="store.storageStep.rootDisk?.description()">
                            </div>
                        }
                    </div>
                        <div class="d-flex align-items-center column-3">
                            @if (selectRootDiskForm.controls.offeringSelect.value) {
                                @if (selectRootDiskForm.controls.offeringSelect.value.isCustomized || !isIso()) {
                                    <input type="number" autocomplete="off" id="inputDiskSize" step="1"
                                        class="form-control storage-item-disk-size-input" formControlName="diskCustomSize">
                                    <label for="inputDiskSize" class="col-form-label ms-2">GB</label>
                                    <span class="ms-2">
                                        <i [ngbPopover]="`${!isIso() ? 'If the Root disk size is left empty the root disk will be created with its default size.' : 'Root Disk Size.' } Value must be between ${customOfferingMinimumSize()} and ${diskSizeCustomOfferingMaxValue} GB`"
                                            triggers="hover" class="col-1 text-secondary fa-solid fa-circle-question"></i>
                                    </span>
                                } @else {
                                    <span class="ms-2">{{ store.storageStep.rootDisk()?.diskSize }} GB</span>
                                }
                            }
                    </div>
                </div>
            </form>
        </li>

        <div cdkDropList (cdkDropListDropped)="drop($event)">
            @for (item of store.storageStep.selectedDataDisks(); track $index) {
                <li class="mt-1 d-flex">
                    <div cdkDrag cdkDragLockAxis="y" [cdkDragData]="item"
                        class="item-container storage-list-layout bg-white px-3 me-1 flex-grow-1">

                        <div class="p-0 d-flex align-items-center justify-content-between column-1">
                            <div class="item-index">{{$index + 2}}</div>
                        </div>

                        <div class="column-2 d-flex flex-nowrap align-items-center gap-2">
                            <div class="p-0 pt-1 item-drag">
                                <i class="fa-solid fa-grip-vertical"></i>
                            </div>
                            <input class="form-control" type="text" disabled="true" [value]="item.offeringName">
                        </div>

                        <div class="d-flex align-items-center column-3">
                            <span class="ms-2 text-start">{{item.diskSize}} GB</span>
                        </div>
                    </div>
                    <button id="remove-disk" class="btn btn-danger storage-item-remove-btn action-btn"
                        (click)="removeDisk($index)">
                        <i class="fa-solid fa-close"></i>
                    </button>
                </li>
            }
        </div>

        @if (!isIso() || (isIso() && selectRootDiskForm.controls.offeringSelect.value)) {
            <li class="mt-1 d-flex">
                <form [formGroup]="selectDataDisksForm" class="flex-grow-1">
                    <div class="item-container storage-list-layout bg-white px-3 me-1 ">
                        <div class="p-0 d-flex align-items-center justify-content-between column-1">
                            <div class="item-index">{{store.storageStep.selectedDataDisks().length + 2}}</div>
                        </div>

                        <div class="align-items-center column-2">
                            <ng-select [items]="diskOptions()" bindLabel="offeringName" [clearable]="false"
                                placeholder="Select Disk Offering" formControlName="offeringSelect">

                                <ng-template ng-label-tmp let-item="item">
                                    <span>{{item.offeringName}}</span>
                                </ng-template>

                                <ng-template ng-option-tmp let-item="item">
                                    <span>{{item.offeringName}} ({{item.description}})</span>
                                </ng-template>

                            </ng-select>
                        </div>

                        <div class="d-flex align-items-center column-3">
                            @if (selectDataDisksForm.controls.offeringSelect.value) {
                                @if (selectDataDisksForm.controls.offeringSelect.value.isCustomized) {
                                    <input formControlName="diskCustomSize" type="number" id="inputDiskSize" step="1"
                                        class="form-control storage-item-disk-size-input">
                                    <label for="inputDiskSize" class="col-form-label ms-2">GB</label>
                                    <span class="ms-2">
                                        <i [ngbPopover]="`Value must be between ${diskSizeCustomOfferingMinValue} and ${diskSizeCustomOfferingMaxValue} GB`"
                                            triggers="hover" class="col-1 text-secondary fa-solid fa-circle-question"></i>
                                    </span>
                                } @else {
                                    <span class="col-form-label ms-2">{{ selectDataDisksForm.controls.offeringSelect.value.diskSize }} GB</span>
                                }
                            }
                        </div>
                    </div>
                </form>
                <button id="add-disk" class="btn btn-primary storage-item-add-btn action-btn" (click)="addDisk(selectDataDisksForm.value.offeringSelect)"
                    [disabled]="selectDataDisksForm.invalid">
                    <i class="fa-solid fa-plus"></i>
                </button>
            </li>
        }
    </ul>
</div>
