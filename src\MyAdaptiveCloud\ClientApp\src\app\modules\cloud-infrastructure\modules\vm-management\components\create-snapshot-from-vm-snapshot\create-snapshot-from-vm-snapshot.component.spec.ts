import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { CreateSnapshotFromVmSnapshotComponent } from './create-snapshot-from-vm-snapshot.component';

describe('CreateSnapshotFromVmSnapshotComponent', () => {

    let component: CreateSnapshotFromVmSnapshotComponent;
    let fixture: ComponentFixture<CreateSnapshotFromVmSnapshotComponent>;
    let mockVmManagementervice: jasmine.SpyObj<VmManagementService>;
    let mockActiveModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateSnapshotFromVmSnapshotComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService)
            ]
        })
            .compileComponents();

        mockVmManagementervice = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementervice.getVolumesByVirtualMachine.and.returnValue(of([
            {
                id: 'volume-id',
                name: 'Volume 1'
            },
            {
                id: 'volume-id-2',
                name: 'Volume 2'
            }
        ]));
        mockVmManagementervice.createSnapshotFromVirtualMachineSnapshot.and.returnValue(of('job-id'));

        mockActiveModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        fixture = TestBed.createComponent(CreateSnapshotFromVmSnapshotComponent);
        component = fixture.componentInstance;
        component.virtualMachineId.set('test-id');
        component.account.set('test-account');
        component.domainId.set('test-domain');
        component.snapshotCreatedDate.set(new Date('2023-10-01T12:00:00Z'));
        component.snapshotName.set('test-snapshot');
        component.virtualMachineSnapshotId.set('snapshot-id');

        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should load volumes', () => {
            expect(mockVmManagementervice.getVolumesByVirtualMachine).toHaveBeenCalledOnceWith('test-id', 'test-domain', 'test-account');
        });

    });

    describe('Submit', () => {

        it('should close modal on cancel', () => {
            const cancelButton = fixture.debugElement.query(By.css('[data-testid="cancel-button"]')).nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(mockActiveModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit form', fakeAsync(() => {
            const nameInput = fixture.debugElement.query(By.css('[data-testid="name-input"]')).nativeElement as HTMLInputElement;
            nameInput.value = 'New Snapshot Name';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 0);

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementervice.createSnapshotFromVirtualMachineSnapshot).toHaveBeenCalledOnceWith('snapshot-id', 'volume-id-2', 'New Snapshot Name');
            expect(mockActiveModal.close).toHaveBeenCalledOnceWith('job-id');
        }));

    });

});

