﻿using AutoMapper;
using MyAdaptiveCloud.Api.Requests.Ontap;
using MyAdaptiveCloud.Services.DTOs.Configuration;
using MyAdaptiveCloud.Services.DTOs.FileShares;

namespace MyAdaptiveCloud.Api.AutoMapper.FileServer
{
    public class FileServerMapperProfile : Profile
    {
        public FileServerMapperProfile()
        {
            CreateMap<LocalUserCreateRequest, Services.Requests.FileServer.CreateLocalUserRequest>();
            CreateMap<LocalUserModificationRequest, Services.Requests.FileServer.UpdateLocalUserRequest>();
            CreateMap<LocalGroupCreateOrModifyRequest, Services.Requests.FileServer.CreateOrModifyLocalGroupRequest>();
            CreateMap<NetworkFileServersConfigurationDTO, NetworkFileServerConfigurationDTO>();
        }
    }
}