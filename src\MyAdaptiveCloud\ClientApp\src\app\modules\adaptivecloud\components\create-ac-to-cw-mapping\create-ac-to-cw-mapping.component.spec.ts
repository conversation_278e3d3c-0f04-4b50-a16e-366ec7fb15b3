import { TestBed } from '@angular/core/testing';
import { NotificationService } from '@app/shared/services/notification.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';
import { CreateAcToCwMappingComponent } from './create-ac-to-cw-mapping.component';

describe('CreateAcToCwMappingComponent', () => {

    let component: CreateAcToCwMappingComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                CreateAcToCwMappingComponent
            ],
            providers: [
                provideMock(AcToCwMappingService),
                provideMock(NgbActiveModal),
                provideMock(NotificationService)
            ]
        });

        component = TestBed.createComponent(CreateAcToCwMappingComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
