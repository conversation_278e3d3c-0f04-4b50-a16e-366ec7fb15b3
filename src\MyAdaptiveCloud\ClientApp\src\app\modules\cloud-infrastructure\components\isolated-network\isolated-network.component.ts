import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { IsolatedNetworkForm } from '@app/modules/cloud-infrastructure/forms/isolated-network.form';
import { NetworkOfferingViewModel } from '@app/modules/cloud-infrastructure/models/network-offering.view-model';
import { VpcViewModel } from '@app/modules/cloud-infrastructure/models/vpc.view-model';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { networkDomainValidator } from '../../validators/network-validators';

@Component({
    selector: 'app-add-network-isolated',
    imports: [ReactiveFormsModule, NgSelectComponent, NgbPopover],
    templateUrl: './isolated-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IsolatedNetworkComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly networkOfferings = input.required<NetworkOfferingViewModel[]>();
    protected readonly vpcs = input.required<VpcViewModel[]>();

    public form = this.formBuilder.group<IsolatedNetworkForm>({
        description: this.formBuilder.control<string | null>(null, [Validators.required, Validators.maxLength(255)]),
        gateway: this.formBuilder.control(null, Validators.maxLength(100)),
        name: this.formBuilder.control<string | null>(null, [Validators.required, Validators.maxLength(255)]),
        netmask: this.formBuilder.control(null, Validators.maxLength(100)),
        networkDomain: this.formBuilder.control(null, [networkDomainValidator()]),
        networkOffering: this.formBuilder.control(null, Validators.required),
        vpc: this.formBuilder.control(null)
    });

    ngOnInit(): void {
        this.form.controls.networkOffering.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(networkOffering => {
                if (networkOffering.forVPC) {
                    this.form.controls.networkDomain.setValue(null);
                    this.form.controls.vpc.addValidators(Validators.required);
                    this.form.controls.gateway.addValidators(Validators.required);
                    this.form.controls.netmask.addValidators(Validators.required);
                } else {
                    this.form.controls.vpc.setValue(null);
                    this.form.controls.vpc.removeValidators(Validators.required);
                    this.form.controls.gateway.removeValidators(Validators.required);
                    this.form.controls.netmask.removeValidators(Validators.required);
                }
                this.form.controls.vpc.updateValueAndValidity({ emitEvent: false });
                this.form.controls.gateway.updateValueAndValidity({ emitEvent: false });
                this.form.controls.netmask.updateValueAndValidity({ emitEvent: false });
                this.form.controls.networkDomain.updateValueAndValidity({ emitEvent: false });
            });

    }

}
