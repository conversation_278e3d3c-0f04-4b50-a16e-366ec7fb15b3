import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { RebootVmComponent } from './reboot-vm.component';

describe('RebootVmComponent', () => {

    let component: RebootVmComponent;
    let fixture: ComponentFixture<RebootVmComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [RebootVmComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(RebootVmComponent);
        component = fixture.componentInstance;
        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.rebootVirtualMachine.and.returnValue(of('jobId1'));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.virtualMachineId = 'test-id';
        fixture.detectChanges();
    });

    it('should close modal on cancel', () => {
        const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
        cancelButton.click();
        fixture.detectChanges();

        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    it('should close modal with response on successful rebootVirtualMachine call', () => {
        const forceStopControl = fixture.debugElement.query(By.css('#boot-delay')).nativeElement as HTMLInputElement;
        forceStopControl.value = '10';
        forceStopControl.dispatchEvent(new Event('input'));
        fixture.detectChanges();

        const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
        submit.click();
        fixture.detectChanges();

        expect(mockVmManagementService.rebootVirtualMachine).toHaveBeenCalledOnceWith('test-id', 10);
        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    it('should not submit the call with an invalid boot delay value', () => {
        const forceStopControl = fixture.debugElement.query(By.css('#boot-delay')).nativeElement as HTMLInputElement;
        forceStopControl.value = '100';
        forceStopControl.dispatchEvent(new Event('input'));
        fixture.detectChanges();

        const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
        submit.click();
        fixture.detectChanges();

        expect(mockVmManagementService.rebootVirtualMachine).not.toHaveBeenCalled();
        expect(activeModal.close).not.toHaveBeenCalled();
    });

});

