import { ComponentFixture, TestBed } from '@angular/core/testing';

import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { HostForMigration } from '../../models/host-for-migration.model';
import { VmManagementService } from '../../services/vm-management.service';
import { MigrateHostComponent } from './migrate-host.component';

describe('MigrateHostComponent', () => {
    let component: MigrateHostComponent;
    let fixture: ComponentFixture<MigrateHostComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockActiveModal: jasmine.SpyObj<NgbActiveModal>;

    const host1: HostForMigration = {
        capabilities: 'hvm,snapshot',
        clusterid: '6fdfeb59-ea6a-48b5-b6a6-79f8dc4c8601',
        clustername: 'KVM01',
        clustertype: 'CloudManaged',
        cpuallocated: '80.65%',
        cpuallocatedpercentage: '80.65%',
        cpuallocatedvalue: 5000,
        cpuallocatedwithoverprovisioning: '20.16%',
        cpunumber: 2,
        cpuspeed: 3100,
        cpuused: '25.7%',
        cpuwithoverprovisioning: '24800',
        created: '2021-06-07T23:35:16+0000',
        disconnected: '2024-06-28T18:48:45+0000',
        events: 'Ping; Remove; HostDown; ShutdownRequested; AgentUnreachable; ManagementServerDown; StartAgentRebalance; AgentDisconnected; AgentConnected; PingTimeout',
        hahost: false,
        hypervisor: 'KVM',
        id: '2d729895-1d7f-4f1f-955a-9baf71c43b3a',
        ipaddress: '************',
        islocalstorageactive: false,
        jobstatus: 0,
        lastpinged: '1970-01-20T13:39:28+0000',
        managementserverid: 345051875226,
        memoryallocated: '29.58%',
        memoryallocatedbytes: **********,
        memoryallocatedpercentage: '29.58%',
        memorytotal: ***********,
        memoryused: **********,
        memorywithoverprovisioning: '***********',
        name: 'dev01kvm01.ipplab.corp',
        networkkbsread: ********,
        networkkbswrite: 0,
        podid: 'e7a69254-b73f-428e-8c72-a3a4821c9dcd',
        podname: 'Pod01',
        requiresStorageMotion: false,
        resourcestate: 'Enabled',
        state: 'Up',
        suitableformigration: true,
        type: 'Routing',
        version: '********',
        zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
        zonename: 'DC1'
    };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [MigrateHostComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService)
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(MigrateHostComponent);
        component = fixture.componentInstance;
        mockActiveModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.findHostsForMigration.and.returnValue(of({ host: [host1], count: 2 }));
        mockVmManagementService.migrateVirtualMachineHost.and.returnValue(of('job-id'));

        component.virtualMachineId = 'test-id';
        fixture.detectChanges();

    });

    it('Initialization', () => {
        expect(component).toBeTruthy();
        expect(mockVmManagementService.findHostsForMigration).toHaveBeenCalledOnceWith('test-id');
    });

    describe('Submit', () => {

        it('should be disabled when no host is selected', () => {
            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            expect(submit.disabled).toBeTrue();
            submit.click();
            fixture.detectChanges();
            expect(mockVmManagementService.migrateVirtualMachineHost).not.toHaveBeenCalled();
        });

        it('should submit with the selected host when one is selected on the table ', () => {
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));

            const rows = dataTableDebugElement.queryAll(By.css('datatable-row-wrapper'));
            (rows[0].query(By.css('.btn.btn-link.p-0')).nativeElement as HTMLButtonElement).click();
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.migrateVirtualMachineHost).toHaveBeenCalledOnceWith('test-id', '2d729895-1d7f-4f1f-955a-9baf71c43b3a');
            expect(mockActiveModal.close).toHaveBeenCalledTimes(1);
        });

    });

});
