using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Api.Requests.Reports;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class OrganizationRecipientsAuthorizeFilter(
        IIdentityService identityService,
        IEntityAuthorizationService entityAuthorizationService,
        IUserContextService userContextService,
        Perms[] perms,
        int distance,
        string name) : BaseAsyncBulkAuthorizationFilter(perms, distance, name)
    {
        public async override Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            int userId = identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
                context.Result = new UnauthorizedResult();
            else
            {
                context.ActionArguments.TryGetValue("organizationId", out var orgId);
                int organizationId = (int)orgId;
                var organizationExists = await entityAuthorizationService.OrganizationExists(organizationId, false);
                if (organizationExists)
                {
                    // If the request body parameter is not found, then the request is invalid
                    context.ActionArguments.TryGetValue(_requestName, out var request);

                    if (request == null)
                        context.Result = new ForbidResult();
                    else
                    {
                        if (_perms != null && _perms.Length > 0)
                        {
                            if (!userContextService.HasPermission(userId, organizationId, _distance, _perms))
                                context.Result = new ForbidResult();
                            else
                            {
                                AuthorizeFilterHelpers.SetOrganizationId(context, organizationId);
                                var recipientsOrganizations = request.GetType().GetProperty(_name).GetValue(request, null) as List<OrganizationRecipients>;

                                // If there are any elements in the list, proceed to authorize each one, otherwise, treat as a non-op and proceed to the next action in the pipeline 
                                if (recipientsOrganizations.Any())
                                {
                                    var descendantOrganizationsId = entityAuthorizationService.GetDescendantOrganizationsId(organizationId).ToHashSet();
                                    descendantOrganizationsId.Add(organizationId);
                                    var recipientsOrganizationsId = recipientsOrganizations.Select(o => o.OrganizationId).ToHashSet();
                                    var allOrgsAuthorized = recipientsOrganizationsId.IsSubsetOf(descendantOrganizationsId);

                                    if (allOrgsAuthorized)
                                        await next();
                                    else
                                        context.Result = new ForbidResult();
                                }
                                else
                                    await next();
                            }
                        }
                        else
                            context.Result = new ForbidResult();
                    }
                }
                else
                    context.Result = new BadRequestResult();
            }
        }
    }

    public class OrganizationRecipientsAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public OrganizationRecipientsAuthorizeAttribute(params Perms[] perms) : base(typeof(OrganizationRecipientsAuthorizeFilter), perms)
        {
            Name = "OrganizationRecipients";
        }
    }
}