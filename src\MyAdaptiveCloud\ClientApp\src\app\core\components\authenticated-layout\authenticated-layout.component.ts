import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { UserContextService } from '@app/shared/services/user-context.service';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';
import { LoadingComponent } from '../loading/loading.component';
import { NotificationComponent } from '../notification/notification.component';
import { SidebarComponent } from '../sidebar/sidebar.component';

@Component({
    selector: 'app-authenticated-layout',
    imports: [AsyncPipe, FooterComponent, SidebarComponent, NotificationComponent, LoadingComponent, HeaderComponent, RouterOutlet],
    templateUrl: './authenticated-layout.component.html',
    styleUrl: './authenticated-layout.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AuthenticatedLayoutComponent implements OnInit {
    private readonly router = inject(Router);
    protected readonly userContextService = inject(UserContextService);

    ngOnInit(): void {
        if (this.router.url === '/') {
            this.router.navigate(['/home']);
        }
    }

}
