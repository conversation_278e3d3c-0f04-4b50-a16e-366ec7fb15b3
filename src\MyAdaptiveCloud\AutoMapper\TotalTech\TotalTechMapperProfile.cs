﻿using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.Files;
using MyAdaptiveCloud.Services.DTOs.TotalTech;

namespace MyAdaptiveCloud.Api.AutoMapper.TotalTech
{
    public class TotalTechMapperProfile : Profile
    {
        public TotalTechMapperProfile()
        {
            CreateMap<FileAdministrationDTO, TotalTechDocumentDTO>()
                .ForMember(dest => dest.DocumentId, option => option.MapFrom(src => src.FileAdministrationId))
                .ForMember(dest => dest.Name, option => option.MapFrom(src => src.Name))
                .ForMember(dest => dest.LastUpdated, option => option.MapFrom(src => src.UploadDate));
        }
    }
}

