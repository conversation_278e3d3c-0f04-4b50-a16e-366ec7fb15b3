<ngx-datatable #table class="table bootstrap no-detail-row" />

<ng-template #currencyCellTemplate let-value="value" ngx-datatable-cell-template>
  @if (value > 0) {
    {{ value | currency }}
  } @else {
    -
  }
</ng-template>

<ng-template #quantityCellTemplate let-value="value" ngx-datatable-cell-template>
  @if (value > 0) {
    {{ value | number:'1.0-3' }}
  } @else {
    -
  }
</ng-template>


<ng-template #summaryTemplate let-column="column" let-value="value" ngx-datatable-cell-template>
  <div class="summary-container">
    <div class="chip" container="body">
      @if (column.prop === 'cost') {
        <strong><span class="chip-content">{{ getTotal() | currency }}</span></strong>
      }
      @if (column.prop === 'type') {
        <strong><span class="chip-content">{{ descriptionLabel() }}</span></strong>
      }
    </div>
  </div>
</ng-template>