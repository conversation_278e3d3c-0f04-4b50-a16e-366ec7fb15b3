<div class="modal-header">
  <h4 class="modal-title">Manage Folders</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="close()"></button>
</div>
<div class="modal-body p-0">
  <div class="d-flex">
    <div class="col-5 tree-container overflow-auto">
      <app-device-folders-tree [folder]="foldersTreeStore.foldersRoot$ | async" [showDevices]="false"
        [showCounts]="true" [allowDragAndDrop]="true" [showDiscoveredDevicesFolders]="true" />
    </div>
    <div class="col-7 border-start ps-3 pt-2 tree-container overflow-auto">
      @if ((foldersTreeStore.selectedFolder$ | async); as folder) {
        <app-device-folders-breadcrumb
          [folder]="folder"
          [isNavigable]="true"
          (breadcrumbItemSelected)="selectFolder($event)"/>
        <div class="container">
          <div class="p-2 d-flex item">
            <h5 class="col-9 text-truncate lh-sm">{{ folder?.name() }}</h5>
            @if (!folder.isUnassignedDevicesFolder()) {
              @if (canEditFolders) {
                <span class="text-secondary ms-4 clickable">
                  <i title="Add new folder" (click)="openCreateFolderModal(folder)"
                  class="add-folder-btn fa-solid fa-folder-plus"></i>
                </span>
              }
              @if (!canEditFolders) {
                <span class="text-secondary ms-4 disabled-icon">
                  <i title="Add new folder" class="add-folder-btn fa-solid fa-folder-plus"></i>
                </span>
              }
              @if (canEditFolders) {
                <span class="text-secondary ms-2 clickable">
                  <i title="Edit folder {{folder.name()}}" (click)="openEditFolderModal(folder)"
                  class="fas fa-pen-to-square"></i>
                </span>
              }
              @if (!canEditFolders) {
                <span class="text-secondary ms-2 disabled-icon">
                  <i title="Edit folder {{folder.name()}}" class="fas fa-pen-to-square"></i>
                </span>
              }
              @if (canEditFolders && !folder.isOrganizationFolder()) {
                <span
                  class="text-secondary ms-2 clickable" (click)="openDeleteFolderModal(folder)">
                  <i title="Delete folder {{folder.name()}}" class="far fa-trash-can"></i>
                </span>
              }
              @if (!canEditFolders && !folder.isOrganizationFolder()) {
                <span
                  class="text-secondary ms-2 clickable disabled-icon">
                  <i title="Delete folder {{folder.name()}}" class="far fa-trash-can"></i>
                </span>
              }
            }
          </div>
        </div>
        <app-device-folders-detail-panel />
      }
    </div>
  </div>
</div>
