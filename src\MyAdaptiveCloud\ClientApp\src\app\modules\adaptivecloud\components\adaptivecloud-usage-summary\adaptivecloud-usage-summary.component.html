<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap" />
    </div>
</div>

<ng-template #usageTemplate let-value="value" let-column="column" ngx-datatable-cell-template>
    @if (column.prop !== 'accountTotal') {
    <div [ngbPopover]="usageTooltip" triggers="hover" container="body">
        @if (value.totalCost > 0 || value.quantity > 0) {
        {{
        value.totalCost | currency }}
        } @else {
        -
        }
    </div>
    <ng-template #usageTooltip>
        @if (value.totalCost > 0 || value.quantity > 0) {
        @if (column.prop !== 'accountName' && column.prop !== 'accountTotal' && column.name !== 'Actions' && column.prop
        !== 'licensing') {
        {{ value.quantity | number:'1.0-3' }} @if (column.prop !== 'ipAddress') {
        <span>
            {{ value.unitDescription }} </span>
        }@if (column.prop !== 'networkBytes') {
        <span>
            &#64; {{ value.unitCost | currency }} each</span>
        }
        }
        @if (column.prop === 'licensing') {
        @for (license of value.licenses; track license) {
        <span>
            {{ license.unitDescription }}: {{ license.quantity | number:'1.0-3' }} &#64; {{ license.unitCost |
            currency
            }}<br>
        </span>
        }
        }
        } @else {
        No Usage
        }
    </ng-template>
    }
    @if (column.prop === 'accountTotal' && value >= 0) {
    {{ value | currency }}
    }
</ng-template>

<ng-template #accountNameTemplate let-row="row">
    @if (toItem(row); as row) {
        <div class="clickable" (click)="showAccountDetails(row)">
            <u>{{ row.accountName }}</u>
        </div>
    }
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        <app-table-action [icon]="'fa-solid fa-magnifying-glass-plus'" [enabled]="true" [title]="'View Account Details'"
            (clickHandler)="showAccountDetails(row)" />
    }
</ng-template>

<ng-template #summaryTemplate let-column="column">
    <div class="summary-container">
        <div class="chip" [ngbPopover]="totalSummary"
            [disablePopover]="column.prop === 'accountName' || column.prop === 'accountTotal'" triggers="hover"
            container="body">
            @if (column.prop === 'accountName') {
            <strong><span class="chip-content">Totals</span></strong>
            }
            @if (column.prop === 'accountTotal') {
            <strong><span class="chip-content">{{ total().accountTotal | currency }}</span></strong>
            }
            @if (column.prop !== 'accountName' && column.prop !== 'accountTotal') {
            <strong><span class="chip-content">{{ total()[column.prop].totalCost | currency }}</span></strong>
            }
        </div>
        <ng-template #totalSummary>
            @if (column.prop !== 'accountName' && column.prop !== 'accountTotal' && column.prop !== 'licensing') {
            <strong>
                <span class="chip-content">
                    {{ total()[column.prop].quantity | number:'1.0-3' }} @if (column.prop !== 'ipAddress') {
                    <span>
                        {{ total()[column.prop].unitDescription }} </span>
                    }@if (column.prop !== 'networkBytes') {
                    <span>
                        &#64; {{ total()[column.prop].unitCost | currency }} each</span>
                    }
                </span>
            </strong>
            }
            @if (column.prop === 'licensing') {
            @for (license of total()[column.prop].licenses; track license) {
            <span>
                <strong>
                    {{ license.unitDescription }}: {{ license.quantity | number:'1.0-3' }} &#64; {{ license.unitCost |
                    currency }}<br>
                </strong>
            </span>
            }
            }
        </ng-template>
    </div>
</ng-template>
