import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AgentManagementContainerComponent } from './agent-management-container.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { ActivatedRoute } from '@angular/router';

describe('AgentManagementContainerComponent', () => {
    let component: AgentManagementContainerComponent;
    let fixture: ComponentFixture<AgentManagementContainerComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [AgentManagementContainerComponent],
            providers: [provideMock(ActivatedRoute)]
        })
            .compileComponents();

        fixture = TestBed.createComponent(AgentManagementContainerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
