import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbDropdown, NgbDropdownItem, NgbDropdownMenu } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { AffinityGroup } from '../../../../models/affinity-group.model';
import { SSHKeyPair } from '../../../../models/ssh-key-pair';
import { VmAffinityGroupsService } from '../../../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../../../services/vm-management.service';
import { VmMediaService } from '../../../../services/vm-media-service';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { DiskOffering } from '../../models/disk-offering.model';
import { CreateVmComputeService } from '../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../services/create-vm-network.service';
import { CreateVmService } from '../../services/create-vm-service';
import { CreateVmStorageService } from '../../services/create-vm-storage.service';
import { CreateVmWizardSummaryComponent } from './summary.component';

describe('CreateVmWizardSummaryComponent', () => {
    let component: CreateVmWizardSummaryComponent;
    let fixture: ComponentFixture<CreateVmWizardSummaryComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;

    const zones: ZoneViewModel[] = [
        { id: 'zone-id-1', name: 'zone 1' },
    ];

    const mockGetAffinityGroupsResponse: AffinityGroup[] = [
        {
            id: '1',
            account: 'admin',
            description: 'test group 1',
            domain: 'ROOT',
            domainid: 'domain1',
            name: 'group 1',
            project: 'project1',
            projectid: 'projectid1',
            type: 'type1',
            virtualmachineIds: 'vm1'
        }
    ];

    const mockGetSshKeyPairsResponse: SSHKeyPair[] = [
        {
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain1',
            fingerprint: 'fingerprint1',
            name: 'keypair 1'
        }
    ];

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    beforeEach(() => {

        const mockZoneDomainAccountStore = { ...getMockZoneDomainAccountStore(), zones: signal(zones) };

        TestBed.configureTestingModule({
            imports: [CreateVmWizardSummaryComponent],
            providers: [
                CreateVMWizardStore,
                provideMock(CreateVmComputeService),
                provideMock(VmMediaService),
                provideMock(CreateVmNetworkService),
                provideMock(CreateNetworkService),
                provideMock(UserContextService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(CloudInfraPermissionService),
                provideMock(CreateVmStorageService),
                provideMock(CreateVmService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
            ]
        });

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of(mockGetAffinityGroupsResponse));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of(mockGetSshKeyPairsResponse));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'account',
                domainId: 'domain-id'
            }
        } as UserContext;

        fixture = TestBed.createComponent(CreateVmWizardSummaryComponent);
        component = fixture.componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('navigateToStep', () => {

        beforeEach(() => {
            fixture.detectChanges();
        });

        it('should not navigate if step is not accessible', () => {
            const store = TestBed.inject(CreateVMWizardStore);
            const navigateToStepSpy = spyOn(store, 'navigateToStep');

            component.navigateToStep(1, false);

            expect(navigateToStepSpy).not.toHaveBeenCalled();
        });

        it('should navigate if step is accessible', () => {
            const store = TestBed.inject(CreateVMWizardStore);
            const navigateToStepSpy = spyOn(store, 'navigateToStep');

            component.navigateToStep(1, true);

            expect(navigateToStepSpy).toHaveBeenCalledOnceWith(1);
        });

    });

    describe('Reset', () => {

        it('should reset the appropriate store values when clicking on Clear', () => {

            const spy = spyOn(component.store, 'reset').and.callThrough();

            fixture.detectChanges();
            const resetButton = fixture.debugElement.query(By.css('#reset-button')).nativeElement as HTMLButtonElement;
            resetButton.click();
            fixture.detectChanges();
            expect(spy).toHaveBeenCalledTimes(1);
            expect(component.store.domainId()).toEqual('domain-id');
            expect(component.store.account()).toEqual('account');
            expect(component.store.zones()).toEqual(zones);
            expect(component.store.advancedSettingsStep.affinityGroups()).toEqual(mockGetAffinityGroupsResponse);
            expect(component.store.advancedSettingsStep.sshKeyPairs()).toEqual([
                {
                    account: 'admin',
                    domain: 'ROOT',
                    domainid: 'domain1',
                    fingerprint: 'fingerprint1',
                    name: 'keypair 1',
                    id: 'keypair-1'
                }
            ]);

        });

    });

    describe('Create VirtualMachine', () => {

        let canCreateSpy: jasmine.Spy;
        let createVirtualMachineSpy: jasmine.Spy;
        let createAndPowerOnButton: HTMLButtonElement;
        let createAndPowerOnButton2: HTMLButtonElement;
        let createAndDoNotPowerOnButton: HTMLButtonElement;

        beforeEach(() => {
            canCreateSpy = spyOn(component.store, 'canCreateVirtualMachine');
            createVirtualMachineSpy = spyOn(component.store, 'createVirtualMachine');
            const dropDown = fixture.debugElement.query(By.directive(NgbDropdown));
            createAndPowerOnButton = dropDown.query(By.css('.btn-primary')).nativeElement as HTMLButtonElement;
            const dropDownMenu = dropDown.query(By.directive(NgbDropdownMenu));
            const dropDownItems = dropDownMenu.queryAll(By.directive(NgbDropdownItem));
            createAndPowerOnButton2 = dropDownItems[0].nativeElement as HTMLButtonElement;
            createAndDoNotPowerOnButton = dropDownItems[1].nativeElement as HTMLButtonElement;
        });

        it('should not create virtual machine if canCreate is false', () => {
            canCreateSpy.and.returnValue(false);
            fixture.detectChanges();
            expect(createAndPowerOnButton.disabled).toBeTrue();

        });

        it('should call create with power on true', () => {
            canCreateSpy.and.returnValue(true);
            fixture.detectChanges();

            createAndPowerOnButton.click();
            fixture.detectChanges();

            expect(createVirtualMachineSpy).toHaveBeenCalledOnceWith(true);
        });

        it('should call create with power on true', () => {
            canCreateSpy.and.returnValue(true);
            fixture.detectChanges();

            createAndPowerOnButton2.click();
            fixture.detectChanges();

            expect(createVirtualMachineSpy).toHaveBeenCalledOnceWith(true);
        });

        it('should call create with power on true', () => {
            canCreateSpy.and.returnValue(true);
            fixture.detectChanges();

            createAndDoNotPowerOnButton.click();
            fixture.detectChanges();

            expect(createVirtualMachineSpy).toHaveBeenCalledOnceWith(false);
        });
    });

});
