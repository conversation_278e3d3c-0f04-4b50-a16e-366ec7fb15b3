<form class="mt-3" [formGroup]="form">
    <div class="row mb-3">
        <label for="name" class="col-3 col-form-label">Name<span class="required-asterisk">*</span></label>
        <div class="col">
            <input class="form-control" id="name" formControlName="name"
                [class]="{ 'is-invalid': form.controls.name.invalid && form.controls.name.dirty }" autocomplete="off" />
        </div>
    </div>
    <div class="row mb-3">
        <label for="description" class="col-3 col-form-label">Description<span
                class="required-asterisk">*</span></label>
        <div class="col">
            <input class="form-control" id="description" formControlName="description"
                [class]="{ 'is-invalid': form.controls.description.invalid && form.controls.description.dirty }" />
        </div>
    </div>
    <div class="row mb-3">
        <label class="col-3 col-form-label">Network Offering<span class="required-asterisk">*</span></label>
        <div class="col">
            <ng-select [items]="networkOfferings()" bindLabel="name" formControlName="networkOffering"
                [class]="{ 'is-invalid': form.controls.networkOffering.invalid && form.controls.networkOffering.dirty }" />
        </div>
    </div>
    @if (form.controls.networkOffering.value?.forVPC) {
        <div class="row mb-3">
            <label class="col-3 col-form-label">VPC
                <span class="required-asterisk">*</span>
            </label>
            <div class="col">
                <ng-select [items]=" vpcs()" bindLabel="name" bindValue="id" formControlName="vpc"
                    [class]="{ 'is-invalid': form.controls.vpc.invalid && form.controls.vpc.dirty }" />
            </div>
        </div>
    }
    @if (form.controls.networkOffering.value && !form.controls.networkOffering.value.forVPC) {
        <div class="row mb-3">
            <label for="networkDomain" class="col-3 col-form-label">Network Domain</label>
            <div class="col">
                <input class="form-control" id="networkDomain" formControlName="networkDomain"
                    [class]="{ 'is-invalid': form.controls.networkDomain.invalid && form.controls.networkDomain.dirty }">
            </div>
        </div>
    }
    <div class="row mb-3">
        <label for="gateway" class="col-3 col-form-label">Gateway
            <i class="fa-solid fa-circle-info text-secondary"
                [ngbPopover]="'Required for shared networks and isolated networks when it belongs to VPC.'"
                triggers="hover" container="body"></i>
            @if (form.controls.networkOffering.value?.forVPC) {
                <span class="required-asterisk">*</span>
            }
        </label>
        <div class="col">
            <input class="form-control" id="gateway" formControlName="gateway"
                [class]="{ 'is-invalid': form.controls.gateway.invalid && form.controls.gateway.dirty }">
        </div>
    </div>
    <div class="row mb-3">
        <label for="netmask" class="col-3 col-form-label">Netmask
            <i class="fa-solid fa-circle-info text-secondary"
                [ngbPopover]="'Required for shared networks and isolated networks when it belongs to VPC.'"
                triggers="hover" container="body"></i>
            @if (form.controls.networkOffering.value?.forVPC) {
                <span class="required-asterisk">*</span>
            }
        </label>
        <div class="col">
            <input class="form-control" id="netmask" formControlName="netmask"
                [class]="{ 'is-invalid': form.controls.netmask.invalid && form.controls.netmask.dirty }">
        </div>
    </div>
</form>
