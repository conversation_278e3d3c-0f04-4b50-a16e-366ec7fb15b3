import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { ApprovalRequiredComponent } from './approvalrequired.component';

describe('ApprovalrequiredComponent', () => {
    let fixture: ComponentFixture<ApprovalRequiredComponent>;

    beforeEach(() => {

        TestBed.configureTestingModule({
            providers: [
                provideMock(Router),
                provideMock(UserContextService)
            ],
            imports: [
                ApprovalRequiredComponent
            ]
        });

        fixture = TestBed.createComponent(ApprovalRequiredComponent);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(fixture.componentInstance).toBeTruthy();
    });

});
