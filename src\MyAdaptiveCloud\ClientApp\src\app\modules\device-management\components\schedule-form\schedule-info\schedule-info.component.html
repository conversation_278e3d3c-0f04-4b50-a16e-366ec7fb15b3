@if (form) {
    <form class="form-horizontal" [formGroup]="form" novalidate>
        <div class="mb-3 row">
            <div class="col-xl-12">
                <label class="form-label">Name</label>
                <input class="form-control" formControlName="name" placeholder="Name"
                    [class]="{ 'is-invalid': !form.controls.name.valid && form.controls.name.touched }"
                    [class]="{ 'is-valid': form.controls.name.valid && form.controls.name.touched }" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-xl-12">
                <label class="form-label">Description</label>
                <textarea rows="5" maxlength="256" class="form-control" formControlName="description"
                    placeholder="Description"
                    [class]="{ 'is-invalid': !form.controls.description.valid && form.controls.description.touched}">
                </textarea>
            </div>
        </div>
        <div class="my-6">
            <hr />
            <h5 class="modal-title">Enabled Features</h5>
            <br />
            <div class="form-check">
                <input class="form-check-input" id="updates" type="checkbox" formControlName="hasMicrosoftUpdates" />
                <label class="form-check-label" for="updates">
                    Microsoft Updates
                    <span><i title="Enables the Microsoft Updates management section"
                            class="text-secondary fa fa-info-circle"></i></span>
                </label>
            </div>
        </div>
        @if (isAgentManagementFlagEnabled) {
            @let allReleaseTags = allReleaseTags$ | async;
            <div class="my-6">
                <hr />
                <div class="d-flex align-items-center gap-2">
                    <h5 class="modal-title mb-0">AdaptiveCloud Agent Version</h5>
                    <i class="text-secondary fa fa-info-circle" [ngbPopover]="releaseTypeMessageTemplate" triggers="hover" container="body"></i>
                </div>

                <div class="col-xl-12 mt-2 d-flex align-items-center gap-2">
                    <ng-select [items]="allReleaseTags" bindLabel="releaseTagName" bindValue="releaseTagName"
                        [formControl]="form.controls.releaseTagName" [searchable]="false" [clearable]="false" />

                    <ng-template #releaseTypeMessageTemplate>
                        <p class="mb-2">
                            The following AdaptiveCloud Agent version types are available to be installed for the devices under this policy.
                        </p>
                        @for (releaseTag of allReleaseTags; track releaseTag.id) {
                            <p class="mb-1">
                                <span class="fw-bold">{{ releaseTag.releaseTagName }}</span><br />
                                {{ releaseTag.description }}
                                @if (releaseTag.agentVersion) {
                                - Agent Version: {{ releaseTag.agentVersion }}
                                }
                            </p>
                        }
                    </ng-template>
                </div>

            </div>
        }
    </form>
}
