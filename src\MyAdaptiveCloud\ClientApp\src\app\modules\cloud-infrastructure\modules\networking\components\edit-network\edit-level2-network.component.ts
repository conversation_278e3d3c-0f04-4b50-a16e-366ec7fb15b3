import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { EditLevel2NetworkForm } from '../../forms/edit-level2-network.form';
import { NetworkListViewModel } from '../../models/network-list.view-model';
import { NetworkingService } from '../../services/networking.service';
import { validateCidr } from '@app/modules/cloud-infrastructure/validators/network-validators';

@Component({
    selector: 'app-edit-level2-network',
    imports: [BtnSubmitComponent, ReactiveFormsModule],
    templateUrl: './edit-level2-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class EditLevel2NetworkComponent implements OnInit {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly networkingService = inject(NetworkingService);
    private readonly formBuilder = inject(FormBuilder);

    protected form: FormGroup<EditLevel2NetworkForm>;

    readonly network = signal<NetworkListViewModel>(null);

    ngOnInit(): void {
        this.form = this.formBuilder.group<EditLevel2NetworkForm>({
            cidr: this.formBuilder.control<string>(this.network().cidr, validateCidr()),
            description: this.formBuilder.control<string | null>(this.network().description, [Validators.required, Validators.maxLength(255)]),
            name: this.formBuilder.control<string>(this.network().name, [Validators.required, Validators.maxLength(255)]),
        });
    }

    protected submit() {
        if (this.form.valid) {
            this.networkingService.editLevel2Network(
                this.network().id,
                this.form.value.name,
                this.form.value.description,
                this.form.value.cidr
            ).subscribe(jobId => {
                this.activeModal.close(jobId);
            });
        }
    }

}
