import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { Network } from '@app/modules/cloud-infrastructure/models/network';
import { Vpc } from '@app/modules/cloud-infrastructure/models/vpc.model';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { FiltersFormComponent } from '@app/shared/components/datatable/filters-form/filters-form.component';
import { PillFilterComponent } from '@app/shared/components/datatable/pill-filter/pill-filter.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, map, Observable, of, skip, switchMap, take } from 'rxjs';
import { NETWORK_LIST_CONSTANTS } from '../../models/network-list.constants';
import { VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS } from '../../models/virtual-private-cloud-list.constants';
import { VirtualPrivateCloudListViewModel } from '../../models/virtual-private-cloud-list.view-model';
import { VirtualPrivateCloudListFilters } from '../../requests/virtual-private-cloud-list.filter';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';
import { CreateVirtualPrivateCloudComponent } from '../create-virtual-private-cloud/create-virtual-private-cloud.component';
import { EditVirtualPrivateCloudComponent } from '../edit-virtual-private-cloud/edit-virtual-private-cloud.component';
import { ListVirtualPrivateCloudsFilterComponent } from '../list-virtual-private-clouds-filter/list-virtual-private-clouds-filter.component';
import { RestartVirtualPrivateCloudComponent } from '../restart-virtual-private-cloud/restart-virtual-private-cloud.component';

@Component({
    selector: 'app-list-virtual-private-clouds',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent, FiltersFormComponent, ListVirtualPrivateCloudsFilterComponent, PillFilterComponent, AsyncPipe],
    templateUrl: './list-virtual-private-clouds.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListVirtualPrivateCloudsComponent extends BaseListComponent<VirtualPrivateCloudListViewModel> implements OnInit {
    private readonly userContextService = inject(UserContextService);
    private readonly vpcService = inject(VirtualPrivateCloudService);
    protected readonly store = inject(ZoneDomainAccountStore);
    private readonly modalService = inject(ModalService);
    protected readonly networkingPermissionService = inject(NetworkingPermissionService);

    private readonly columns = signal<TableColumn[]>(null);
    private readonly virtualPrivateCloudList = signal<VirtualPrivateCloudListViewModel[]>(null);

    private readonly statusRow = viewChild.required<TemplateRef<never>>('statusRow');

    private readonly selectedDomain$ = toObservable(this.store.selectedDomain);
    private readonly selectedAccount$ = toObservable(this.store.selectedAccount);

    constructor() {
        super();
        this.filters = new VirtualPrivateCloudListFilters();
        this.pagination = this.filters.request;
    }

    ngOnInit(): void {

        this.columns.set([
            {
                cellTemplate: this.statusRow(),
                name: 'State',
                prop: VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.stateKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 150
            },
            {
                name: 'Name',
                prop: VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.nameKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                canAutoResize: true,
                resizeable: false,
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.domainKey,
                name: 'Domain'
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: true,
                width: 200,
                prop: VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.accountKey,
                name: 'Account',
            },
            {
                name: 'Description',
                prop: VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.descriptionKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Zone',
                prop: VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.zoneNameKey,
                headerTemplate: this.headerTemplateSortable(),
                resizeable: false,
                canAutoResize: true,
                width: 100
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 120
            }
        ]);

        // When the user context has its own domain, do not pass account so the result includes all VMs in the domain
        const accountName = this.userContextService.currentUser.cloudInfraUserContext.hasMappedDomain ? null : this.userContextService.currentUser.cloudInfraUserContext.accountName;
        this.vpcService.getVirtualPrivateCloudList()
            .pipe(take(1))
            .subscribe(res => {
                this.virtualPrivateCloudList.set(this.mapVirtualPrivateCloudListResponse(res));
                this.onDomainAccountChanged(this.userContextService.currentUser.cloudInfraUserContext.domainId, accountName);
            });

        this.selectedDomain$.pipe(
            skip(1),
            filter(domain => !!domain),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(domain => {
            this.onDomainAccountChanged(domain.id, this.store.getAccount());
        });

        this.selectedAccount$.pipe(
            skip(1),
            filter(account => !!account),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(account => {
            this.onDomainAccountChanged(this.store.getDomainId(), account.name);
        });

    }

    protected openAddVirtualPrivateCloudModal(): void {
        if (this.networkingPermissionService.canCreateVirtualPrivateCloud() && this.store.getAccount()) {
            const modalRef = this.modalService.openModalComponent(CreateVirtualPrivateCloudComponent);
            (modalRef.componentInstance as CreateVirtualPrivateCloudComponent).zones.set(this.store.zones());
            (modalRef.componentInstance as CreateVirtualPrivateCloudComponent).domainId.set(this.store.getDomainId());
            (modalRef.componentInstance as CreateVirtualPrivateCloudComponent).account.set(this.store.getAccount());
            modalRef.closed
                .pipe(
                    take(1),
                    filter(res => !!(res as Network)),
                    map(res => this.mapVirtualPrivateCloudListResponse([res]))
                )
                .subscribe(res => {
                    this.virtualPrivateCloudList.update(value => [...value, ...res]);
                    super.initialize(() => this.getVirtualPrivateCloudList$(), this.columns());
                });
        }
    }

    protected deleteVirtualPrivateCloud(virtualPrivateCloudIdId: string): void {
        if (this.networkingPermissionService.canDeleteVirtualPrivateCloud()) {
            this.modalService.openDeleteConfirmationDialog('Delete Virtual Private Cloud', 'Are you sure you want to delete this Virtual Private Cloud?', 'delete')
                .closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                    switchMap(() => this.vpcService.deleteVirtualPrivateCloud(virtualPrivateCloudIdId))
                ).subscribe();
        }
    }

    protected restartVirtualPrivateCloud(virtualPrivateCloudId: VirtualPrivateCloudListViewModel): void {
        if (this.networkingPermissionService.canRestartVirtualPrivateCloud()) {
            const modal = this.modalService.openModalComponent(RestartVirtualPrivateCloudComponent);
            (modal.componentInstance as RestartVirtualPrivateCloudComponent).virtualPrivateCloud.set(virtualPrivateCloudId);
            modal
                .closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                ).subscribe();
        }
    }

    protected editVirtualPrivateCloud(virtualPrivateCloud: VirtualPrivateCloudListViewModel): void {
        if (this.networkingPermissionService.canEditVirtualPrivateCloud()) {
            const modal = this.modalService.openModalComponent(EditVirtualPrivateCloudComponent);
            (modal.componentInstance as EditVirtualPrivateCloudComponent).virtualPrivateCloud.set(virtualPrivateCloud);
            modal
                .closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                ).subscribe();
        }
    }

    private onDomainAccountChanged(domainId: string, account: string): void {
        let columns = [...this.columns()];
        if (account) {
            columns = [...columns.filter(c => c.prop !== NETWORK_LIST_CONSTANTS.accountKey && c.prop !== NETWORK_LIST_CONSTANTS.domainKey)];
        } else if (domainId && !this.store.isRootDomainSelected()) {
            columns = [...columns.filter(c => c.prop !== NETWORK_LIST_CONSTANTS.domainKey)];
        }

        if (this.table().columns?.length) {
            this.table().columns = [...columns];
        }

        super.initialize(() => this.getVirtualPrivateCloudList$(), columns);
    }

    private mapVirtualPrivateCloudListResponse(vpcs: Vpc[]): VirtualPrivateCloudListViewModel[] {
        let virtualPrivateCloudViewModelList: VirtualPrivateCloudListViewModel[] = [];
        if (vpcs?.length > 0) {
            virtualPrivateCloudViewModelList = vpcs.map(vpc => {
                const viewModel: VirtualPrivateCloudListViewModel = {
                    account: vpc.account.trim(),
                    description: vpc.displaytext.trim(),
                    domain: vpc.domain.trim(),
                    domainId: vpc.domainid,
                    id: vpc.id,
                    isRedundant: !!vpc.redundantvpcrouter,
                    name: vpc.name?.trim(),
                    state: vpc.state,
                    zoneName: vpc.zonename,
                    zoneId: vpc.zoneid
                };
                return viewModel;
            });
        }
        return virtualPrivateCloudViewModelList;
    }

    private getVirtualPrivateCloudList$(): Observable<ApiDatasetResult<VirtualPrivateCloudListViewModel[]>> {
        // Apply filters, including selected domain and account, search term and zone filters
        const filteredVpcList = [...this.virtualPrivateCloudList().filter(network => {
            const matchesDomain = this.store.selectedDomain() ? network.domainId === this.store.selectedDomain().id : true;
            const matchesAccount = this.store.getAccount() ? network.account === this.store.getAccount() : true;
            const matchesSearchTerm = !this.pagination.searchTerm ||
                network.name.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                network.state.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                network.zoneName.toLowerCase().includes(this.pagination.searchTerm.toLowerCase());

            const matchesZoneFilter = !this.pagination[NETWORK_LIST_CONSTANTS.zoneIdKey] || network.zoneId === this.pagination[NETWORK_LIST_CONSTANTS.zoneIdKey];

            return matchesDomain && matchesAccount && matchesSearchTerm && matchesZoneFilter;
        })];

        // Apply sorting
        if (this.pagination.orderBy === VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.nameKey) {
            filteredVpcList.sort(sortByProperty(VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.nameKey as keyof VirtualPrivateCloudListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.stateKey) {
            filteredVpcList.sort(sortByProperty(VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.stateKey as keyof VirtualPrivateCloudListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.zoneNameKey) {
            filteredVpcList.sort(sortByProperty(VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.zoneNameKey as keyof VirtualPrivateCloudListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.domainKey) {
            filteredVpcList.sort(sortByProperty(VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.domainKey as keyof VirtualPrivateCloudListViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.accountKey) {
            filteredVpcList.sort(sortByProperty(VIRTUAL_PRIVATE_CLOUD_LIST_CONSTANTS.accountKey as keyof VirtualPrivateCloudListViewModel, this.pagination.orderDir === 'asc'));
        }

        // Apply pagination
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = startIndex + this.pagination.pageSize;
        const paginatedList = filteredVpcList.slice(startIndex, endIndex);

        return of({ data: paginatedList, totalCount: filteredVpcList.length });
    }

}

