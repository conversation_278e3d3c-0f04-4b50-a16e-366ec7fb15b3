import { TestBed } from '@angular/core/testing';
import { ApiService } from '@app/shared/services/api.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { AcCwMappingListRequest } from '../requests/ac-cw-mapping-list.request';
import { AcCwMappingRequest } from '../requests/ac-cw-mapping.request';
import { AcCwVmMappingListRequest } from '../requests/ac-cw-vm-mapping-list.request';
import { AcCwVmMappingRequest } from '../requests/ac-cw-vm.mapping.request';
import { AgreementRequest } from '../requests/agreement.request';
import { CwCompaniesRequest } from '../requests/cw-companies.request';
import { AcToCwMappingService } from './ac-cw-mapping.service';

describe('AcToCwMappingService', () => {
    let service: AcToCwMappingService;
    let mockApiService: jasmine.SpyObj<ApiService>;
    const endpoint = 'acmapping';

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ApiService),
                AcToCwMappingService
            ]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(AcToCwMappingService);
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(service).toBeTruthy();
        });
    });

    describe('API', () => {
        it('should use the right endpoint for getUnmappedAccountsDomains', () => {
            service.getUnmappedAccountsDomains();
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/acUnmappedAccountsDomains`);
        });

        it('should use the right endpoint for searchMapping', () => {
            const request: AcCwMappingListRequest = {
                pageSize: 25,
                currentPage: 1,
                orderBy: 'acName',
                orderDir: 'asc',
                searchTerm: '',
            };
            mockApiService.get.and.returnValue(of({ totalCount: 0 }));
            service.getMappings(request);
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/mappinglist`, request);
        });

        it('should use the right endpoint for searchCompanies', () => {
            const request = {
                keyword: 'something'
            } as CwCompaniesRequest;
            service.searchCompanies(request);
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/cwCompanies`, request);
        });

        it('should use the right endpoint for getAgreements', () => {
            const request = {
                cwCompanyId: 13,
                status: 'happy'
            } as AgreementRequest;
            service.getAgreements(request);
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/cwAgreements`, request);
        });

        it('should use the right endpoint for createMapping', () => {
            const request = {
                acId: '13',
                acName: 'Martin'
            } as AcCwMappingRequest;
            service.createMapping(request);
            expect(mockApiService.post).toHaveBeenCalledWith(`${endpoint}/mapping`, request);
        });

        it('should use the right endpoint for editMapping', () => {
            const request = {
                acId: '13',
                acName: 'Martin'
            } as AcCwMappingRequest;
            service.editMapping(13, request);
            expect(mockApiService.put).toHaveBeenCalledWith(`${endpoint}/mapping`, 13, request);
        });

        it('should use the right endpoint for deleteMapping', () => {
            service.deleteMapping(13);
            expect(mockApiService.delete).toHaveBeenCalledWith(`${endpoint}/mapping`, 13);
        });

        it('should use the right endpoint for getProductMappings', () => {
            service.getProductMappings();
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/productMapping`);
        });

        it('should use the right endpoint for getVmMappingPeriods', () => {
            service.getVmMappingPeriods();
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/vmMapping/periods`);
        });

        it('should use the right endpoint for getVmMappings', () => {
            const request = {
                pageSize: 25,
                currentPage: 1,
                orderBy: 'cwProductName',
                orderDir: 'asc',
                month: new Date()
            } as AcCwVmMappingListRequest;
            mockApiService.get.and.returnValue(of({ data: {} }));
            service.getVmMappings(request);
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/vmMapping`, request);
        });

        it('should use the right endpoint for createVmMapping', () => {
            const request = {
                acId: '10',
                acName: 'something',
                acType: 'any',
                productMapId: 12,
                priority: 2,
                quantityValue: '2',
                isFormula: false,
                account: 'one',
                domain: 'any',
                startDate: new Date(),
                endDate: new Date()
            } as AcCwVmMappingRequest;
            service.createVmMapping(request);
            expect(mockApiService.post).toHaveBeenCalledWith(`${endpoint}/vmMapping`, request);
        });

        it('should use the right endpoint for updateVmMapping', () => {
            const request = {
                acId: '10',
                acName: 'something',
                acType: 'any',
                productMapId: 12,
                priority: 2,
                quantityValue: '2',
                isFormula: false,
                account: 'one',
                domain: 'any',
                startDate: new Date(),
                endDate: new Date()
            } as AcCwVmMappingRequest;
            service.updateVmMapping(13, request);
            expect(mockApiService.put).toHaveBeenCalledWith(`${endpoint}/vmMapping`, 13, request);
        });

        it('should use the right endpoint for deleteVmMapping', () => {
            service.deleteVmMapping(13);
            expect(mockApiService.delete).toHaveBeenCalledWith(`${endpoint}/vmMapping`, 13);
        });
    });
});
