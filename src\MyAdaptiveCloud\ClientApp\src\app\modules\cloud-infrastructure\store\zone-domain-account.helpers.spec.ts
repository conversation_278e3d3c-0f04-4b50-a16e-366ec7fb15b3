import { CloudInfraAccountViewModel } from '../models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '../models/cloud-infra-domain.view-model';
import { findDomainById, updateAccountsByDomainId, updateSubDomainsByDomainId } from './zone-domain-account.helpers';

describe('ZoneDomainAccountHelpers', () => {

    const mockDomains: CloudInfraDomainViewModel[] = [
        {
            id: '1',
            name: 'Root',
            hasChild: true,
            level: 0,
            accounts: [],
            isExpanded: true,
            subDomains: [
                {
                    id: '1-1',
                    name: 'Child 1',
                    hasChild: true,
                    level: 1,
                    accounts: [],
                    isExpanded: false,
                    subDomains: [
                        {
                            id: '1-1-1',
                            name: 'Grandchild 111',
                            hasChild: false,
                            subDomains: [],
                            level: 2,
                            accounts: [],
                            isExpanded: false
                        }
                    ]
                },
                {
                    id: '1-2',
                    name: 'Child 2',
                    hasChild: false,
                    subDomains: [],
                    level: 1,
                    accounts: [],
                    isExpanded: false
                }
            ]
        }
    ];

    describe('findDomainById', () => {
        it('should find a domain by ID in a nested structure', () => {
            const result = findDomainById('1-1', mockDomains);
            expect(result?.name).toEqual('Child 1');
        });

        it('should return null if no domain with the given ID exists', () => {
            const result = findDomainById('non-existent-id', mockDomains);
            expect(result).toBeNull();
        });
    });

    describe('updateSubDomainsByDomainId', () => {
        it('should update the subDomains of a domain with the given ID', () => {
            const newSubDomains: CloudInfraDomainViewModel[] = [
                {
                    id: '1-1-1-new', name: 'New Subdomain', hasChild: false, subDomains: [], level: 0,
                    accounts: [],
                    isExpanded: false
                }
            ];
            const updatedDomains = updateSubDomainsByDomainId('1-1', mockDomains, newSubDomains);

            const updatedDomain = findDomainById('1-1', updatedDomains);
            expect(updatedDomain?.subDomains).toEqual(newSubDomains);
        });

        it('should not change anything if the domain ID does not exist', () => {
            const newSubDomains: CloudInfraDomainViewModel[] = [
                {
                    id: 'non-existent', name: 'Non-existent Subdomain', hasChild: false, subDomains: [], level: 1,
                    accounts: [],
                    isExpanded: false
                }
            ];
            const updatedDomains = updateSubDomainsByDomainId('non-existent-id', mockDomains, newSubDomains);

            expect(updatedDomains).toEqual(mockDomains);
        });
    });

    describe('updateAccountsByDomainId', () => {
        const mockAccounts: CloudInfraAccountViewModel[] = [
            {
                id: 'account1', name: 'Account 1', domainId: '1-1'
            },
            {
                id: 'account2', name: 'Account 2', domainId: '1-1'
            },
            {
                id: 'account3', name: 'Account 3', domainId: '1-2'
            }
        ];

        it('should update accounts of a domain with the given ID', () => {
            const updatedDomains = updateAccountsByDomainId('1-1', mockDomains, mockAccounts);

            const updatedDomain = findDomainById('1-1', updatedDomains);
            expect(updatedDomain?.accounts).toEqual([
                {
                    id: 'account1', name: 'Account 1', domainId: '1-1'
                },
                {
                    id: 'account2', name: 'Account 2', domainId: '1-1'
                }
            ]);
        });

        it('should not change anything if the domain ID does not exist', () => {
            const updatedDomains = updateAccountsByDomainId('non-existent-id', mockDomains, mockAccounts);

            expect(updatedDomains).toEqual(mockDomains);
        });
    });
});
