export interface SharedNetworkRequest {
  name: string;
  displaytext: string;
  networkofferingid: string;
  hideipaddressusage: boolean;
  vlan: string;
  gateway?: string | null;
  netmask?: string | null;
  startip?: string | null;
  endip?: string | null;
  gatewayv6?: string | null;
  cidr?: string | null;
  startipv6?: string | null;
  endipv6?: string | null;
  networkdomain?: string | null;
}

