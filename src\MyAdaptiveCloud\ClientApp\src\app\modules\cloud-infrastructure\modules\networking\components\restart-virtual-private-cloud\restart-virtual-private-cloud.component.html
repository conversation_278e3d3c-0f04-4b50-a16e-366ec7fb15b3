<div class="modal-header">
    <h4 class="modal-title">Restart Virtual Private Cloud</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <p>Are you sure you want to restart this Virtual Private Cloud?</p>
    <p>Making a non-redundant Virtual Private Cloud redundant will force a clean up. The Networks will not be available for a couple of
        minutes.</p>
    <form [formGroup]="form()" class="row g-3">
        <div class="form-check">
            <label class="form-check-label clickable">Clean Up
                <i class="fa-solid fa-circle-info text-secondary" [ngbPopover]="'Cleans up old network elements.'"
                    triggers="hover" placement="right" container="body">
                </i>
                <input type="checkbox" class="form-check-input clickable" formControlName="cleanup"
                    data-testid="cleanup-checkbox" />
            </label>
        </div>
        @if (!virtualPrivateCloud().isRedundant) {
            <div class="form-check">
                <label class="form-check-label clickable">Make Redundant
                    <i class="fa-solid fa-circle-info text-secondary"
                        [ngbPopover]="'Turn a single Virtual Private Cloud into a redundant one.'" triggers="hover" placement="right"
                        container="body">
                    </i>
                    <input type="checkbox" class="form-check-input clickable" formControlName="makeRedundant"
                        data-testid="make-redundant-checkbox" />
                </label>
            </div>
        }
        <div class="form-check">
            <label class="form-check-label clickable">Live patch Network's router(s)
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Live patches the router software before restarting it. This parameter will only work when cleanup is false.'" triggers="hover"
                    placement="right" container="body">
                </i>
                <input type="checkbox" class="form-check-input clickable" formControlName="livePatchNetworkRouters"
                    data-testid="live-patch-network-routers-checkbox" />
            </label>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    <app-btn-submit [disabled]="form().invalid || isSubmitting()" [btnClasses]="'btn-primary'"
        (submitClickEvent)="restart()">Restart</app-btn-submit>
</div>
