import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { ConfirmationDialogComponent } from '@app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { VmStateEnum } from '@app/shared/models/cloud-infra/vm-state.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmDetails } from '../../models/vm-detail.model';
import { VmActionsService } from '../../services/vm-actions.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { VmManagementService } from '../../services/vm-management.service';
import { VmMediaService } from '../../services/vm-media-service';
import { AttachIsoComponent } from '../attach-iso/attach-iso.component';
import { DestroyVmComponent } from '../destroy-vm/destroy-vm.component';
import { MigrateHostComponent } from '../migrate-host/migrate-host.component';
import { RebootVmComponent } from '../reboot-vm/reboot-vm.component';
import { ReinstallVmComponent } from '../reinstall-vm/reinstall-vm.component';
import { ResetPasswordComponent } from '../reset-password/reset-password.component';
import { ResetSSHKeyPairComponent } from '../reset-ssh-key-pair/reset-ssh-key-pair.component';
import { SnapshotVmComponent } from '../snapshot-vm/snapshot-vm.component';
import { SnapshotVolumeComponent } from '../snapshot-volume/snapshot-volume.component';
import { StartVmComponent } from '../start-vm/start-vm.component';
import { StopVmComponent } from '../stop-vm/stop-vm.component';
import { VmDetailsContainerComponent } from './vm-details-container.component';

describe('VmDetailsContainerComponent', () => {
    let fixture: ComponentFixture<VmDetailsContainerComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockModalService: jasmine.SpyObj<ModalService>;
    let mockVmManagementPermissionService: jasmine.SpyObj<VmManagementPermissionService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockCloudInfrastructureQueueService: jasmine.SpyObj<CloudInfrastructureJobQueueService>;
    let vmActionsService: VmActionsService;
    let vmDetailsStateService: VmDetailsStateService;

    let el: DebugElement;

    let mockVmInstance: VmDetails;

    beforeEach(() => {

        mockVmInstance = {
            affinitygroup: [],
            id: 'vm-001',
            name: '1-Running',
            displayname: 'web-server-01',
            account: 'account',
            domainid: 'id',
            domain: 'example.com',
            state: VmStateEnum.Running,
            zoneid: 'zone-101',
            zonename: 'Zone A',
            hostid: 'host-202',
            isoid: 'iso-404',
            nic: [],
            osdisplayname: 'Linux',
            passwordenabled: true,
            created: new Date(2025, 10, 10).toISOString(),
            cpunumber: 2,
            memory: 4096,
            cpuused: '50%',
            memorykbs: 2048000,
            templatename: 'Ubuntu 20.04',
            isdynamicallyscalable: true,
            keypairs: [{ key: 'ssh-key', value: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...' }],
            tags: [],
            vgpu: 'NVIDIA vGPU',
            cpuUsagePercentage: 50,
            isAgentInstalled: false,
            memoryUsagePercentage: 50,
            memoryUsagePercentageString: '50%',
        };

        TestBed.configureTestingModule({
            imports: [
                VmDetailsContainerComponent,
                RouterModule,
            ],
            providers: [
                provideMock(VmManagementService),
                provideMock(UserContextService),
                provideMock(VmManagementPermissionService),
                VmDetailsStateService,
                provideMock(ActivatedRoute),
                provideMock(ModalService),
                provideMock(NgbActiveModal),
                provideMock(CloudInfrastructureSessionService),
                provideMock(VmMediaService),
                provideMock(CloudInfrastructureApiService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: getMockZoneDomainAccountStore(),
                },
                provideMock(CloudInfraPermissionService),
                ChangeDetectorRef,
                DestroyVmComponent,
                StopVmComponent,
                RebootVmComponent,
                StartVmComponent,
                ResetSSHKeyPairComponent,
                SnapshotVmComponent,
                ConfirmationDialogComponent,
                AttachIsoComponent,
                MigrateHostComponent,
                SnapshotVolumeComponent,
                ReinstallVmComponent,
                ResetPasswordComponent,
                VmActionsService
            ]
        });

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getConsoleUrl.and.returnValue(of(''));

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.detachIso.and.returnValue(of('job-id'));

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;

        mockCloudInfrastructureQueueService = TestBed.inject(CloudInfrastructureJobQueueService) as jasmine.SpyObj<CloudInfrastructureJobQueueService>;
        mockCloudInfrastructureQueueService.updates$ = of();
        mockCloudInfrastructureQueueService.idsAndStatusLoading$ = of();

        mockVmManagementPermissionService = TestBed.inject(VmManagementPermissionService) as jasmine.SpyObj<VmManagementPermissionService>;

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;

        vmActionsService = TestBed.inject(VmActionsService);

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                roleType: 'User',
            } as CloudInfraUserContext
        } as UserContext;

        vmDetailsStateService = TestBed.inject(VmDetailsStateService);

        fixture = TestBed.createComponent(VmDetailsContainerComponent);

        fixture.componentRef.setInput('vmId', 'vm-001');

        el = fixture.debugElement;
    });

    describe('Status Icon and Popover', () => {

        it('should display stopped icon without agent installed', () => {

            mockVmInstance.state = VmStateEnum.Stopped;
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const statusIcon = el.query(By.css('.vm-icon-status'));
            expect(statusIcon).toBeTruthy();
            expect(statusIcon.nativeElement.classList).toContain('icon-vm-stopped-without-agent');
        });

        it('should bind popover context correctly for a running VM', () => {
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const popoverDiv = el.query(By.css('.d-flex.text-secondary.justify-content-center'));
            expect(popoverDiv).toBeTruthy();
        });

    });

    describe('Details Section', () => {
        it('should display VM display name', () => {
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const displayNameElement = el.query(By.css('.first-row .name span'));
            expect(displayNameElement).toBeTruthy();
            expect(displayNameElement.nativeElement.textContent).toContain(mockVmInstance.displayname);
        });

        it('should display VM ID', () => {

            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const idElement = el.query(By.css('.second-row .id-cell span:last-child'));
            expect(idElement).toBeTruthy();
            expect(idElement.nativeElement.textContent).toContain(mockVmInstance.id);
        });

        it('should display formatted created date', () => {
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const createdDateElement = el.query(By.css('.second-row .created-cell span:last-child'));
            expect(createdDateElement).toBeTruthy();
            const datePipe = new DatePipe('en-US');
            expect(createdDateElement.nativeElement.textContent).toContain(datePipe.transform(mockVmInstance.created, 'MM/dd/yyyy'));
        });

        it('should display internal name if isRootAdmin is true', () => {
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);
            fixture.detectChanges();

            const internalNameElement = el.query(By.css('.second-row .internal-cell span:last-child'));
            expect(internalNameElement).toBeTruthy();
            expect(internalNameElement.nativeElement.textContent).toContain(mockVmInstance.name);
        });

        it('should NOT display internal name if isRootAdmin is false', () => {
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);
            fixture.detectChanges();

            const internalNameElement = el.query(By.css('.second-row .internal-cell'));
            expect(internalNameElement).toBeNull();
        });
    });

    describe('Status pills', () => {

        it('should display running status pill with check icon and text-success', () => {
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const statusPill = el.queryAll(By.css('.pill-badge'))[0];
            expect(statusPill).toBeTruthy();
            expect(statusPill.nativeElement.classList).toContain('text-success');
            expect(statusPill.query(By.css('.fa-solid.fa-check'))).toBeTruthy();
            expect(statusPill.nativeElement.textContent).toContain(VmStateEnum.Running);
        });

        it('should display stopped status pill with stop icon and text-danger', () => {
            mockVmInstance.state = VmStateEnum.Stopped;
            vmDetailsStateService.selectedVM.set(mockVmInstance);

            fixture.detectChanges();
            const statusPill = el.queryAll(By.css('.pill-badge'))[0];
            expect(statusPill).toBeTruthy();

            expect(statusPill.nativeElement.classList).toContain('text-danger');
            expect(statusPill.query(By.css('.fa-solid.fa-stop'))).toBeTruthy();
            expect(statusPill.nativeElement.textContent).toContain(VmStateEnum.Stopped);
        });

        it('should display "Agent Not Installed" pill with x-mark icon and text-danger', () => {
            mockVmInstance.state = VmStateEnum.Stopped;
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const agentPill = el.queryAll(By.css('.pill-badge'))[1];
            expect(agentPill).toBeTruthy();
            expect(agentPill.nativeElement.classList).toContain('text-danger');
            expect(agentPill.query(By.css('.fa-solid.fa-circle-xmark'))).toBeTruthy();
            expect(agentPill.nativeElement.textContent).toContain('Agent not installed');
        });

        it('should display "ISO Attached" pill with check icon and text-success', () => {
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const isoPill = el.queryAll(By.css('.pill-badge'))[2];
            expect(isoPill).toBeTruthy();
            expect(isoPill.nativeElement.classList).toContain('text-success');
            expect(isoPill.query(By.css('.fa-solid.fa-check'))).toBeTruthy();
            expect(isoPill.nativeElement.textContent).toContain('ISO attached');
        });

        it('should display "ISO not attached" pill with x-mark icon and text-danger', () => {
            mockVmInstance.isoid = null;
            vmDetailsStateService.selectedVM.set(mockVmInstance);
            fixture.detectChanges();

            const isoPill = el.queryAll(By.css('.pill-badge'))[2];
            expect(isoPill).toBeTruthy();
            expect(isoPill.nativeElement.classList).toContain('text-danger');
            expect(isoPill.query(By.css('.fa-solid.fa-circle-xmark'))).toBeTruthy();
            expect(isoPill.nativeElement.textContent).toContain('ISO not attached');
        });
    });

    describe('Actions', () => {

        describe('Reinstall VM', () => {

            it('should enable the action when the user has permissions and the VM is running', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ReinstallVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const reinstallButton = fixture.debugElement.query(By.css('[data-testid="reinstall-vm"]'));
                reinstallButton.nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(false);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="reinstall-vm"]'));
                fixture.detectChanges();

                expect(button).toBeNull();
            });

            it('should disable the action when the VM is destroyed', () => {
                mockVmInstance.state = VmStateEnum.Destroyed;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="reinstall-vm"]'));
                fixture.detectChanges();

                expect(button).toBeNull();
            });

            it('should enable the action when the user has permissions and the VM is running', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ReinstallVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const snapshotButton = fixture.debugElement.query(By.css('[data-testid="reinstall-vm"]'));
                snapshotButton.nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });
        });

        describe('Reset SSH Key Pair', () => {

            it('should enable the action when the user has permissions and the VM is stopped', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ResetSSHKeyPairComponent)
                } as NgbModalRef;
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockModalService.openModalComponent.and.returnValue(modalRef);
                mockVmManagementPermissionService.canResetSSHKeyPairForVirtualMachine.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="reset-ssh-pair"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canResetSSHKeyPairForVirtualMachine.and.returnValue(false);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);

                fixture.detectChanges();
                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();
                const button = fixture.debugElement.query(By.css('[data-testid="reset-ssh-pair"]'));

                expect(button).toBeNull();
                fixture.detectChanges();
            });
        });

        describe('Reset VM Password', () => {

            it('should enable the action when the user has permissions and the VM is stopped', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ResetPasswordComponent)
                } as NgbModalRef;
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockModalService.openModalComponent.and.returnValue(modalRef);
                mockVmManagementPermissionService.canResetVirtualMachinePassword.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="reset-password"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canResetSSHKeyPairForVirtualMachine.and.returnValue(false);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();
                const button = fixture.debugElement.query(By.css('[data-testid="reset-password"]'));

                expect(button).toBeNull();
                fixture.detectChanges();
            });
        });

        describe('Destroy VM', () => {

            it('should enable the action when the user has permissions and the VM is stopped', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(DestroyVmComponent)
                } as NgbModalRef;
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockModalService.openModalComponent.and.returnValue(modalRef);
                mockVmManagementPermissionService.canDestroyVirtualMachine.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="destroy-vm"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canDestroyVirtualMachine.and.returnValue(false);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();
                const button = fixture.debugElement.query(By.css('[data-testid="destroy-vm"]'));

                expect(button).toBeNull();
                fixture.detectChanges();
            });
        });

        describe('Snapshot VM', () => {

            it('should enable the action when the user has permissions and the VM is running', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(SnapshotVmComponent)
                } as NgbModalRef;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockModalService.openModalComponent.and.returnValue(modalRef);
                mockVmManagementPermissionService.canSnapshotVirtualMachine.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="snapshot-vm"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canSnapshotVirtualMachine.and.returnValue(false);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();
                const button = fixture.debugElement.query(By.css('[data-testid="snapshot-vm"]'));

                expect(button).toBeNull();
                fixture.detectChanges();
            });
        });

        describe('Snapshot Volume', () => {

            it('should enable the action when the user has permissions and the VM is running', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(SnapshotVolumeComponent)
                } as NgbModalRef;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockModalService.openModalComponent.and.returnValue(modalRef);
                mockVmManagementPermissionService.canSnapshotVolume.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="snapshot-volume"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canSnapshotVolume.and.returnValue(false);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();
                const button = fixture.debugElement.query(By.css('[data-testid="snapshot-volume"]'));

                expect(button).toBeNull();
                fixture.detectChanges();
            });
        });

        describe('Attach Iso', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openAttachIsoModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM does not have an iso attached', () => {
                mockVmManagementPermissionService.canAttachIso.and.returnValue(true);
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(AttachIsoComponent)
                } as NgbModalRef;
                mockVmInstance.isoid = null;
                mockModalService.openModalComponent.and.returnValue(modalRef);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="attach-iso"]'));

                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.zoneid, mockVmInstance.domainid, mockVmInstance.account, mockVmInstance.name);
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canAttachIso.and.returnValue(false);
                mockVmInstance.isoid = null;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="attach-iso"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM already has an iso attached', () => {
                mockVmManagementPermissionService.canAttachIso.and.returnValue(true);
                mockVmInstance.isoid = 'some-iso-id';
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="attach-iso"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

        });

        describe('Detach Iso', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openEjectIsoModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM has an iso attached', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canEjectIso.and.returnValue(true);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="eject-iso"]'));

                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.name);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canEjectIso.and.returnValue(false);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="eject-iso"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM does not have an iso attached', () => {
                mockVmManagementPermissionService.canEjectIso.and.returnValue(true);
                mockVmInstance.isoid = null;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="eject-iso"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM is not running', () => {
                mockVmManagementPermissionService.canEjectIso.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="eject-iso"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });
        });

        describe('Start VM', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openStartVmModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM is stopped', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(StartVmComponent)
                } as NgbModalRef;
                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canStartVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="start-vm"]'));

                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.name, mockVmInstance.zoneid);
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canStartVirtualMachine.and.returnValue(false);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="start-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM is not stopped', () => {
                mockVmManagementPermissionService.canStartVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Running;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="start-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });
        });

        describe('Stop VM', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openStopVmModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM is running', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(StopVmComponent)
                } as NgbModalRef;
                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canStopVirtualMachine.and.returnValue(true);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="stop-vm"]'));

                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.name);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canStopVirtualMachine.and.returnValue(false);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="stop-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM is not running', () => {
                mockVmManagementPermissionService.canStopVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="stop-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });
        });

        describe('Reboot VM', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openRebootVmModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM is running', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(RebootVmComponent)
                } as NgbModalRef;
                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canRebootVirtualMachine.and.returnValue(true);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="reboot"]'));

                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.name);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canRebootVirtualMachine.and.returnValue(false);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="reboot"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM is not running', () => {
                mockVmManagementPermissionService.canRebootVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="reboot"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });
        });

        describe('View Console', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openConsole').and.callThrough();
            });

            it('should enable the action when the VM is running', () => {
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="view-console"]'));

                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id);
            });

            it('should disable the action when the VM is not running', () => {
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="view-console"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });
        });

        describe('Migrate Host', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openMigrateHostModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM is running', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(MigrateHostComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                mockVmManagementPermissionService.canMigrateVirtualMachineHost.and.returnValue(true);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="migrate-host"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.name);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canMigrateVirtualMachineHost.and.returnValue(false);
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="migrate-host"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM is not running', () => {
                mockVmManagementPermissionService.canMigrateVirtualMachineHost.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Stopped;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="migrate-host"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });
        });

        describe('Expunge VM', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openExpungeDestroyedVirtualMachineModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM is destroyed', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canExpungeVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Destroyed;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="expunge-vm"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.name);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canExpungeVirtualMachine.and.returnValue(false);
                mockVmInstance.state = VmStateEnum.Destroyed;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="expunge-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM is not destroyed', () => {
                mockVmManagementPermissionService.canExpungeVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Running;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="expunge-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });
        });

        describe('Recover VM', () => {

            let spy: jasmine.Spy;

            beforeEach(() => {
                spy = spyOn(vmActionsService, 'openRecoverVirtualMachineModal').and.callThrough();
            });

            it('should enable the action when the user has permissions and the VM is destroyed', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;
                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canRecoverVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Destroyed;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="recover-vm"]'));
                button.nativeElement.click();
                fixture.detectChanges();

                expect(spy).toHaveBeenCalledOnceWith(mockVmInstance.id, mockVmInstance.name);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canRecoverVirtualMachine.and.returnValue(false);
                mockVmInstance.state = VmStateEnum.Destroyed;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="recover-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

            it('should disable the action when the VM is not destroyed', () => {
                mockVmManagementPermissionService.canRecoverVirtualMachine.and.returnValue(true);
                mockVmInstance.state = VmStateEnum.Running;
                vmDetailsStateService.selectedVM.set(mockVmInstance);
                fixture.detectChanges();

                const toggleButton = fixture.debugElement.query(By.css('button[ngbDropdownToggle]'));
                toggleButton.nativeElement.click();
                fixture.detectChanges();

                const button = fixture.debugElement.query(By.css('[data-testid="recover-vm"]'));

                expect(button).toBeNull();
                expect(spy).not.toHaveBeenCalled();
            });

        });

    });

});
