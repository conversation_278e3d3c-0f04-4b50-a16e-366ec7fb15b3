import { DeviceAlertThresholdTypeNameEnum } from '../components/device-thresholds/models/device-alert-threshold-type-name.enum';
import { DeviceThresholdInheritanceTypeEnum } from '../components/device-thresholds/models/device-threshold-inheritance-type.enum';

export class Disk {
    public name: string;
    public sizeTotal?: number;
    public sizeUsed?: number;
    public diskUsage?: number;
    public warningThreshold?: number;
    public errorThreshold?: number;
    public criticalThreshold?: number;
    public inheritanceType: DeviceThresholdInheritanceTypeEnum;
    public alertThresholdTypeName: DeviceAlertThresholdTypeNameEnum;
    public formattedName: string;
}
