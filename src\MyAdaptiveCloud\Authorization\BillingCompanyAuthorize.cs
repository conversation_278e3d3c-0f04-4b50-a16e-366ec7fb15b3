﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class BillingCompanyAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public BillingCompanyAuthorizeFilter(IUserContextService userContextService,
            IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
                _ = int.TryParse(val, out int companyId);

                var billingCompanyExists = await _entityAuthorizationService.BillingCompanyExists(companyId);

                if (billingCompanyExists)
                {
                    if (_perms != null && _perms.Count() > 0)
                    {
                        //RootOrgAuthorize
                        if (!_userContextService.HasPermission(userId, Constants.RootOrganizationId, _distance, _perms))
                        {
                            context.Result = new ForbidResult();
                        }
                    }
                    else
                    {
                        context.Result = new ForbidResult();
                    }
                }
                else
                {
                    context.Result = new BadRequestResult();
                }
            }

            await Task.CompletedTask;
        }
    }

    //RootOrgAuthorize
    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class BillingCompanyAuthorize : BaseAuthorizeAttribute
    {
        public BillingCompanyAuthorize(params Perms[] perms) : base(typeof(BillingCompanyAuthorizeFilter), perms)
        {
            Name = "companyId";
        }
    }
}