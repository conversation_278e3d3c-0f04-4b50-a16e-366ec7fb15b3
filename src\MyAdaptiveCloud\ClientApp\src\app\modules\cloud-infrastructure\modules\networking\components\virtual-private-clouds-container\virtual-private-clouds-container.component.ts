import { Component } from '@angular/core';
import { ChangeDetectionStrategy } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-virtual-private-clouds-container',
    imports: [RouterOutlet],
    templateUrl: './virtual-private-clouds-container.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class VirtualPrivateCloudsContainerComponent {

}
