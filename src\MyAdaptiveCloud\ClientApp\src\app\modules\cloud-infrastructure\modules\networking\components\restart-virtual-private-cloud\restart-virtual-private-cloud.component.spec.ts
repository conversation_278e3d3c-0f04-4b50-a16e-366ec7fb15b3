import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';
import { RestartVirtualPrivateCloudComponent } from './restart-virtual-private-cloud.component';

describe('RestartVirtualPrivateCloudComponent', () => {

    let component: RestartVirtualPrivateCloudComponent;
    let fixture: ComponentFixture<RestartVirtualPrivateCloudComponent>;
    let mockVirtualPrivateCloudService: jasmine.SpyObj<VirtualPrivateCloudService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [RestartVirtualPrivateCloudComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VirtualPrivateCloudService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(RestartVirtualPrivateCloudComponent);
        component = fixture.componentInstance;
        mockVirtualPrivateCloudService = TestBed.inject(VirtualPrivateCloudService) as jasmine.SpyObj<VirtualPrivateCloudService>;
        mockVirtualPrivateCloudService.restartVirtualPrivateCloud.and.returnValue(of('jobId1'));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;
    });

    it('should close modal on cancel', () => {

        component.virtualPrivateCloud.set({
            account: 'account',
            description: 'Test description',
            id: 'test-id',
            name: 'Test VPC',
            isRedundant: false,
            state: 'Enabled',
            domain: 'domain',
            domainId: 'domain-id',
            zoneId: 'zone-id',
            zoneName: 'zone-name',
        });

        fixture.detectChanges();
        const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
        cancelButton.click();
        fixture.detectChanges();

        expect(activeModal.close).toHaveBeenCalledTimes(1);
    });

    it('should make redundant and cleanup when the VPC is not redundant and make redundant is selected', () => {

        component.virtualPrivateCloud.set({
            account: 'account',
            description: 'Test description',
            id: 'test-id',
            name: 'Test VPC',
            isRedundant: false,
            state: 'Enabled',
            domain: 'domain',
            domainId: 'domain-id',
            zoneId: 'zone-id',
            zoneName: 'zone-name',
        });

        fixture.detectChanges();

        const makeRedundantControl = fixture.debugElement.query(By.css('[data-testid="make-redundant-checkbox"]')).nativeElement as HTMLInputElement;
        makeRedundantControl.checked = true;
        makeRedundantControl.dispatchEvent(new Event('change'));
        fixture.detectChanges();

        const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
        submit.click();
        fixture.detectChanges();

        expect(mockVirtualPrivateCloudService.restartVirtualPrivateCloud).toHaveBeenCalledOnceWith('test-id', true, null, true);
        expect(activeModal.close).toHaveBeenCalledOnceWith('jobId1');
    });

    it('should disable livePatchNetworkRouters when cleanup is enabled', () => {

        component.virtualPrivateCloud.set({
            account: 'account',
            description: 'Test description',
            id: 'test-id',
            name: 'Test VPC',
            isRedundant: true,
            state: 'Enabled',
            domain: 'domain',
            domainId: 'domain-id',
            zoneId: 'zone-id',
            zoneName: 'zone-name',
        });

        fixture.detectChanges();

        const cleanupControl = fixture.debugElement.query(By.css('[data-testid="cleanup-checkbox"]')).nativeElement as HTMLInputElement;
        cleanupControl.checked = true;
        cleanupControl.dispatchEvent(new Event('change'));
        fixture.detectChanges();

        const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
        submit.click();
        fixture.detectChanges();

        expect(mockVirtualPrivateCloudService.restartVirtualPrivateCloud).toHaveBeenCalledOnceWith('test-id', true, null, null);
        expect(activeModal.close).toHaveBeenCalledOnceWith('jobId1');
    });

    it('should disable livePatchNetworkRouters when cleanup is enabled', () => {

        component.virtualPrivateCloud.set({
            account: 'account',
            id: 'test-id',
            name: 'Test VPC',
            isRedundant: true,
            state: 'Enabled',
            description: 'Test description',
            domain: 'domain',
            domainId: 'domain-id',
            zoneId: 'zone-id',
            zoneName: 'zone-name',
        });

        fixture.detectChanges();

        const makeRedundantCheckbox = fixture.debugElement.query(By.css('[data-testid="make-redundant-checkbox"]'));
        expect(makeRedundantCheckbox).toBeNull();
    });

});

