import { CurrencyPipe, DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, input, output, signal, viewChild } from '@angular/core';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { AdaptiveCloudUsageSummaryAccount, AdaptiveCloudUsageSummaryAccountDetail } from '@app/shared/models/adaptivecloud/adaptivecloud-usage-summary-account.mode';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { ElementSelector } from '@app/shared/models/element-selector.model';
import { AdaptiveCloudUsageSharedService } from '@app/shared/services/adaptivecloud-usage-shared.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';

@Component({
    selector: 'app-adaptivecloud-usage-summary',
    imports: [NgxDatatableModule, NgbPopover, TableActionComponent, DecimalPipe, CurrencyPipe],
    templateUrl: './adaptivecloud-usage-summary.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdaptiveCloudUsageSummaryComponent extends BaseListClientComponent<AdaptiveCloudUsageSummaryAccount> implements OnInit {
    private readonly usageService = inject(AdaptiveCloudUsageSharedService);
    private readonly userContextService = inject(UserContextService);

    private readonly usageTemplate = viewChild<TemplateRef<never>>('usageTemplate');
    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');
    private readonly summaryTemplate = viewChild<TemplateRef<never>>('summaryTemplate');
    private readonly accountNameTemplate = viewChild<TemplateRef<never>>('accountNameTemplate');

    readonly period = input.required<string>();
    readonly showDetails = output<AdaptiveCloudUsageSummaryAccount>();
    readonly dataLoaded = output<ElementSelector[]>();

    protected readonly total = signal<AdaptiveCloudUsageSummaryAccount>(null);

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Account',
                prop: 'accountName',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.accountNameTemplate()
            },
            {
                name: 'vCPU',
                prop: 'vCpu',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: this.costComparator.bind(this),
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'RAM',
                prop: 'ram',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: this.costComparator.bind(this),
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'IP Address',
                prop: 'ipAddress',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: this.costComparator.bind(this),
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'Network Bytes',
                prop: 'networkBytes',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: this.costComparator.bind(this),
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'Primary Storage',
                prop: 'primaryStorage',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: this.costComparator.bind(this),
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'Secondary Storage',
                prop: 'secondaryStorage',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: this.costComparator.bind(this),
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'Licensing',
                prop: 'licensing',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: this.costComparator.bind(this),
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'Total',
                prop: 'accountTotal',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.usageTemplate(),
                comparator: (totalA: number, totalB: number) => totalA - totalB,
                summaryTemplate: this.summaryTemplate()
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                width: 80,
                sortable: false,
                resizeable: false,
                canAutoResize: false,
            }
        ];

        const table = this.table();
        table.summaryRow = true;
        table.summaryPosition = 'bottom';

        this.usageService.getUsageSummary(this.userContextService.currentUser.organizationId, this.period()).subscribe(res => {
            super.initialize(() => of({ data: res.data.accounts, message: '' }), columns);
            this.total.set(res.data.total);
        });

    }

    private costComparator(
        costA: AdaptiveCloudUsageSummaryAccountDetail,
        costB: AdaptiveCloudUsageSummaryAccountDetail
    ): number {
        return costA.totalCost - costB.totalCost;
    }

    public showAccountDetails(row: AdaptiveCloudUsageSummaryAccount): void {
        this.showDetails.emit(row);
    }

    public override onDataLoaded() {
        super.onDataLoaded();
        this.dataLoaded.emit(this.rows?.map(account => ({ id: account.accountId, name: account.accountName })));
    }
}
