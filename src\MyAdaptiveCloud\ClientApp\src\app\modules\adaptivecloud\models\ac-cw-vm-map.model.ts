import { IUserAction } from '@app/shared/models/user-actions/user-action';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';
import { AcCwProductMap } from './ac-cw-product-map.model';

export class AcCwVmMap implements IUserAction {
    public organizationId: number;
    public canEdit: UserActionState;
    public canDelete: UserActionState;
    public canCreate: UserActionState;
    public canView: UserActionState;
    public id: number;
    public acId: string;
    public acName: string;
    public acType: string;
    public priority: number;
    public quantityValue: string;
    public isFormula: boolean;
    public productMap: AcCwProductMap;
    public cwProductName: string;
    public account: string;
    public domain: string;
    public startDate: Date;
    public endDate?: Date;
}
