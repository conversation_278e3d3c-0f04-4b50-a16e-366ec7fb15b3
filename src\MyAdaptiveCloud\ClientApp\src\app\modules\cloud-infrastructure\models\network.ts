export interface Network {
    account: string;
    acltype: string;
    egressdefaultpolicy?: boolean;
    broadcastdomaintype?: string;
    broadcasturi?: string;
    canusefordeploy: boolean;
    cidr?: string;
    created: string;
    displaynetwork: boolean;
    displaytext: string;
    dns1: string;
    dns2: string;
    domain: string;
    domainid: string;
    hasannotations: boolean;
    gateway?: string;
    id: string;
    ip6dns1: string;
    ip6dns2: string;
    ispersistent: boolean;
    issystem: boolean;
    name: string;
    networkdomain?: string;
    networkofferingavailability: string;
    networkofferingconservemode: boolean;
    networkofferingdisplaytext: string;
    networkofferingid: string;
    networkofferingname: string;
    physicalnetworkid: string;
    privatemtu: number;
    publicmtu: number;
    receivedbytes: number;
    redundantrouter: boolean;
    related: string;
    restartrequired: boolean;
    sentbytes: number;
    specifyipranges: boolean;
    state: 'Implemented' | 'Implementing' | 'Allocated' | 'Setup';
    strechedl2subnet: boolean;
    supportsvmautoscaling: boolean;
    tags: string[];
    traffictype: string;
    type: 'Isolated' | 'Shared' | 'L2';
    vpcname?: string;
    vlan?: string;
    zoneid: string;
    zonename: string;
}
