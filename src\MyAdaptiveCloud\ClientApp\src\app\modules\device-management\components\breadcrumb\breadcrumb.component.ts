import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { DeviceFolderTreeNode } from '../../models/device-folder-tree-node';
import { DeviceTreeNode } from '../../models/device-tree-node';

@Component({
    selector: 'app-device-folders-breadcrumb',
    templateUrl: './breadcrumb.component.html',
    styleUrl: './breadcrumb.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceFoldersBreadcrumbComponent {

    public readonly folder = input.required<DeviceFolderTreeNode>();
    public readonly textClass = input<string>('fw-semibold');
    public readonly isNavigable = input<boolean>(true);
    public readonly device = input<DeviceTreeNode>();

    public readonly breadcrumbItemSelected = output<DeviceFolderTreeNode | DeviceTreeNode>();
}
