import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiService } from '@app/shared/services/api.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { Observable } from 'rxjs';
import { MitigationViewModel } from '../models/mitigation-view-model';
import { DDOS_MITIGATION_ENDPOINT_SEGMENTS } from './ddos-mitigation-endpoint-segments';

@Injectable({
    providedIn: 'root'
})
export class DDoSMitigationMitigationService {

    private readonly apiService = inject(ApiService);
    private readonly userContext = inject(UserContextService);

    getList(showLoader: boolean): Observable<ApiDataResult<MitigationViewModel[]>> {
        return this.apiService.get(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/${DDOS_MITIGATION_ENDPOINT_SEGMENTS.MITIGATION}/${this.userContext.currentUser.organizationId}`, null, showLoader);
    }

}

