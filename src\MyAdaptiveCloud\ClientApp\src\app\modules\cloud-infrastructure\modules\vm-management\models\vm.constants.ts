export const VIRTUAL_MACHINES_ENDPOINT_NAMES = {
    listVirtualMachines: 'listVirtualMachines',
    listVolumes: 'listVolumes',
    stopVirtualMachine: 'stopVirtualMachine',
    destroyVirtualMachine: 'destroyVirtualMachine',
    rebootVirtualMachine: 'rebootVirtualMachine',
    startVirtualMachine: 'startVirtualMachine',
    listPods: 'listPods',
    listClusters: 'listClusters',
    listHosts: 'listHosts',
    listSSHKeyPairs: 'listSSHKeyPairs',
    resetSSHKeyForVirtualMachine: 'resetSSHKeyForVirtualMachine',
    snapshotVirtualMachine: 'createVMSnapshot',
    expungeVirtualMachine: 'expungeVirtualMachine',
    recoverVirtualMachine: 'recoverVirtualMachine',
    attachIso: 'attachIso',
    ejectIso: 'detachIso',
    migrateVirtualMachine: 'migrateVirtualMachine',
    findHostsForMigration: 'findHostsForMigration',
    snapshotVolume: 'createSnapshot',
    reinstallVirtualMachine: 'restoreVirtualMachine',
    listNetworks: 'listNetworks',
    resetVirtualMachinePassword: 'resetPasswordForVirtualMachine',
    listServiceOfferings: 'listServiceOfferings',
    listAffinityGroups: 'listAffinityGroups',
    listNetworkOfferings: 'listNetworkOfferings',
    createNetwork: 'createNetwork',
    listVPCs: 'listVPCs',
    createVirtualMachine: 'deployVirtualMachine',
    listIsos: 'listIsos',
    listTemplates: 'listTemplates',
    createVolume: 'createVolume',
    createTags: 'createTags',
    deleteTags: 'deleteTags',
    updateVirtualMachineAffinityGroups: 'updateVMAffinityGroup',
    listAffinityGroupTypes: 'listAffinityGroupTypes',
    createAffinityGroup: 'createAffinityGroup',
    listVirtualMachineSnapshots: 'listVMSnapshot',
    createSnapshotFromVirtualMachineSnapshot: 'createSnapshotFromVMSnapshot',
    deleteVirtualMachineSnapshot: 'deleteVMSnapshot',
    revertToVirtualMachineSnapshot: 'revertToVMSnapshot'
};

export const MAX_BOOT_DELAY = 65;
export const MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB = 32;
export const CPU_CUSTOM_OFFERING_MIN_VALUE = 1;
