import { inject, Injectable } from '@angular/core';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { forkJoin, map, Observable, of, switchMap } from 'rxjs';
import { NETWORKING_ENDPOINT_NAMES } from '../models/networking.constants';
import { RemoteVpnGateway } from '../models/remote-vpn-gateway';
import { CreateRemoteVpnGatewayRequest } from '../requests/create-remote-vpn-gateway.request';
import { CreateRemoteVpnGatewayResponse } from '../responses/create-remote-vpn-gateway.response';
import { DeleteRemoteVpnGatewayResponse } from '../responses/delete-remote-vpn-gateway.response';
import { ListRemoteVpnGatewayResponse, ListRemoteVpnGateways } from '../responses/list-remote-vpn-gateway.response';

@Injectable({
    providedIn: 'root'
})
export class RemoteVpnGatewayService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getRemoteVpnGatewayList(): Observable<RemoteVpnGateway[]> {
        const pageSize = 500;

        // Fetch the first batch, which will return the total count, then fetch the rest of the records in parallel
        return this.getRemoteVpnGatewayListBatch(1, pageSize).pipe(
            map(res => {
                const records = [...res?.vpncustomergateway ?? []];
                const remainingRecords = res.count - pageSize;

                if (remainingRecords > 0) {
                    const countOfRequestBatches = Math.ceil(remainingRecords / pageSize);
                    const requests: Observable<ListRemoteVpnGateways>[] = [];
                    for (let i = 2; i <= countOfRequestBatches + 1; i++) {
                        requests.push(this.getRemoteVpnGatewayListBatch(i, pageSize));
                    }
                    return forkJoin(requests).pipe(map(responses => {
                        responses.forEach(response => {
                            records.push(...response.vpncustomergateway);
                        });
                        return records;
                    }));
                }
                return of(records);

            }),
            switchMap(records => records)
        );
    }

    deleteRemoteVpnGateway(id: string): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.deleteRemoteVpnGateway,
            id,
        };

        return this.cloudInfraApiService.get<DeleteRemoteVpnGatewayResponse>(params).pipe(map(response => response.deletevpncustomergatewayresponse?.jobid));
    }

    createRemoteVpnGateway(request: CreateRemoteVpnGatewayRequest): Observable<string> {
        const params = {
            command: NETWORKING_ENDPOINT_NAMES.createRemoteVpnGateway,
            cidrlist: request.cidrList,
            espPolicy: request.perfectForwardSecrecy ? `${request.espEncryption}-${request.espHash};${request.perfectForwardSecrecy}` : `${request.espEncryption}-${request.espHash}`,
            gateway: request.gateway,
            ikepolicy: request.ikeDH ? `${request.ikeEncryption}-${request.ikeHash};${request.ikeDH}` : `${request.ikeEncryption}-${request.ikeHash}`,
            ipsecpsk: request.ipSecurityPreSharedKey,
            account: request.account,
            domainid: request.domainId,
            name: request.name,
            dpd: request.deadPeerDetection ? 'true' : 'false',
            esplifetime: request.espLifetime.toString(),
            ikeversion: request.ikeVersion,
            forceencap: request.forceEncapsulation ? 'true' : 'false',
            ikelifetime: request.ikeLifetime.toString(),
            splitconnections: request.splitConnections ? 'true' : 'false'
        };

        return this.cloudInfraApiService.get<CreateRemoteVpnGatewayResponse>(params).pipe(map(response => response.createvpncustomergatewayresponse?.jobid));
    }

    private getRemoteVpnGatewayListBatch(currentPage: number, pageSize: number): Observable<ListRemoteVpnGateways> {
        const params: Record<string, string> = {
            command: NETWORKING_ENDPOINT_NAMES.listRemoteVpnGateways,
            listall: 'true',
            pagesize: pageSize.toString(),
            page: currentPage.toString()
        };

        return this.cloudInfraApiService.get<ListRemoteVpnGatewayResponse>(params)
            .pipe(map((response: ListRemoteVpnGatewayResponse) => (response.listvpncustomergatewaysresponse)));
    }

}
