import { FormControl } from '@angular/forms';
import { NetworkOfferingViewModel } from '../models/network-offering.view-model';

export interface Layer2NetworkForm {
    name: FormControl<string | null>;
    description: FormControl<string | null>;
    networkOffering: FormControl<NetworkOfferingViewModel | null>;
    vlan: FormControl<string | null>;
    bypassVLanId: FormControl<boolean | null>;
    secondaryVLanType: FormControl<string | null>;
    secondaryVLanId: FormControl<string | null>;
}
