import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { NetworkingService } from '../../services/networking.service';
import { EditLevel2NetworkComponent } from './edit-level2-network.component';

describe('EditLevel2NetworkComponent', () => {

    let fixture: ComponentFixture<EditLevel2NetworkComponent>;
    let component: EditLevel2NetworkComponent;

    let mockNetworkingService: jasmine.SpyObj<NetworkingService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [EditLevel2NetworkComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(NetworkingService),
            ]
        });

        mockNetworkingService = TestBed.inject(NetworkingService) as jasmine.SpyObj<NetworkingService>;
        mockNetworkingService.editLevel2Network.and.returnValue(of('job-id'));

        fixture = TestBed.createComponent(EditLevel2NetworkComponent);
        component = fixture.componentInstance;
        component.network.set({
            id: 'network-id',
            name: 'Test Network',
            description: 'Test Network Description',
            cidr: '***********/24',
            networkDomain: 'test-domain',
            networkOfferingId: 'offering-id',
            zoneId: 'zone-id',
            domainId: 'domain-id',
            account: 'test-account',
            domain: 'Test Domain',
            isSystem: false,
            state: 'Implemented',
            type: 'L2',
            vpc: null,
            zoneName: 'Test Zone',
        });

        fixture.detectChanges();
    });

    describe('Form Interaction', () => {

        let submit: HTMLButtonElement;

        beforeEach(() => {
            submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
        });

        it('should initialize form with network values', () => {
            submit.click();

            fixture.detectChanges();
            expect(mockNetworkingService.editLevel2Network).toHaveBeenCalledOnceWith('network-id', 'Test Network', 'Test Network Description', '***********/24');
        });

        it('should not submit when name is invalid', () => {

            const nameInput = fixture.debugElement.query(By.css('[data-testid="name-input"]')).nativeElement as HTMLInputElement;
            nameInput.value = '';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();

            nameInput.value = '';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();

            nameInput.value = '213123-012932-0193-29m-83m2r8m9vrmruivueivuuuewvfdsfsfsfdsfjsdklfjsklfjskldfjKfjklfjklfjrkjcmewrumucwrmcicomuewmoriueworicuwormicuworimuwcirpppppppppppppppppppppppppppppfdsfdsfsfsdooofsfsofpsdofpsdofpsdofpsofpsdopfospfospfospfmuwcoiruomiwcruoiwcuroimwcurmoiw';
            nameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();
            expect(mockNetworkingService.editLevel2Network).not.toHaveBeenCalled();

        });

        it('should not submit when description is invalid', () => {

            const descriptionInput = fixture.debugElement.query(By.css('[data-testid="description-input"]')).nativeElement as HTMLInputElement;
            descriptionInput.value = '';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();

            descriptionInput.value = '';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();

            descriptionInput.value = '213123-012932-0193-29m-83m2r8m9vrmruivueivuuuewvfdsfsfsfdsfjsdklfjsklfjskldfjKfjklfjklfjrkjcmewrumucwrmcicomuewmoriueworicuwormicuworimuwcirpppppppppppppppppppppppppppppfdsfdsfsfsdooofsfsofpsdofpsdofpsdofpsofpsdopfospfospfospfmuwcoiruomiwcruoiwcuroimwcurmoiw';
            descriptionInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();
            expect(mockNetworkingService.editLevel2Network).not.toHaveBeenCalled();

        });

        it('should not submit when cidr is invalid', () => {
            const cidrInput = fixture.debugElement.query(By.css('[data-testid="cidr-input"]')).nativeElement as HTMLInputElement;
            cidrInput.value = 'Invalid CIDR';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            cidrInput.value = '';
            cidrInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submit.click();
            expect(mockNetworkingService.editLevel2Network).not.toHaveBeenCalled();
        });

    });

});
