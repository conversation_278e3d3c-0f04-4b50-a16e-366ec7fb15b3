import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { Bgp } from '../../models/bgp.model';
import { DdosMitigationBgpAdvertisementsComponent } from './ddos-mitigation-bgp-advertisements.component';
import { DDoSMitigationBgpService } from '../../services/ddos-mitigation-bgp.service';

describe('DdosMitigationBgpAdvertisementsComponent', () => {

    const data: Bgp[] = [
        {
            neighborIp: '**************',
            neighborRemoteAs: 393775,
            prefix: '************/29',
            nextHop: '**************',
            asPath: [],
            communities: [],
            med: 0,
            localPref: 100,
            origin: 0,
            aggregator: null,
            atomicAggregate: false,
            extendedCommunities: [],
            largeCommunities: [],
            clusterList: [
                '**************',
                '**************'
            ],
            originatorId: '**************',
            simulate: false
        },
        {
            neighborIp: '**************',
            neighborRemoteAs: 393775,
            prefix: '************/29',
            nextHop: '**************',
            asPath: [],
            communities: [],
            med: 0,
            localPref: 100,
            origin: 0,
            aggregator: null,
            atomicAggregate: false,
            extendedCommunities: [],
            largeCommunities: [],
            clusterList: [
                '**************',
                '**************'
            ],
            originatorId: '**************',
            simulate: false
        },
    ];

    let fixture: ComponentFixture<DdosMitigationBgpAdvertisementsComponent>;
    let mockDDoSMitigationBgpService: jasmine.SpyObj<DDoSMitigationBgpService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationBgpAdvertisementsComponent],
            providers: [
                provideMock(DDoSMitigationBgpService),
            ]
        })
            .compileComponents();

        mockDDoSMitigationBgpService = TestBed.inject(DDoSMitigationBgpService) as jasmine.SpyObj<DDoSMitigationBgpService>;
        mockDDoSMitigationBgpService.getAdvertisedList.and.returnValue(of({ data, message: '' }));

        fixture = TestBed.createComponent(DdosMitigationBgpAdvertisementsComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            fixture.detectChanges();
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
        });

        it('should call getAdvertisedBgpList', () => {
            expect(mockDDoSMitigationBgpService.getAdvertisedList).toHaveBeenCalledTimes(1);
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });

    });
});
