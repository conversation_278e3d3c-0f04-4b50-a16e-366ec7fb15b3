<form class="mt-3" [formGroup]="form">
    <div class="row mb-3">
        <label for="name" class="col col-form-label">Name<span class="required-asterisk">*</span></label>
        <div class="col-8">
            <input class="form-control" id="name" formControlName="name"
                [class]="{ 'is-invalid': form.controls.name.invalid && form.controls.name.dirty }" autocomplete="off" />
        </div>
    </div>

    <div class="row mb-3">
        <label for="description" class="col col-form-label">Description</label>
        <div class="col-8">
            <input class="form-control" id="description" formControlName="description" />
        </div>
    </div>

    <div class="row mb-3">
        <label class="col col-form-label">Network Offering<span class="required-asterisk">*</span></label>
        <div class="col-8">
            <ng-select [items]="networkOfferings()" bindLabel="name" formControlName="networkOffering"
                [class]="{ 'is-invalid': form.controls.networkOffering.invalid && form.controls.networkOffering.dirty }" />
        </div>
    </div>
    <div class="row mb-3">
        <label for="vlanVni" class="col col-form-label">VLAN/VNI<span class="required-asterisk">*</span></label>
        <div class="col-8">
            <input class="form-control" id="vlanVni" formControlName="vlanVni"
                [class]="{ 'is-invalid': form.controls.vlanVni.invalid && form.controls.vlanVni.dirty }" />
            @if (form.hasError('pattern', 'vlanVni') && form.controls.vlanVni.dirty) {
                <div class="text-danger mt-1">Only positive integer numbers are allowed.</div>
            }
        </div>
    </div>

    <!-- IPv4 Block -->
    <!-- Validation message for either IPv4 or IPv6 -->
    @if (form.hasError('ipv4OrIpv6Incomplete') && form.touched) {
        <div class="text-danger mt-1">IPv4 or all IPv6 fields must be completed.</div>
    }

    <fieldset class="border rounded mb-3 p-3">
        <legend class="float-none w-auto px-2 text-muted small">IPv4</legend>

        <div class="row mb-3">
            <label for="ipv4Gateway" class="col col-form-label d-flex align-items-center gap-1">
                IPv4 Gateway
                <i [ngbPopover]="'The gateway of the network. Required for shared networks and isolated networks when it belongs to VPC'"
                    triggers="hover" class="fa-solid fa-circle-info text-secondary text-nowrap"></i>
            </label>
            <div class="col-8">
                <input class="form-control" id="ipv4Gateway" formControlName="ipv4Gateway" />
            </div>
        </div>

        <div class="row mb-3">
            <label for="ipv4Netmask" class="col col-form-label d-flex align-items-center gap-1">
                IPv4 Netmask
                <i [ngbPopover]="'The netmask of the network. Required for shared networks and isolated networks when it belongs to VPC'"
                    triggers="hover" class="fa-solid fa-circle-info text-secondary"></i>
            </label>
            <div class="col-8">
                <input class="form-control" id="ipv4Netmask" formControlName="ipv4Netmask" />
            </div>
        </div>

        <div class="row mb-3">
            <label for="ipv4StartIp" class="col col-form-label">
                IPv4 Start IP
                <i [ngbPopover]="'The beginning IP address in the network IP range'" triggers="hover"
                    class="fa-solid fa-circle-info fa-circle-info text-secondary gap-1"></i>
            </label>
            <div class="col-8">
                <input class="form-control" id="ipv4StartIp" formControlName="ipv4StartIp" />
            </div>
        </div>

        <div class="row mb-3">
            <label for="ipv4EndIp" class="col col-form-label">IPv4 End IP
                <i [ngbPopover]="'The ending IP address in the network IP range. If not specified, will be defaulted to startIP.'"
                    triggers="hover" class="fa-solid fa-circle-info text-secondary"></i>
            </label>
            <div class="col-8">
                <input class="form-control" id="ipv4EndIp" formControlName="ipv4EndIp" />
            </div>
        </div>
    </fieldset>

    <!-- IPv6 Block -->
    <!-- Validation message for either IPv4 or IPv6 -->
    @if (form.hasError('ipv4OrIpv6Incomplete') && form.touched) {
        <div class="text-danger mt-1">IPv4 or all IPv6 fields must be completed.</div>
    }

    <fieldset class="border rounded mb-3 p-3">
        <legend class="float-none w-auto px-2 text-muted small">IPv6</legend>

        <div class="row mb-3">
            <label for="ipv6Gateway" class="col col-form-label d-flex align-items-center gap-1">
                IPv6 Gateway
                <i [ngbPopover]="'The gateway of the IPv6 network. Required for Shared networks'" triggers="hover"
                    class="fa-solid fa-circle-info text-secondary text-nowrap"></i>
            </label>
            <div class="col-8">
                <input class="form-control" id="ipv6Gateway" formControlName="ipv6Gateway" />
            </div>
        </div>

        <div class="row mb-3">
            <label for="ipv6CIDR" class="col col-form-label">IPv6 CIDR
                <i [ngbPopover]="'The CIDR of IPv6 network. Must be at least /64.'" triggers="hover"
                    class="fa-solid fa-circle-info text-secondary"></i>
            </label>
            <div class="col-8">
                <input class="form-control" id="ipv6CIDR" formControlName="ipv6CIDR" />
            </div>
        </div>

        <div class="row mb-3">
            <label for="ipv6StartIp" class="col col-form-label">IPv6 Start IP <i
                    [ngbPopover]="'The beginning IPv6 address in the IPv6 network range.'" triggers="hover"
                    class="fa-solid fa-circle-info text-secondary"></i></label>
            <div class="col-8">
                <input class="form-control" id="ipv6StartIp" formControlName="ipv6StartIp" />
            </div>
        </div>

        <div class="row mb-3">
            <label for="ipv6EndIp" class="col col-form-label">IPv6 End IP
                <i [ngbPopover]="'The ending IPv6 address in the IPv6 network range'" triggers="hover"
                    class="fa-solid fa-circle-info text-secondary"></i>
            </label>
            <div class="col-8">
                <input class="form-control" id="ipv6EndIp" formControlName="ipv6EndIp" />
            </div>
        </div>
    </fieldset>


    <!-- Network Domain -->
    <div class="row mb-3">
        <label for="networkDomain" class="col col-form-label">Network Domain</label>
        <div class="col-8">
            <input class="form-control" id="networkDomain" formControlName="networkDomain" />
        </div>
    </div>

    <!-- Checkbox -->
    <div class="row mb-3">
        <div class="col">
            <div class="form-check">
                <input class="form-check-input" id="hideIpAddressUsage" type="checkbox"
                    formControlName="hideIpAddressUsage" />
                <label class="form-check-label" for="hideIpAddressUsage">
                    Hide IP Address Usage
                    <i [ngbPopover]="'When toggled on, IP address usage for the network will not be exported by the listUsageRecords API.'"
                        triggers="hover" class="fa-solid fa-circle-info text-secondary"></i>
                </label>
            </div>
        </div>
    </div>
    
</form>
