import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { WhitelabelImageDirective } from '@app/shared/directives/whitelabel-image.directive';
import { WhiteLabelImageType } from '@app/shared/models/whitelabel-image-type';
import { UserContextService } from '@app/shared/services/user-context.service';

@Component({
    selector: 'app-preloader',
    imports: [WhitelabelImageDirective, AsyncPipe],
    templateUrl: './preloader.component.html',
    styleUrl: './preloader.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class PreloaderComponent {
    protected readonly userContextService = inject(UserContextService);

    protected readonly whiteLabelImageType = WhiteLabelImageType;

}
