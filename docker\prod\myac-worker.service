[Unit]
Description=MyAdaptiveCloud Worker Service
After=docker.service
Requires=docker.service

[Service]
WorkingDirectory=/home/<USER>/myac-worker
ExecStart=docker compose --file /home/<USER>/myac-worker/docker-compose.yaml up -d
ExecStop=docker compose --file /home/<USER>/myac-worker/docker-compose.yaml down
Restart=no
RemainAfterExit=yes
SyslogIdentifier=myac-worker
User=myac

[Install]
WantedBy=multi-user.target
