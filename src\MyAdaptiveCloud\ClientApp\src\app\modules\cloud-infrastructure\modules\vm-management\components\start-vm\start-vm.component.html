<div class="modal-header">
    <h4 class="modal-title">Start VM</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="cancel()"></button>
</div>
<div class="modal-body">
    <p>Please confirm that you want to start this VM</p>
    <form [formGroup]="form" class="row g-3">
        @if (cloudInfraPermissionService.isRootAdmin()) {
        <div class="col-12">
            <label class="form-label">Pod
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Destination Pod ID to deploy the VM to.'" triggers="hover" container="body"></i>
            </label>
            <ng-select [items]="pods$| async" bindLabel="name" bindValue="id" formControlName="pod" />
        </div>
        <div class="col-12">
            <label class="form-label">Cluster
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Destination Cluster ID to deploy the VM to.'" triggers="hover" container="body"></i>
            </label>
            <ng-select [items]="clusters$ | async" bindLabel="name" bindValue="id" formControlName="cluster" />
        </div>
        <div class="col-12">
            <label class="form-label">Host
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Destination Host ID to deploy the VM to.'" triggers="hover" container="body"></i>
            </label>
            <ng-select [items]="hosts$ | async" bindLabel="name" bindValue="id" formControlName="host" />
        </div>
        }
        <div class="col-12">
            <label for="boot-delay" class="form-label">Boot Delay
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Number of seconds to wait for input on how to boot VM. Max delay of 65 seconds.'"
                    triggers="hover" container="body"></i>
            </label>
            <input id="boot-delay" class="form-control" formControlName="bootDelay"
                [class]="{ 'is-invalid': form.controls.bootDelay.invalid }" />
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    <app-btn-submit [disabled]="form.invalid || isSubmitting()" [btnClasses]="'btn-primary'"
        (submitClickEvent)="startVirtualMachine()">OK</app-btn-submit>
</div>
