import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, OnInit, signal } from '@angular/core';
import { UserContextService } from '@app/shared/services/user-context.service';
import { AdaptiveCloudUsageDetailsService } from '../../models/adaptive-cloud-usage-details-service.model';
import { AdaptiveCloudUsageDetailsVirtualMachineService } from '../../models/adaptive-cloud-usage-details-virtual-machine-service.model';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AdaptiveCloudUsageDetailServiceComponent } from './adaptivecloud-usage-detail-service.component';

@Component({
    selector: 'app-adaptivecloud-usage-detail',
    imports: [AdaptiveCloudUsageDetailServiceComponent, DatePipe],
    templateUrl: './adaptivecloud-usage-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdaptiveCloudUsageDetailComponent implements OnInit {
    private readonly usageService = inject(AdaptiveCloudUsageService);
    private readonly userContextService = inject(UserContextService);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) selectedPeriod: string;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) selectedAccountId: string;

    protected readonly storageServices = signal<AdaptiveCloudUsageDetailsService[]>([]);
    protected readonly virtualMachineServices = signal<AdaptiveCloudUsageDetailsVirtualMachineService[]>([]);
    protected readonly networkServices = signal<AdaptiveCloudUsageDetailsService[]>([]);
    protected readonly isDataLoaded = signal<boolean>(false);

    public ngOnInit(): void {
        this.usageService
            .getUsageDetails({ accountId: this.selectedAccountId, period: this.selectedPeriod, organizationId: this.userContextService.currentUser.organizationId })
            .subscribe(res => {
                this.virtualMachineServices.set(res.data.virtualMachineServices);
                this.storageServices.set(res.data.storageServices);
                this.networkServices.set(res.data.networkServices);
                this.isDataLoaded.update(u => !u);
            });
    }

}
