import { ComponentFixture, discardPeriodicTasks, fakeAsync, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { SnapshotVolumeComponent } from './snapshot-volume.component';

describe('SnapshotVolumeComponent', () => {

    let component: SnapshotVolumeComponent;
    let fixture: ComponentFixture<SnapshotVolumeComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [SnapshotVolumeComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(SnapshotVolumeComponent);
        component = fixture.componentInstance;
        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.snapshotVolume.and.returnValue(of('jobId1'));
        mockVmManagementService.getVolumesByVirtualMachine.and.returnValue(of([{ name: 'Volume 1', id: 'V1' }, { name: 'Volume 2', id: 'V2' }]));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.inputData.set({
            virtualMachineId: 'test-id',
            domainId: 'domain-id',
            account: 'account'
        });
    });

    describe('Submit', () => {

        it('should close modal on cancel', () => {

            fixture.detectChanges();
            const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit the form with snapshot memory set to true when the vm is running', fakeAsync(() => {

            fixture.detectChanges();

            const nameControl = fixture.debugElement.query(By.css('#snapshotName')).nativeElement as HTMLInputElement;
            nameControl.value = 'Volume Name';
            nameControl.dispatchEvent(new Event('input'));

            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            const expungeControl = fixture.debugElement.query(By.css('#asyncBackup')).nativeElement as HTMLInputElement;
            expungeControl.checked = true;
            expungeControl.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.snapshotVolume).toHaveBeenCalledOnceWith('V2', true, 'Volume Name', 'domain-id', 'account');
            expect(activeModal.close).toHaveBeenCalledTimes(1);

            discardPeriodicTasks();
        }));

    });

});

