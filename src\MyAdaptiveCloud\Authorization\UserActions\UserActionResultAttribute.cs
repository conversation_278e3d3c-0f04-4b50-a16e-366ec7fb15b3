﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ActionConstraints;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Api.ViewModel;
using MyAdaptiveCloud.Core.Common.UserActions;
using MyAdaptiveCloud.Services.Services;
using System.Reflection;

namespace MyAdaptiveCloud.Api.Authorization.UserActions
{
    public class UserActionResultAttribute<T> : IAsyncResultFilter where T : IUserAction
    {
        private IUserContextService _userContextService;
        private IIdentityService _identityService;
        private IHttpContextAccessor _httpContextAccessor;

        public UserActionResultAttribute(
            IUserContextService userContextService,
            IIdentityService identityService,
            IHttpContextAccessor httpContextAccessor)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
        {
            if ((context.ActionDescriptor.ActionConstraints[0] as HttpMethodActionConstraint)?.HttpMethods.All(x => x == "GET") == false)
                throw new InvalidOperationException("UserActionResultAttribute can only be used on HTTP GET actions");

            var objectResult = context.Result as ObjectResult;

            ApiDataResult<T> result = null;
            List<T> dataSetResult = (objectResult.Value as ApiDataSetResult<List<T>>)?.Data ?? (objectResult.Value as ApiDataResult<List<T>>)?.Data;
            if (dataSetResult == null)
            {
                result = objectResult.Value as ApiDataResult<T>;
            }

            // Get all the properties having the UserActionAttribute in the supplied type T
            var properties = typeof(T).GetProperties().Where(prop => Attribute.IsDefined(prop, typeof(UserActionAttribute)));

            // If at least one property with the attribute has been found, initiate checks
            if (properties != null && properties.Any())
            {
                var userId = _identityService.PersonIdFromPrincipal(_httpContextAccessor.HttpContext.User);

                // Loop through each element in the result list, if any, and set the property value based on the attribute result
                if (dataSetResult?.Count > 0)
                {
                    foreach (var item in dataSetResult)
                    {
                        var organizationId = Convert.ToInt32(item.GetType().GetProperty("OrganizationId").GetValue(item));
                        foreach (var property in properties)
                        {
                            var userActionAttribute = property.GetCustomAttributes<UserActionAttribute>().First();
                            CalculateAttribute(item, property, userActionAttribute, organizationId, userId);
                        }
                    }
                }

                // If the result is a single object, set the property value based on the attribute result
                if (result != null && result.Data != null)
                {
                    var organizationId = Convert.ToInt32(result.Data.GetType().GetProperty("OrganizationId").GetValue(result.Data));
                    foreach (var property in properties)
                    {
                        var userActionAttribute = property.GetCustomAttributes<UserActionAttribute>().First();
                        CalculateAttribute(result.Data, property, userActionAttribute, organizationId, userId);
                    }
                }
            }

            await next();
        }

        /// <summary>
        ///     Calculates whether the supplied property should be set to Allowed of Forbidden
        ///     based on the item organization id and the permission parameters specified in 
        ///     the property attribute
        /// </summary>
        /// <param name="item">The item in the result to calculate against</param>
        /// <param name="property">The property in the item to set the value</param>
        /// <param name="attribute">The attribute containing the permissions and rules</param>
        /// <param name="organizationId">The value of the OrganizationId property in the item</param>
        private void CalculateAttribute(T item, PropertyInfo property, UserActionAttribute attribute, int organizationId, int userId)
        {
            var state = UserActionStateEnum.Forbidden;

            if (attribute.Permissions.Any())
            {
                var isAllowed = _userContextService.HasPermission(userId, organizationId, attribute.Distance, attribute.Permissions);
                state = isAllowed ? UserActionStateEnum.Allowed : UserActionStateEnum.Forbidden;
            }

            item.GetType().GetProperty(property.Name).SetValue(item, state);
        }
    }
}