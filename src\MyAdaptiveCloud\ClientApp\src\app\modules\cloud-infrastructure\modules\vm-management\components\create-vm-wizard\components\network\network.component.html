<div class="network-step">

    <div class="d-flex justify-content-between w-100">
        <div class="title">{{ label }}<span class="required-asterisk">*</span></div>
        @if (vmManagementPermissionService.canCreateNetwork()) {
            <div class="action-buttons">
                <button class="btn btn-primary" type="button" (click)="openAddNetworkModal()">
                    Create Network
                </button>
            </div>
        }
    </div>

    <div class="mt-3">
        <div class="container">
            <div class="row list-header">
                <div class="col-11 d-flex align-items-center text-secondary">
                    <div class="col-1">NIC</div>

                    <div class="col-3">Network</div>

                    <div class="col">Type <span><i class="ms-1 fa fa-solid fa-circle-question"
                                [title]="typeHelpText"></i></span></div>

                    <div class="col">VPC</div>

                    <div class="col me-1"> IP Address</div>

                    <div class="col">MAC Address</div>

                </div>
                <div class="col-1 actions-cell"></div>
            </div>
        </div>

        <div cdkDropList class="drop-list container mt-2" (cdkDropListDropped)="drop($event)">
            @for (row of store.networkStep.selectedNetworks(); track row.id) {
            <div class="row mt-1">
                <div class="selected-row item-container col-11 d-flex align-items-center bg-white" cdkDrag
                    cdkDragLockAxis="y" [cdkDragData]="row">
                    <div class="col-1 d-flex align-items-center">
                        <div class="badge bg-white text-black border rounded-pill fs-6 fw-normal">
                            {{$index + 1}}</div>
                        <i class="mx-2 movable fa-solid fa-grip-vertical item-drag"></i>
                    </div>

                    <div [title]="row.name" class="col-3">
                        {{row.name}}
                    </div>

                    <div [title]="row.type" class="col">
                        {{row.type}}
                    </div>

                    <div [title]="row.vpcname" class="col">
                        {{row.vpcname}}
                    </div>

                    <div [title]="row.ipaddress" class="col me-1">
                        {{row.ipaddress}}
                    </div>

                    <div [title]="row.macaddress" class="col">
                        {{row.macaddress}}
                    </div>
                </div>
                <div class="col-1 actions-cell">
                    <button class="btn btn-danger action-btn remove-network" (click)="removeSelectedNetwork(row.id)">
                        <i class="fa-solid fa-close"></i>
                    </button>
                </div>
            </div>

            }
        </div>
        <div class="container mt-1">
            <form [formGroup]="form" class="add-network-form row">
                <div class="form-row item-container col-11 bg-white d-flex align-items-center">
                    <div class="col-1">
                        <div class="badge bg-white text-black border rounded-pill fs-6 fw-normal">
                            {{store.networkStep.selectedNetworks().length + 1}}</div>
                    </div>

                    <div class="col-3">
                        <ng-select formControlName="networkSelectedId" [items]="availableNetworks()" bindLabel="name"
                            bindValue="id" placeholder="Select Network" />
                    </div>

                    <div class="col">
                        <input class="form-control non-editable" readonly [value]="selectedNetwork()?.type || '-'" />
                    </div>

                    <div class="col">
                        <input class="form-control non-editable" readonly [value]="selectedNetwork()?.vpcname || '-'" />
                    </div>

                    <div class="col d-flex align-items-center me-1">
                        <input class="form-control" placeholder="DHCP (If Available)" formControlName="ipAddress"
                            id="ipAddress"
                            [class]="{ 'is-invalid': (form.controls.ipAddress.invalid || form.errors?.['invalidIpAddress']) && form.controls.ipAddress.dirty }" />
                        <i [title]="ipHelpText" triggers="hover"
                            class="col-1 text-secondary ms-1 fa-solid fa-circle-question"></i>
                    </div>

                    <div class="col d-flex align-items-center">
                        <input class="form-control" placeholder="Auto-Generated" formControlName="macAddress"
                            id="macAddress"
                            [class]="{ 'is-invalid': (form.controls.macAddress.invalid || form.errors?.['invalidMacAddress']) && form.controls.macAddress.dirty }" />
                        <i [title]="macHelpText" triggers="hover"
                            class="col-1 ms-1 text-secondary fa-solid fa-circle-question"></i>
                    </div>

                </div>
                <div class="col-1">
                    <button class="btn btn-primary add-network-btn action-btn" (click)="addNetwork()"
                        [disabled]="form?.invalid">
                        <i class="fa-solid fa-plus"></i>
                    </button>
                </div>
            </form>
        </div>

    </div>
</div>
