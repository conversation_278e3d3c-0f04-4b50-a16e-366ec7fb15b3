# MyAdaptiveCloud Project Overview

## Table of Contents
- [High-Level Architecture](#high-level-architecture)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Component Interactions](#component-interactions)
- [Key Features](#key-features)
- [Development Environment](#development-environment)

## High-Level Architecture

MyAdaptiveCloud is a comprehensive cloud infrastructure management platform built with a modern microservices-oriented architecture. The system follows a layered approach with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Angular 20)                    │
├─────────────────────────────────────────────────────────────┤
│                  API Gateway (.NET 9.0)                    │
├─────────────────────────────────────────────────────────────┤
│              Business Logic & Services Layer                │
├─────────────────────────────────────────────────────────────┤
│                   Data Access Layer                        │
├─────────────────────────────────────────────────────────────┤
│    MySQL Databases (Main, Agent, Logs, Billing)           │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

1. **Web API** (`src/MyAdaptiveCloud`) - Main ASP.NET Core application serving both API endpoints and Angular SPA
2. **Worker Services** (`src/MyAdaptiveCloud.WorkerServices`) - Background services for long-running tasks
3. **Angular Frontend** (`src/MyAdaptiveCloud/ClientApp`) - Modern SPA for user interface
4. **Data Layer** (`src/MyAdaptiveCloud.Data`) - Entity Framework Core models and repositories
5. **Core Library** (`src/MyAdaptiveCloud.Core`) - Shared business logic and domain models
6. **Services Library** (`src/MyAdaptiveCloud.Services`) - Business services and external API integrations

## Technology Stack

### Backend Technologies
- **.NET 9.0** - Primary framework for backend services
- **ASP.NET Core** - Web API and hosting framework
- **Entity Framework Core** - ORM with MySQL provider (Pomelo)
- **AutoMapper** - Object-to-object mapping
- **SignalR** - Real-time communication
- **Kestrel** - Web server

### Frontend Technologies
- **Angular 20** - Frontend framework
- **TypeScript 5.8** - Primary language for frontend
- **NgRx Signals** - State management
- **Bootstrap 5.3** - UI framework
- **SCSS** - Styling preprocessor
- **Highcharts** - Data visualization
- **Quill** - Rich text editor

### Database & Storage
- **MySQL/MariaDB** - Primary database system
- **Liquibase** - Database migration tool
- **Multiple Databases**:
  - `myadaptivecloud` - Main application data
  - `acagent` - Agent management data
  - `myadaptivecloudlogs` - Application logs
  - `Services` - Billing data

### DevOps & Infrastructure
- **Docker** - Containerization
- **Jenkins** - CI/CD pipelines
- **Poetry** - Python dependency management (for testing)
- **Playwright** - End-to-end testing
- **GitHub Container Registry** - Container image storage

## Project Structure

```
client-center/
├── src/                           # Source code
│   ├── MyAdaptiveCloud/          # Main web application
│   │   ├── ClientApp/            # Angular frontend
│   │   ├── Controllers/          # API controllers
│   │   ├── Authorization/        # Auth filters & policies
│   │   ├── Services/             # Application services
│   │   └── Program.cs            # Application entry point
│   ├── MyAdaptiveCloud.Core/     # Core business logic
│   ├── MyAdaptiveCloud.Data/     # Data access layer
│   ├── MyAdaptiveCloud.Services/ # Business services
│   └── MyAdaptiveCloud.WorkerServices/ # Background services
├── tests/                        # Unit tests
├── testAutomation/               # E2E tests (Python/Playwright)
├── docker/                       # Docker configuration
├── systemd/                      # Linux service files
└── MyAdaptiveCloud.sln          # Solution file
```

### Key Directories Explained

- **Controllers**: RESTful API endpoints organized by feature area
- **Authorization**: Custom authorization filters and policies
- **Repositories**: Data access patterns with Entity Framework
- **Services**: Business logic and external service integrations
- **Hubs**: SignalR hubs for real-time communication
- **Middleware**: Custom HTTP middleware components

## Component Interactions

### Request Flow
1. **Client Request** → Angular app makes HTTP request
2. **API Gateway** → ASP.NET Core routes to appropriate controller
3. **Authorization** → Custom authorization filters validate permissions
4. **Controller** → Handles request, delegates to services
5. **Service Layer** → Implements business logic
6. **Repository** → Data access via Entity Framework
7. **Database** → MySQL operations
8. **Response** → JSON response back to client

### Authentication Flow
- **Cookie Authentication** - Primary method for web users
- **HMAC-SHA256** - API authentication for external clients
- **Basic Authentication** - Legacy support
- **OpenID Connect** - Integration with KeyCloak SSO

### Real-time Communication
- **SignalR Hubs** - Server-to-client notifications
- **Background Services** - Long-running tasks and job processing
- **Message Queues** - Asynchronous task processing

## Key Features

### Cloud Infrastructure Management
- Virtual machine provisioning and management
- Network configuration and monitoring
- Storage management and backup solutions
- Multi-tenant organization support

### Device Management
- Agent-based device monitoring
- Remote desktop and shell access
- Software inventory and updates
- Alert rules and escalation chains

### User & Organization Management
- Role-based access control (RBAC)
- Multi-organization hierarchy
- User provisioning and permissions
- Billing and subscription management

### Monitoring & Alerting
- Real-time device monitoring
- Custom alert rules and thresholds
- Notification systems (email, SMS)
- Comprehensive logging and reporting

## Development Environment

### Prerequisites
- **.NET 9.0 SDK**
- **Node.js 18+** (for Angular)
- **MySQL/MariaDB** (for local development)
- **Docker** (optional, for containerized development)
- **Poetry** (for Python test automation)

### Local Development Setup
1. Clone repository
2. Restore .NET packages: `dotnet restore`
3. Install Angular dependencies: `npm install` (in ClientApp folder)
4. Configure database connections in `appsettings.Development.json`
5. Run database migrations
6. Start backend: `dotnet run`
7. Start frontend: `ng serve` (in ClientApp folder)

### Environment Configuration
- **Development**: Local development with hot reload
- **Testing**: Automated testing environment
- **Staging**: Pre-production testing
- **Production**: Live environment with optimizations

The application supports multiple deployment scenarios from local development to containerized production environments with comprehensive CI/CD pipelines.
