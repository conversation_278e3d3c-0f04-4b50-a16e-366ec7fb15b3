import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { ZoneViewModel } from '../../../../models/zone.view-model';
import { CreateNetworkService } from '../../../../services/create-network.service';
import { AffinityGroup } from '../../models/affinity-group.model';
import { OsTypeFilter } from '../../models/os-type-filter.enum';
import { OsType } from '../../models/os-type.enum';
import { SSHKeyPair } from '../../models/ssh-key-pair';
import { VmAffinityGroupsService } from '../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../services/vm-management.service';
import { VmMediaService } from '../../services/vm-media-service';
import { CreateVmWizardAdvancedSettingsComponent } from './components/advanced-settings/advanced-settings.component';
import { DEFAULT_KEYBOARD_LANG } from './components/advanced-settings/vm-available-keyboard-langs';
import { CreateVmWizardComputeComponent } from './components/compute/compute.component';
import { CreateVmWizardNetworkComponent } from './components/network/network.component';
import { CreateVmWizardSetupComponent } from './components/setup/setup.component';
import { CreateVmWizardStorageComponent } from './components/storage/storage.component';
import { CreateVMWizardStore } from './create-vm-wizard-store';
import { CreateVMWizardComponent } from './create-vm-wizard.component';
import { CreateVmRequestStatus } from './models/create-vm-request-status.emun';
import { CreateVmWizardStepEnum } from './models/create-vm-wizard-steps.enum';
import { DiskOffering } from './models/disk-offering.model';
import { CreateVmComputeService } from './services/create-vm-compute.service';
import { CreateVmNetworkService } from './services/create-vm-network.service';
import { CreateVmService } from './services/create-vm-service';
import { CreateVmStorageService } from './services/create-vm-storage.service';

describe('CreateVMWizardComponent', () => {
    let component: CreateVMWizardComponent;
    let fixture: ComponentFixture<CreateVMWizardComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockCreateVmService: jasmine.SpyObj<CreateVmService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;

    const zones: ZoneViewModel[] = [
        { id: 'zone-id-1', name: 'zone 1' },
    ];

    const mockGetAffinityGroupsResponse: AffinityGroup[] = [
        {
            id: '1',
            account: 'admin',
            description: 'test group 1',
            domain: 'ROOT',
            domainid: 'domain1',
            name: 'group 1',
            project: 'project1',
            projectid: 'projectid1',
            type: 'type1',
            virtualmachineIds: 'vm1'
        },
        {
            id: '2',
            account: 'admin',
            description: 'test group 2',
            domain: 'ROOT',
            domainid: 'domain2',
            name: 'group 2',
            project: 'project2',
            projectid: 'projectid2',
            type: 'type2',
            virtualmachineIds: 'vm2'
        },
        {
            id: '3',
            account: 'admin',
            description: 'test group 3',
            domain: 'ROOT',
            domainid: 'domain3',
            name: 'group 3',
            project: 'project3',
            projectid: 'projectid3',
            type: 'type3',
            virtualmachineIds: 'vm3'
        }
    ];

    const mockGetSshKeyPairsResponse: SSHKeyPair[] = [
        {
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain1',
            fingerprint: 'fingerprint1',
            name: 'keypair 1'
        },
        {
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain2',
            fingerprint: 'fingerprint2',
            name: 'keypair 2'
        },
        {
            account: 'admin',
            domain: 'ROOT',
            domainid: 'domain3',
            fingerprint: 'fingerprint3',
            name: 'keypair 3'
        }
    ];

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    beforeEach(async () => {

        const mockZoneDomainAccountStore = { ...getMockZoneDomainAccountStore(), zones: signal(zones) };

        TestBed.configureTestingModule({
            imports: [CreateVMWizardComponent],
            providers: [
                provideMock(CreateVmComputeService),
                provideMock(CreateNetworkService),
                provideMock(CreateVmNetworkService),
                provideMock(VmMediaService),
                provideMock(CloudInfraPermissionService),
                provideMock(CreateVmService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(UserContextService),
                provideMock(CreateVmStorageService),
                provideMock(CloudInfrastructureJobQueueService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
                CreateVMWizardStore,
                provideMock(ActivatedRoute)
            ]
        });

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of(mockGetSshKeyPairsResponse));

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;

        mockCreateVmService = TestBed.inject(CreateVmService) as jasmine.SpyObj<CreateVmService>;

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'account',
                domainId: 'domain-id'
            }
        } as UserContext;
        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of(mockGetAffinityGroupsResponse));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        fixture = TestBed.createComponent(CreateVMWizardComponent);
        component = fixture.componentInstance;

        fixture.detectChanges();
    });

    describe('Initializtion', () => {

        it('should initialize store', () => {
            expect(mockVmAffinityGroupsService.getAffinityGroups).toHaveBeenCalledOnceWith('domain-id', 'account');
            expect(mockVmManagementService.getKeyPairList).toHaveBeenCalledOnceWith('domain-id', 'account');
            expect(component.store.domainId()).toEqual('domain-id');
            expect(component.store.account()).toEqual('account');
            expect(component.store.zones()).toEqual(zones);
            expect(component.store.advancedSettingsStep().affinityGroups).toEqual(mockGetAffinityGroupsResponse);
            expect(component.store.advancedSettingsStep().sshKeyPairs).toEqual([
                {
                    account: 'admin',
                    domain: 'ROOT',
                    domainid: 'domain1',
                    fingerprint: 'fingerprint1',
                    name: 'keypair 1',
                    id: 'keypair-1'
                },
                {
                    account: 'admin',
                    domain: 'ROOT',
                    domainid: 'domain2',
                    fingerprint: 'fingerprint2',
                    name: 'keypair 2',
                    id: 'keypair-2'
                },
                {
                    account: 'admin',
                    domain: 'ROOT',
                    domainid: 'domain3',
                    fingerprint: 'fingerprint3',
                    name: 'keypair 3',
                    id: 'keypair-3'
                }
            ]);
            expect(component.store.totalSteps()).toBe(5);

        });

    });

    describe('Navigation', () => {

        it('should set the steps collection in the correct order', () => {
            expect(component.steps[CreateVmWizardStepEnum.Setup]).toEqual(CreateVmWizardSetupComponent);
            expect(component.steps[CreateVmWizardStepEnum.Compute]).toEqual(CreateVmWizardComputeComponent);
            expect(component.steps[CreateVmWizardStepEnum.Storage]).toEqual(CreateVmWizardStorageComponent);
            expect(component.steps[CreateVmWizardStepEnum.Network]).toEqual(CreateVmWizardNetworkComponent);
            expect(component.steps[CreateVmWizardStepEnum.AdvancedSettings]).toEqual(CreateVmWizardAdvancedSettingsComponent);
            expect(component.store.totalSteps()).toEqual(5);
        });

    });

    describe('Create Virtual Machine', () => {

        beforeEach(() => {
            // Set default store values
            component.store.setTemplate({
                zone: zones[0],
                virtualMachineName: 'test-vm',
                template: { id: 'template-1', name: 'template 1', description: 'template 1', size: 100 },
                osType: OsType.ISO,
                osTypeFilter: OsTypeFilter.Public
            }, true);

            component.store.setComputeStepFormValue({
                serviceOffering: { id: 'service-offering-1', name: 'service offering 1', cpuNumber: 4, memory: 8192, isCustom: false },
            }, true);

            component.store.setRootDisk({
                description: 'root disk', diskSize: 100, id: 'diskOffering1', isCustomized: false, offeringName: 'root disk'
            });

            component.store.setStorageStepValidity(true);

            component.store.setSelectedNetworks([{
                id: 'network-2',
                name: 'Test Network 2',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC 2',
                ipaddress: '***********',
                macaddress: '00:11:22:33:44:56',
                gateway: '*************'
            }]);

            component.store.setAdvancedSettings({

                affinityGroups: {
                    selectAll: false,
                    options: [{
                        name: 'name',
                        id: 'affinity-group-id'
                    }]
                },
                keyboardLanguage: DEFAULT_KEYBOARD_LANG,
                sshKeyPairs: {
                    selectAll: false,
                    options: [{
                        name: 'key-pair-name',
                        id: 'key-pair-id'
                    }]
                },
                userdata: 'test user data'
            }, true);
        });

        it(('should create VM successfully with the default value'), () => {

            mockVmManagementService.startVirtualMachine.and.returnValue(of('start-job-id'));
            mockCreateVmService.createVirtualMachine.and.returnValue(of({ virtualMachineId: 'vm-id', jobId: 'create-job-id' }));

            component.store.createVirtualMachine(true);
            fixture.detectChanges();

            expect(mockCreateVmService.createVirtualMachine).toHaveBeenCalledOnceWith({
                affinityGroups: ['affinity-group-id'],
                diskOfferingId: 'diskOffering1',
                hypervisor: 'KVM',
                keyboardLanguage: DEFAULT_KEYBOARD_LANG,
                name: 'test-vm',
                keypairs: ['key-pair-name'],
                networkIds: ['network-2'],
                rootDiskSize: null,
                serviceOffering: {
                    cpuNumber: 4,
                    memory: 8192,
                    isCustom: false,
                    id: 'service-offering-1',
                    name: 'service offering 1'
                },
                size: null,
                templateId: 'template-1',
                userdata: 'test user data',
                zoneId: 'zone-id-1'
            }, 'domain-id', 'account');

            expect(mockVmManagementService.startVirtualMachine).toHaveBeenCalledOnceWith('vm-id', null, null, null, null);

            expect(mockVmMediaService.createVolume).not.toHaveBeenCalled();

            expect(component.store.create()).toEqual({
                createVirtualMachineJobId: 'create-job-id',
                startVirtualMachineJobId: 'start-job-id',
                virtualMachineId: 'vm-id',
                requestStatus: CreateVmRequestStatus.Success
            });

        });

        it(('should create VM successfully with the additional disks value'), () => {
            component.store.setSelectedDataDisk([{
                description: 'root disk',
                diskSize: 100,
                id: 'diskOffering1',
                isCustomized: false,
                offeringName: 'root disk'
            },
            {
                description: 'root disk',
                diskSize: 50,
                id: 'diskOffering2',
                isCustomized: true,
                offeringName: 'root disk'
            }]);

            mockVmManagementService.startVirtualMachine.and.returnValue(of('start-job-id'));
            mockCreateVmService.createVirtualMachine.and.returnValue(of({ virtualMachineId: 'vm-id', jobId: 'create-job-id' }));
            mockVmMediaService.createVolume.and.returnValue(of('create-volume-job-id'));

            component.store.createVirtualMachine(true);
            fixture.detectChanges();

            expect(mockCreateVmService.createVirtualMachine).toHaveBeenCalledOnceWith({
                affinityGroups: ['affinity-group-id'],
                diskOfferingId: 'diskOffering1',
                hypervisor: 'KVM',
                keyboardLanguage: DEFAULT_KEYBOARD_LANG,
                name: 'test-vm',
                keypairs: ['key-pair-name'],
                networkIds: ['network-2'],
                rootDiskSize: null,
                serviceOffering: {
                    cpuNumber: 4,
                    memory: 8192,
                    isCustom: false,
                    id: 'service-offering-1',
                    name: 'service offering 1'
                },
                size: null,
                templateId: 'template-1',
                userdata: 'test user data',
                zoneId: 'zone-id-1'
            }, 'domain-id', 'account');

            expect(mockVmManagementService.startVirtualMachine).toHaveBeenCalledOnceWith('vm-id', null, null, null, null);

            expect(mockVmMediaService.createVolume).toHaveBeenCalledTimes(2);
            expect(mockVmMediaService.createVolume).toHaveBeenCalledWith('vm-id', 'diskOffering1', 'zone-id-1', 'domain-id', 'account', null);
            expect(mockVmMediaService.createVolume).toHaveBeenCalledWith('vm-id', 'diskOffering2', 'zone-id-1', 'domain-id', 'account', 50);

            expect(component.store.create()).toEqual({
                createVirtualMachineJobId: 'create-job-id',
                startVirtualMachineJobId: 'start-job-id',
                virtualMachineId: 'vm-id',
                requestStatus: CreateVmRequestStatus.Success
            });

        });

        it(('should set in error state if start VM returns an error'), () => {

            mockVmManagementService.startVirtualMachine.and.throwError('Start error');
            mockCreateVmService.createVirtualMachine.and.returnValue(of({ virtualMachineId: 'vm-id', jobId: 'create-job-id' }));

            component.store.createVirtualMachine(true);
            fixture.detectChanges();

            expect(mockCreateVmService.createVirtualMachine).toHaveBeenCalledOnceWith({
                affinityGroups: ['affinity-group-id'],
                diskOfferingId: 'diskOffering1',
                hypervisor: 'KVM',
                keyboardLanguage: DEFAULT_KEYBOARD_LANG,
                name: 'test-vm',
                keypairs: ['key-pair-name'],
                networkIds: ['network-2'],
                rootDiskSize: null,
                serviceOffering: {
                    cpuNumber: 4,
                    memory: 8192,
                    isCustom: false,
                    id: 'service-offering-1',
                    name: 'service offering 1'
                },
                size: null,
                templateId: 'template-1',
                userdata: 'test user data',
                zoneId: 'zone-id-1'
            }, 'domain-id', 'account');

            expect(mockVmManagementService.startVirtualMachine).toHaveBeenCalledOnceWith('vm-id', null, null, null, null);

            component.store.setSelectedNetworks([{
                id: 'network-2',
                name: 'Test Network 2',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC 2',
                ipaddress: '***********',
                macaddress: '00:11:22:33:44:56',
                gateway: '*************'
            }]);

            component.store.setAdvancedSettings({

                affinityGroups: {
                    selectAll: false,
                    options: [{
                        name: 'name',
                        id: 'affinity-group-id'
                    }]
                },
                keyboardLanguage: DEFAULT_KEYBOARD_LANG,
                sshKeyPairs: {
                    selectAll: false,
                    options: [{
                        name: 'key-pair-name',
                        id: 'key-pair-id'
                    }]
                },
                userdata: 'test user data'
            }, true);
        });

        it(('should create VM successfully with the default value'), () => {

            mockVmManagementService.startVirtualMachine.and.returnValue(of('start-job-id'));
            mockCreateVmService.createVirtualMachine.and.returnValue(of({ virtualMachineId: 'vm-id', jobId: 'create-job-id' }));

            component.store.createVirtualMachine(true);
            fixture.detectChanges();

            expect(mockCreateVmService.createVirtualMachine).toHaveBeenCalledOnceWith({
                affinityGroups: ['affinity-group-id'],
                diskOfferingId: 'diskOffering1',
                hypervisor: 'KVM',
                keyboardLanguage: DEFAULT_KEYBOARD_LANG,
                name: 'test-vm',
                keypairs: ['key-pair-name'],
                networkIds: ['network-2'],
                rootDiskSize: null,
                serviceOffering: {
                    cpuNumber: 4,
                    memory: 8192,
                    isCustom: false,
                    id: 'service-offering-1',
                    name: 'service offering 1'
                },
                size: null,
                templateId: 'template-1',
                userdata: 'test user data',
                zoneId: 'zone-id-1'
            }, 'domain-id', 'account');

            expect(mockVmManagementService.startVirtualMachine).toHaveBeenCalledOnceWith('vm-id', null, null, null, null);

            expect(mockVmMediaService.createVolume).not.toHaveBeenCalled();

            expect(component.store.create()).toEqual({
                createVirtualMachineJobId: 'create-job-id',
                startVirtualMachineJobId: 'start-job-id',
                virtualMachineId: 'vm-id',
                requestStatus: CreateVmRequestStatus.Success
            });

        });

        it(('should set in error state if start VM returns an error'), () => {

            mockVmManagementService.startVirtualMachine.and.throwError('Start error');
            mockCreateVmService.createVirtualMachine.and.returnValue(of({ virtualMachineId: 'vm-id', jobId: 'create-job-id' }));

            component.store.createVirtualMachine(true);
            fixture.detectChanges();

            expect(mockCreateVmService.createVirtualMachine).toHaveBeenCalledOnceWith({
                affinityGroups: ['affinity-group-id'],
                diskOfferingId: 'diskOffering1',
                hypervisor: 'KVM',
                keyboardLanguage: DEFAULT_KEYBOARD_LANG,
                name: 'test-vm',
                keypairs: ['key-pair-name'],
                networkIds: ['network-2'],
                rootDiskSize: null,
                serviceOffering: {
                    cpuNumber: 4,
                    memory: 8192,
                    isCustom: false,
                    id: 'service-offering-1',
                    name: 'service offering 1'
                },
                size: null,
                templateId: 'template-1',
                userdata: 'test user data',
                zoneId: 'zone-id-1'
            }, 'domain-id', 'account');

            expect(mockVmManagementService.startVirtualMachine).toHaveBeenCalledOnceWith('vm-id', null, null, null, null);

            expect(mockVmMediaService.createVolume).not.toHaveBeenCalled();

            expect(component.store.create()).toEqual({
                createVirtualMachineJobId: 'create-job-id',
                startVirtualMachineJobId: null,
                virtualMachineId: 'vm-id',
                requestStatus: CreateVmRequestStatus.Error
            });

            expect(component.store.create()).toEqual({
                createVirtualMachineJobId: 'create-job-id',
                startVirtualMachineJobId: null,
                virtualMachineId: 'vm-id',
                requestStatus: CreateVmRequestStatus.Error
            });

        });

    });

});
