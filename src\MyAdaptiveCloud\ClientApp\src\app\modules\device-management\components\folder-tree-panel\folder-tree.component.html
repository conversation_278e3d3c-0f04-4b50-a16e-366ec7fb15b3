@if (folder()) {
    @if (allowDragAndDrop()) {
        <div class="fill-content" [style]="{'padding-left': folder().level() * 25 + 'px' }" [class]="css()" #t="ngbTooltip"
            [ngbTooltip]="movementTextTip" [disableTooltip]="!isTooltipEnabled()" cdkDropList
            [id]="folder().getUniqueStringId()" [cdkDropListData]="[folder()]" (cdkDropListDropped)="drop($event)"
            (cdkDropListEntered)="dropListEntered($event)" (cdkDropListExited)="dropListExited($event)"
            data-testid="drag-and-drop">
            <ng-container *ngTemplateOutlet="treeContent" />
        </div>
    }
    @else {
        <div class="fill-content" [style]="{'padding-left': folder().level() * 25 + 'px' }" [class]="css()">
            <ng-container *ngTemplateOutlet="treeContent" />
        </div>
    }

    <ng-template #treeContent>
        <div class="py-2 d-flex clickable">
            @if (!isFoldersRoot()) {
                <span class="col-1 text-center" (click)="onToggleFolder(folder())">
                    @if (canToggle()) {
                        <i class="fa-solid toggle-arrow" [class]="folder().isExpanded() ? 'fa-angle-down' : 'fa-angle-right'"></i>
                    }
                </span>
            }
            <span class="col-1 text-center" [class]="folder().isRootOrganization() ? 'ps-3 me-3' : ''">
                <i class="fa-solid"
                    [class]="folder().isOrganizationFolder() ? folder().isPartnerOrganization() ? 'fa-handshake' : 'fa-building' : folder().isExpanded() ? 'fa-folder-open' : 'fa-folder'"></i>
            </span>
            <span (click)="selectFolder()" class="ms-2 col-8" data-testid="folder-name"
                [title]="`${folder().name()} ${folder().description() ? ' - ' + folder().description() : ''}`">
                {{folder().name()}}
            </span>
            @if(showCounts() && !allowMultiSelect()) {
                <span class="col-1">
                    @if(folder().isOrganizationFolder()) {
                        <span class="badge rounded-pill align-middle fw-normal" data-testid="device-count"
                            [class]="'text-bg-secondary bg-opacity-50'"
                            [title]="'Total devices in ' + folder().name() + ': ' + folder().deviceCountOnlySelf() + '\nTotal devices in ' + folder().name() + ' and SubOrgs: ' + folder().deviceCountSelfAndChildren()">
                            {{ folder().deviceCountOnlySelf() }}
                        </span>
                    }
                    @else {
                        <span class="badge rounded-pill align-middle fw-normal" data-testid="device-count"
                            [class]="folder().getUniqueStringId() === selectedFolderUniqueId() ? 'text-secondary bg-light ':'text-bg-secondary bg-opacity-50'">
                            {{ folder().isExpanded() ? folder().deviceCountOnlySelf() : folder().deviceCountSelfAndChildren() }}
                        </span>
                    }
                </span>
            }
        </div>
    </ng-template>

    @if (folder().isExpanded()) {
        <div>
            @if(showDevices()) {
                @for(device of folder().folderDevices(); track device.agentId) {
                    @if(shouldShowDevice(device)) {
                        @if (!folder().isOrganizationFolder()) {
                            <div class="clickable py-1" (click)="selectDevice(device)"
                                [class]="device.isSelected() || (selectedDevice() && selectedDevice()?.agentId === device?.agentId) ? 'org-selected bg-tree-secondary' : 'text-secondary'"
                                [style]="{'padding-left':  folder().level() * 25 + 'px' }">
                                <span class="col-1">&nbsp;&nbsp;</span>
                                <span class="ms-5">
                                    <i class="ms-1 fa-solid"
                                        [class]="showDevicesType() === deviceType.Vm && device.cloudStackVM ? 'icon-vm-nav-device' : 'fa-desktop'"></i>
                                    {{device.name()}}
                                </span>
                            </div>
                        }
                    }
                }
            }
            @for(subFolder of folder().getSubFolders(); track subFolder.getUniqueStringId()) {
                    @if (showDiscoveredDevicesFolders() || !subFolder.isUnassignedDevicesFolder()) {
                    <app-device-folders-tree [folder]="subFolder" [showDevicesType]="showDevicesType()" [showDevices]="showDevices()"
                        [showCounts]="showCounts()" [showDiscoveredDevicesFolders]="showDiscoveredDevicesFolders()"
                        [allowNavigation]="allowNavigation()" [allowMultiDeviceSelection]="allowMultiDeviceSelection()"
                        [allowDragAndDrop]="allowDragAndDrop()" />
                }
            }
        </div>
    }
}

<ng-template #movementTextTip>{{(folderMovementText())??''}}</ng-template>
