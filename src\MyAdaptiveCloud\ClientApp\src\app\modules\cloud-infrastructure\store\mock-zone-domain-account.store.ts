import { Signal, signal, WritableSignal } from '@angular/core';
import { CloudInfraAccountViewModel } from '../models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '../models/cloud-infra-domain.view-model';

const selectedAccount: CloudInfraAccountViewModel = {
    id: 'account-123',
    name: 'Account 1',
    domainId: 'domain-789'
};

const selectedDomain: CloudInfraDomainViewModel = {
    id: 'id',
    name: 'ROOT',
    level: 0,
    hasChild: false,
    subDomains: [],
    accounts: [selectedAccount],
    isExpanded: true,
};

export interface MockZoneDomainAccountStore {
    domains: Signal<CloudInfraDomainViewModel[]>;
    acc: Signal<boolean>;
    selectedDomain: Signal<CloudInfraDomainViewModel>;
    selectedAccount: Signal<CloudInfraAccountViewModel>;
    domainOrAccountSelected: Signal<boolean>;
    loadingId: Signal<string | null>;
    loadingAccountId: Signal<string | null>;
    getDomainId: Signal<string>;
    getAccount: WritableSignal<string>;
    mainDomain: Signal<CloudInfraDomainViewModel>;
    initialized: Signal<boolean>;
    zones: Signal<{ id: string; name: string }[]>;
    isRootDomainSelected: Signal<boolean>;
}

export function getMockZoneDomainAccountStore(): MockZoneDomainAccountStore {
    return {
        domains: signal<CloudInfraDomainViewModel[]>([]),
        acc: signal(false),
        selectedDomain: signal(selectedDomain),
        selectedAccount: signal(selectedAccount),
        domainOrAccountSelected: signal(true),
        loadingId: signal<string | null>(null),
        loadingAccountId: signal<string | null>(null),
        getDomainId: signal('domain-id'),
        getAccount: signal('account'),
        mainDomain: signal(selectedDomain),
        initialized: signal(true),
        zones: signal([{
            id: '1',
            name: 'Zone 1'
        },
        {
            id: '2',
            name: 'Zone 2'
        }]),
        isRootDomainSelected: signal(false)
    };
}
