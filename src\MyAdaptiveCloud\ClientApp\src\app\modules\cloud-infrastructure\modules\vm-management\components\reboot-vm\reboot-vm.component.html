<div class="modal-header">
    <h4 class="modal-title">Reboot VM</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <p>Please confirm that you want to reboot this VM</p>
    <form [formGroup]="form" class="row g-3">
        <div class="col-12">
            <label for="boot-delay" class="form-label">Boot Delay
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Number of seconds to wait for input on how to boot VM. Max delay of 65 seconds.'"
                    triggers="hover" container="body"></i></label>
            <input id="boot-delay" class="form-control" formControlName="bootDelay"
                [class]="{ 'is-invalid': form.controls.bootDelay.invalid }" />
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    <app-btn-submit [disabled]="form.invalid || isSubmitting()" [btnClasses]="'btn-primary'"
        (submitClickEvent)="rebootVirtualMachine()">OK</app-btn-submit>
</div>
