import { Component, ChangeDetectionStrategy } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-remote-access-vpn-users-container',
    imports: [RouterOutlet],
    templateUrl: './remote-access-vpn-users-container.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class RemoteAccessVpnUsersContainerComponent {

}
