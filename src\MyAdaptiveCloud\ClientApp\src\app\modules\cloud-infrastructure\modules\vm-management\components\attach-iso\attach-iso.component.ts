import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { distinctUntilChanged, Observable, of, startWith, tap } from 'rxjs';
import { AttachIsoForm } from '../../forms/attach-iso.form';
import { OsTypeFilter } from '../../models/os-type-filter.enum';
import { OsType } from '../../models/os-type.enum';
import { TemplateViewModel } from '../../models/template.view.model';
import { VmMediaService } from '../../services/vm-media-service';
import { MediaSelectorComponent } from '../media-selector/media-selector.component';

@Component({
    selector: 'app-attach-iso',
    imports: [ReactiveFormsModule, BtnSubmitComponent, MediaSelectorComponent, AsyncPipe],
    templateUrl: './attach-iso.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AttachIsoComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmMediaService = inject(VmMediaService);
    private readonly destroyRef = inject(DestroyRef);

    readonly inputData = signal<{
        virtualMachineId: string;
        virtualMachineZoneId: string;
        domainId: string;
        account: string;
    }>(null);

    protected readonly isSubmitting = signal<boolean>(false);
    protected readonly form = this.formBuilder.group<AttachIsoForm>({
        iso: this.formBuilder.control<TemplateViewModel | null>(null, Validators.required),
        osTypeFilter: this.formBuilder.control(OsTypeFilter.Featured)
    });
    protected isos$: Observable<TemplateViewModel[]>;
    protected readonly osTypeFilter = OsTypeFilter;
    protected readonly osType = OsType;

    private featuredISOs: TemplateViewModel[];
    private publicISOs: TemplateViewModel[];
    private myISOs: TemplateViewModel[];

    ngOnInit(): void {

        this.form.controls.osTypeFilter.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                distinctUntilChanged(),
                startWith(this.form.controls.osTypeFilter.value)
            )
            .subscribe(value => {
                if (value === OsTypeFilter.Featured) {
                    this.isos$ = this.featuredISOs ? of(this.featuredISOs) :
                        this.vmMediaService.getFeaturedISOsByZoneId(this.inputData().virtualMachineZoneId).pipe(tap(isos => this.featuredISOs = isos));
                } else if (value === OsTypeFilter.Public) {
                    this.isos$ = this.publicISOs ? of(this.publicISOs) :
                        this.vmMediaService.getPublicISOsByZoneId(this.inputData().virtualMachineZoneId, this.inputData().domainId, this.inputData().account).pipe(tap(isos => this.publicISOs = isos));
                } else if (value === OsTypeFilter.MyTemplates) {
                    this.isos$ = this.myISOs ? of(this.myISOs) :
                        this.vmMediaService.getMyISOsByZoneId(this.inputData().virtualMachineZoneId, this.inputData().domainId, this.inputData().account).pipe(tap(isos => this.myISOs = isos));
                }
            });
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected attachIso() {
        this.isSubmitting.set(true);
        if (this.form.valid) {

            this.vmMediaService.attachIso(
                this.inputData().virtualMachineId,
                this.form.controls.iso.value.id
            )
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
