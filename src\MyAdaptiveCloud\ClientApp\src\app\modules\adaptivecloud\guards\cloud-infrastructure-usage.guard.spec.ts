import { TestBed } from '@angular/core/testing';
import { PermissionService } from '@app/shared/services/permission.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { CloudInfrastructureUsageGuard } from './cloud-infrastructure-usage.guard';

describe('CloudInfrastructureUsageGuard', () => {
    let guard: CloudInfrastructureUsageGuard;
    let mockPermissionService: jasmine.SpyObj<PermissionService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(PermissionService),
                CloudInfrastructureUsageGuard
            ]
        });

        mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
        guard = TestBed.inject(CloudInfrastructureUsageGuard);
    });

    it('should return false when canViewCloudInfraUsage is not allowed ', () => {
        mockPermissionService.canViewCloudInfraUsage.and.returnValue(false);
        const expected = guard.canActivate();
        expect(expected).toBeFalse();
    });

    it('should return true when canViewCloudInfraUsage is allowed ', () => {
        mockPermissionService.canViewCloudInfraUsage.and.returnValue(true);
        const expected = guard.canActivate();
        expect(expected).toBeTrue();
    });
});
