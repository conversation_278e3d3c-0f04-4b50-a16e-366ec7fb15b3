import { Injectable, inject } from '@angular/core';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { UserAuthenticator } from '@app/core/models/user-authenticator';
import { UpdateUserAuthenticatorRequest } from '@app/core/requests/update-user-authenticator.request';
import { Observable } from 'rxjs';
import { ApiService } from '@app/shared/services/api.service';

@Injectable({
    providedIn: 'root'
})

export class TwoFactorAuthenticationService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'twofactorauthentication';

    getUserAuthenticatorsList(): Observable<ApiDatasetResult<UserAuthenticator[]>> {
        return this.apiService.get<ApiDatasetResult<UserAuthenticator[]>>(`${this.endpoint}/authenticators`);
    }

    deleteAuthenticator(credentialId: string): Observable<void> {
        return this.apiService.delete(`${this.endpoint}`, credentialId);
    }

    updateUserAuthenticator(credentialId: string, updateUserAuthenticatorRequest: UpdateUserAuthenticatorRequest): Observable<ApiResult> {
        return this.apiService.put<ApiResult, UpdateUserAuthenticatorRequest>(`${this.endpoint}`, credentialId, updateUserAuthenticatorRequest);
    }

    addAuthenticator(): Observable<ApiResult> {
        return this.apiService.post<ApiResult, null>(`${this.endpoint}`, null);
    }
}
