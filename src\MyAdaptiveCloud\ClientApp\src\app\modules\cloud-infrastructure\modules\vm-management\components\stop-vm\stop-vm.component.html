@if (form) {
  <div class="modal-header">
    <h4 class="modal-title">Stop VM</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="cancel()"></button>
  </div>
  <div class="modal-body">
    <form [formGroup]="form">
      <p>Please confirm that you want to stop this VM</p>
      <div class="mb-3 row">
        <div class="input-group pt-2">
          <div class="col-6 mb-2">
            <input id="force-stop" formControlName="forceStop" class="form-check-input me-2"
              type="checkbox" />
            <label for="force-stop" class="form-check-label">
              Force Stop
              <i [ngbPopover]="'Force stop the VM (vm is marked as stopped even when command fails to be sent to the backend, otherwise a force poweroff is attempted). The caller knows the VM is stopped.'"
              triggers="hover" container="body" class="fa fa-info-circle text-secondary"></i>
            </label>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    <app-btn-submit [disabled]="form.invalid || isSubmitting()" [btnClasses]="'btn-primary'"
    (submitClickEvent)="stopVirtualMachine()">OK</app-btn-submit>
  </div>
}
