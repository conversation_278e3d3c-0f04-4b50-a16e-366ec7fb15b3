export interface CreateRemoteVpnGatewayRequest {
    account: string;
    cidrList: string;
    domainId: string;
    deadPeerDetection: boolean;
    espLifetime: number;
    espEncryption: string;
    espHash: string;
    forceEncapsulation: boolean;
    gateway: string;
    ikeEncryption: string;
    ikeHash: string;
    ikeDH: string;
    ikeLifetime: number;
    ikeVersion: string;
    ipSecurityPreSharedKey: string;
    name: string;
    perfectForwardSecrecy: string | null;
    splitConnections: boolean | null;
}
