import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { Bgp } from '../../models/bgp.model';
import { CommunitiesPipe } from '../../pipes/communities.pipe';
import { DDosMitigationValuePipe } from '../../pipes/ddos-mitigation-value.pipe';
import { DDoSMitigationBgpService } from '../../services/ddos-mitigation-bgp.service';
import { DdosMitigationBgpAdvertisementsDetailsComponent } from '../ddos-mitigation-bgp-advertisements-details/ddos-mitigation-bgp-advertisements-details.component';

@Component({
    selector: 'app-ddos-mitigation-bgp-advertisements',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent, NgbPopover],
    templateUrl: './ddos-mitigation-bgp-advertisements.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})

export class DdosMitigationBgpAdvertisementsComponent extends BaseListClientComponent<Bgp> implements OnInit {
    private readonly ddosMitigationBgpService = inject(DDoSMitigationBgpService);
    private readonly modalService = inject(ModalService);

    private readonly headerTemplate = viewChild.required<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild.required<TemplateRef<never>>('actionsTemplate');
    private readonly prefixTemplate = viewChild.required<TemplateRef<never>>('prefixTemplate');

    ngOnInit(): void {

        const columns: TableColumn[] = ([
            {
                name: 'Prefix',
                prop: 'prefix',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.prefixTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200
            },
            {
                name: 'Next Hop',
                prop: 'nextHop',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Neighbor IP',
                prop: 'neighborIp',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Communities',
                prop: 'communities',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                pipe: new CommunitiesPipe(),
                width: 200
            },
            {
                name: 'Local Preference',
                prop: 'localPref',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'AS Path',
                prop: 'asPath',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
                pipe: new DDosMitigationValuePipe()
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false
            },
        ]);

        super.initialize(() => this.ddosMitigationBgpService.getAdvertisedList(), columns);
    }

    protected viewDetails(item: Bgp): void {
        const modalRef = this.modalService.openModalComponent(DdosMitigationBgpAdvertisementsDetailsComponent);
        (modalRef.componentInstance as DdosMitigationBgpAdvertisementsDetailsComponent).bgpDetails.set(item);
    }

}
