﻿using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.Files;
using MyAdaptiveCloud.Services.DTOs.PartnerResourceHub;

namespace MyAdaptiveCloud.Api.AutoMapper.PartnerResourceHub
{
    public class PartnerResourceHubProfile : Profile
    {
        public PartnerResourceHubProfile()
        {
            CreateMap<FileAdministrationDTO, PartnerResourceHubDocumentDTO>()
                .ForMember(dest => dest.DocumentId, option => option.MapFrom(src => src.FileAdministrationId))
                .ForMember(dest => dest.Name, option => option.MapFrom(src => src.Name))
                .ForMember(dest => dest.LastUpdated, option => option.MapFrom(src => src.UploadDate));
        }
    }
}