import { IUserAction } from '@app/shared/models/user-actions/user-action';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';
import { ScheduleType } from '@app/shared/models/schedule-type.enum';
import { AutoApprovalUpdateCategoryInfo } from './auto-approval-update-category-info.model';

export class Schedule implements IUserAction {
    public organizationId: number;
    public canEdit: UserActionState;
    public canDelete: UserActionState;
    public canCreate: UserActionState;
    public canView: UserActionState;
    public scheduleId: number;
    public name: string;
    public description: string;
    /**
     * UTC startDate string in the format 'yyyy-MM-ddTHH:mm:ss+00:00' Example: 2021-01-02T16:20:00+00:00
     * @remarks
     * This is a string as it does not get automatically serialized into a Date object by JavaScript.
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @type {string}
     */
    // public startDate: string;   // This is not a Date object, it is a string as it does not get automatically serialized into a Date object

    /**
     * Time zone of the startDate when it was last saved by UI.  Example: 'America/Chicago'
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @note Leaving this here commented out as it is intentionally not used and startDateTimeLocalizedString is utilized instead
     * @type {string}
     */
    // public startDateTimeZone: string;

    /**
     * "Localized" version of the startDate to be used in the UI bsDatePicker for representing date and hours in the local time zone of the
     * browser as it would have appeared in the time zone of the browser when startDate was last modified in the UI.
     * @remarks
     * Below example is for the case when the browser time zone is America/Los_Angeles (UTC-08:00) America/Los_Angeles timezone would be coming in on the http request header for this
     * conversion to work on the api side. X-IPP-UI-Client-TimeZone is automatically populated on every request.
     * Example:
     *     startDateTimeLocalizedString = '2021-01-02T16:30:00-08:00'
     * @type {string}
     */
    public startDateTimeLocalized: string;
    public scheduleType: ScheduleType;
    public isEnabled: boolean;
    public freqRecurrenceFactor: number;
    public months: number[];
    public monthDays: number[];
    public monthOnWeek: number[];
    public monthOnWeekNumber: number[];
    public weeklyDays: number[];
    public updateCategoriesAutoApproval: AutoApprovalUpdateCategoryInfo[];
}
