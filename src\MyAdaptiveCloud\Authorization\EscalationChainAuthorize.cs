﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class EscalationChainAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public EscalationChainAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService,
            IIdentityService identityService, IUserContextService userContextService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int escalationChainId);

            var escalationChainOrganizationId = await _entityAuthorizationService.GetEscalationChainOrganizationId(escalationChainId);
            if (escalationChainOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, escalationChainOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, escalationChainOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class EscalationChainAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public EscalationChainAuthorizeAttribute(params Perms[] perms) : base(typeof(EscalationChainAuthorizeFilter), perms)
        {
            Name = "escalationChainId";
        }
    }
}