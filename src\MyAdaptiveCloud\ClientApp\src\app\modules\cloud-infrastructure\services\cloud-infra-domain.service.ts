import { inject, Injectable } from '@angular/core';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { map, Observable } from 'rxjs';
import { CloudInfraDomain } from '../models/cloud-infra-domain.model';
import { CloudInfraDomainViewModel } from '../models/cloud-infra-domain.view-model';
import { CLOUD_INFRA_ENDPOINT_NAMES } from '../models/cloud-infra.constants';
import { ListDomainChildrenResponse } from '../responses/list-domain-children.response';
import { ListDomainResponse } from '../responses/list-domain.response';

@Injectable({
    providedIn: 'root'
})
export class CloudInfraDomainService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getDomainList(id: string): Observable<CloudInfraDomainViewModel[]> {
        const params: Record<string, string> = {
            command: CLOUD_INFRA_ENDPOINT_NAMES.listDomains,
            listall: 'true',
            id
        };

        return this.cloudInfraApiService.get<ListDomainResponse>(params)
            .pipe(map(response => response.listdomainsresponse?.domain.map(domain => this.mapDomain(domain)).sort(sortByProperty('name')) ?? []));
    }

    getDomainChildrenList(id: string): Observable<CloudInfraDomainViewModel[]> {
        const params: Record<string, string> = {
            command: CLOUD_INFRA_ENDPOINT_NAMES.listDomainChildren,
            listall: 'true',
            id
        };

        return this.cloudInfraApiService.get<ListDomainChildrenResponse>(params)
            .pipe(map(response => response.listdomainchildrenresponse?.domain.map(domain => this.mapDomain(domain)).sort(sortByProperty('name')) ?? []));
    }

    private mapDomain(domain: CloudInfraDomain): CloudInfraDomainViewModel {
        return {
            id: domain.id,
            name: domain.name?.trim(),
            level: domain.level,
            hasChild: domain.haschild,
            isExpanded: false,
            subDomains: [],
            accounts: []
        };
    }

}
