# Augment Ignore File
# This file specifies patterns for files and directories that should be excluded from Augment's context engine

# Build outputs and binaries
**/bin/
**/obj/
**/out/
**/dist/
**/build/
**/target/
**/Debug/
**/Release/

# Package managers and dependencies
**/node_modules/
**/packages/
**/.pnpm-store/
**/package-lock.json
**/yarn.lock
**/pnpm-lock.yaml

# Python dependencies and virtual environments
**/__pycache__/
**/*.pyc
**/*.pyo
**/*.pyd
**/.Python
**/env/
**/venv/
**/ENV/
**/env.bak/
**/venv.bak/
**/.venv/
**/poetry.lock
**/.pytest_cache/
**/.coverage
**/htmlcov/
**/.tox/
**/.cache/
**/.mypy_cache/
**/.ruff_cache/

# .NET specific
**/packages/
**/*.nupkg
**/*.snupkg
**/*.nuget.props
**/*.nuget.targets
**/project.lock.json
**/project.fragment.lock.json
**/artifacts/
**/.dotnet/

# IDE and editor files
**/.vs/
**/.vscode/
**/.idea/
**/*.swp
**/*.swo
**/*~
**/.DS_Store
**/Thumbs.db
**/*.user
**/*.suo
**/*.userosscache
**/*.sln.docstates
**/*.tmp
**/*.temp

# Logs and temporary files
**/logs/
**/*.log
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*
**/lerna-debug.log*
**/.npm
**/.eslintcache
**/.stylelintcache

# Test results and coverage
**/TestResults/
**/coverage/
**/*.trx
**/*.coverage
**/*.coveragexml
**/test-results/
**/playwright-report/
**/test-results-*/

# Docker
**/.dockerignore
**/Dockerfile
**/docker-compose*.yml
**/docker-compose*.yaml

# CI/CD
**/Jenkinsfile*
**/.github/
**/.gitlab-ci.yml
**/.travis.yml
**/azure-pipelines.yml

# Security and secrets
**/.secrets/
**/appsettings.secret.json
**/*.key
**/*.pem
**/*.p12
**/*.pfx
**/certs/

# Database files
**/*.db
**/*.sqlite
**/*.sqlite3
**/*.mdf
**/*.ldf

# Generated files
**/Generated_Code/
**/auto-generated/
**/*generated*
**/*.designer.cs
**/*.g.cs
**/*.g.i.cs

# Angular specific
**/e2e/
**/.angular/
**/karma.conf*.js

# Backup and archive files
**/*.bak
**/*.backup
**/*.old
**/*.orig
**/*.zip
**/*.tar
**/*.tar.gz
**/*.rar
**/*.7z

# OS generated files
**/.DS_Store
**/.DS_Store?
**/._*
**/.Spotlight-V100
**/.Trashes
**/ehthumbs.db
**/Thumbs.db

# Large media files (optional - uncomment if needed)
# **/*.mp4
# **/*.avi
# **/*.mov
# **/*.wmv
# **/*.flv
# **/*.webm
# **/*.mp3
# **/*.wav
# **/*.flac
# **/*.aac
# **/*.jpg
# **/*.jpeg
# **/*.png
# **/*.gif
# **/*.bmp
# **/*.tiff
# **/*.svg
# **/*.ico

# Documentation build outputs
**/docs/_build/
**/site/

# Temporary and cache directories
**/tmp/
**/temp/
**/.cache/
**/.temp/
