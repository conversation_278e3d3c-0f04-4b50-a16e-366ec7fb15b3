import { NetworkOfferingViewModel } from '@app/modules/cloud-infrastructure/models/network-offering.view-model';
import { VpcViewModel } from '@app/modules/cloud-infrastructure/models/vpc.view-model';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { VmNetwork } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm-network.model';
import { TemplateViewModel } from '../../../models/template.view.model';
import { ComputeStepFormValue } from '../forms/compute-step.form';
import { SetupStepFormValue } from '../forms/setup-step.form';
import { AdvancedSettingsModel } from './advanced-settings.model';
import { AffinityGroup } from '../../../models/affinity-group.model';
import { CreateVmRequestStatus } from './create-vm-request-status.emun';
import { DiskOffering } from './disk-offering.model';
import { ServiceOfferingViewModel } from './service-offering.view-model';
import { SshKeyPairViewModel } from './ssh-keypair.view-model';

export interface CreateVMWizardState {
    domainId: string,
    account: string,
    currentStep: number;
    totalSteps: number;
    zones: ZoneViewModel[];
    setupStep: {
        templates: {
            featuredTemplates: TemplateViewModel[];
            publicTemplates: TemplateViewModel[];
            myTemplates: TemplateViewModel[];
            featuredISOs: TemplateViewModel[];
            publicISOs: TemplateViewModel[];
            myISOs: TemplateViewModel[];
        },
        form: Partial<SetupStepFormValue>;
        isValid: boolean;
    };
    computeStep: {
        presetServiceOfferings: ServiceOfferingViewModel[];
        customServiceOffering: ServiceOfferingViewModel;
        form: Partial<ComputeStepFormValue>;
        isValid: boolean;
    };
    storageStep: {
        selectedDataDisks: DiskOffering[];
        rootDisk: DiskOffering;
        isValid: boolean;
        diskOfferings: DiskOffering[];
    };
    networkStep: {
        networks: VmNetwork[];
        selectedNetworks: VmNetwork[];
        availableNetworks: VmNetwork[];
        isValid: boolean;
        isolatedNetworkOfferings: NetworkOfferingViewModel[];
        layer2NetworkOfferings: NetworkOfferingViewModel[];
        sharedNetworkOfferings: NetworkOfferingViewModel[];
        vpcs: VpcViewModel[];
        requestStatus: CreateVmRequestStatus;
    };
    advancedSettingsStep: {
        isValid: boolean;
        isAccessible: boolean;
        affinityGroups: AffinityGroup[];
        sshKeyPairs: SshKeyPairViewModel[];
        selectedSettings: AdvancedSettingsModel;
        displaySettings: boolean;
    };
    create: {
        requestStatus: CreateVmRequestStatus;
        createVirtualMachineJobId: string | null;
        startVirtualMachineJobId: string | null;
        virtualMachineId: string | null;
    }
}
