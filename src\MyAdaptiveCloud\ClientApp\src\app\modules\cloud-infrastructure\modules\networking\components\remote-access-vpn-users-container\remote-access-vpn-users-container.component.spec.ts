import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RemoteAccessVpnUsersContainerComponent } from './remote-access-vpn-users-container.component';

describe('RemoteAccessVpnUsersContainerComponent', () => {
    let component: RemoteAccessVpnUsersContainerComponent;
    let fixture: ComponentFixture<RemoteAccessVpnUsersContainerComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RemoteAccessVpnUsersContainerComponent]
        })
            .compileComponents();

        fixture = TestBed.createComponent(RemoteAccessVpnUsersContainerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
