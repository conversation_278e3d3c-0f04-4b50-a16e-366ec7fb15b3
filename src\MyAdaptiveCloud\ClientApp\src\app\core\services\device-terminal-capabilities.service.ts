import {inject, Injectable} from '@angular/core';
import {ApiService} from '@app/shared/services/api.service';
import {Observable} from 'rxjs';
import {ApiDataResult} from '@app/shared/models/api-service/api.data.result';

@Injectable({
    providedIn: 'root'
})
export class DeviceTerminalCapabilitiesService {
    private readonly apiService = inject(ApiService);
    private readonly endpoint = 'devices';

    canRemoteCommandsKeyByKey(agentId: number): Observable<ApiDataResult<boolean>> {
        return this.apiService.get<ApiDataResult<boolean>>(`${this.endpoint}/${agentId}/terminal/can-send-key-by-key`);
    }
}
