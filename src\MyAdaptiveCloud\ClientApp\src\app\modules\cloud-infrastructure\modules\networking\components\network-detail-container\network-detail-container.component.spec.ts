import { provideMock } from '@app/shared/specs/spy-helpers';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NetworkDetailContainerComponent } from './network-detail-container.component';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { NetworkingDetailService } from '../../services/networking-detail.service';
import { ActivatedRoute } from '@angular/router';

describe('NetworkDetailContainerComponent', () => {
    let component: NetworkDetailContainerComponent;
    let fixture: ComponentFixture<NetworkDetailContainerComponent>;
    let mockNetworkingService: NetworkingDetailService;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [NetworkDetailContainerComponent],
            providers: [
                NetworkingDetailService,
                provideMock(CloudInfraPermissionService),
                provideMock(ActivatedRoute)
            ],
        })
            .compileComponents();

        mockNetworkingService = TestBed.inject(NetworkingDetailService);
        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);
        mockNetworkingService.selectedNetwork.set({
            id: 'test-id-123',
            account: 'TestAccount',
            acltype: '',
            canusefordeploy: false,
            created: '2025-05-28T14:27:55+0000',
            displaynetwork: false,
            displaytext: 'Test Description',
            dns1: '',
            dns2: '',
            domain: 'TestDomain',
            domainid: '',
            hasannotations: false,
            ip6dns1: '',
            ip6dns2: '',
            ispersistent: false,
            issystem: false,
            name: 'Test Network',
            networkofferingavailability: '',
            networkofferingconservemode: false,
            networkofferingdisplaytext: '',
            networkofferingid: '',
            networkofferingname: 'Test Offering',
            physicalnetworkid: '',
            privatemtu: 0,
            publicmtu: 0,
            receivedbytes: 0,
            redundantrouter: true,
            related: '',
            restartrequired: true,
            sentbytes: 0,
            specifyipranges: false,
            state: 'Implemented',
            strechedl2subnet: false,
            supportsvmautoscaling: false,
            tags: [],
            traffictype: '',
            type: 'Isolated',
            zoneid: '',
            zonename: 'TestZone',
            cidr: '10.0.0.0/24',
            vpcname: ''
        });
        fixture = TestBed.createComponent(NetworkDetailContainerComponent);

        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should display mapped network fields in the template', () => {
        fixture.detectChanges();
        const compiled = fixture.nativeElement as HTMLElement;

        expect(compiled.textContent).toContain('Test Network');
        expect(compiled.textContent).toContain('Implemented');
        expect(compiled.textContent).toContain('Description:');
        expect(compiled.textContent).toContain('Create Date:');
    });
});
