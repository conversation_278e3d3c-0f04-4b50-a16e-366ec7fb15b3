import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { Vpc } from '@app/modules/cloud-infrastructure/models/vpc.model';
import { CloudInfraAccountService } from '@app/modules/cloud-infrastructure/services/cloud-infra-account.service';
import { CloudInfraDomainService } from '@app/modules/cloud-infrastructure/services/cloud-infra-domain.service';
import { CloudInfraZoneService } from '@app/modules/cloud-infrastructure/services/cloud-infra-zone.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';
import { ListVirtualPrivateCloudsComponent } from './list-virtual-private-clouds.component';

describe('ListVirtualPrivateCloudsComponent', () => {
    let fixture: ComponentFixture<ListVirtualPrivateCloudsComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVirtualPrivateCloudService: jasmine.SpyObj<VirtualPrivateCloudService>;
    let mockCloudInfraZoneService: jasmine.SpyObj<CloudInfraZoneService>;
    let mockNetworkingPermissionService: jasmine.SpyObj<NetworkingPermissionService>;

    const zones = [{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }];

    const vpcList: Vpc[] = [
        {
            id: 'b403fc9e-380e-41c5-9aff-2c6b1bdc9f12',
            name: 'Gorilla L1',
            displaytext: 'Gorilla L2',
            zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
            zonename: 'DC1',
            state: 'Enabled',
            account: 'Gorilla',
            domainid: 'dda0f2d2-b998-4d92-806b-8380b830f08c',
            domain: 'gregsgaming',
            restartrequired: false,
            tags: [],
            cidr: '',
            created: '2025-06-11T16:35:27+0000',
            publicmtu: 1500,
            distributedvpcrouter: false,
            redundantvpcrouter: false,
            regionlevelvpc: false,
            networkdomain: '',
            network: [],
            vpcofferingid: 'c79b6615-9b2a-4d34-b43d-bc866d262cb3',
            vpcofferingname: 'DefaultL2NetworkOffering',
            fordisplay: true,
            hasannotations: false,
            service: []
        },
        {
            id: 'b403fc9e-380e-41c5-9aff-2c6b1bdc9f12',
            name: 'Gorilla L2',
            displaytext: 'Gorilla L2',
            zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
            zonename: 'DC1',
            state: 'Inactive',
            account: 'Gorilla',
            domainid: 'dda0f2d2-b998-4d92-806b-8380b830f08c',
            domain: 'gregsgaming',
            restartrequired: false,
            tags: [],
            cidr: '',
            created: '2025-06-11T16:35:27+0000',
            publicmtu: 1500,
            distributedvpcrouter: false,
            redundantvpcrouter: false,
            regionlevelvpc: false,
            networkdomain: '',
            network: [],
            vpcofferingid: 'c79b6615-9b2a-4d34-b43d-bc866d262cb3',
            vpcofferingname: 'DefaultL2NetworkOffering',
            fordisplay: true,
            hasannotations: false,
            service: []
        },
        {
            id: 'b403fc9e-380e-41c5-9aff-2c6b1bdc9f12',
            name: 'Gorilla L3',
            displaytext: 'Gorilla L2',
            zoneid: 'c0665c38-48b7-456b-9629-7327bc4f90f2',
            zonename: 'DC1',
            state: 'Enabled',
            account: 'Gorilla',
            domainid: 'dda0f2d2-b998-4d92-806b-8380b830f08c',
            domain: 'gregsgaming',
            restartrequired: false,
            tags: [],
            cidr: '',
            created: '2025-06-11T16:35:27+0000',
            publicmtu: 1500,
            distributedvpcrouter: false,
            redundantvpcrouter: false,
            regionlevelvpc: false,
            networkdomain: '',
            network: [],
            vpcofferingid: 'c79b6615-9b2a-4d34-b43d-bc866d262cb3',
            vpcofferingname: 'DefaultL2NetworkOffering',
            fordisplay: true,
            hasannotations: false,
            service: []
        },
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                ListVirtualPrivateCloudsComponent
            ],
            providers: [
                provideMock(UserContextService),
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraAccountService),
                provideMock(CloudInfraDomainService),
                provideMock(CloudInfraZoneService),
                provideMock(NetworkingPermissionService),
                provideMock(VirtualPrivateCloudService),
                ZoneDomainAccountStore
            ]
        });

        mockVirtualPrivateCloudService = TestBed.inject(VirtualPrivateCloudService) as jasmine.SpyObj<VirtualPrivateCloudService>;
        mockVirtualPrivateCloudService.getVirtualPrivateCloudList.and.returnValue(of(vpcList));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        mockUserContextService.currentUser = {
            organizationId: 1,
            cloudInfraUserContext: {
                accountName: 'account',
                domainId: 'domainId',
                hasMappedDomain: true,
            } as CloudInfraUserContext
        } as UserContext;

        mockCloudInfraZoneService = TestBed.inject(CloudInfraZoneService) as jasmine.SpyObj<CloudInfraZoneService>;
        mockCloudInfraZoneService.getZones.and.returnValue(of(zones));

        mockNetworkingPermissionService = TestBed.inject(NetworkingPermissionService) as jasmine.SpyObj<NetworkingPermissionService>;
        mockNetworkingPermissionService.canViewVirtualPrivateCloudList.and.returnValue(true);

        fixture = TestBed.createComponent(ListVirtualPrivateCloudsComponent);
    });

    describe('Initialization', () => {

        it('should call getNetworks without account when context have a mapped domain', () => {
            mockUserContextService.currentUser = {
                organizationId: 1,
                cloudInfraUserContext: {
                    accountName: 'account',
                    domainId: 'domainId',
                    hasMappedDomain: true,
                } as CloudInfraUserContext
            } as UserContext;

            fixture.detectChanges();

            expect(mockVirtualPrivateCloudService.getVirtualPrivateCloudList).toHaveBeenCalledTimes(1);

        });
    });

    describe('Data Binding', () => {

        it('should display the vpc state in the first column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(1)').textContent.trim()).toEqual(vpcList[0].state);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(1)').textContent.trim()).toEqual(vpcList[1].state);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(1)').textContent.trim()).toEqual(vpcList[2].state);
        });

        it('should display the vpc name in the second column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(vpcList[0].name);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(vpcList[1].name);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(vpcList[2].name);
        });

        it('should display the account name in the third column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(vpcList[0].account);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(vpcList[1].account);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(vpcList[2].account);
        });

        it('should display the description name in the fourth column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(vpcList[0].displaytext);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(vpcList[1].displaytext);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(vpcList[2].displaytext);
        });

        it('should display the network zone name in the fifth column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(5)').textContent.trim()).toEqual(vpcList[0].zonename);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(5)').textContent.trim()).toEqual(vpcList[1].zonename);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(5)').textContent.trim()).toEqual(vpcList[2].zonename);
        });

    });
});
