FROM mcr.microsoft.com/dotnet/aspnet:9.0

WORKDIR /home

# --------------- Layer 1: Install NodeJS ---------------
ENV NODE_VERSION=22.x
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    gnupg \ 
    && curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - \
    && apt-get update \ 
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*


# --------------- Layer 2: Set playwright env and prepare cache directory ---------------
EN<PERSON> PLAYWRIGHT_BROWSERS_PATH=/usr/local/share/ms-playwright

RUN mkdir -p $PLAYWRIGHT_BROWSERS_PATH \
    && chmod -R 755 $PLAYWRIGHT_BROWSERS_PATH

# --------------- Layer 3: Install Playwright + Chromium ---------------
RUN npm install -g  playwright@1.52.0 \
    &&  npx playwright install --with-deps chromium
