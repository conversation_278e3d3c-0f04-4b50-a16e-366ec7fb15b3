###############################################################################
# Spec file for MyAdaptiveCloud Background Worker Service
################################################################################
#
%define name myac-worker
%define username myac
%define usergroup %{username}
%define homedir /home/<USER>
%define installdir %{homedir}/%{name}
%define publishdir src/MyAdaptiveCloud.WorkerServices/bin/[rR]elease/net9.0/publish
%define pwdfile %{installdir}/docker_passwd
%define grpfile %{installdir}/docker_group
%define envfile %{installdir}/.env

Summary: MyAdaptiveCloud Background Worker Service
Name: %{name}
Version: 1.0
Release: %{_release}
License: IP Pathways Commercial
URL: http://www.ippathways.com
Group: System
Packager: <PERSON>
Requires(pre): shadow-utils
Requires: bash
Requires: docker-ce
Buildroot: %{_top}/rpmbuild/
#BuildRoot: $WORKSPACE/rpmbuild/

# Build with the following syntax:
# rpmbuild --target noarch -bb whitelabel.spec

%description
MyAdaptiveCloud Background Worker Service that performs many background tasks such as email, db cleanup, alert processing, etc.

%prep
################################################################################
# Create the build tree and copy the files from the development directories    #
# into the build tree.                                                         #
################################################################################
echo "BUILDROOT = $RPM_BUILD_ROOT"
echo "BUILDROOT = %{RPM_BUILD_ROOT}"
echo "TOP = %{_top}"
echo "RPMDIR = %{_rpmdir}"
#rm -rf %{_rpmdir}/
#mkdir -p %{_rpmdir}
mkdir -p $RPM_BUILD_ROOT%{installdir}/
mkdir -p $RPM_BUILD_ROOT/RPMS/noarch $RPM_BUILD_ROOT/SOURCES $RPM_BUILD_ROOT/SPECS $RPM_BUILD_ROOT/SRPMS
mkdir -p $RPM_BUILD_ROOT/usr/lib/systemd/system/

# Copy the publish output into the rpm build env
cp -r $WORKSPACE/%{publishdir}/* $RPM_BUILD_ROOT%{installdir}/
cp -r $WORKSPACE/%{publishdir}/.playwright $RPM_BUILD_ROOT%{installdir}/
cp $WORKSPACE/docker/prod/myac-worker.service $RPM_BUILD_ROOT/usr/lib/systemd/system/
cp $WORKSPACE/docker/prod/docker-compose-worker.yaml $RPM_BUILD_ROOT%{installdir}/docker-compose.yaml
cp $WORKSPACE/docker/dockerfile $RPM_BUILD_ROOT%{installdir}/dockerfile
exit

%files
%defattr(-, %{username}, %{usergroup})
#%attr(0744, root, root) %{installdir}/hosts/*
%{installdir}
%config %{installdir}/appsettings.json
%config /usr/lib/systemd/system/myac-worker.service

# Run during installation, prior to installing the files
%pre
# Create the user and group, if necessary
# Since we're creating a system user, it won't create a home directory or copy /etc/skel
# However, we need the home directory, but not /etc/skel, so we can go ahead and create it and ensure proper permissions during this
mkdir -p %{homedir}
getent group %{usergroup} >/dev/null || groupadd -r %{usergroup}
getent passwd %{username} >/dev/null || \
    useradd -r -g %{usergroup} -d %{homedir} -s /bin/bash -c "MyAdaptiveCloud user" %{username}
usermod -aG docker %{usergroup}
chown -R %{username}:%{usergroup} %{homedir}

# Run after installation of files
%post
if [ ! -f %{pwdfile} ]
then
    echo "Creating passwd file"
    echo "%{username}:x:$(id -u %{username}):$(id -g %{username})::%{homedir}:/bin/bash" > %{pwdfile}
    chown %{username}:%{username} %{pwdfile}
fi
if [ ! -f %{grpfile} ]
then
    echo "Creating group file"
    echo "%{username}:x:$(id -g %{username}):" > %{grpfile}
    chown %{username}:%{username} %{grpfile}
fi
# Create an env file that has the uid and gid of the user
if [ ! -f %{envfile} ]
then
    echo "Creating env file"
    echo "MY_UID=$(id -u %{username})" > %{envfile}
    echo "MY_GID=$(id -g %{username})" >> %{envfile}
    chown %{username}:%{username} %{envfile}
fi

# Run on uninstall for files not listed in %files
# Restart the service after the old version has been uninstalled
%postun
%systemd_postun_with_restart myac-worker.service

# Cleanup after rpm build process
%clean
rm -rf $RPM_BUILD_ROOT

# List changes
%changelog
