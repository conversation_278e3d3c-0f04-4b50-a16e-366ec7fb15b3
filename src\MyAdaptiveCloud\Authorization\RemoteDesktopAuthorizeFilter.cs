﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Core.Common.Agent;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Helper;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class RemoteDesktopAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public RemoteDesktopAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            int.TryParse(val, out int agentId);

            var agent = await _entityAuthorizationService.AgentActionByVersionService(agentId);

            if (agent == null)
            {
                context.Result = new BadRequestResult();
                return;
            }

            var canRemoteDesktop = AgentActionAvailabilityHelper.IsAgentActionAvailable(AgentActionTypeEnum.RemoteDesktopConnectionInitiate,
                agent.AgentServiceMajor, agent.AgentServiceMinor, agent.AgentServiceBuild);
            if (!canRemoteDesktop)
            {
                context.Result = new BadRequestResult();
            }

            return;
        }
    }

    public class RemoteDesktopAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public RemoteDesktopAuthorizeAttribute(params Perms[] perms) : base(typeof(RemoteDesktopAuthorizeFilter), perms)
        {
            Name = "agentId";
        }
    }
}