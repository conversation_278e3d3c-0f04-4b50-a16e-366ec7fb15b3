using AutoMapper;
using MyAdaptiveCloud.Api.Requests.Contacts;
using MyAdaptiveCloud.Services.DTOs.Schedule;
using MyAdaptiveCloud.Services.Requests.Person;
using MyAdaptiveCloud.Services.Requests.Services;
using MyAdaptiveCloud.Services.Requests.Updates;
using MyAdaptiveCloud.Services.Requests.UserRoles;

namespace MyAdaptiveCloud.Api.AutoMapper
{
    public class RequestMapperProfile : Profile
    {
        public RequestMapperProfile()
        {
            CreateMap<Requests.ACMappings.AcMappingRequest, Services.Requests.ACMappings.AcMappingRequest>();
            CreateMap<Requests.AdaptiveCloud.UsageSummaryRequest, Services.Requests.AdaptiveCloud.UsageSummaryRequest>();
            CreateMap<Requests.AdaptiveCloud.UsageReportRequest, Services.Requests.AdaptiveCloud.UsageReportRequest>();
            CreateMap<CreateContactRequest, Services.Requests.Contacts.CreateContactRequest>()
                .ForMember(model => model.CurrentUserId, option => option.Ignore())
                .ForMember(model => model.OrganizationId, option => option.Ignore());
            CreateMap<ContactListRequest, Services.Requests.Contacts.ContactListRequest>();
            CreateMap<Requests.Configuration.EditConfigurationRequest, Services.Requests.Configuration.EditConfigurationRequest>();
            CreateMap<Requests.Configuration.EditConfigurationValueRequest, Services.Requests.Configuration.EditConfigurationValueRequest>();
            CreateMap<Requests.UserRoles.UserRoleListRequest, UserRoleListRequest>();
            CreateMap<Requests.Users.UserListRequest, Services.Requests.Users.UserListRequest>();
            CreateMap<Requests.Users.CreateUserRequest, Services.Requests.Users.CreateUserWithRoleRequest>();
            CreateMap<Requests.Role.UpdateRoleRequest, Services.Requests.Role.UpdateRoleRequest>();
            CreateMap<Requests.Role.CreateRoleRequest, Services.Requests.Role.CreateRoleRequest>();
            CreateMap<Requests.Registration.CreateCompanyAndContactRequest, Services.Requests.Registration.CreateCompanyAndContactRequest>();
            CreateMap<Requests.Registration.CreateDomainRequest, Services.Requests.Registration.CreateDomainRequest>();
            CreateMap<Requests.TwoFactorAuthentication.UpdateAuthenticatorRequest, Services.Requests.TwoFactorAuthentication.UpdateAuthenticatorRequest>();
            CreateMap<Requests.Service.CreateServiceRequest, CreateServiceRequest>();
            CreateMap<Requests.Service.EditServiceRequest, EditServiceRequest>();
            CreateMap<Requests.AdaptiveCloud.UsageLogsListRequest, Services.Requests.AdaptiveCloud.UsageRunLogsListRequest>();
            CreateMap<Requests.Schedule.CreateEditScheduleRequest, Services.Requests.Schedule.CreateEditScheduleRequest>()
                .AfterMap((s, d) => d.ScheduleType = (s.ScheduleType == ScheduleTypeEnum.Monthly && s.ScheduleTypeMonthsType == Requests.Schedule.ScheduleTypeMonthsTypeEnum.On)
                    ? ScheduleTypeEnum.MonthlyRelative
                    : d.ScheduleType);
            CreateMap<Requests.Reports.ReportScheduleUpdateCreation, Services.Requests.Schedule.CreateEditScheduleRequest>()
                .ForMember(model => model.Name, option => option.Ignore())
                .ForMember(model => model.Description, option => option.Ignore())
                .ForMember(model => model.IsEnabled, option => option.Ignore())
                .AfterMap((s, d) => d.ScheduleType = (s.ScheduleType == ScheduleTypeEnum.Monthly && s.ScheduleMonthsType == Requests.Schedule.ScheduleTypeMonthsTypeEnum.On)
                    ? ScheduleTypeEnum.MonthlyRelative
                    : d.ScheduleType);

            CreateMap<Requests.Profile.ResetPasswordRequest, Services.Requests.Profile.ResetPasswordRequest>();
            CreateMap<Requests.UserRoles.EditUserRoleRequest, EditUserRoleRequest>();
            CreateMap<Requests.Users.EditUserRequest, EditPersonRequest>();
            CreateMap<Requests.Profile.EditProfileRequest, Services.Requests.Profile.EditProfileRequest>();
            CreateMap<Requests.ACMappings.AcVmMappingRequest, Services.Requests.ACMappings.AcVmMappingRequest>();

            CreateMap<Requests.Policy.UpdateCategoriesToAutoApprovalRequest, Services.Requests.Policy.UpdateCategoriesToAutoApprovalRequest>();
            CreateMap<Requests.Policy.CreateEditPolicyRequest, Services.Requests.Policy.CreateEditPolicyRequest>();

            CreateMap<Requests.Unsubscribe.UnsubscribeRequest, Services.Requests.Unsubscribe.UnsubscribeRequest>();
            CreateMap<Requests.Unsubscribe.UnsubscribeServiceOrganizationRequest, Services.Requests.Unsubscribe.UnsubcsribeServiceOrganizationRequest>();
            CreateMap<Requests.DataProtection.SubOrganizationsListRequest, Services.Requests.DataProtection.SubOrganizationsListRequest>();
            CreateMap<Requests.Updates.AgentsByFolderByUpdateRequest, AgentsByFolderByUpdateRequest>();
            CreateMap<Requests.Updates.SoftwareUpdatesListRequest, SoftwareUpdatesListRequest>();
            CreateMap<Requests.DataProtection.CreateOrganizationForTenantRequest, Services.Requests.DataProtection.CreateOrganizationForTenantRequest>();
            CreateMap<Requests.DataProtection.LinkOrganizationToTenantRequest, Services.Requests.DataProtection.LinkOrganizationToTenantRequest>();
            CreateMap<Requests.DataProtection.UsersListRequest, Services.Requests.DataProtection.UsersListRequest>();
            CreateMap<Requests.DataProtection.CreateUserRequest, Services.Requests.DataProtection.CreateUserRequest>();
            CreateMap<Requests.DataProtection.CreateCustomerTenantRequest, Services.Requests.DataProtection.CreateCustomerTenantRequest>();
            CreateMap<Requests.InvitationCode.CreateUpdateInvitationCodeRequest, Services.Requests.InvitationCode.CreateUpdateInvitationCodeRequest>();
            CreateMap<Requests.InvitationCode.GetInvitationCodeRequest, Services.Requests.InvitationCode.GetInvitationCodeRequest>();
            CreateMap<Requests.ApiUsers.CreateUserApiRequest, Services.Requests.Users.CreateUserApiRequest>();
            CreateMap<Requests.ApiUsers.ApiUserListRequest, Services.Requests.Users.ApiUserListRequest>();
            CreateMap<Requests.Tenax.OrganizationsListRequest, Services.Requests.Tenax.OrganizationsListRequest>();
            CreateMap<Requests.Tenax.UsersListRequest, Services.Requests.Tenax.UsersListRequest>();
            CreateMap<Requests.Tenax.CreateUserRequest, Services.Requests.Tenax.CreateUserRequest>();
            CreateMap<Requests.Tenax.EditUserRequest, Services.Requests.Tenax.EditUserRequest>();
            CreateMap<Requests.Reports.RecipientsRequest, Services.Requests.Reports.RecipientsRequest>();
        }
    }
}