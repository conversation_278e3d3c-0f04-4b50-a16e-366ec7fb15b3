using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class DeviceFolderAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public DeviceFolderAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
            IUserContextService userContextService, Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int folderId);

            var folderOrganizationId = await _entityAuthorizationService.GetDeviceFolderOrganizationId(folderId);
            if (folderOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, folderOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, folderOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class DeviceFolderAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public DeviceFolderAuthorizeAttribute(params Perms[] perms) : base(typeof(DeviceFolderAuthorizeFilter), perms)
        {
            Name = "deviceFolderId";
        }
    }
}