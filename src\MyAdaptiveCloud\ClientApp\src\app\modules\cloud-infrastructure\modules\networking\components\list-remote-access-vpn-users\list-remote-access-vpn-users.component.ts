import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, Observable, of, skip, switchMap, take } from 'rxjs';
import { VpnUser } from '../../models/vpn-user';
import { VPN_USER_LIST_CONSTANTS } from '../../models/vpn-user-list.constants';
import { VpnUserViewModel } from '../../models/vpn-user.view-model';
import { VpnUserListRequest } from '../../requests/vpn-user-list.request';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { VpnUsersService } from '../../services/vpn-users.service';
import { CreateVpnUserComponent } from '../create-vpn-user/create-vpn-user.component';

@Component({
    selector: 'app-list-remote-access-vpn-users',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent],
    templateUrl: './list-remote-access-vpn-users.component.html',
    styleUrl: './list-remote-access-vpn-users.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListRemoteAccessVpnUsersComponent extends BaseListComponent<VpnUserViewModel> implements OnInit {

    private readonly vpnUsersService = inject(VpnUsersService);
    readonly store = inject(ZoneDomainAccountStore);
    protected readonly networkingPermissionService = inject(NetworkingPermissionService);
    private readonly modalService = inject(ModalService);
    private readonly userContextService = inject(UserContextService);

    private readonly vpnUserList = signal<VpnUserViewModel[]>([]);
    private readonly selectedDomain$ = toObservable(this.store.selectedDomain);
    private readonly selectedAccount$ = toObservable(this.store.selectedAccount);

    private readonly statusRow = viewChild.required<TemplateRef<never>>('statusRow');
    private columns: TableColumn[] = [];

    constructor() {
        super();
        this.pagination = new VpnUserListRequest();
    }

    ngOnInit(): void {
        this.columns = [
            {
                cellTemplate: this.statusRow(),
                name: 'State',
                prop: VPN_USER_LIST_CONSTANTS.stateKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 150
            },
            {
                name: 'User Name',
                prop: VPN_USER_LIST_CONSTANTS.userNameKey,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                canAutoResize: true,
                resizeable: true,
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
                prop: VPN_USER_LIST_CONSTANTS.domainKey,
                name: 'Domain'
            },
            {
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
                prop: VPN_USER_LIST_CONSTANTS.accountKey,
                name: 'Account',
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 120
            }
        ];

        // When the user context has its own domain, do not pass account so the result includes all VMs in the domain
        const accountName = this.userContextService.currentUser.cloudInfraUserContext.hasMappedDomain ? null : this.userContextService.currentUser.cloudInfraUserContext.accountName;
        this.vpnUsersService.getVpnUsers()
            .pipe(take(1))
            .subscribe(res => {
                this.vpnUserList.set(this.mapVpnUserListResponse(res));
                this.onDomainAccountChanged(this.userContextService.currentUser.cloudInfraUserContext.domainId, accountName);
            });

        this.selectedDomain$.pipe(
            skip(1),
            filter(domain => !!domain),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(domain => {
            this.onDomainAccountChanged(domain.id, this.store.getAccount());
        });

        this.selectedAccount$.pipe(
            skip(1),
            filter(account => !!account),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(account => {
            this.onDomainAccountChanged(this.store.getDomainId(), account.name);
        });

    }

    private onDomainAccountChanged(domainId: string, account: string): void {
        let columns = [...this.columns];
        if (account) {
            columns = [...columns.filter(c => c.prop !== VPN_USER_LIST_CONSTANTS.accountKey && c.prop !== VPN_USER_LIST_CONSTANTS.domainKey)];
        } else if (domainId && !this.store.isRootDomainSelected()) {
            columns = [...columns.filter(c => c.prop !== VPN_USER_LIST_CONSTANTS.domainKey)];
        }

        if (this.table().columns?.length) {
            this.table().columns = [...columns];
        }

        super.initialize(() => this.getVpnUserList$(), this.columns);
    }

    private mapVpnUserListResponse(vpnUsers: VpnUser[]): VpnUserViewModel[] {
        let vpnUserViewModelList: VpnUserViewModel[] = [];
        if (vpnUsers?.length > 0) {
            vpnUserViewModelList = vpnUsers.map(vpnUser => {
                const viewModel: VpnUserViewModel = {
                    account: vpnUser.account.trim(),
                    domain: vpnUser.domain.trim(),
                    domainId: vpnUser.domainid,
                    id: vpnUser.id,
                    userName: vpnUser.username.trim(),
                    state: vpnUser.state,
                };
                return viewModel;
            });
        }
        return vpnUserViewModelList;
    }

    private getVpnUserList$(): Observable<ApiDatasetResult<VpnUserViewModel[]>> {
        // Apply filters, including selected domain and account and search term
        const filteredVPNUserList = [...this.vpnUserList().filter(vpnUser => {
            const matchesDomain = this.store.selectedDomain() ? vpnUser.domainId === this.store.selectedDomain().id : true;
            const matchesAccount = this.store.getAccount() ? vpnUser.account === this.store.getAccount() : true;
            const matchesSearchTerm = !this.pagination.searchTerm ||
                vpnUser.state.toLowerCase().includes(this.pagination.searchTerm.toLowerCase()) ||
                vpnUser.userName.toLowerCase().includes(this.pagination.searchTerm.toLowerCase());
            return matchesDomain && matchesAccount && matchesSearchTerm;
        })];

        // Apply sorting
        if (this.pagination.orderBy === VPN_USER_LIST_CONSTANTS.stateKey) {
            filteredVPNUserList.sort(sortByProperty(VPN_USER_LIST_CONSTANTS.stateKey as keyof VpnUserViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === VPN_USER_LIST_CONSTANTS.userNameKey) {
            filteredVPNUserList.sort(sortByProperty(VPN_USER_LIST_CONSTANTS.userNameKey as keyof VpnUserViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === VPN_USER_LIST_CONSTANTS.domainKey) {
            filteredVPNUserList.sort(sortByProperty(VPN_USER_LIST_CONSTANTS.domainKey as keyof VpnUserViewModel, this.pagination.orderDir === 'asc'));
        } else if (this.pagination.orderBy === VPN_USER_LIST_CONSTANTS.accountKey) {
            filteredVPNUserList.sort(sortByProperty(VPN_USER_LIST_CONSTANTS.accountKey as keyof VpnUserViewModel, this.pagination.orderDir === 'asc'));
        }

        // Apply pagination
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = startIndex + this.pagination.pageSize;
        const paginatedList = filteredVPNUserList.slice(startIndex, endIndex);

        return of({ data: paginatedList, totalCount: filteredVPNUserList.length });
    }

    protected openAddVpnUserModal(): void {
        if (this.store.getAccount() && this.networkingPermissionService.canAddVpnUser()) {
            const modalRef = this.modalService.openModalComponent(CreateVpnUserComponent);
            (modalRef.componentInstance as CreateVpnUserComponent).domainId.set(this.store.getDomainId());
            (modalRef.componentInstance as CreateVpnUserComponent).account.set(this.store.getAccount());
            modalRef.closed
                .pipe(
                    take(1),
                    filter(jobId => !!jobId)
                )
                .subscribe();
        }
    }

    protected deleteVpnUser(vpnUser: VpnUserViewModel): void {
        if (this.networkingPermissionService.canDeleteVpnUser()) {
            this.modalService.openDeleteConfirmationDialog('Delete VPN User', 'Are you sure you want to delete this VPN user?', 'delete')
                .closed
                .pipe(
                    take(1),
                    filter(res => !!res),
                    switchMap(() => this.vpnUsersService.deleteVpnUser(vpnUser.userName, vpnUser.domainId, vpnUser.account))
                )
                .subscribe();
        }
    }

}
