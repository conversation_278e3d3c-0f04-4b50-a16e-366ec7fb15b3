﻿using AutoMapper;

namespace MyAdaptiveCloud.Api.AutoMapper.DeviceFolders
{
    public class DeviceFoldersMapperProfile : Profile
    {
        public DeviceFoldersMapperProfile()
        {
            CreateMap<Requests.DeviceFolders.CreateDeviceFolderRequest, Services.Requests.DeviceFolders.CreateDeviceFolderRequest>();
            CreateMap<Requests.DeviceFolders.UpdateDeviceFolderRequest, Services.Requests.DeviceFolders.DeviceFolderUpdates>();
            CreateMap<Requests.DeviceFolders.MoveDeviceFolderItemsRequest, Services.Requests.DeviceFolders.MoveDeviceFolderItemsRequest>();
            CreateMap<Requests.DeviceFolders.MoveDeviceFoldersRequest, Services.Requests.DeviceFolders.MoveDeviceFoldersRequest>();
            CreateMap<Requests.DeviceFolders.AffectedDevicesRequest, Services.Requests.DeviceFolders.AffectedDevicesRequest>();
            CreateMap<Requests.DeviceFolders.MoveDevicesToUnassignedRequest, Services.Requests.DeviceFolders.MoveDevicesToUnassignedRequest>();
        }
    }
}