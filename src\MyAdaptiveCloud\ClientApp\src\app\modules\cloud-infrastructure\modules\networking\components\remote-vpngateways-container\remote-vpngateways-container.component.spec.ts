import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RemoteVpngatewaysContainerComponent } from './remote-vpngateways-container.component';

describe('RemoteVpngatewaysContainerComponent', () => {
    let component: RemoteVpngatewaysContainerComponent;
    let fixture: ComponentFixture<RemoteVpngatewaysContainerComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RemoteVpngatewaysContainerComponent]
        })
            .compileComponents();

        fixture = TestBed.createComponent(RemoteVpngatewaysContainerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
