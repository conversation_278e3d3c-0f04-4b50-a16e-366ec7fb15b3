<div class="modal-header">
  <h4 class="modal-title">Select Organization</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>

<div class="modal-body">
  <div class="card card-default">
    <div class="card-body">
      <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)"
        [placeholder]="'Search Organization'" [dataItemName]="'name'" />
      <ngx-datatable #table class="table bootstrap no-detail-row" />
    </div>
  </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
  <span (click)="sort()" class="clickable">
    {{ column.name }}
    <span
      [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
    </span>
  </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
  <span>
    @if (row.organizationId !== userContextService.currentUser?.organizationId) {
      <a href="#"
      (click)="selectOrganization(row.organizationId)" class="text-decoration-none">Select</a>
    }
  </span>
</ng-template>
