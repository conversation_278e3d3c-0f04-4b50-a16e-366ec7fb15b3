import { inject, Injectable } from '@angular/core';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { map, Observable } from 'rxjs';
import { CLOUD_INFRA_ENDPOINT_NAMES } from '../models/cloud-infra.constants';
import { ZoneViewModel } from '../models/zone.view-model';
import { ListZoneResponse } from '../responses/list-zone.response';

@Injectable({
    providedIn: 'root'
})
export class CloudInfraZoneService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getZones(): Observable<ZoneViewModel[]> {
        const params: Record<string, string> = {
            command: CLOUD_INFRA_ENDPOINT_NAMES.listZones,
            listall: 'true',
        };

        return this.cloudInfraApiService.get<ListZoneResponse>(params)
            .pipe(map((response: ListZoneResponse) => (response.listzonesresponse?.zone ?? [])
                .filter(zone => zone.allocationstate === 'Enabled')
                .sort((a, b) => a.name.localeCompare(b.name))
                .map(z => ({
                    id: z.id,
                    name: z.name
                }))));
    }

}
