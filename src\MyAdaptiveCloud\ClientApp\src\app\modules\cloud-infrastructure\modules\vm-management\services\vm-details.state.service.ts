import { Injectable, signal } from '@angular/core';
import { VmInstance } from '@app/shared/models/cloud-infra/vm-instance.model';
import { BehaviorSubject } from 'rxjs';
import { VmDetails } from '../models/vm-detail.model';

@Injectable({
    providedIn: 'root'
})
export class VmDetailsStateService {

    readonly selectedVM = signal<VmDetails | null>(null);

    mapVmInstanceToVmDetails(vm: VmInstance): VmDetails {
        const memoryUsagePercentage = this.getMemoryUsagePercentage(vm);
        const vmDetail: VmDetails = {
            ...vm,
            isAgentInstalled: false, // TODO CHECK isAgentInstalled prop
            cpuUsagePercentage: this.getCpuUsagePercentage(vm),
            memoryUsagePercentage,
            memoryUsagePercentageString: memoryUsagePercentage ? `${memoryUsagePercentage}%` : '0%',
        };
        return vmDetail;
    }

    readonly isLoading$ = new BehaviorSubject<boolean>(false);

    private getCpuUsagePercentage(vm: VmInstance): number {
        const percentage = vm.cpuused ? parseFloat(vm.cpuused.replace('%', '')) : 0;
        return percentage;
    }

    private getMemoryUsagePercentage(vm: VmInstance): number {
        return vm.memorykbs ? Math.round((vm.memorykbs / (vm.memory * 1024)) * 100) : 0;
    }
}
