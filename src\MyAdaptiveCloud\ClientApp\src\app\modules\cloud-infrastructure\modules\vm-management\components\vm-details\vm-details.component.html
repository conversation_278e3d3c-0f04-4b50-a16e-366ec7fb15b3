<div class="general-details-middle card">
    <div class="card-header bg-white">
        <h4 class="card-title text-secondary fw-bold">Virtual Machine Name</h4>
        <h5 class="card-subtitle text-secondary">{{vm().displayname}}</h5>
    </div>
    <div class="card-body d-flex flex-row">
        <div class="card w-50 me-2">
            <div class="card-header bg-white text-secondary fw-bold">
                Compute Offering
            </div>
            <div class="card-body">
                <div class="cpu">
                    <div class="text-secondary fw-bold"># of vCPUs </div>
                    <div class="text-secondary">{{ vm().cpunumber }}</div>
                    <div class="d-flex flex-row align-items-center">
                        <div class="progress w-75" style="height: 5px;">
                            <div class="progress-bar text-primary" role="progressbar" [style.width]="vm().cpuused"
                                [attr.aria-valuenow]="vm().cpuUsagePercentage" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <span class="text-secondary w-25 ms-3">{{vm().cpuused || '0%'}} Used</span>
                    </div>
                </div>
                <div class="memory">
                    <div class="text-secondary fw-bold">Memory (in MB) </div>
                    <div class="text-secondary">{{ vm().memory }}</div>
                    <div class="d-flex flex-row align-items-center">
                        <div class="progress w-75" style="height: 5px;">
                            <div class="progress-bar text-primary" role="progressbar"
                                [style.width]="vm().memoryUsagePercentageString"
                                [attr.aria-valuenow]="vm().memoryUsagePercentage" aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <span class="text-secondary w-25 ms-3">{{vm().memoryUsagePercentage}}% Used</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card w-50">
            <div class="card-body location d-flex flex-row">
                <div class="col">
                    <div class="zone d-flex flex-row mb-2">
                        <div class="d-flex flex-column">
                            <div class="text-secondary fw-bold">Zone Name</div>
                            <div class="text-secondary">{{ vm().zonename}}</div>
                        </div>
                    </div>
                    <div class="os d-flex flex-row mb-2">
                        <div class="d-flex flex-column">
                            <div class="text-secondary fw-bold">OS Type</div>
                            <div class="text-secondary">{{ vm().osdisplayname}}</div>
                        </div>
                    </div>
                    <div class="key d-flex flex-row mb-2">
                        <div class="d-flex flex-column">
                            <div class="text-secondary fw-bold">SSH Key Pair</div>
                            @for (pair of vm().keypairs; track $index) {
                            <div class="text-secondary">{{ pair.value }}</div>
                            }
                            @empty {
                            <div class="text-secondary"> N/A </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="gpu d-flex flex-row mb-2">
                        <div class="d-flex flex-column">
                            <div class="text-secondary fw-bold">vGPU</div>
                            <div class="text-secondary">{{ vm().vgpu ?? 'N/A'}}</div>
                        </div>
                    </div>
                    <div class="scalable d-flex flex-row mb-2">
                        <div class="d-flex flex-column">
                            <div class="text-secondary fw-bold">Dynamically Scalable</div>
                            <div class="text-secondary">{{ vm().isdynamicallyscalable ? 'Yes' : 'No'}}</div>
                        </div>
                    </div>
                    <div class="template d-flex flex-row mb-2">
                        <div class="d-flex flex-column">
                            <div class="text-secondary fw-bold">Template</div>
                            <div class="text-secondary">{{ vm().templatename }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if (cloudInfraPermissionService.isRootAdmin()) {
    <div class="general-details-bottom card">
        <div class="card-header fw-bold text-secondary pt-2">
            Tags
        </div>
        <div class="card-sub-header">
            <div class="container-fluid text-secondary">
                <div class="row d-flex align-items-center p-1">
                    <div class="col-5 fw-bold">Key</div>
                    <div class="col-5 fw-bold">Value</div>
                </div>
            </div>
        </div>
        <div class="card-body tags-list">
            <div>
                @for (tag of vm().tags; track tag.key) {
                    <div class="row align-items-center pb-2">
                        <div class="col-5">
                            <input class="form-control opacity-25" [value]="tag.key" readonly />
                        </div>
                        <div class="col-5">
                            <input class="form-control opacity-25" [value]="tag.value" readonly />
                        </div>
                        <div class="col-2">
                            <button class="btn btn-danger action-btn" data-testid="remove-tag" [disabled]="isLoading()"
                                (click)="deleteTags(tag.key, tag.value)">
                                <i class="fa-solid fa-close"></i>
                            </button>
                        </div>
                    </div>
                }
            </div>
                <form [formGroup]="tagsForm">
                    <div class="row align-items-center">
                        <div class="col-5">
                            <input class="form-control" placeholder="Key" formControlName="tagKey" id="tag"
                                [class]="{ 'is-invalid': tagsForm.controls.tagKey.invalid && tagsForm.controls.tagValue.dirty }" />
                        </div>
                        <div class="col-5">
                            <input class="form-control" placeholder="Value" formControlName="tagValue" id="value"
                                [class]="{ 'is-invalid': tagsForm.controls.tagValue.invalid && tagsForm.controls.tagValue.dirty }" />
                        </div>
                        <div class="col-2">
                            <button class="btn btn-primary action-btn" (click)="createTags()" data-testid="add-tag"
                                [disabled]="tagsForm?.invalid || isLoading()">
                                <i class="fa-solid fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </form>
        </div>
    </div>
}
