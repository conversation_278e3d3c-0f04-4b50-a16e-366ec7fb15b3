﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class ActivationKeyAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public ActivationKeyAuthorizeFilter(IUserContextService userContextService, IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _entityAuthorizationService = entityAuthorizationService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int activationKeyId);

            var activationKeyOrganizationId = await _entityAuthorizationService.GetActivationKeyOrganizationId(activationKeyId);

            if (activationKeyOrganizationId.HasValue)
            {
                if (_perms != null && _perms.Count() > 0)
                {
                    if (!_userContextService.HasPermission(userId, activationKeyOrganizationId.Value, _distance, _perms))
                    {
                        context.Result = new ForbidResult();
                    }
                }
                else
                {
                    context.Result = new ForbidResult();
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }

            return;
        }

        [AttributeUsage(AttributeTargets.Method, Inherited = false)]
        public class ActivationKeyAuthorizeAttribute : BaseAuthorizeAttribute
        {
            public ActivationKeyAuthorizeAttribute(params Perms[] perms) : base(typeof(ActivationKeyAuthorizeFilter), perms)
            {
                Name = "activationKeyId";
            }
        }
    }
}