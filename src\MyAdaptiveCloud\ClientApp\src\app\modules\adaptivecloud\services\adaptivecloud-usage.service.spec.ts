import { TestBed } from '@angular/core/testing';
import { ApiService } from '@app/shared/services/api.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { AdaptiveCloudUsageService } from './adaptivecloud-usage.service';

describe('AdaptivecloudUsageService', () => {
    let service: AdaptiveCloudUsageService;
    let mockApiService: jasmine.SpyObj<ApiService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ApiService),
                AdaptiveCloudUsageService
            ]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(AdaptiveCloudUsageService);
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(service).toBeTruthy();
        });
    });

    describe('API', () => {
        it('should use the right endpoint for loadUsagePeriods', () => {
            const organizationId = 2;
            service.getUsagePeriods(organizationId);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/usage/organization/${organizationId}/periods`);
        });

        it('should use the right endpoint for loadUsagePeriodsOverall', () => {
            service.getUsagePeriodsOverall();
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/usage/periods`);
        });

        it('should use the right endpoint for loadUsageOverall', () => {
            const period = '202201';
            service.getUsageOverall(period);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/usage/overall`, { period });
        });

        it('should use the right endpoint for loadUsageSummary', () => {
            const period = '202201';
            service.getUsageOverallSummaryCsv(period);
            expect(mockApiService.downloadFile).toHaveBeenCalledOnceWith(`${service.endpoint}/usage/overall/summary`, { period });
        });

        it('should use the right endpoint for getVirtualMachines', () => {
            const acId = '2';
            service.getVirtualMachines(acId);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/virtualMachines?acId=${acId}`);
        });

        it('should use the right endpoint for getOsCategories', () => {
            service.getOsCategories(1);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/osCategories/1`);
        });

        it('should use the right endpoint for getOsTypes with osCategoryId', () => {
            const osCategoryId = '2';
            service.getOsTypes(1, osCategoryId);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/osTypes/1`, { osCategoryId });
        });

        it('should use the right endpoint for getOsTypes without osCategoryId', () => {
            service.getOsTypes(1);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/osTypes/1`);
        });

        it('should use the right endpoint for getAcUsageRunLogs', () => {
            const request = {
                pageSize: 25,
                currentPage: 1,
                orderBy: 'date',
                orderDir: 'asc',
                command: '',
                month: '',
                startTime: '',
                endTime: '',
                errorCount: 0
            };
            service.getAcUsageRunLogs(request);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/acUsageRunLogs`, request);
        });

        it('should use the right endpoint for getAcUsageRunLogDetails', () => {
            const runlogId = 2;
            service.getAcUsageRunLogDetails(runlogId);
            expect(mockApiService.get).toHaveBeenCalledWith(`${service.endpoint}/acUsageRunLogDetails?runlogId=${runlogId}`);
        });

    });
});
