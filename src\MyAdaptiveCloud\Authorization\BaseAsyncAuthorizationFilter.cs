using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Core.Permissions;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    /// Verifies that the current user's Role is authorized to access the target Organization's information
    /// </summary>
    public abstract class BaseAsyncAuthorizationFilter : IAsyncAuthorizationFilter
    {
        protected readonly string _name;
        protected readonly int _distance;
        protected readonly Perms[] _perms;

        public BaseAsyncAuthorizationFilter(Perms[] perms, int distance = 0, string name = null)
        {
            _name = name;
            _perms = perms;
            _distance = distance;
        }

        public abstract Task OnAuthorizationAsync(AuthorizationFilterContext context);
    }
}