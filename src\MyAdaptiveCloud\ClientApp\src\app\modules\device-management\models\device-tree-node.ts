import { signal } from '@angular/core';
import { Device } from './device';
import { DeviceFolderTreeNode } from './device-folder-tree-node';

export class DeviceTreeNode {
    readonly parent = signal<DeviceFolderTreeNode | null>(null);
    readonly isSelected = signal<boolean>(false);
    readonly isSelectedForAction = signal<boolean>(false);

    type: string;
    agentId: number;
    orgId: number;
    hostname: string;
    readonly description = signal<string>('');
    readonly name = signal<string>('');
    path?: string;
    folderId?: number;
    cloudStackVM?: boolean;

    constructor(device?: Device, parent?: DeviceFolderTreeNode) {
        this.agentId = device?.agentId;
        this.orgId = device?.orgId;
        this.hostname = device?.hostname;
        this.path = device?.path;
        this.folderId = device?.folderId;
        this.cloudStackVM = device?.cloudStackVM;

        if (device?.name) {
            this.name.set(device.name);
        }

        if (device?.description) {
            this.description.set(device.description);
        }

        if (parent) {
            this.parent.set(parent);
        }
    }

    toggleSelected() {
        this.isSelected.update(value => !value);
    }

    toggleSelectedForAction() {
        this.isSelectedForAction.update(value => !value);
    }
}
