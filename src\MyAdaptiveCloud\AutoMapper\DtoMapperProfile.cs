using AutoMapper;
using MyAdaptiveCloud.Api.AutoMapper.Schedule;
using MyAdaptiveCloud.Api.Requests.Contacts;
using MyAdaptiveCloud.Api.ViewModel.AcMapping;
using MyAdaptiveCloud.Api.ViewModel.CloudInfraStructure;
using MyAdaptiveCloud.Api.ViewModel.Notification;
using MyAdaptiveCloud.Api.ViewModel.Policy;
using MyAdaptiveCloud.Api.ViewModel.Report;
using MyAdaptiveCloud.Api.ViewModel.Schedule;
using MyAdaptiveCloud.Api.ViewModel.TwoFactorAuthentication;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.Apis.ConnectWise.Model;
using MyAdaptiveCloud.Services.Apis.KeyCloak.Model;
using MyAdaptiveCloud.Services.AutoMapper.Extensions;
using MyAdaptiveCloud.Services.DTOs.ACMapping;
using MyAdaptiveCloud.Services.DTOs.Policy;
using MyAdaptiveCloud.Services.DTOs.Report;
using MyAdaptiveCloud.Services.Requests.ACMappings;

namespace MyAdaptiveCloud.Api.AutoMapper
{
    public class DtoMapperProfile : Profile
    {
        public DtoMapperProfile()
        {
            CreateMap<Credential, AuthenticatorProviderViewModel>().ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.UserLabel));
            CreateMap<ACUsageRunLog, UsageLogsViewModel>();

            CreateMap<UpdateCategoryAutoApprovalPolicyViewModel, UpdateCategoryAutoApprovalPolicyDTO>();
            CreateMap<UpdateCategoryAutoApprovalPolicyDTO, UpdateCategoryAutoApprovalPolicyViewModel>();

            // Intentionally not mapping User to UserViewModel using mapper as it is not a 1:1 mapping and is handled in UserExtensions.cs
            // CreateMap<Services.Model.User, UserViewModel>();
            CreateMap<Services.DTOs.Schedule.Schedule, ScheduleViewModel>()
                .ForMember(dest => dest.StartDateTimeLocalized,
                    opt => opt.MapFrom<StartDateTimeLocalizedResolver>())
                .IgnoreViewModelUserActions();

            CreateMap<ReportRequestEditDTO, ReportRequestViewModel>().IgnoreViewModelUserActions();

            CreateMap<PolicyDTO, PolicyViewModel>()
                .IgnoreViewModelUserActions();

            CreateMap<EditContactRequest, Services.Requests.Contacts.EditContactRequest>();
            CreateMap<ServiceTicket, ServiceTicketViewModel>();
            CreateMap<ServiceTicketConfiguration, ServiceTicketConfigurationViewModel>();
            CreateMap<ACMappingSearchViewModel, ACMappingSearchRequest>();
            CreateMap<ACMappingDTO, ACMappingViewModel>();
            CreateMap<AcUnmappedAccountDomainModel, AcUnmappedAccountDomainViewModel>();
        }
    }
}