import { VmStatusEnum } from '@app/shared/models/cloud-infra/vm-status.enum';
import { DynamicListFilterGroupOptions } from '@app/shared/models/datatable/filters/dynamic-list-filter-group-options';
import { FilterGroupAndRequest } from '@app/shared/models/datatable/filters/filter-group-and-request';
import { FilterOption } from '@app/shared/models/datatable/filters/filter.option';
import { ListFilterGroupOptions } from '@app/shared/models/datatable/filters/list-filter-group-options';

export class VmListFilters extends FilterGroupAndRequest {

    private statusGroupOption: FilterOption<string>[] = [
        new FilterOption<string>(
            VmStatusEnum.Destroyed,
            VmStatusEnum.Destroyed,
            false,
            VmStatusEnum.Destroyed,
        ),
        new FilterOption<string>(
            VmStatusEnum.Present,
            VmStatusEnum.Present,
            false,
            VmStatusEnum.Present,
        ),
        new FilterOption<string>(
            VmStatusEnum.Running,
            VmStatusEnum.Running,
            false,
            VmStatusEnum.Running,
        ),
        new FilterOption<string>(
            VmStatusEnum.Stopped,
            VmStatusEnum.Stopped,
            false,
            VmStatusEnum.Stopped,
        ),
    ];

    constructor() {
        super();
        this.orderBy = 'name';
        this.orderDir = 'asc';
        this.filterGroupOptions.push(new DynamicListFilterGroupOptions('Zone', 'zone'));
        this.filterGroupOptions.push(new ListFilterGroupOptions(
            'Status',
            'state',
            false,
            this.statusGroupOption,
        ));
    }
}

