<form class="mt-3" [formGroup]="form">
    <div class="row mb-3">
        <label for="name" class="col-3 col-form-label">Name<span class="required-asterisk">*</span></label>
        <div class="col">
            <input class="form-control" id="name" formControlName="name"
                [class]="{ 'is-invalid': form.controls.name.invalid && form.controls.name.dirty }" autocomplete="off" />
        </div>
    </div>
    <div class="row mb-3">
        <label for="description" class="col-3 col-form-label">Description<span
                class="required-asterisk">*</span></label>
        <div class="col">
            <input class="form-control" id="description" formControlName="description"
                [class]="{ 'is-invalid': form.controls.description.invalid && form.controls.description.dirty }" />
        </div>
    </div>
    <div class="row mb-3">
        <label class="col-3 col-form-label">Network Offering<span class="required-asterisk">*</span></label>
        <div class="col">
            <ng-select [items]="networkOfferings()" bindLabel="name" formControlName="networkOffering"
                [class]="{ 'is-invalid': form.controls.networkOffering.invalid && form.controls.networkOffering.dirty }" />
        </div>
    </div>
    @if (form.controls.networkOffering.value?.specifyVLan) {
    <div class="row mb-3">
        <label class="col-3 col-form-label" for="vlan">VLAN / VNI
            <span class="required-asterisk">*</span>
        </label>
        <div class="col">
            <input class="form-control" id="vlan" formControlName="vlan"
                [class]="{ 'is-invalid': form.controls.vlan.invalid && form.controls.vlan.dirty }" />
        </div>
    </div>
    <div class="row mb-3">
        <label class="col-3 col-form-label" for="bypassVLanId">Bypass VLAN ID / Range Overlap</label>
        <div class="col align-content-center">
            <input id="bypassVLanId" class="form-check-input" type="checkbox" formControlName="bypassVLanId" />
        </div>
    </div>
    <div class="row mb-3">
        <label class="col-3 col-form-label">Secondary VLAN Type</label>
        <div class="col align-content-center">
            @for (secondaryVlanType of secondaryVlanTypes; track secondaryVlanType; ; let index = $index) {
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" formControlName="secondaryVLanType"
                    id="secondaryVlanType_{{index}}" [value]="secondaryVlanType">
                <label class="form-check-label" for="secondaryVlanType_{{index}}">{{ secondaryVlanType }}</label>
            </div>
            }
        </div>
    </div>
    @if (form.controls.secondaryVLanType.value === SecondaryVlanType.Community || form.controls.secondaryVLanType.value
    === SecondaryVlanType.Isolated) {
    <div class="row mb-3">
        <label for="secondaryVLanId" class="col-3 col-form-label">Secondary VLAN ID
            <span class="required-asterisk">*</span></label>
        <div class="col">
            <input class="form-control" id="secondaryVLanId" formControlName="secondaryVLanId" />
        </div>
    </div>
    }
    }
</form>