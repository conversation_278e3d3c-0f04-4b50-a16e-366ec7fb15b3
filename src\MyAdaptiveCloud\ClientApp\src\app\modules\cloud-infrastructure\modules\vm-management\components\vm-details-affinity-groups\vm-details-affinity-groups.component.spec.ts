import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { getMockZoneDomainAccountStore, MockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { ConfirmationDialogComponent } from '@app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ModalService } from '@app/shared/services/modal.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { AffinityGroup } from '../../models/affinity-group.model';
import { VmDetails } from '../../models/vm-detail.model';
import { VmAffinityGroupsService } from '../../services/vm-affinity-groups.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { AddToAffinityGroupComponent } from '../add-to-affinity-group/add-to-affinity-group.component';
import { VmDetailsAffinityGroupsComponent } from './vm-details-affinity-groups.component';

describe('VmDetailsAffinityGroupsComponent', () => {
    let fixture: ComponentFixture<VmDetailsAffinityGroupsComponent>;
    let vmDetailsStateService: VmDetailsStateService;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementPermissionService: jasmine.SpyObj<VmManagementPermissionService>;
    let mockModalService: jasmine.SpyObj<ModalService>;
    let affinityGroups: AffinityGroup[];

    let dataTableDebugElement: DebugElement;
    let dataTable: HTMLElement;

    let mockZoneDomainAccountStore: MockZoneDomainAccountStore;

    beforeEach(() => {

        mockZoneDomainAccountStore = getMockZoneDomainAccountStore();

        const vm = {
            id: 'vm1',
        } as VmDetails;

        affinityGroups = [
            { id: 'ag1', name: 'Affinity Group 1', description: 'Test Affinity Group 1', type: 'host anti-affinity' } as AffinityGroup,
            { id: 'ag2', name: 'Affinity Group 2', description: 'Test Affinity Group 2', type: 'host affinity' } as AffinityGroup
        ];

        TestBed.configureTestingModule({
            imports: [
                VmDetailsAffinityGroupsComponent
            ],
            providers: [
                VmDetailsStateService,
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementPermissionService),
                provideMock(NgbActiveModal),
                provideMock(ModalService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
                ConfirmationDialogComponent,
                AddToAffinityGroupComponent
            ]
        });

        vmDetailsStateService = TestBed.inject(VmDetailsStateService);

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroupsByVirtualMachine.and.returnValue(of(affinityGroups));
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of([]));
        mockVmAffinityGroupsService.getAffinityGroupTypes.and.returnValue(of([]));

        mockVmManagementPermissionService = TestBed.inject(VmManagementPermissionService) as jasmine.SpyObj<VmManagementPermissionService>;
        mockVmManagementPermissionService.canUpdateVmAffinityGroups.and.returnValue(true);
        mockVmManagementPermissionService.canCreateAffinityGroup.and.returnValue(true);

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;

        vmDetailsStateService.selectedVM.set(vm);

        fixture = TestBed.createComponent(VmDetailsAffinityGroupsComponent);

        dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
        dataTable = dataTableDebugElement.nativeElement;
    });

    describe('Initialization', () => {

        it('should load affinity groups for the VM', () => {
            fixture.detectChanges();
            expect(mockVmAffinityGroupsService.getAffinityGroupsByVirtualMachine).toHaveBeenCalledOnceWith('vm1');
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(affinityGroups.length);
        });

        it('should show the Add Affinity Group button when user has permission and enable it when there is an account selected', () => {
            fixture.detectChanges();
            const addButton = fixture.debugElement.query(By.css('[data-testid="add-affinity-group-button"]'));
            expect(addButton).toBeDefined();
            expect((addButton.nativeElement as HTMLButtonElement).disabled).toBeFalse();
        });

        it('should show the Add Affinity Group button when user has permission and disable it when there is no account selected', () => {
            mockZoneDomainAccountStore.getAccount.set(null);
            fixture.detectChanges();
            const addButton = fixture.debugElement.query(By.css('[data-testid="add-affinity-group-button"]'));
            expect(addButton).toBeDefined();
            expect((addButton.nativeElement as HTMLButtonElement).disabled).toBeTrue();
        });

        it('should not show the Add Affinity Group button when user lacks permission', () => {
            mockVmManagementPermissionService.canUpdateVmAffinityGroups.and.returnValue(false);
            fixture.detectChanges();
            const addButton = fixture.debugElement.query(By.css('[data-testid="add-affinity-group-button"]'));
            expect(addButton).toBeNull();
        });

        it('should not show the remove affinity action button when user lacks permission', () => {
            mockVmManagementPermissionService.canUpdateVmAffinityGroups.and.returnValue(false);
            fixture.detectChanges();
            const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
            expect(actions.length).toBe(0);
        });

        it('should show the remove affinity action button when user has permission', () => {
            mockVmManagementPermissionService.canUpdateVmAffinityGroups.and.returnValue(true);
            fixture.detectChanges();
            const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
            expect(actions.length).toBe(2);
        });

        it('should show the expected data in the grid', () => {
            fixture.detectChanges();
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            const secondRow = rows[1];
            const secondRowCells = secondRow.querySelectorAll('.datatable-body-cell');

            expect(firstRowCells[0].textContent).toBe(affinityGroups[0].name);
            expect(firstRowCells[1].textContent).toBe(affinityGroups[0].description);
            expect(firstRowCells[2].textContent).toBe(affinityGroups[0].type);

            expect(secondRowCells[0].textContent).toBe(affinityGroups[1].name);
            expect(secondRowCells[1].textContent).toBe(affinityGroups[1].description);
            expect(secondRowCells[2].textContent).toBe(affinityGroups[1].type);
        });

    });

    describe('Component Interaction', () => {

        it('should open the remove modal when clicking on the action', () => {

            mockModalService.openDeleteConfirmationDialog.and.returnValue({
                closed: of(true),
                componentInstance: TestBed.inject(ConfirmationDialogComponent)
            } as NgbModalRef);

            fixture.detectChanges();

            const actions = dataTableDebugElement.queryAll(By.directive(TableActionComponent));
            actions[0].query(By.css('span')).nativeElement.click();
            fixture.detectChanges();

            expect(mockModalService.openDeleteConfirmationDialog).toHaveBeenCalledTimes(1);
        });

        it('should open the add modal when clicking on the action', () => {

            mockModalService.openModalComponent.and.returnValue({
                closed: of(true),
                componentInstance: TestBed.inject(AddToAffinityGroupComponent)
            } as NgbModalRef);

            fixture.detectChanges();

            const addButton = fixture.debugElement.query(By.css('[data-testid="add-affinity-group-button"]'));
            addButton.nativeElement.click();
            fixture.detectChanges();

            expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
        });

    });

});
