﻿using AutoMapper;

namespace MyAdaptiveCloud.Api.AutoMapper.Logs
{
    public class LogRequestMapperProfile : Profile
    {
        public LogRequestMapperProfile()
        {
            CreateMap<Requests.Logs.LogListRequest, Services.Requests.Logs.LogListRequest>()
                .ForMember(
                    dest => dest.ViewErrors,
                    src => src.MapFrom(
                        src => src.ViewHttpRequests != Requests.Logs.LogListViewHttpRequest.None
                    )
                )
                .ForMember(
                    dest => dest.ViewGETRequests,
                    src => src.MapFrom(
                        src => src.ViewHttpRequests == Requests.Logs.LogListViewHttpRequest.ViewAll
                    )
                );
        }
    }
}