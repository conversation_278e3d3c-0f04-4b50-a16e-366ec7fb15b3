import { Injectable, inject } from '@angular/core';
import { ActivationKey } from '@app/modules/device-management/modules/activation-keys/models/activation-key.model';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class ActivationKeysService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'activationkeys';

    getActivationKeysForOrg(organizationId: number): Observable<ApiDataResult<ActivationKey[]>> {
        return this.apiService.get<ApiDataResult<ActivationKey[]>>(`${this.endpoint}/organization/${organizationId}`);
    }

    createActivationKey(organizationId: number): Observable<ApiResult> {
        return this.apiService.post<ApiResult, null>(
            `${this.endpoint}/${organizationId}`,
            null
        );
    }

    toggleActivationKey(activationKeyId: number): Observable<ApiDataResult<ActivationKey>> {
        return this.apiService.post<ApiDataResult<ActivationKey>, null>(
            `${this.endpoint}/${activationKeyId}/toggle`,
            null
        );
    }
}
