@if (networkingDetailService.selectedNetwork()){
<div class="card card-default mt-4">
    <div class="cards-container">
        <ul class="tabs-nav nav nav-underline pt-2">
            <li class="nav-item">
                <a class="nav-link px-4" [routerLink]="['.']" routerLinkActive="active">
                    Details
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link px-4" [routerLink]="['egress-rules']" routerLinkActive="active">
                    Egress Rules
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link px-4" [routerLink]="['public-ip-address']" routerLinkActive="active">
                    Public IP Address
                </a>
            </li>
            @if (cloudInfraPermissionService.isRootAdmin()) {
                <li class="nav-item">
                    <a class="nav-link px-4" [routerLink]="['virtual-routers']" routerLinkActive="active">
                        Virtual Routers
                    </a>
                </li>
            }
            <li class="nav-item">
                <a class="nav-link px-4" [routerLink]="['vpn-connection']" routerLinkActive="active">
                    VPN Connection
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link px-4" [routerLink]="['view-virtual-machines']" routerLinkActive="active">
                    View Virtual Machines
                </a>
            </li>
        </ul>
    </div>
    <!-- Header shareable -->
    <div class="mt-2 p-3 border-bottom">
        <div class="general-details-top d-flex">
            <div class="status col-1 d-flex">
                <i class="fa fa-network-wired fa-3x text-primary"></i>
            </div>
            <div class="details d-flex flex-column col-11">
                <div class="first-row d-flex flex-row">
                    <div class="name mx-2 me-2">
                        <span class="text-primary fs-4">{{ networkingDetailService.selectedNetwork().name }}</span>
                    </div>
                    <span class="pt-1">
                        @switch (networkingDetailService.selectedNetwork().state) {
                        @case ('Allocated') {
                        <span title="Network is allocated and will be fully setup once a VM is assigned to it"
                            class="badge rounded-pill border border-secondary text-secondary">
                            <i class="fa fa-check-circle text-primary me-1 status-icon"></i>
                            Allocated
                        </span>
                        }
                        @case ('Implemented') {
                        <span title="Network is implemented and fully setup"
                            class="badge rounded-pill border border-success text-success">
                            <i class="fa fa-check-circle text-success me-1 status-icon"></i>
                            Implemented
                        </span>
                        }
                        @case ('Implementing') {
                        <span title="Network is being implemented"
                            class="badge rounded-pill border border-info text-info">
                            <i class="icon-network-implementing status-icon me-1"></i>
                            Implementing
                        </span>
                        }
                        @case ('Setup') {
                        <span title="Network was pre-setup and is ready for use"
                            class="badge rounded-pill border border-success text-success">
                            <i class="fa fa-check-circle text-success me-1 status-icon"></i>
                            Setup
                        </span>
                        }
                        }
                    </span>
                </div>
                <div class="second-row d-flex flex-row mt-3">
                    <div class="id-cell mx-2">
                        <span class="text-secondary fw-bold">Description:</span>
                        <span class="text-secondary">{{ networkingDetailService.selectedNetwork().displaytext }}</span>
                    </div>
                    <div class="created-cell mx-2">
                        <span class="text-secondary fw-bold">Create Date:</span>
                        <span class="text-secondary">
                            {{
                            networkingDetailService.selectedNetwork().created
                            ? (networkingDetailService.selectedNetwork().created | date: 'MM/dd/yyyy')
                            : '-'
                            }}
                        </span>
                    </div>
                </div>
            </div>
            <fieldset>
                <div class="actions col-1 d-flex justify-content-end align-items-end">
                    <button class="btn btn-outline-secondary me-1" title="Edit Network"><i
                            class="fa fa-edit"></i></button>
                    <button class="btn btn-outline-secondary me-1" title="Restart Network"><i
                            class="fa fa-sync"></i></button>
                    <button class="btn btn-outline-secondary me-1" title="Refresh Page"><i
                            class="fa fa-redo"></i></button>
                    <button class="btn btn-outline-secondary me-1" title="Delete Network"><i
                            class="fa fa-trash"></i></button>
                </div>
            </fieldset>
        </div>
    </div>
    <div class="card-body">
        <router-outlet></router-outlet>
    </div>
</div>
}
