import { computed } from '@angular/core';
import { ORGANIZATION_FOLDER_ID_PREFIX } from '@app/shared/constants/shared-folder-devices-constants';
import { DeviceFolderTreeNode } from './device-folder-tree-node';
import { DraggingItemsSelection } from './dragging-items-selection';
import { OrganizationFolder } from './organization-folder.model';

export class OrganizationFolderTreeNode extends DeviceFolderTreeNode {
    private _organizationId: number;
    private _parentOrganizationId: number = null;
    private _isPartnerOrganization: boolean;

    constructor(organization: Partial<OrganizationFolder>, subfolders?: DeviceFolderTreeNode[], level?: number, parent?: DeviceFolderTreeNode, isExpanded?: boolean) {
        super(null, level ?? 0, parent, subfolders);
        this._organizationId = organization.organizationId;
        this._parentOrganizationId = (parent?.getId() ?? organization.parentOrganizationId) ?? null;
        this.hasSubfolders.update(value => value || organization.hasSubfolders);
        this._isPartnerOrganization = !!organization.isPartner;
        this._deviceCountSelfAndChildren.set(organization.deviceCount ?? 0);
        this._deviceCountOnlySelf.set(organization.deviceCountCurrentFolder ?? 0);
        this.name.set(organization.name);
        this.isExpanded.set(!!isExpanded);
    }

    override isPartnerOrganization(): boolean {
        return this._isPartnerOrganization;
    }

    override isRootOrganization(): boolean {
        return this._parentOrganizationId === null;
    }

    // Since the ids of folders and organizations are not unique between them, we add a prefix to make them unique.
    override getUniqueStringId(): string {
        return `${ORGANIZATION_FOLDER_ID_PREFIX}${this.getId().toString()}`;
    }

    override getId() {
        return this._organizationId;
    }

    override readonly getParentId = computed(() => this._parentOrganizationId);

    override isOrganizationFolder(): boolean {
        return true;
    }

    override isUnassignedDevicesFolder(): boolean {
        return false;
    }

    override readonly deviceCountSelfAndChildren = computed(() => (this._deviceCountSelfAndChildren()));

    override readonly shouldLoadDevices = computed(() => false);

    override addSubfolders(folders: DeviceFolderTreeNode[]): void {
        super.addSubfolders(folders);
        // hasSubfolders is true if there is at least one subfolder that is not UnassignedDevicesFolderTreeNode
        this.hasSubfolders.set(this.subFolders().filter(subFolder => !subFolder.isUnassignedDevicesFolder()).length > 0);
    }

    override removeSubfolders(folders: DeviceFolderTreeNode[]) {
        super.removeSubfolders(folders);
        this.hasSubfolders.set(this.subFolders().filter(subFolder => !subFolder.isUnassignedDevicesFolder()).length > 0);
    }

    override readonly shouldLoadSubfolders = computed(() => {
        const shouldLoadUnassignedDevicesFolder = this.subFolders().length === 0 && this._deviceCountOnlySelf() > 0;
        return !this.isRootOrganization() && (!!this.hasSubfolders() && !this.subFolders().length || shouldLoadUnassignedDevicesFolder);
    });

    override folderBelongsToTheOrganization(folder: DeviceFolderTreeNode): boolean {
        return this === folder.getOrganization();
    }

    override canReceiveItems(movingItems: DraggingItemsSelection): boolean {
        return !movingItems.hasDevices() &&
            movingItems.folders.every(folderToMove => !folderToMove.isOrganizationFolder() && (folderToMove.getParentId() ?? null) !== null
                && this.folderBelongsToTheOrganization(folderToMove) && !this.movingThisFolderCreatesACycle(folderToMove));
    }
}
