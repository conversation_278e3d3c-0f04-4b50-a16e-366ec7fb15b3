import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DevicesService } from '../../services/devices.service';
import { DownloadAgentInstallerComponent } from './download-agent-installer.component';

describe('DownloadAgentInstallerComponent', () => {
    let component: DownloadAgentInstallerComponent;
    let fixture: ComponentFixture<DownloadAgentInstallerComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DownloadAgentInstallerComponent],
            providers: [
                provideMock(DevicesService),
                provideMock(UserContextService),
                provideMock(ModalService)],
        })
            .compileComponents();

        fixture = TestBed.createComponent(DownloadAgentInstallerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
