import { inject, Injectable } from '@angular/core';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NETWORKING_ENDPOINT_NAMES } from '../models/networking.constants';

@Injectable({
    providedIn: 'root'
})
export class NetworkingPermissionService {

    private readonly userContextService = inject(UserContextService);

    public canViewNetworkList(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.listNetworks);
    }

    public canViewVpnUserList(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.listVpnUsers);
    }

    public canViewVirtualPrivateCloudList(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.listVirtualPrivateClouds);
    }

    public canViewRemoteVpnGatewaysList(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.listRemoteVpnGateways);
    }

    public canRestartNetwork(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.restartNetwork);
    }

    public canCreateNetwork(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.createNetwork);
    }

    public canDeleteNetwork(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.deleteNetwork);
    }

    public canEditNetwork(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.editNetwork);
    }

    public canAddVpnUser(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.addVpnUser);
    }

    public canDeleteVpnUser(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.deleteVpnUser);
    }

    public canRestartVirtualPrivateCloud(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.restartNetwork);
    }

    public canDeleteVirtualPrivateCloud(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.deleteVirtualPrivateCloud);
    }

    public canCreateVirtualPrivateCloud(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || (cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.createNetwork) && cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.listVirtualPrivateCloudOfferings));
    }

    public canEditVirtualPrivateCloud(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.editVirtualPrivateCloud);
    }

    public canCreateRemoteVpnGateway(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || (cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.createRemoteVpnGateway));
    }

    public canEditRemoteVpnGateway(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || (cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.editRemoteVpnGateway));
    }

    public canDeleteRemoteVpnGateway(): boolean {
        const cloudInfraContext = this.userContextService.currentUser.cloudInfraUserContext;
        return this.hasAllPermissions(cloudInfraContext) || (cloudInfraContext.permissions.includes(NETWORKING_ENDPOINT_NAMES.deleteRemoteVpnGateway));
    }

    private hasAllPermissions(cloudInfraContext: CloudInfraUserContext): boolean {
        return cloudInfraContext?.permissions.length === 1 && cloudInfraContext.permissions[0] === '*';
    }

}
