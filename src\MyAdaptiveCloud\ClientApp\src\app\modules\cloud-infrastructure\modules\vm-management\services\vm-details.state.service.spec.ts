import { TestBed } from '@angular/core/testing';
import { VmDetails } from '../models/vm-detail.model';
import { VmDetailsStateService } from './vm-details.state.service';

describe('VmDetailsStateService', () => {
    let service: VmDetailsStateService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [VmDetailsStateService]
        });
        service = TestBed.inject(VmDetailsStateService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('mapVmInstanceToVmDetails', () => {
        it('should handle vm.cpuused as null or undefined', () => {
            const mockVmInstance = {
                id: 'vm-202',
                name: 'VM without CPU usage',
                cpuused: null,
                memorykbs: 100000,
                memory: 512,
            } as VmDetails;

            let result = service.mapVmInstanceToVmDetails(mockVmInstance);
            expect(result.cpuUsagePercentage).toBe(0);

            const mockVmInstanceUndefinedCpu = {
                id: 'vm-203',
                name: 'VM with undefined CPU usage',
                // cpuused is undefined
                memorykbs: 100000,
                memory: 512,
            } as VmDetails;

            result = service.mapVmInstanceToVmDetails(mockVmInstanceUndefinedCpu);
            expect(result.cpuUsagePercentage).toBe(0);
        });

        it('should handle vm.cpuused as an empty string', () => {
            const mockVmInstance = {
                id: 'vm-204',
                name: 'VM with empty CPU usage',
                cpuused: '',
                memorykbs: 100000,
                memory: 512,
            } as VmDetails;

            const result = service.mapVmInstanceToVmDetails(mockVmInstance);
            expect(result.cpuUsagePercentage).toBe(0);
        });

        it('should handle vm.memorykbs as null or undefined', () => {
            const mockVmInstance = {
                id: 'vm-205',
                name: 'VM without memory usage',
                cpuused: '10%',
                memorykbs: null,
                memory: 1024,
            } as VmDetails;

            let result = service.mapVmInstanceToVmDetails(mockVmInstance);
            expect(result.memoryUsagePercentage).toBe(0);
            expect(result.memoryUsagePercentageString).toBe('0%');

            const mockVmInstanceUndefinedMemory = {
                id: 'vm-206',
                name: 'VM with undefined memory usage',
                cpuused: '10%',
                // memorykbs is undefined
                memory: 1024,
            } as VmDetails;

            result = service.mapVmInstanceToVmDetails(mockVmInstanceUndefinedMemory);
            expect(result.memoryUsagePercentage).toBe(0);
            expect(result.memoryUsagePercentageString).toBe('0%');
        });

        it('should calculate memory usage correctly when memorykbs is 0', () => {
            const mockVmInstance = {
                id: 'vm-208',
                name: 'VM with 0 memory used',
                cpuused: '10%',
                memorykbs: 0,
                memory: 1024,
            } as VmDetails;

            const result = service.mapVmInstanceToVmDetails(mockVmInstance);
            expect(result.memoryUsagePercentage).toBe(0);
            expect(result.memoryUsagePercentageString).toBe('0%');
        });
    });
});
