﻿using AutoMapper;

namespace MyAdaptiveCloud.Api.AutoMapper.UserRole
{
    public class UserRoleMappingProfile : Profile
    {
        public UserRoleMappingProfile()
        {
            CreateMap<Requests.UserRoles.OrganizationMemberListRequest, Services.Requests.Users.UserListRequest>();
            CreateMap<Requests.UserRoles.CreateUserRoleRequest, Services.Requests.UserRoles.CreateUserRoleRequest>()
                .ForMember(model => model.CreatedDate, option => option.Ignore())
                .ForMember(model => model.OrganizationId, option => option.Ignore())
                .ForMember(model => model.CreatedBy, option => option.Ignore())
                .ForMember(model => model.IsApproved, option => option.Ignore());

        }
    }
}
