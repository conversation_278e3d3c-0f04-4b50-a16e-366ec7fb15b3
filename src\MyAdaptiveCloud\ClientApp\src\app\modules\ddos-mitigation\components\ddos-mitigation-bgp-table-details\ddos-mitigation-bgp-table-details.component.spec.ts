import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Bgp } from '../../models/bgp.model';
import { DdosMitigationBgpTableDetailsComponent } from './ddos-mitigation-bgp-table-details.component';

describe('DdosMitigationBgpTableDetailsComponent', () => {

    const bgp: Bgp = {
        neighborIp: '**************',
        neighborRemoteAs: 393775,
        prefix: '************/29',
        nextHop: '**************',
        asPath: [],
        communities: [],
        med: 0,
        localPref: 100,
        origin: 0,
        aggregator: null,
        atomicAggregate: false,
        extendedCommunities: [],
        largeCommunities: [],
        clusterList: [
            '**************',
            '**************'
        ],
        originatorId: '**************',
        simulate: false
    };

    let component: DdosMitigationBgpTableDetailsComponent;
    let fixture: ComponentFixture<DdosMitigationBgpTableDetailsComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DdosMitigationBgpTableDetailsComponent],
            providers: [
                provideMock(NgbActiveModal)
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(DdosMitigationBgpTableDetailsComponent);
        component = fixture.componentInstance;
    });

    it('should render modal title correctly', () => {
        const title = fixture.debugElement.query(By.css('.modal-title')).nativeElement;
        expect(title.textContent).toBe('Details');
    });

    it('should render neighbor IP correctly', () => {
        component.bgpDetails.set(bgp);
        fixture.detectChanges();
        const container = fixture.debugElement.query(By.css('.container'));
        expect(container.queryAll(By.css('.text-secondary')).length).toBe(15);
        expect(container.queryAll(By.css('.content')).length).toBe(15);
    });

    it('should render empty body if bgpDetails is null', () => {
        component.bgpDetails.set(null);
        fixture.detectChanges();

        const body = fixture.debugElement.query(By.css('.modal-body'));
        expect(body.nativeElement.textContent.trim()).toBe('');
        expect(body.queryAll(By.css('.details-container')).length).toBe(0);
    });

});
