import os
import sys
import getopt
import subprocess
import requests
import json
import socket
import string
import secrets
import docker

from urllib3.connection import HTTPConnection
from urllib3.connectionpool import HTTPConnectionPool
from requests.adapters import HTTPAdapter
from jinja2 import Environment, FileSystemLoader, select_autoescape

env = Environment(
    loader=FileSystemLoader('docker'),
    autoescape=select_autoescape()
)


class Config(dict):
    def __init__(self, *args, name='config.json', **kwargs):
        self.config = os.path.join(os.getcwd(), name)

        super(Config, self).__init__(*args, **kwargs)


    @staticmethod
    def union(dict1, dict2):
        for k, v in dict2.items():
            if k in dict1:
                if isinstance(v, dict):
                    Config.union(dict1[k], dict2[k])
                elif isinstance(v, list):
                    dict1[k] = list(set(dict1[k]).union(set(v)))
                else:
                    dict1[k] = v
            else:
                dict1[k] = v


    def load(self, cfgName=None):
        """load a JSON config file from disk"""
        try:
            with open(cfgName if cfgName else self.config, 'r') as cfgfile:
                cfgjson = json.load(cfgfile)
                #self.update(json.load(cfgfile))
                Config.union(self, cfgjson)
        except:
            pass

    def save(self):
        with open(self.config, 'w') as cfgfile:
            cfgfile.write(json.dumps(self))

    def getValue(self, category, setting):
        settingdict = self
        if category:
            if category in self:
                settingdict = self[category]
            else:
                return None
        return settingdict[setting] if setting in settingdict else None


scriptpath = os.path.realpath(os.path.dirname(__file__))
config = Config(name=f'{scriptpath}/deployproject.json')
config.load()
secretjson = f'{scriptpath}/../../secret.json'
os.path.exists(secretjson) and config.load(secretjson) 
dnsApikey = config.getValue('PowerDNS', 'apikey')
dnsBaseurl = config.getValue('PowerDNS', 'baseurl')
dnsZone = config.getValue('PowerDNS', 'zone')
dnsTimeout = config.getValue('PowerDNS', 'timeout')
ngunitHost = config.getValue('NginxUnit', 'host')
ngunitSock = config.getValue('NginxUnit', 'socket')

kcHost = config.getValue('KeyCloak', 'host')
kcRealm = config.getValue('KeyCloak', 'realm')
kcClientId = config.getValue('KeyCloak', 'client_id')
kcClientSecret = config.getValue('KeyCloak', 'client_secret')
kcApiClientId = config.getValue('KeyCloak', 'api_client_id')
kcApiClientSecret = config.getValue('KeyCloak', 'api_client_secret')
kcUser = config.getValue('KeyCloak', 'username')
kcSecret = config.getValue('KeyCloak', 'secret')


class NginxUnitConnection(HTTPConnection):
    def __init__(self):
        super().__init__("localhost")

    def connect(self):
        self.sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        self.sock.connect(ngunitSock)


class NginxUnitConnectionPool(HTTPConnectionPool):
    def __init__(self):
        super().__init__("localhost")

    def _new_conn(self):
        return NginxUnitConnection()


class NginxUnitAdapter(HTTPAdapter):
    def get_connection(self, url, proxies=None):
        return NginxUnitConnectionPool()


class PowerDns:
    def __init__(self, baseurl, apikey, zone, host, timeout=30):
        self.baseurl = baseurl
        self.headers = { 'X-Api-Key': apikey }
        self.server = 'localhost'
        self.zone = self.fixupQuery(zone)
        self.host = host
        self.timeout = timeout

    # Ensure we have a dot at the end of a query/zone
    def fixupQuery(self, zone):
        if zone[len(zone) - 1] != '.':
            zone += '.'
        return zone


    def buildJson(self, project, action = 'REPLACE'):
        if action not in [ 'REPLACE', 'DELETE' ]:
            print(f'Invalid action {action}, please use either REPLACE or DELETE')
            return None
        return {'rrsets': [{'name': f'{project}.{self.fixupQuery(self.host)}', 'type': 'CNAME', 'comments': [], 'records': [{'content': self.fixupQuery(self.host), 'disabled': False}], 'ttl': 86400, 'changetype': action, 'priority': 0}]}


    def fetchZones(self, output = True):
        response = requests.get(self.baseurl + '/servers/' + self.server + '/zones', headers=self.headers, timeout=self.timeout)
        if output:
            print(json.dumps(response.json(), indent=4))
        return response


    def fetchZone(self, output = True):
        response = requests.get(self.baseurl + '/servers/' + self.server + '/zones/' + self.zone, headers=self.headers, timeout=self.timeout)
        if output:
            print(json.dumps(response.json(), indent=4))
        return response


    def fetchEntry(self, name, output = False):
        response = self.fetchZone(False)
        name = self.fixupQuery(name)
        for entry in response.json()['rrsets']:
            print(entry)
            if entry['name'] == f'{name}' and entry['type'] == 'CNAME':
                return entry
        else:
            return None


    def getDnsEntry(self, project):
        entry = self.fetchEntry(f'{project}.{self.host}', True)
        if entry:
            print(json.dumps(entry, indent=4))
        else:
            print('no match found for project')
        return entry


    def addDnsEntry(self, project):
        entry = self.getDnsEntry(project)
        if not entry:
            print('Patching in dns entry')
            response = requests.patch(f'{self.baseurl}/servers/{self.server}/zones/{self.zone}', json=self.buildJson(project, 'REPLACE'), headers=self.headers, timeout=self.timeout)
            return response


    def removeDnsEntry(self, project):
        entry = self.getDnsEntry(project)
        if entry:
            print('Removing dns entry')
            response = requests.patch(f'{self.baseurl}/servers/{self.server}/zones/{self.zone}', json=self.buildJson(project, 'DELETE'), headers=self.headers, timeout=self.timeout)
            return response


class DockerCompose:
    def __init__(self, project):
        self.project = project
        labelfilter = 'com.docker.compose.project' + (f'={project}' if project else '')
        self.filters = {
            'label': labelfilter,
        }


    ProjectArgs = [ 'docker-compose', '--project-directory', '.', '--file', 'docker/compose.yaml', '-p' ]
    UpArgs = [ 'up', '-d' ]
    DownArgs = [ 'down', '--volumes' ] # --volumes flag will cause it to remove named and anonymous volumes when shutting down
    PortArgs = [ 'port', 'backend', '8000' ]
    PsArgs = [ 'ps' ]
    ImagesArgs = [ 'images' ]
    BackendImagesArgs = [ 'images', 'backend', '--quiet' ]
    LogsArgs = [ 'logs' ]
    ListArgs = [ 'ls' ]
    BuildArgs = [ 'build' ]


    def getArgs(self, args):
        return DockerCompose.ProjectArgs + [ self.project ] + args if self.project else [ 'docker-compose' ] + args


    def build(self):
        completedProcess = subprocess.run(args=self.getArgs(DockerCompose.BuildArgs))
        completedProcess.check_returncode()


    def start(self):
        running = self.ls()
        if len(running):
            print(f'Project {self.project} is already running, please use recreate')
        else:
            completedProcess = subprocess.run(args=self.getArgs(DockerCompose.UpArgs))
            completedProcess.check_returncode()


    def stop(self):
        completedProcess = subprocess.run(args=self.getArgs(DockerCompose.DownArgs))
        completedProcess.check_returncode()


    def recreate(self):
        running = self.ls()
        if len(running):
            print(f'Project {self.project} is already running')
            self.stop()
            self.build()
        self.start()


    def port(self):
        completedProcess = subprocess.run(args=self.getArgs(DockerCompose.PortArgs), capture_output=True, text=True)
        completedProcess.check_returncode()
        portOutput = completedProcess.stdout
        return portOutput.strip()

    def containers(self):
        completedProcess = subprocess.run(args=self.getArgs(DockerCompose.PsArgs), capture_output=True, text=True)
        return completedProcess.stdout


    def images(self):
        completedProcess = subprocess.run(args=self.getArgs(DockerCompose.ImagesArgs), capture_output=True, text=True)
        return completedProcess.stdout


    def logs(self):
        completedProcess = subprocess.run(args=self.getArgs(DockerCompose.LogsArgs), capture_output=True, text=True)
        return completedProcess.stdout


    def ls(self, *args, **kwargs):
        client = docker.from_env()
        return client.containers.list(all=True, filters=self.filters)
        #completedProcess = subprocess.run(args=self.getArgs(DockerCompose.ListArgs + list(args)), capture_output=True, text=True)
        #return completedProcess.stdout


    def projectls(self):
        containers = self.ls()
        project_dict = {}
        for ct in containers:
            project_name = ct.labels['com.docker.compose.project']
            if not project_name in project_dict:
                project_dict[project_name] = []
            project_dict[project_name].append(ct)

        projects = [ { 'name': k, 'status': len(project_dict[k]), 'url': ('https://' +  (f'{k}.' if k != 'master' else '') + 'labmy.ippathways.us') if k != 'billingdb' else '' } for k in sorted(project_dict) ]
        return projects


class NginxUnit:
    def __init__(self, project):
        self.setProject(project)
        self.session = requests.Session()
        self.session.mount('http://nginx-unit/', NginxUnitAdapter())


    def setProject(self, project):
        self.project = project
        self.vhost = f'{self.project}.{ngunitHost}' if self.project != 'master' and self.project != 'main' else ngunitHost


    def getConfig(self):
        response = self.session.get('http://nginx-unit/config')
        response.raise_for_status()
        return response


    def findVHost(self, vhost):
        response = self.session.get('http://nginx-unit/config/routes')
        response.raise_for_status()
        # Search through the existing routes to see if ours is in there
        idx = 0
        foundidx = None
        for route in response.json():
            if 'host' in route['match'] and route['match']['host'] == vhost:
                #print(idx, route['match']['host'], '->', route['action']['proxy'] if 'proxy' in route['action'] else '')
                foundidx = idx
                break
            idx += 1
        return route if foundidx is not None else None, foundidx


    def addVHost(self, port):
        # Use the project as a prefix on the host unless this is our master/main branch, then just use the host
        proxy = f'http://{port}'
        route, idx = self.findVHost(self.vhost)
        if idx is not None:
            if 'proxy' in route['action'] and route['action']['proxy'] == proxy:
                print('VHost already exists')
                return
            else:
                # Remove this mismatched route so we can add the correct one
                print('Removing invalid VHost prior to adding new')
                response = self.session.delete(f'http://nginx-unit/config/routes/{idx}')
                response.raise_for_status()

        vhostdata = {
            'match': {
                'host': self.vhost
            },
            'action': {
                'proxy': proxy
            }
        }
        response = self.session.post('http://nginx-unit/config/routes', json=vhostdata)
        response.raise_for_status()
        print(f'vHost added for {self.vhost} on port {port}')
        keycloak and keycloak.addRedirectUri(f'https://{self.vhost}/*')


    def rerouteVHost(self):
        _, idx = self.findVHost(self.vhost)
        if idx is not None:
            response = self.session.delete(f'http://nginx-unit/config/routes/{idx}')
            response.raise_for_status()
            print(response)
            print(f'vHost removed for {self.vhost}')

            vhostdata = {
                'match': {
                    'host': self.vhost
                },
                'action': {
                    'return': 503
                }
                #'action': {
                #    'share': '/home/<USER>/redeploying.html'
                #}
            }
            response = self.session.post('http://nginx-unit/config/routes', json=vhostdata)
            response.raise_for_status()
            print(f'vHost for {self.vhost} adjusted to return 503')


    def removeVHost(self):
        _, idx = self.findVHost(self.vhost)
        if idx is not None:
            response = self.session.delete(f'http://nginx-unit/config/routes/{idx}')
            response.raise_for_status()
            print(response)
            print(f'vHost removed for {self.vhost}')
        keycloak and keycloak.removeRedirectUri(f'https://{self.vhost}/*')


class BearerAuth(requests.auth.AuthBase):
    def __init__(self, token):
        self.token = token
    def __call__(self, r):
        r.headers["authorization"] = "Bearer " + self.token
        return r


class KeyCloak:
    def __init__(self, host, realm, client_id, client_secret, api_client_id, api_client_secret, username, secret):
        self.host = host
        self.realm = realm
        self.client_id = client_id
        self.client_secret = client_secret
        self.api_client_id = api_client_id
        self.api_client_secret = api_client_secret
        self.username = username
        self.secret = secret


    def fetchToken(self):
        formdata = {
            'client_id': self.api_client_id,
            'client_secret': self.api_client_secret,
            'scope': 'openid',
            'grant_type': 'password',
            'username': self.username,
            'password': self.secret,
        }
        response = requests.post(f'{self.host}/auth/realms/{self.realm}/protocol/openid-connect/token', data=formdata)
        response.raise_for_status()
        return response.json()


    def getClient(self, token=None):
        if not token:
            tokenResponse = self.fetchToken()
            token = tokenResponse['access_token']
        # Fetch the oidc client, not the api client!
        params = {
            'clientId': self.client_id,
            'search': True,
        }
        response = requests.get(f'{self.host}/auth/admin/realms/{self.realm}/clients', auth=BearerAuth(token), params=params)
        response.raise_for_status()
        clients = response.json()
        if len(clients) != 1:
            raise Exception(f'Improper result when searching for client {self.client_id}, found {len(clients)}')
        return clients[0]


    def addRedirectUri(self, redirectUri):
        tokenResponse = self.fetchToken()
        token = tokenResponse['access_token']
        client = self.getClient(token)
        client_uuid = client['id']
        redirectUris = client['redirectUris']
        # Adjust redirect Uris
        if redirectUri in redirectUris:
            print(f'Redirect Uri {redirectUri} already exists, no need to add it')
            return
        print(f'Adding Redirect Uri {redirectUri}')
        redirectUris.append(redirectUri)
        data = { 'redirectUris': redirectUris }
        response = requests.put(f'{self.host}/auth/admin/realms/{self.realm}/clients/{client_uuid}', auth=BearerAuth(token), json=data)
        response.raise_for_status()


    def removeRedirectUri(self, redirectUri):
        tokenResponse = self.fetchToken()
        token = tokenResponse['access_token']
        client = self.getClient(token)
        client_uuid = client['id']
        redirectUris = client['redirectUris']
        # Adjust redirect Uris
        if redirectUri not in redirectUris:
            print(f'Redirect Uri {redirectUri} doesn''t exist, no need to remove it')
            return
        print(f'Removing Redirect Uri {redirectUri}')
        redirectUris.remove(redirectUri)
        data = { 'redirectUris': redirectUris }
        response = requests.put(f'{self.host}/auth/admin/realms/{self.realm}/clients/{client_uuid}', auth=BearerAuth(token), json=data)
        response.raise_for_status()


def configureSecrets(project):
    path = 'docker'
    secretfiles = [
        'myacdb/liquibase.local.properties',
        'myacdb/password.txt',
        'agentdb/password.txt',
        'myaclogsdb/password.txt',
        'myaclogsdb/liquibase.local.properties',
        'compose.yaml',
        'worker-services/appsettings.secret.json',
    ]
    passwd = ''.join((secrets.choice(string.ascii_letters + string.digits) for i in range(10)))
    # Gracefully handle an acagent db password file if it exists in this path
    agentsecretsfile = os.path.expanduser('~/acagent/Docker/password.txt')
    agentdbpass = None
    if os.path.exists(agentsecretsfile):
        with open(agentsecretsfile, encoding='ascii') as agentfile:
            agentdbpass = agentfile.read()
    params = {
        'password': passwd,
        'project': project,
        'useLiveAgentDb': True,
        'agentdbpass': agentdbpass if agentdbpass else passwd,
    }
    for secretfile in secretfiles:
        template = env.get_template(secretfile + '.jinja')
        #print(template.render(params))
        template.stream(params).dump(f'{path}/{secretfile}')


def start(project):
    configureSecrets(project)
    
    dc = DockerCompose(project)
    dc.start()
    # Get the external port
    port = dc.port()
    print(f'Port is {port}')
    # Add nginx-unit config
    global nginxUnit
    nginxUnit and nginxUnit.addVHost(port)
    # Add DNS entry
    #pdns = PowerDns(dnsBaseurl, dnsApikey, dnsZone, ngunitHost, dnsTimeout)
    #pdns.addDnsEntry(project)


def stop(project):
    # Remove nginx-unit config
    global nginxUnit
    nginxUnit and nginxUnit.removeVHost()
    # Stop the docker containers
    dc = DockerCompose(project)
    dc.stop()
    # Remove DNS entry
    #pdns = PowerDns(dnsBaseurl, dnsApikey, dnsZone, ngunitHost, dnsTimeout)
    #pdns.removeDnsEntry(project)


def recreate(project):
    configureSecrets(project)

    global nginxUnit
    nginxUnit and nginxUnit.rerouteVHost()
    dc = DockerCompose(project)
    dc.recreate()
    port = dc.port()
    # Add nginx-unit config
    nginxUnit and nginxUnit.addVHost(port)
    # Add DNS entry
    #pdns = PowerDns(dnsBaseurl, dnsApikey, dnsZone, ngunitHost, dnsTimeout)
    #pdns.addDnsEntry(project)


def redirect(project):
    global nginxUnit
    nginxUnit and nginxUnit.rerouteVHost()


def status(project):
    #pdns = PowerDns(dnsBaseurl, dnsApikey, dnsZone, ngunitHost, dnsTimeout)
    #response = pdns.addDnsEntry(project)
    #print(pdns.getDnsEntry(project))
    #response = pdns.removeDnsEntry(project)
    #print(pdns.getDnsEntry(project))
    global nginxUnit
    if nginxUnit:
        vhostroute, vhostidx = nginxUnit.findVHost(f'{project}.{ngunitHost}')
        print('vHost:')
        if vhostidx is not None:
            print(f"{vhostroute['match']['host']} -> {vhostroute['action']['proxy'] if 'proxy' in vhostroute['action'] else ''}")
        else:
            print(f'No vHost for project {project}')
        print()
        #print(json.dumps(nginxUnit.getConfig().json(), indent=4))
        #nginxUnit.removeVHost()
        #print(json.dumps(nginxUnit.getConfig().json(), indent=4))
        #nginxUnit.addVHost('127.0.0.1:49232')
        #print(json.dumps(nginxUnit.getConfig().json(), indent=4))
    dc = DockerCompose(project)
    ls = dc.ls()
    print('Docker Compose Projects:')
    print(ls)
    containers = dc.containers()
    print('Docker Containers:')
    print(containers)
    images = dc.images()
    print('Docker Images:')
    print(images)


def vhost(project):
    global nginxUnit
    if nginxUnit:
        if project is not None:
            output, _ = nginxUnit.findVHost(nginxUnit.vhost)
        else:
            output = nginxUnit.getConfig().json()
        print(json.dumps(output, indent=4))
    else:
        print('No NginxUnit found, so vhosts cannot be queried')


def addvhost(project):
    global nginxUnit
    if nginxUnit:
        if project is not None:
            dc = DockerCompose(project)
            port = dc.port()
            nginxUnit and nginxUnit.addVHost(port)
        else:
            output = nginxUnit.getConfig().json()
    else:
        print('No NginxUnit found, so vhosts cannot be queried')


def logs(project):
    dc = DockerCompose(project)
    logs = dc.logs()
    print(logs)


def ls(project):
    dc = DockerCompose(project)
    ls = dc.ls()
    print(ls)


def dns(project):
    pdns = PowerDns(dnsBaseurl, dnsApikey, dnsZone, ngunitHost, dnsTimeout)
    #response = pdns.addDnsEntry('*')
    #print(response.text)
    pdns.fetchZone()


def projects():
    dc = DockerCompose(project=None)
    containers = dc.ls()
    project_dict = {}
    for ct in containers:
        project_name = ct.labels['com.docker.compose.project']
        if not project_name in project_dict:
            project_dict[project_name] = []
        project_dict[project_name].append(ct)
    return project_dict


def fixup():
    global nginxUnit
    dc = DockerCompose(project=None)
    containers = dc.ls()
    for ct in containers:
        # Look for containers with -backend suffixes, as these will be our MYAC backend containers that have an exposed port
        if '-backend' in ct.name:
            project_name = ct.labels['com.docker.compose.project']
            networkports = ct.attrs['NetworkSettings']['Ports']
            for cport in networkports:
                for portconfig in networkports[cport]:
                    hostip = portconfig['HostIp']
                    hostport = portconfig['HostPort']
                    print(f'Fixing port for {project_name} - port {hostport}')
                    if nginxUnit:
                        nginxUnit.setProject(project_name)
                        nginxUnit.addVHost(f'{hostip}:{hostport}')


nginxUnit = None
keycloak = None


def redirectUriForProject(project):
    return f'https://{project}.{ngunitHost}/*'


def main(argv):
    usage = 'Usage:\n\t' + argv[0] + '<Action (start, stop, status)> <ProjectName>\n'
    try:
        #opts, args = getopt.getopt(argv[1:], 'hs:z:t:r:c:', ['server=', 'zone=', 'type=', 'record=', 'comment='])
        opts, args = getopt.getopt(argv[1:], '')
    except getopt.GetoptError:
        print(usage)
        sys.exit(2)
    if len(args) < 1:
        print(usage)
        exit(0)

    action = args[0]
    # Lowercase all project/branch names
    project = args[1].lower() if len(args) > 1 else None
    # Transpose any dots to dashes in project/branch names
    project = project.replace('.', '-') if project else None
    print(f'Project is {project}, action is {action}')
    #print(opts, args);
    #for opt, arg in opts:
    #    print(opt, arg)

    # Set up env vars for UID and GID in case they're needed by something we run (like docker-compose)
    os.environ['UID'] = str(os.getuid())
    os.environ['GID'] = str(os.getgid())

    global nginxUnit, keycloak
    nginxUnit = NginxUnit(project) if ngunitHost and ngunitSock and os.path.exists(ngunitSock) else None
    print(f"NginxUnit is {'enabled' if nginxUnit else 'disabled'}")
    keycloak = KeyCloak(kcHost, kcRealm, kcClientId, kcClientSecret, kcApiClientId, kcApiClientSecret, kcUser, kcSecret) if kcHost and kcRealm and kcApiClientId and kcApiClientSecret and kcUser and kcSecret else None
    print(f"KeyCloak is {'enabled' if keycloak else 'disabled'}")

    if action == 'start':
        start(project)
    elif action == 'stop':
        stop(project)
    elif action == 'recreate':
        recreate(project)
    elif action == 'status':
        status(project)
    elif action == 'vhost':
        vhost(project)
    elif action == 'addvhost':
        addvhost(project)
    elif action == 'logs':
        logs(project)
    elif action == 'ls':
        ls(project)
    elif action == 'dns':
        dns(project)
    elif action == 'cfg':
        configureSecrets(project)
    elif action == 'kcls':
        if keycloak:
            client = keycloak.getClient()
            print(json.dumps(client['redirectUris'], indent=4))
    elif action == 'kcadd':
        keycloak and keycloak.addRedirectUri(redirectUriForProject(project))
    elif action == 'kcrm':
        keycloak and keycloak.removeRedirectUri(redirectUriForProject(project))
    elif action == 'redirect':
        redirect(project)
    elif action == 'fixup':
        fixup()
    elif action == 'projects':
        print(projects())
    else:
        print(usage)


if __name__ == "__main__":
    main(sys.argv)
