import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { map } from 'rxjs';

export const cloudInfrastructureCredentialsGuard: CanActivateFn = () => {
    const cloudInfrastructureSessionService = inject(CloudInfrastructureSessionService);
    const userContextService = inject(UserContextService);
    return cloudInfrastructureSessionService.login().pipe(map(() => !!userContextService.currentUser.cloudInfraUserContext));
};
