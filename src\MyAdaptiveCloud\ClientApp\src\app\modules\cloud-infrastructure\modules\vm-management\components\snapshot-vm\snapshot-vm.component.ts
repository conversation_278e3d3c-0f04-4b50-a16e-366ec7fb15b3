import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { VmSnapshotForm } from '../../forms/vm-snapshot.form';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-snapshot-vm',
    imports: [ReactiveFormsModule, BtnSubmitComponent],
    templateUrl: './snapshot-vm.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SnapshotVmComponent {

    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);

    readonly virtualMachineId = signal<string>(null);

    protected readonly isSubmitting = signal<boolean>(false);

    protected readonly form = this.formBuilder.group<VmSnapshotForm>({
        name: this.formBuilder.control<string | null>(null, Validators.maxLength(255)),
        description: this.formBuilder.control<string | null>(null, Validators.maxLength(255))
    });

    protected cancel() {
        this.activeModal.close();
    }

    protected snapshotVirtualMachine() {
        this.isSubmitting.set(true);
        if (this.form.valid) {

            this.vmManagementService.snapshotVirtualMachine(
                this.virtualMachineId(),
                this.form.controls.name.value,
                this.form.controls.description.value,
                true
            )
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
