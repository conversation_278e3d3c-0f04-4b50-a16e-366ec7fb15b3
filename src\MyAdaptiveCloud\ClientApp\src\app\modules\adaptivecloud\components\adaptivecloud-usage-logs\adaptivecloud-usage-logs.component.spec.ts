import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ModalService } from '@app/shared/services/modal.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { AdaptiveCloudUsageLogModel } from '../../models/adaptivecloud-usage-log.model';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AdaptiveCloudUsageLogsComponent } from './adaptivecloud-usage-logs.component';

describe('AdaptiveCloudUsageLogsComponent', () => {
    let component: AdaptiveCloudUsageLogsComponent;
    let fixture: ComponentFixture<AdaptiveCloudUsageLogsComponent>;
    let mockLogsService: jasmine.SpyObj<AdaptiveCloudUsageService>;
    let mockModalService: jasmine.SpyObj<ModalService>;

    const data: AdaptiveCloudUsageLogModel[] = [
        {
            id: 1,
            command: 'daily',
            startTime: '02/03/2022',
            endTime: '04/03/2022',
            month: '02/06/2022',
            errorCount: 1
        },

        {
            id: 2,
            command: 'daily',
            startTime: '02/03/2022',
            endTime: '04/03/2022',
            month: '02/06/2022',
            errorCount: 2
        },
        {
            id: 3,
            command: 'daily',
            startTime: '02/03/2022',
            endTime: '04/03/2022',
            month: '02/06/2022',
            errorCount: 3
        },
        {
            id: 4,
            command: 'daily',
            startTime: '02/03/2022',
            endTime: '04/03/2022',
            month: '02/06/2022',
            errorCount: 4
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(AdaptiveCloudUsageService),
                provideMock(ModalService),
                provideMock(PermissionService)
            ],
            imports: [
                AdaptiveCloudUsageLogsComponent
            ]
        }).compileComponents();

        mockLogsService = TestBed.inject(AdaptiveCloudUsageService) as jasmine.SpyObj<AdaptiveCloudUsageService>;
        mockLogsService.getAcUsageRunLogs.and.returnValue(of({ data, message: 'success', totalCount: data.length }));
        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;
        mockModalService.openDeleteConfirmationDialog.and.returnValue({ closed: of(true) } as NgbModalRef);
        fixture = TestBed.createComponent(AdaptiveCloudUsageLogsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('OnInit', () => {

        it('should call AdaptiveCloudUsageService', () => {
            expect(mockLogsService.getAcUsageRunLogs).toHaveBeenCalledOnceWith(component.pagination);
        });

        it('should have the right amount of data', () => {
            expect(component.table().count).toEqual(4);
            expect(component.table().rows).toEqual(data);
        });
    });

    describe('Component Interaction', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
            fixture.detectChanges();
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });

        it('should display the correct data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const thirdRow = rows[2];
            const rowWrapper = thirdRow.querySelectorAll<HTMLElement>('datatable-body-cell span');
            const thirdColumn = rowWrapper[3].title;
            expect(thirdColumn).toEqual(data[2].endTime);
        });
    });
});
