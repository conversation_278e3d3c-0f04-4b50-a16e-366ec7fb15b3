import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { map, Observable } from 'rxjs';
import { VolumeSnapshotForm } from '../../forms/volume-snapshot.form';
import { NameIdModel } from '../../models/name-id.model';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-snapshot-volume',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgSelect<PERSON>omponent, NgbPopover, AsyncPipe],
    templateUrl: './snapshot-volume.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SnapshotVolumeComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);

    readonly inputData = signal<{
        virtualMachineId: string;
        domainId: string;
        account: string;
    }>(null);

    protected readonly isSubmitting = signal<boolean>(false);
    protected form: FormGroup<VolumeSnapshotForm>;
    protected volumes$: Observable<NameIdModel[]>;

    ngOnInit(): void {

        this.volumes$ = this.vmManagementService.getVolumesByVirtualMachine(this.inputData().virtualMachineId, this.inputData().domainId, this.inputData().account)
            .pipe(map(res => res?.map(volume => ({ id: volume.id, name: volume.name }))));

        this.form = this.formBuilder.group<VolumeSnapshotForm>({
            snapshotName: this.formBuilder.control<string | null>(null, Validators.maxLength(255)),
            volumeId: this.formBuilder.control<string | null>(null, Validators.required),
            asyncBackup: this.formBuilder.control<boolean>(false, Validators.required)
        });
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected snapshotVolume() {
        this.isSubmitting.set(true);
        if (this.form.valid) {

            this.vmManagementService.snapshotVolume(
                this.form.controls.volumeId.value,
                this.form.controls.asyncBackup.value,
                this.form.controls.snapshotName.value,
                this.inputData().domainId,
                this.inputData().account
            )
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
