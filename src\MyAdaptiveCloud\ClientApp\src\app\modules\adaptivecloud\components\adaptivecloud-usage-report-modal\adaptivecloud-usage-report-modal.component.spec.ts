import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { ReportFormat } from '@app/shared/models/report-format.enum';
import { ReportType } from '@app/shared/models/report-type.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AdaptiveCloudUsageReportModalComponent } from './adaptivecloud-usage-report-modal.component';

describe('AdaptiveCloudUsageReportModalComponent', () => {
    let fixture: ComponentFixture<AdaptiveCloudUsageReportModalComponent>;
    let component: AdaptiveCloudUsageReportModalComponent;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockAdaptiveCloudUsageService: jasmine.SpyObj<AdaptiveCloudUsageService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AdaptiveCloudUsageReportModalComponent
            ],
            providers: [
                provideMock(UserContextService),
                provideMock(AdaptiveCloudUsageService),
                provideMock(NgbActiveModal),
                FormBuilder
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockAdaptiveCloudUsageService = TestBed.inject(AdaptiveCloudUsageService) as jasmine.SpyObj<AdaptiveCloudUsageService>;
        mockUserContextService.currentUser = { organizationId: 15 } as UserContext;

        fixture = TestBed.createComponent(AdaptiveCloudUsageReportModalComponent);
        component = fixture.componentInstance;
    });

    describe('generateReport', () => {

        it('should be not be called when form is invalid', () => {
            component.accounts = [{ id: 1, name: '1' }, { id: 2, name: '2' }];
            component.selectedAccounts = [];
            component.period = '202211';
            component.reportType = ReportType.Detail;
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();
            fixture.detectChanges();

            expect(mockAdaptiveCloudUsageService.getUsageReport).not.toHaveBeenCalled();
        });

        it('should be called when form is valid', () => {
            component.accounts = [{ id: 1, name: '1' }, { id: 2, name: '2' }];
            component.selectedAccounts = [{ id: 1, name: '1' }, { id: 2, name: '2' }];
            component.period = '202211';
            component.reportType = ReportType.Detail;
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
            submit.click();
            fixture.detectChanges();

            expect(mockAdaptiveCloudUsageService.getUsageReport).toHaveBeenCalledOnceWith(15, {
                accounts: ['1', '2'], period: '202211', reportFormat: ReportFormat.CSV, reportType: ReportType.Detail
            });
        });
    });
});
