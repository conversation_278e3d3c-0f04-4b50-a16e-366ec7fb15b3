import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { debounceTime, distinctUntilChanged, take } from 'rxjs';
import { WeeklyScheduleForm } from '@app/modules/device-management/models/schedule/weekly-schedule.form';
import { ScheduleStore, WeeklyScheduleState } from '@app/modules/device-management/components/schedule-form/schedule.component.store';
import { selectedWeekdaysValidator } from '@app/modules/device-management/validators/schedule-validator';
import { DayOfWeek } from '@app/shared/models/day-of-week.enum';

@Component({
    selector: 'app-weekly-schedule',
    imports: [ReactiveFormsModule],
    templateUrl: './weekly-schedule.component.html',
    styleUrl: './weekly-schedule.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class WeeklyScheduleComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    private readonly scheduleStore = inject(ScheduleStore);
    private readonly destroyRef = inject(DestroyRef);

    protected weekDays = [
        { name: DayOfWeek[DayOfWeek.Sunday], value: DayOfWeek.Sunday },
        { name: DayOfWeek[DayOfWeek.Monday], value: DayOfWeek.Monday },
        { name: DayOfWeek[DayOfWeek.Tuesday], value: DayOfWeek.Tuesday },
        { name: DayOfWeek[DayOfWeek.Wednesday], value: DayOfWeek.Wednesday },
        { name: DayOfWeek[DayOfWeek.Thursday], value: DayOfWeek.Thursday },
        { name: DayOfWeek[DayOfWeek.Friday], value: DayOfWeek.Friday },
        { name: DayOfWeek[DayOfWeek.Saturday], value: DayOfWeek.Saturday }
    ];

    protected form: FormGroup<WeeklyScheduleForm>;

    public enable() {
        this.form.enable();
    }

    public disable() {
        this.form.disable();
    }

    ngOnInit(): void {
        this.form = this.formBuilder.group<WeeklyScheduleForm>({
            freqRecurrenceFactor: this.formBuilder.control<number>(null, [Validators.min(1), Validators.max(999), Validators.pattern('[0-9]+'), Validators.required]),
            weeklyDays: this.formBuilder.array(this.weekDays.map(() => new FormControl(false)), selectedWeekdaysValidator)
        });
        this.subscribeToWeeklyFormState();
        this.scheduleStore.getScheduleTypeFormState$.pipe(take(1))
            .subscribe(scheduleTypeForm => {
                if (scheduleTypeForm.isReadOnly) {
                    this.form.disable();
                } else {
                    this.subscribeToWeeklyFormValueChanges();
                }
            });
    }

    private subscribeToWeeklyFormValueChanges() {
        this.form.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe(value => {
                const _parsedFormValue: WeeklyScheduleState = this.parseFormRawValueToStateData(value);
                this.scheduleStore.updateScheduleTypeWeeklyFormState(_parsedFormValue);
            });
    }

    private parseFormRawValueToStateData(value: Partial<{
        freqRecurrenceFactor: number | null,
        weeklyDays: boolean[]
    }>): WeeklyScheduleState {
        return {
            freqRecurrenceFactor: value.freqRecurrenceFactor,
            weeklyDays: value.weeklyDays
                .map((day, index) => (day ? this.weekDays[index].value : null))
                .filter(day => day !== null),
            isValid: this.form.valid
        };
    }

    private subscribeToWeeklyFormState() {
        this.scheduleStore.getScheduleTypeWeeklyFormState$
            .pipe(take(1))
            .subscribe((_scheduleTypeWeeklyForm: WeeklyScheduleState) => {
                this.setFormValueFromStateData(_scheduleTypeWeeklyForm);
            });
    }

    private setFormValueFromStateData(_scheduleTypeWeeklyForm: WeeklyScheduleState) {
        const weeklyDaysRawValue = this.form.controls.weeklyDays.value.map((_: boolean, index: number) => _scheduleTypeWeeklyForm.weeklyDays.includes(index + 1));
        this.form.controls.freqRecurrenceFactor.setValue(_scheduleTypeWeeklyForm.freqRecurrenceFactor, { emitEvent: false });
        this.form.controls.weeklyDays.setValue(weeklyDaysRawValue, { emitEvent: false });
    }
}
