<div class="modal-header">
    <h4 class="modal-title">User Profile</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body modal-lg">
    <div class="mb-6 row">
        <nav ngbNav #nav="ngbNav" class="nav-tabs">
            <ng-container [ngbNavItem]>
                <a ngbNavLink>Details</a>
                <ng-template [ngbNavContent]>
                    <ng-container *ngComponentOutlet="editProfileTab" />
                </ng-template>
            </ng-container>
            <ng-container [ngbNavItem]>
                <a ngbNavLink>Authenticators</a>
                <ng-template [ngbNavContent]>
                    <ng-container *ngComponentOutlet="twoFactorAuthenticationTab" />
                </ng-template>
            </ng-container>
            <ng-container [ngbNavItem]>
                <a ngbNavLink>Subscriptions</a>
                <ng-template [ngbNavContent]>
                    <ng-container *ngComponentOutlet="manageSubscriptionsComponent" />
                </ng-template>
            </ng-container>
            <ng-container [ngbNavItem]>
                <a ngbNavLink>API Clients</a>
                <ng-template [ngbNavContent]>
                    <ng-container *ngComponentOutlet="apiClientsComponent; inputs: apliClientInputs" />
                </ng-template>
            </ng-container>
        </nav>
        <div [ngbNavOutlet]="nav"></div>
    </div>
</div>