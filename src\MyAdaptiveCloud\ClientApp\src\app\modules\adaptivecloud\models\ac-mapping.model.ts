import { BillingStartDateStatus } from './ac-mapping-billing-start-date-status.model';
import { BillingStatus } from './ac-mapping-billing-status.model';

export class AcMapping {
    public id: number;
    public acId: string;
    public acName: string;
    public acType: string;
    public cwCompanyId?: number;
    public cwCompanyName: string;
    public cwCompanyIdentifier: string;
    public cwAgreementName: string;
    public cwAgreementId?: number;
    public enabled: boolean;
    public acEntityExists: boolean;
    public billingStartDate?: Date;
    public cwBillingStartDate?: Date;
    public nextInvoiceDate?: Date;
    public billingStatus: BillingStatus;
    public billingStartDateStatus: BillingStartDateStatus;
}
