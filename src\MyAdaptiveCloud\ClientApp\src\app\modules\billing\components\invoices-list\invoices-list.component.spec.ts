import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed, discardPeriodicTasks, fakeAsync, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { Invoice } from '../../models/invoice.model';
import { BillingService } from '../../services/billing.service';

import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { InvoicesListComponent } from './invoices-list.component';

describe('InvoicesListComponent', () => {
    let component: InvoicesListComponent;
    let fixture: ComponentFixture<InvoicesListComponent>;
    let mockBillingService: jasmine.SpyObj<BillingService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    const data: Invoice[] = [
        {
            invoiceId: 1,
            invoiceType: 'any',
            description: 'something',
            invoiceDate: new Date(),
            invoiceNumber: '5',
            dueDate: new Date(),
            invoiceAmount: 4,
            invoiceBalance: 34,
            canView: UserActionState.Allowed,
            canEdit: UserActionState.Allowed,
            canCreate: UserActionState.Allowed,
            canDelete: UserActionState.Allowed,
            organizationId: 4
        },
        {
            invoiceId: 2,
            invoiceType: 'any',
            description: 'something',
            invoiceDate: new Date(),
            invoiceNumber: '6',
            dueDate: new Date(),
            invoiceAmount: 6,
            invoiceBalance: 10.05,
            canView: UserActionState.Allowed,
            canEdit: UserActionState.Allowed,
            canCreate: UserActionState.Allowed,
            canDelete: UserActionState.Allowed,
            organizationId: 4
        },
        {
            invoiceId: 3,
            invoiceType: 'any',
            description: 'something',
            invoiceDate: new Date(),
            invoiceNumber: '8',
            dueDate: new Date(),
            invoiceAmount: 8,
            invoiceBalance: 10.5,
            canView: UserActionState.Allowed,
            canEdit: UserActionState.Allowed,
            canCreate: UserActionState.Allowed,
            canDelete: UserActionState.Allowed,
            organizationId: 4
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                InvoicesListComponent
            ],
            providers: [
                provideMock(BillingService),
                provideMock(UserContextService)
            ]
        });

        mockBillingService = TestBed.inject(BillingService) as jasmine.SpyObj<BillingService>;
        mockBillingService.getInvoicesByOrganization.and.returnValue(of({ data, message: 'success', totalCount: data.length }));
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1
        } as UserContext;
        fixture = TestBed.createComponent(InvoicesListComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(component).toBeTruthy();
        });
    });

    describe('OnInit', () => {

        it('should call notificationService', () => {
            expect(mockBillingService.getInvoicesByOrganization).toHaveBeenCalled();
        });

        it('should have the right amount of data', () => {
            expect(component.table().count).toEqual(data.length);
            expect(component.table().rows).toEqual(data);
        });
    });

    describe('Component Interaction', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
            fixture.detectChanges();
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });

        it('should call the delete function on click', () => {
            spyOn(component, 'downloadInvoice');
            const rows = dataTable.querySelectorAll<HTMLElement>('.fa-download');
            const button = rows[0];
            button.click();
            expect(component.downloadInvoice).toHaveBeenCalled();
        });

        it('should display the correct data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const thirdRow = rows[2];
            const rowWrapper = thirdRow.querySelectorAll<HTMLElement>('datatable-body-cell span');
            const firstColumn = rowWrapper[0].title;
            expect(firstColumn).toEqual(data[2].invoiceNumber);
        });

        it('should filter the Billing list', () => {
            spyOn(component, 'downloadInvoice');
            const rows = dataTable.querySelectorAll<HTMLElement>('.fa-download');
            const button = rows[0];
            button.click();
            expect(component.downloadInvoice).toHaveBeenCalled();
        });

        it('should filter by the text and then update the text adding a new incremental criteria', fakeAsync(() => {
            fixture.detectChanges();
            const filterCriteria = '5';
            const autoSearchBoxComponent = fixture.debugElement.query(By.directive(AutoSearchBoxComponent));
            const input = autoSearchBoxComponent.query(By.css('input')).nativeElement as HTMLInputElement;
            input.value = filterCriteria;
            input.dispatchEvent(new Event('input'));
            tick(501);
            fixture.detectChanges();

            let rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstWrapper = firstRow.querySelectorAll<HTMLElement>('datatable-body-cell span');

            expect(rows.length).toBe(3);
            expect(firstWrapper[0].innerText).toContain(filterCriteria);

            const newfilterCriteria = '8';
            input.value = newfilterCriteria;
            input.dispatchEvent(new Event('input'));
            tick(501);
            fixture.detectChanges();

            rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRowNewCriteria = rows[0];
            const firstWrapperNewCriteria = firstRowNewCriteria.querySelectorAll<HTMLElement>('datatable-body-cell span');

            expect(rows.length).toBe(1);
            expect(firstWrapperNewCriteria[0].innerText).toContain(newfilterCriteria);

            discardPeriodicTasks();
        }));
    });
});
