<form [formGroup]="form">
  @if (!useIntervalsForMetrics()) {
    <div class="d-flex align-items-center justify-content-between pt-2 gap-3">
      <div>
        <div class="input-group">
          <label for="warning" class="col-form-label me-2">Warning</label>
          <input class="form-control rounded" id="warning" formControlName="warning">
          <div class="input-group-append d-flex align-items-center p-1">
            <small>{{deviceAlertThresholdTypeName()}}</small>
          </div>
        </div>
      </div>

      <div>
        <div class="input-group">
          <label for="error" class="col-form-label me-2">Error</label>
          <input class="form-control rounded" id="error" formControlName="error">
          <div class="input-group-append d-flex align-items-center p-1">
            <small>{{deviceAlertThresholdTypeName()}}</small>
          </div>
        </div>
      </div>

      <div>
        <div class="input-group">
          <label for="critical" class="col-form-label me-2">Critical</label>
          <input class="form-control rounded" id="critical" formControlName="critical">
          <div class="input-group-append d-flex align-items-center p-1">
            <small>{{deviceAlertThresholdTypeName()}}</small>
          </div>
        </div>
      </div>
    </div>
  }

  @if (useIntervalsForMetrics()) {
    <div class="row">
      <div class="col-4">
        <label class="col-form-label d-inline">Warning</label>
        <select class="form-select  d-inline" formControlName="warning">
          <option value=null></option>
          @for (interval of intervals(); track interval) {
            <option [value]="interval.value">{{ interval.name }}</option>
          }
        </select>
      </div>
      <div class="col-4">
        <label class="col-form-label d-inline">Error</label>
        <select class="form-select d-inline" formControlName="error">
          <option value=null></option>
          @for (interval of intervals(); track interval) {
            <option [value]="interval.value">{{ interval.name }}</option>
          }
        </select>
      </div>
      <div class="col-4">
        <label class="col-form-label d-inline">Critical</label>
        <select class="form-select d-inline" formControlName="critical">
          <option value=null></option>
          @for (interval of intervals(); track interval) {
            <option [value]="interval.value">{{ interval.name }}</option>
          }
        </select>
      </div>
    </div>
  }

  @if (form.controls.warning.touched) {
    <app-device-thresholds-metrics-errors [propertyName]="'Warning'"
      [errors]="form.controls.warning?.errors" />
  }
  @if (form.controls.error.touched) {
    <app-device-thresholds-metrics-errors [propertyName]="'Error'"
      [errors]="form.controls.error?.errors" />
  }
  @if (form.controls.critical.touched) {
    <app-device-thresholds-metrics-errors [propertyName]="'Critical'"
      [errors]="form.controls.critical?.errors" />
  }

  @if (form.errors) {
    <div>
      @for (error of form.errors | keyvalue; track error) {
        <small class="text-danger">
          {{ error.value }}
        </small>
      }
    </div>
  }
</form>
