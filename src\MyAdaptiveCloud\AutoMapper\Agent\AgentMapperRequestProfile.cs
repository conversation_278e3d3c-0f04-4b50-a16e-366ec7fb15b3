using AutoMapper;
using MyAdaptiveCloud.Services.DTOs.Agent;
using MyAdaptiveCloud.Services.Requests.Agent;
using MyAdaptiveCloud.Services.Requests.AgentManagement.MsiManagement;
using MyAdaptiveCloud.Services.Requests.AgentManagement.ReleaseTags;

namespace MyAdaptiveCloud.Api.AutoMapper.Agent
{
    public class AgentMapperRequestProfile : Profile
    {
        public AgentMapperRequestProfile()
        {
            CreateMap<Requests.Agent.AgentInstalledListRequest, AgentInstalledListRequest>();
            CreateMap<Requests.Agent.AgentListRequest, AgentListRequest>();
            CreateMap<Requests.Agent.AlertedAgentListRequest, AlertedAgentListRequest>();
            CreateMap<Requests.Agent.EditAgentRequest, EditAgentRequest>();

            CreateMap<Requests.Agent.SoftwareInventoryRequest, SoftwareInventoryDetailsRequest>()
                .ForMember(dest => dest.SoftwareName, opt => opt.Ignore());
            CreateMap<Requests.Agent.SoftwareInventoryDetailsRequest, SoftwareInventoryDetailsRequest>();

            CreateMap<Requests.Agent.ExportToCSVOptions, ExportToCSVOptions>();
            CreateMap<Requests.Agent.SoftwareInventoryExportRequest, SoftwareInventoryExportRequest>();

            CreateMap<Requests.Agent.SoftwareInventoryApplicationExportRequest, SoftwareInventoryApplicationExportDTO>();
            CreateMap<Requests.Agent.SoftwareInventoryApplicationRequest, SoftwareInventoryApplicationRequest>();
            CreateMap<Requests.Agent.SoftwareInventoryUninstallRequest, SoftwareInventoryUninstallRequest>();

            CreateMap<Requests.Agent.BulkAgentRemoteCommandsRequest, BulkAgentRemoteCommandsRequest>();

            CreateMap<Requests.Agent.TerminalConnectionRequest, TerminalConnectionRequest>();

            CreateMap<Requests.Agent.ReleaseTagListRequest, ReleaseTagListRequest>();
            CreateMap<Requests.Agent.UpdateReleaseTagRequest, UpdateReleaseTagRequest>();

            CreateMap<Requests.Agent.UploadMsiRequest, UploadInstallerRequest>()
                .ForMember(dest => dest.ServiceType, opt => opt.Ignore())
                .ForMember(dest => dest.BuildVersion, opt => opt.MapFrom(src => src.Build));
        }
    }
}