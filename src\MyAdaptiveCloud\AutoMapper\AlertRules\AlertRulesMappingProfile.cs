﻿using AutoMapper;
using MyAdaptiveCloud.Api.Requests.AlertRules;
using MyAdaptiveCloud.Services.DTOs.AlertRules;

namespace MyAdaptiveCloud.Api.AutoMapper.AlertRules
{
    public class AlertRulesMappingProfile : Profile
    {
        public AlertRulesMappingProfile()
        {
            CreateMap<AlertRulesMetricRequest, DeviceAlertRuleCriteriaDTO>()
                .ForMember(dest => dest.DeviceAlertRuleCriteriaId, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertRuleId, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertTypeId, opt => opt.MapFrom(src => src.MetricId))
                .ForMember(dest => dest.DeviceAlertRuleComparerId, opt => opt.MapFrom(src => src.Comparer))
                .ForMember(dest => dest.Enabled, opt => opt.Ignore())
                .ForMember(dest => dest.DisplayOrder, opt => opt.Ignore())
                .ForMember(dest => dest.Description, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceAlertThresholdLevelId, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore());

            CreateMap<DeviceAlertRuleCriteriaDTO, AlertRulesMetricRequest>()
                .ForMember(dest => dest.AlertRuleMetricId, opt => opt.Ignore())
                .ForMember(dest => dest.MetricId, opt => opt.MapFrom(src => src.DeviceAlertTypeId))
                .ForMember(dest => dest.Comparer, opt => opt.MapFrom(src => src.DeviceAlertRuleComparerId))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.DeviceAlertThresholdLevelId));

            CreateMap<AlertRulesDeviceRequest, AlertRulesDeviceDTO>()
                .ForMember(dest => dest.Path, opt => opt.Ignore())
                .ForMember(dest => dest.Name, opt => opt.Ignore())
                .ForMember(dest => dest.FolderId, opt => opt.Ignore());

            CreateMap<CreateEditAlertRulesRequest, AlertRulesDTO>()
                .ForMember(dest => dest.AlertRuleId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByName, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedByName, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.MatchedDevices, opt => opt.Ignore())
                .ForMember(dest => dest.ActionName, opt => opt.Ignore())
                .ForMember(dest => dest.Order, opt => opt.Ignore())
                .ForMember(dest => dest.AlertRulesCriteria, opt => opt.MapFrom(src => src.Metrics))
                .ForMember(dest => dest.Devices, opt => opt.MapFrom(src => src.Devices));
        }
    }
}