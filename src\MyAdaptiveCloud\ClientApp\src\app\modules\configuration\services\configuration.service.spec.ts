import { TestBed } from '@angular/core/testing';
import { ApiService } from '@app/shared/services/api.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { EditConfigurationRequest } from '../requests/edit-configuration.request';
import { ConfigurationService } from './configuration.service';

describe('ConfigurationService', () => {
    let service: ConfigurationService;
    let mockApiService: jasmine.SpyObj<ApiService>;
    const endpoint = 'configuration';

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ApiService),
                ConfigurationService
            ]
        });
        mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
        service = TestBed.inject(ConfigurationService);
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(service).toBeTruthy();
        });
    });

    describe('API', () => {
        it('should use the right endpoint for editConfigurationValue', () => {
            const request = {
                values: []
            } as EditConfigurationRequest;
            service.editConfigurationValue(request);
            expect(mockApiService.post).toHaveBeenCalledWith(`${endpoint}`, request);
        });

        it('should use the right endpoint for getList', () => {
            service.getList();
            expect(mockApiService.get).toHaveBeenCalledWith(`${endpoint}/list`);
        });
    });
});
