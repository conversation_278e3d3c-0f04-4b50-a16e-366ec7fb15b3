import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { CloudInfraAccountViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-domain.view-model';
import { CloudInfraAccountService } from '@app/modules/cloud-infrastructure/services/cloud-infra-account.service';
import { CloudInfraDomainService } from '@app/modules/cloud-infrastructure/services/cloud-infra-domain.service';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { ListNetworksComponent } from '../list-networks/list-networks.component';
import { ListRemoteAccessVpnUsersComponent } from '../list-remote-access-vpn-users/list-remote-access-vpn-users.component';
import { ListRemoteVpnGatewaysComponent } from '../list-remote-vpn-gateways/list-remote-vpn-gateways.component';
import { ListVirtualPrivateCloudsComponent } from '../list-virtual-private-clouds/list-virtual-private-clouds.component';
import { NetworkingManagementComponent } from './networking-management.component';
import { BreadcrumbNavService } from '@app/shared/services/breadcrumb-nav.service';
import { UserContext } from '@app/shared/models/user-context.model';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { CloudInfraZoneService } from '@app/modules/cloud-infrastructure/services/cloud-infra-zone.service';
import { By } from '@angular/platform-browser';

describe('NetworkingManagementComponent', () => {
    let fixture: ComponentFixture<NetworkingManagementComponent>;
    let mockCloudInfraAccountService: jasmine.SpyObj<CloudInfraAccountService>;
    let mockCloudInfraDomainService: jasmine.SpyObj<CloudInfraDomainService>;
    let mockNetworkingPermissionService: jasmine.SpyObj<NetworkingPermissionService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    const account1: CloudInfraAccountViewModel = {
        id: '1',
        name: 'Account 1',
        domainId: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
    };

    const domain1: CloudInfraDomainViewModel = {
        accounts: [account1],
        hasChild: true,
        id: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
        isExpanded: false,
        level: 0,
        name: 'ROOT',
        subDomains: []
    };

    beforeEach(async () => {
        TestBed.configureTestingModule({
            imports: [
                NetworkingManagementComponent,
                ListNetworksComponent,
                ListVirtualPrivateCloudsComponent,
                ListRemoteAccessVpnUsersComponent,
                ListRemoteVpnGatewaysComponent
            ],
            providers: [
                ZoneDomainAccountStore,
                provideMock(ActivatedRoute),
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraAccountService),
                provideMock(CloudInfraDomainService),
                provideMock(UserContextService),
                provideMock(NetworkingPermissionService),
                provideMock(BreadcrumbNavService),
                provideMock(UserContextService),
                provideMock(CloudInfraZoneService)
            ]
        });

        mockCloudInfraAccountService = TestBed.inject(CloudInfraAccountService) as jasmine.SpyObj<CloudInfraAccountService>;
        mockCloudInfraDomainService = TestBed.inject(CloudInfraDomainService) as jasmine.SpyObj<CloudInfraDomainService>;
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockNetworkingPermissionService = TestBed.inject(NetworkingPermissionService) as jasmine.SpyObj<NetworkingPermissionService>;
        mockNetworkingPermissionService.canViewNetworkList.and.returnValue(true);
        mockNetworkingPermissionService.canViewVpnUserList.and.returnValue(true);
        mockNetworkingPermissionService.canViewVirtualPrivateCloudList.and.returnValue(true);
        mockNetworkingPermissionService.canViewRemoteVpnGatewaysList.and.returnValue(true);

        mockCloudInfraAccountService.getAccountById.and.returnValue(of([account1]));
        mockCloudInfraDomainService.getDomainList.and.returnValue(of([domain1]));
        mockUserContextService.currentUser = {
            userId: 1,
            organizationId: 50
        } as UserContext;
        fixture = TestBed.createComponent(NetworkingManagementComponent);
    });

    it('should create the NetworkingManagementComponent', () => {
        expect(fixture.componentInstance).toBeTruthy();
    });

    it('should render the component template', () => {
        fixture.detectChanges();
        const compiled = fixture.nativeElement as HTMLElement;
        expect(compiled).toBeTruthy();
    });

    describe('Initialization', () => {

        it('should select Networks view by default', () => {
            fixture.detectChanges();
            expect(fixture.debugElement.query(By.directive(ListNetworksComponent))).toBeDefined();
            expect(fixture.debugElement.query(By.directive(ListVirtualPrivateCloudsComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListRemoteAccessVpnUsersComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListRemoteVpnGatewaysComponent))).toBeNull();
        });

        it('should select Virtual Private Clouds view', () => {
            fixture.detectChanges();

            const vpcTab = fixture.debugElement.query(By.css('[data-testid="vpc-tab"]')).nativeElement as HTMLAnchorElement;

            vpcTab.click();
            vpcTab.dispatchEvent(new Event('click'));

            fixture.detectChanges();

            expect(fixture.debugElement.query(By.directive(ListVirtualPrivateCloudsComponent))).toBeDefined();
            expect(fixture.debugElement.query(By.directive(ListNetworksComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListRemoteVpnGatewaysComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListRemoteAccessVpnUsersComponent))).toBeNull();
        });

        it('should select Remote Access VPN Users view', () => {
            fixture.detectChanges();

            const remoteAccessVpnTab = fixture.debugElement.query(By.css('[data-testid="remote-access-vpn-tab"]')).nativeElement as HTMLAnchorElement;

            remoteAccessVpnTab.click();
            remoteAccessVpnTab.dispatchEvent(new Event('click'));

            fixture.detectChanges();

            expect(fixture.debugElement.query(By.directive(ListRemoteAccessVpnUsersComponent))).toBeDefined();
            expect(fixture.debugElement.query(By.directive(ListRemoteVpnGatewaysComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListVirtualPrivateCloudsComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListNetworksComponent))).toBeNull();
        });

        it('should select Remote VPN Gateways view', () => {
            fixture.detectChanges();

            const remoteVpnGatewaysTab = fixture.debugElement.query(By.css('[data-testid="remote-vpn-gateways-tab"]')).nativeElement as HTMLAnchorElement;

            remoteVpnGatewaysTab.click();
            remoteVpnGatewaysTab.dispatchEvent(new Event('click'));

            fixture.detectChanges();

            expect(fixture.debugElement.query(By.directive(ListRemoteVpnGatewaysComponent))).toBeDefined();
            expect(fixture.debugElement.query(By.directive(ListRemoteAccessVpnUsersComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListVirtualPrivateCloudsComponent))).toBeNull();
            expect(fixture.debugElement.query(By.directive(ListNetworksComponent))).toBeNull();
        });

    });

    describe('Permissions', () => {

        it('should select Remote VPN Gateways view', () => {

            mockNetworkingPermissionService.canViewNetworkList.and.returnValue(false);
            mockNetworkingPermissionService.canViewVpnUserList.and.returnValue(false);
            mockNetworkingPermissionService.canViewVirtualPrivateCloudList.and.returnValue(false);
            mockNetworkingPermissionService.canViewRemoteVpnGatewaysList.and.returnValue(false);

            fixture.detectChanges();

            const remoteVpnGatewaysTab = fixture.debugElement.query(By.css('[data-testid="remote-vpn-gateways-tab"]'));
            const remoteAccessVpnTab = fixture.debugElement.query(By.css('[data-testid="remote-access-vpn-tab"]'));
            const vpcTab = fixture.debugElement.query(By.css('[data-testid="vpc-tab"]'));
            const networkingTab = fixture.debugElement.query(By.css('[data-testid="networking-tab"]'));

            expect(remoteVpnGatewaysTab).toBeNull();
            expect(remoteAccessVpnTab).toBeNull();
            expect(vpcTab).toBeNull();
            expect(networkingTab).toBeNull();
        });

    });

});
