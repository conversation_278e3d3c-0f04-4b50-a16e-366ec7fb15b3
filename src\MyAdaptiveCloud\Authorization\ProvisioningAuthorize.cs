﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class ProvisioningAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly bool _includeInactiveOrganizations;

        public ProvisioningAuthorizeFilter(
            IUserContextService userContextService,
            IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name, bool includeInactiveOrganizations = false) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
            _includeInactiveOrganizations = includeInactiveOrganizations;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
                _ = int.TryParse(val, out int organizationId);

                var organizationExists = await _entityAuthorizationService.OrganizationExists(organizationId, _includeInactiveOrganizations);
                if (organizationExists)
                {
                    if (_perms != null && _perms.Count() > 0)
                    {
                        if (!_userContextService.HasPermission(userId, organizationId, _distance, _perms))
                        {
                            context.Result = new ForbidResult();
                        }
                        else
                        {
                            AuthorizeFilterHelpers.SetOrganizationId(context, organizationId);
                        }
                    }
                    else
                    {
                        context.Result = new ForbidResult();
                    }
                }
                else
                {
                    context.Result = new BadRequestResult();
                }
            }

            await Task.CompletedTask;
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = true)]
    public class ProvisioningAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public ProvisioningAuthorizeAttribute(params Perms[] perms) : base(typeof(ProvisioningAuthorizeFilter), perms)
        {
            Name = "organizationId";
            Arguments = new object[] { _perms, _name, _distance };
        }
    }
}