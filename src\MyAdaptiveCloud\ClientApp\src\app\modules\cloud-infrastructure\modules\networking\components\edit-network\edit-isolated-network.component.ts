import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NetworkOfferingViewModel } from '@app/modules/cloud-infrastructure/models/network-offering.view-model';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { EditIsolatedNetworkForm } from '../../forms/edit-isolated-network.form';
import { NetworkListViewModel } from '../../models/network-list.view-model';
import { NetworkingService } from '../../services/networking.service';
import { validateCidr } from '@app/modules/cloud-infrastructure/validators/network-validators';

@Component({
    selector: 'app-edit-isolated-network',
    imports: [BtnSubmitComponent, ReactiveFormsModule, NgSelectComponent],
    templateUrl: './edit-isolated-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class EditIsolatedNetworkComponent implements OnInit {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly networkingService = inject(NetworkingService);
    private readonly createNetworkService = inject(CreateNetworkService);
    private readonly formBuilder = inject(FormBuilder);

    protected form: FormGroup<EditIsolatedNetworkForm>;
    readonly network = signal<NetworkListViewModel>(null);

    protected readonly networkOfferings = signal<NetworkOfferingViewModel[]>([]);

    ngOnInit(): void {
        this.createNetworkService.getIsolatedNetworkOfferings(this.network().zoneId, this.network().domainId).subscribe(offerings => {
            this.networkOfferings.update(() => [...offerings]);
        });

        this.form = this.formBuilder.group<EditIsolatedNetworkForm>({
            cidr: this.formBuilder.control<string>(this.network().cidr, validateCidr()),
            description: this.formBuilder.control<string | null>(this.network().description, [Validators.required, Validators.maxLength(255)]),
            name: this.formBuilder.control<string>(this.network().name, [Validators.required, Validators.maxLength(255)]),
            networkDomain: this.formBuilder.control(this.network().networkDomain, Validators.maxLength(100)),
            networkOffering: this.formBuilder.control<string>(this.network().networkOfferingId, Validators.required)
        });
    }

    protected submit() {
        if (this.form.valid) {
            this.networkingService.editIsolatedNetwork(
                this.network().id,
                this.form.value.name,
                this.form.value.description,
                this.form.value.cidr,
                this.form.value.networkOffering,
                this.form.value.networkDomain
            ).subscribe(jobId => {
                this.activeModal.close(jobId);
            });
        }
    }

}
