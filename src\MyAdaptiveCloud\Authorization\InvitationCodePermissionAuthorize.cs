using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class InvitationCodePermissionAuthorize : BaseAsyncAuthorizationFilter
    {
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;

        public InvitationCodePermissionAuthorize(
            IIdentityService identityService,
            IUserContextService userContextService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name, bool includeInactiveOrganizations = false) : base(perms, distance, name)
        {
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int invitationCodeId);

            var organizationIdInvitation = await _entityAuthorizationService.GetOrganizationIdInvitationCode(invitationCodeId);
            if (organizationIdInvitation.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, organizationIdInvitation.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }
    public class InvitationCodePermissionAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public InvitationCodePermissionAuthorizeAttribute(params Perms[] perms) : base(typeof(InvitationCodePermissionAuthorize), perms)
        {
            Name = "invitationCodeId";
        }
    }
}