export interface HostModel {
    capabilities: string;
    clusterid: string;
    clustername: string;
    clustertype: string;
    cpuallocated: string;
    cpuallocatedpercentage: string;
    cpuallocatedvalue: number;
    cpuallocatedwithoverprovisioning: string;
    cpuloadaverage: number;
    cpunumber: number;
    cpusockets: number;
    cpuspeed: number;
    cpuused: string;
    cpuwithoverprovisioning: string;
    created: Date
    details: {
        'Host.OS': string;
        'Host.OS.Kernel.Version': string;
        'Host.OS.Version': string;
        'com.cloud.network.Networks.RouterPrivateIpStrategy': string;
        secured: boolean;
    },
    disconnected: Date
    events: string;
    hahost: boolean;
    hostha:
    {
        haenable: boolean;
        hastate: string;
        haprovider: string;
    }
    hypervisor: string;
    id: string;
    ipaddress: string;
    islocalstorageactive: boolean;
    lastpinged: string;
    managementserverid: string;
    memoryallocated: number;
    memoryallocatedbytes: number;
    memoryallocatedpercentage: string;
    memorytotal: number;
    memoryused: number;
    memorywithoverprovisioning: string;
    name: string;
    networkkbsread: number;
    networkkbswrite: number;
    outofbandmanagement:
    {
        address: string;
        driver: string;
        powerstate: string;
        enabled: boolean;
        port: string;
        username: string;
        password: string;
    }
    podid: string;
    podname: string;
    resourcestate: string;
    state: string;
    type: string;
    ueficapability: boolean;
    version: string;
    zoneid: string;
    zonename: string;
}
