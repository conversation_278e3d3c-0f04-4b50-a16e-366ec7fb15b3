import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'communities'
})
export class CommunitiesPipe implements PipeTransform {
    transform(value: string[] | (string[] | number[])[] | null | undefined): string {
        if (!Array.isArray(value) || value.length === 0) {
            return '-';
        }

        // Check if it's a flat array of strings
        if (typeof value[0] === 'string') {
            return (value as string[]).join(', ');
        }

        // Handle array of [x, y] pairs (tuples)
        return (value as (string[] | number[])[])
            .map(item => {
                if (Array.isArray(item) && item.length === 2) {
                    const [a, b] = item;
                    return `${a}:${b}`;
                }
                return '';
            })
            .filter(Boolean)
            .join(', ') || '-';
    }

}
