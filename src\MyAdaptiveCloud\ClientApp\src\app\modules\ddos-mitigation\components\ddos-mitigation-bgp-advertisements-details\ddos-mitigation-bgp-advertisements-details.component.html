<div class="modal-header">
    <h4 class="modal-title">Details</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body message-list-body">
    @if (bgpDetails()) {
        <div class="details container mt-2">
            <div class="ip-row d-flex flex-column border-bottom pb-2 mb-2">
                <div class="d-flex flex-row">
                    <span class="ip-row-label text-secondary fw-bold me-2">Neighbor IP:</span>
                    <span class="ip-row-value">{{ bgpDetails().neighborIp }}</span>
                </div>
                <div class="d-flex flex-row">
                    <span class="prefix-row-label text-secondary fw-bold me-2">Prefix:</span>
                    <span class="prefix-row-value">{{ bgpDetails().prefix ?? '-' }} </span>
                    @if (bgpDetails().simulate) {
                    <div>
                        <span
                            class="ms-1 ps-1 pe-1 border border-primary bg-primary text-white rounded-circle simulated-badge"
                            [ngbPopover]="'Simulated (not advertised)'" triggers="hover" container="body">S</span>
                    </div>
                    }
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Remote AS</span>
                    <span>{{ bgpDetails().neighborRemoteAs ?? '-' }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Aggregator</span>
                    <span>{{ bgpDetails().aggregator ?? '-'}}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Next Hop</span>
                    <span>{{ bgpDetails().nextHop ?? '-'}}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Atomic Aggregate</span>
                    <span>{{ bgpDetails().atomicAggregate ?? '-' }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">AS Path</span>
                    <span>{{ bgpDetails().asPath?.length ? bgpDetails().asPath.join(', ') :'-' }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Extended Community</span>
                    <span>{{ bgpDetails().extendedCommunities | communities }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Communities</span>
                    <span>{{ bgpDetails().communities | communities }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Large Community</span>
                    <span>{{ bgpDetails().largeCommunities | communities }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Med</span>
                    <span>{{ bgpDetails().med ?? '-' }}</span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Cluster List</span>
                    <span>{{ bgpDetails().clusterList ?? '-' }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Local Pref</span>
                    <span>{{ bgpDetails().localPref ?? '-'}}
                    </span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Originator ID</span>
                    <span>{{ bgpDetails().originatorId ?? '-' }}</span>
                </div>
            </div>
            <div class="d-flex mb-2">
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Origin</span>
                    <span>{{ bgpDetails().origin }}
                    </span>
                </div>
                <div class="col d-flex flex-column">
                    <span class="text-secondary fw-bold">Simulate</span>
                    <span>{{ bgpDetails().simulate ? 'True' : 'False' }}</span>
                </div>
            </div>
        </div>
    }
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-primary" (click)="activeModal.dismiss()">Close</button>
</div>
