import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { JobQueueEvent, JobQueueEventDescription } from '@app/shared/models/job-queue/job-queue-event.enum';
import { DDoSMitigationQueueService } from '@app/shared/services/ddos-mitigation-job-queue.service';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgbDropdown, NgbDropdownItem, NgbDropdownMenu, NgbDropdownToggle } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, map, Observable, of, switchMap, take } from 'rxjs';
import { TaskStatusEnum } from '../../../../shared/models/ddos-mitigation/task-status.enum';
import { MitigationTypeEnum } from '../../models/mitigation-type.enum';
import { MitigationViewModel } from '../../models/mitigation-view-model';
import { StatusEnum } from '../../models/status.enum';
import { TaskStatusMessageEnum } from '../../models/task-status-message.enum';
import { DDoSMitigationBlackholeService } from '../../services/ddos-mitigation-blackhole-service';
import { DDoSMitigationMitigationService } from '../../services/ddos-mitigation-mitigation.service';
import { DDoSMitigationScrubService } from '../../services/ddos-mitigation-scrub.service';
import { DdosMitigationMessagesComponent } from '../ddos-mitigation-messages/ddos-mitigation-messages.component';
import { StartMitigationComponent } from './start-mitigation/start-mitigation.component';

@Component({
    selector: 'app-ddos-mitigation',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, TableActionComponent, DatePipe, NgbDropdownToggle, NgbDropdownItem, NgbDropdown, NgbDropdownMenu],
    templateUrl: './ddos-mitigation.component.html',
    styleUrl: './ddos-mitigation.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})

export class DdosMitigationComponent extends BaseListClientComponent<MitigationViewModel> implements OnInit {
    private readonly ddoSMitigationMitigationService = inject(DDoSMitigationMitigationService);
    private readonly ddoSMitigationScrubService = inject(DDoSMitigationScrubService);
    private readonly ddoSMitigationBlackholeService = inject(DDoSMitigationBlackholeService);
    private readonly modalService = inject(ModalService);
    private readonly notificationService = inject(NotificationService);
    private readonly ddosServiceQueueService = inject(DDoSMitigationQueueService);

    readonly headerTemplate = viewChild.required<TemplateRef<never>>('headerTemplate');
    readonly statusTemplate = viewChild.required<TemplateRef<never>>('statusTemplate');
    readonly dateCellTemplate = viewChild.required<TemplateRef<never>>('dateCellTemplate');
    readonly messageTemplate = viewChild.required<TemplateRef<never>>('messageTemplate');
    readonly actionsTemplate = viewChild.required<TemplateRef<never>>('actionsTemplate');

    protected readonly TaskStatusEnum = TaskStatusEnum;
    protected readonly StatusEnum = StatusEnum;
    protected readonly mitigationTypeEnum = MitigationTypeEnum;

    private readonly mitigationList = signal<MitigationViewModel[]>(null);
    private readonly columns = signal<TableColumn[]>(null);

    constructor() {
        super();
        this.ddosServiceQueueService.taskUpdates$
            .pipe(
                filter(() => (this.mitigationList()?.length ?? 0) > 0),
                filter(taskUpdates => taskUpdates.some(update => this.mitigationList()?.some(row => row.taskId === update.id))),
                switchMap(() => this.ddoSMitigationMitigationService.getList(false)),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(res => {
                this.mitigationList.set(res.data);
                super.initialize(() => this.getMitigationList$(), this.columns());
            });
    }

    ngOnInit(): void {

        this.columns.set([
            {
                name: 'Status',
                prop: 'status',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.statusTemplate(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 80
            },
            {
                name: 'CIDR',
                prop: 'cidr',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
            },
            {
                name: 'Type',
                prop: 'mitigationType',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: false,
                width: 100
            },
            {
                name: 'Start Time',
                prop: 'startTime',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.dateCellTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
            },
            {
                name: 'Stop Time',
                prop: 'stopTime',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.dateCellTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
            },
            {
                name: 'Last Message',
                cellTemplate: this.messageTemplate(),
                sortable: false,
                resizeable: true,
                canAutoResize: true,
            },
            {
                name: 'Actions',
                prop: 'actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 100
            },
        ]);

        this.ddoSMitigationMitigationService.getList(true)
            .pipe(take(1))
            .subscribe(res => {
                this.mitigationList.set(res.data);
                super.initialize(() => this.getMitigationList$(), this.columns());
                this.table().sorts = [{ prop: 'startTime', dir: 'desc' }];
            });
    }

    private getMitigationList$(): Observable<ApiDataResult<MitigationViewModel[]>> {
        const filteredList = [...this.mitigationList()];
        return of({ data: filteredList, message: '' });
    }

    protected stop(item: MitigationViewModel): void {
        const type = item.mitigationType;
        const title = `Stop ${type} Mitigation`;
        const content = `Are you sure you want to stop this ${type} Mitigation?`;
        const prefix = item.cidr;
        const modalRef = this.modalService.openConfirmationDialog({ content, title, showCancelButton: true });
        modalRef.closed.pipe(take(1), filter(res => !!res)).subscribe(() => {
            if (type === MitigationTypeEnum.Scrub) {
                this.ddoSMitigationScrubService.stop(prefix).subscribe(res => {
                    this.setLoadingStatusToRows(item.cidr, res.data.taskId, TaskStatusMessageEnum.Stopping);
                    this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.StopScrubMitigation]);
                    this.ddosServiceQueueService.addToQueue(res.data.taskId, item.cidr, JobQueueEventDescription[JobQueueEvent.StopScrubMitigation]);
                });
            } else if (type === MitigationTypeEnum.Blackhole) {
                this.ddoSMitigationBlackholeService.stop(prefix).subscribe(res => {
                    this.setLoadingStatusToRows(item.cidr, res.data.taskId, TaskStatusMessageEnum.Stopping);
                    this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.StopBlackholeMitigation]);
                    this.ddosServiceQueueService.addToQueue(res.data.taskId, item.cidr, JobQueueEventDescription[JobQueueEvent.StopBlackholeMitigation]);
                });
            }
        });
    }

    protected restart(item: MitigationViewModel): void {
        const type = item.mitigationType;
        const title = `Start ${type} Mitigation`;
        const content = `Are you sure you want to start this ${type} Mitigation?`;
        const prefix = item.cidr;
        const modalRef = this.modalService.openConfirmationDialog({ content, title, showCancelButton: true });
        modalRef.closed.pipe(take(1), filter(res => !!res)).subscribe(() => {
            if (type === MitigationTypeEnum.Scrub) {
                this.ddoSMitigationScrubService.start(prefix, false).subscribe(res => {
                    this.setLoadingStatusToRows(item.cidr, res.data.taskId, TaskStatusMessageEnum.Pending);
                    this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.StartScrubMitigation]);
                    this.ddosServiceQueueService.addToQueue(res.data.taskId, item.cidr, JobQueueEventDescription[JobQueueEvent.StartScrubMitigation]);
                });
            } else if (type === MitigationTypeEnum.Blackhole) {
                this.ddoSMitigationBlackholeService.start(prefix, false).subscribe(res => {
                    this.setLoadingStatusToRows(item.cidr, res.data.taskId, TaskStatusMessageEnum.Pending);
                    this.notificationService.notify(JobQueueEventDescription[JobQueueEvent.StartBlackholeMitigation]);
                    this.ddosServiceQueueService.addToQueue(res.data.taskId, item.cidr, JobQueueEventDescription[JobQueueEvent.StartBlackholeMitigation]);
                });
            }
        });
    }

    protected viewMessages(item: MitigationViewModel): void {
        const modalRef = this.modalService.openModalComponent(DdosMitigationMessagesComponent);
        (modalRef.componentInstance as DdosMitigationMessagesComponent).messages.set(item.messages);
    }

    protected openStartMitigationModal(mitigationType: MitigationTypeEnum): void {
        const modalRef = this.modalService.openModalComponent(StartMitigationComponent);
        (modalRef.componentInstance as StartMitigationComponent).mitigationType.set(mitigationType);
        modalRef.closed.pipe(
            take(1),
            filter(res => !!res),
            map(res => ({
                taskId: res.taskId,
                cidr: res.cidr,
                message: res.message,
                simulate: res.simulate
            })),
        ).subscribe(res => {
            this.ddosServiceQueueService.addToQueue(res.taskId, res.cidr, mitigationType === MitigationTypeEnum.Blackhole ?
                JobQueueEventDescription[JobQueueEvent.StartBlackholeMitigation] :
                JobQueueEventDescription[JobQueueEvent.StartScrubMitigation]);
            const mitigation: MitigationViewModel = {
                active: false,
                cidr: res.cidr,
                messages: [res.message],
                taskId: res.taskId,
                mitigationType,
                taskStatus: TaskStatusEnum.Pending,
                simulate: res.simulate,
                status: null,
                taskStatusMessage: TaskStatusMessageEnum.Pending,
                stopTime: null,
                startTime: new Date(),
            };
            const updatedMitigationList = [mitigation, ...this.mitigationList()];
            this.mitigationList.set(updatedMitigationList);
            this.table().rows = updatedMitigationList;
        });
    }

    private setLoadingStatusToRows(cidr: string, taskId: string, taskStatus: TaskStatusMessageEnum): void {
        const rows = this.table().rows as MitigationViewModel[];
        const row = rows.find(r => r.cidr === cidr);
        if (row) {
            row.taskStatus = TaskStatusEnum.Pending;
            row.taskStatusMessage = taskStatus;
            row.taskId = taskId;
            this.table().rows = [...rows];
        }
    }

}
