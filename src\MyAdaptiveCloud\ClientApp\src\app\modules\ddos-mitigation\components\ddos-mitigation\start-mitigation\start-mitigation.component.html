<div class="modal-header">
    <h4 class="modal-title">Start Mitigation - {{ mitigationType() === mitigationTypeEnum.Blackhole ? 'Blackhole' :
        'Scrub' }}</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    @if (form) {
        <form class="form-horizontal" [formGroup]="form" novalidate>
            <div class="row">
                <label>CIDR<span class="required-asterisk">*</span></label>
                <div class="col-9">
                    <input class="form-control" formControlName="cidr" placeholder="Enter CIDR" id="cidr" />
                </div>
                <div class="col-3">
                    <button class="btn btn-primary" id="validate-cidr" [disabled]="form.controls.cidr.invalid || cidrValidationInProgress()"
                        (click)="validateCidr()">Validate</button>
                </div>
            </div>
            @if (validatedCidr()) {
                <div class="col mt-1" id="cidr-validation">
                    @if (isCidrValid()) {
                        <i class="fas fa-check text-success me-1"></i>Validation succeeded
                    } @else {
                        <i class="fas fa-times text-danger me-1"></i>Validation failed
                    }
                    <i class="ms-1 fa fa-info-circle"
                           [ngbPopover]="messageTemplate" triggers="hover" container="body"></i>
                </div>
                <ng-template #messageTemplate>
                    <p>
                        @for (message of validatedCidr()?.messages; track $index) {
                            <span>{{ message }}<br /> </span>
                        }
                    </p>
                </ng-template>
            }
            <div class="col mt-3">
                <label class="form-check-label">
                    <input class="form-check-input" type="checkbox" formControlName="simulate" id="simulate" />
                    Simulate
                </label>
                <i class="ms-1 fa fa-info-circle"
                    [ngbPopover]="'This will simulate all steps of mitigation action, but will not perform the actual mitigation itself'"
                    triggers="hover" container="body"></i>
            </div>
        </form>
    }
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!isCidrValid() || mitigationInProgress()"
        (submitClickEvent)="submitForm()">
        Start Mitigation
    </app-btn-submit>
</div>
