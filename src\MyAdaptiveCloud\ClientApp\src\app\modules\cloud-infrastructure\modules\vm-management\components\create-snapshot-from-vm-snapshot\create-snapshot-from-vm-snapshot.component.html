<div class="modal-header">
    <h4 class="modal-title">Create Volume Snapshot</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.close()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form" class="g-3">
        <p><strong>Virtual Machine Snapshot:</strong> {{ snapshotName() }}</p>
        <p><strong>Created Date:</strong> {{ snapshotCreatedDate() | date: 'yyyy-MM-dd HH:mm:ss' }}</p>
        <div class="mb-3">
            <label for="name" class="form-label">New Volume Snapshot Name<span
                    class="required-asterisk">*</span></label>
            <input id="name" data-testid="name-input" type="text" class="form-control" formControlName="name">
        </div>
        <div class="col-12 mb-3">
            <label class="form-label">Volume<span class="required-asterisk">*</span></label>
            <ng-select [items]="volumes$ | async" bindLabel="name" bindValue="id"
                formControlName="volumeId" />
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" data-testid="cancel-button" class="btn btn-outline-secondary" (click)="activeModal.close()">Cancel</button>
    <app-btn-submit [disabled]="form?.invalid || isSubmitting()" [btnClasses]="'btn-primary'"
        (submitClickEvent)="submit()">Create</app-btn-submit>
</div>
