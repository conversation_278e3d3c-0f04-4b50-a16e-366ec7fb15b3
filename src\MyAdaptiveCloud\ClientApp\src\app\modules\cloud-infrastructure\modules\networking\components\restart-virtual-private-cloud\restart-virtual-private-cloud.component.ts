import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { RestartVirtualPrivateCloudForm } from '../../forms/restart-virtual-private-cloud.form';
import { VirtualPrivateCloudListViewModel } from '../../models/virtual-private-cloud-list.view-model';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';

@Component({
    selector: 'app-restart-virtual-private-cloud',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgbPopover],
    templateUrl: './restart-virtual-private-cloud.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class RestartVirtualPrivateCloudComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly virtualPrivateCloudService = inject(VirtualPrivateCloudService);
    private readonly destroyRef = inject(DestroyRef);

    readonly virtualPrivateCloud = signal<VirtualPrivateCloudListViewModel | null>(null);

    protected readonly isSubmitting = signal<boolean>(false);
    protected readonly form = signal<FormGroup<RestartVirtualPrivateCloudForm>>(null);

    ngOnInit(): void {

        const form = this.formBuilder.group<RestartVirtualPrivateCloudForm>({
            cleanup: this.formBuilder.control<boolean>(false),
            makeRedundant: this.formBuilder.control<boolean | null>(this.virtualPrivateCloud().isRedundant ? null : false),
            livePatchNetworkRouters: this.formBuilder.control<boolean | null>(false)
        });

        form.controls.makeRedundant.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                if (value) {
                    form.controls.cleanup.setValue(true);
                    form.controls.cleanup.disable();
                } else {
                    form.controls.cleanup.enable();
                }
                form.controls.cleanup.updateValueAndValidity();
            });

        form.controls.cleanup.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                if (value) {
                    form.controls.livePatchNetworkRouters.setValue(null);
                    form.controls.livePatchNetworkRouters.disable();
                } else {
                    form.controls.livePatchNetworkRouters.setValue(false);
                    form.controls.livePatchNetworkRouters.enable();
                }
                form.controls.livePatchNetworkRouters.updateValueAndValidity();
            });

        this.form.set(form);
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected restart() {
        if (this.form().valid) {
            this.isSubmitting.set(true);
            this.virtualPrivateCloudService.restartVirtualPrivateCloud(this.virtualPrivateCloud().id, this.form().controls.cleanup.value, this.form().controls.livePatchNetworkRouters.value, this.form().controls.makeRedundant.value)
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
