@import "custom-variables.scss";

.wizard-container {

    display: flex;
    margin: 1rem;

    .wizard-content {

        border: 1px solid $secondary-25;
        flex-basis: 70%;

        .header {
            background-color: white;
            display: flex;
            padding: 1rem;
            border-bottom: 1px solid $secondary-25;

            .title {
                font-size: 1.5rem;
                color: $secondary;
            }
        }

        .content {
            background-color: $secondary-05;
            padding: 1rem;
            border-bottom: 1px solid $secondary-25;
        }

        .footer {
            background-color: white;
            display: flex;
            padding: 1rem;
        }

    }

    .summary-content {
        flex-basis: 30%;
    }

}
