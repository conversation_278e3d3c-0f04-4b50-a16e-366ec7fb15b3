import { signal } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { of } from 'rxjs';
import { CreateNetworkService } from '../../../../../../services/create-network.service';
import { TemplateViewModel } from '../../../../models/template.view.model';
import { VmNetwork } from '../../../../models/vm-network.model';
import { VmAffinityGroupsService } from '../../../../services/vm-affinity-groups.service';
import { VmManagementPermissionService } from '../../../../services/vm-management-permission.service';
import { VmManagementService } from '../../../../services/vm-management.service';
import { VmMediaService } from '../../../../services/vm-media-service';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { DiskOffering } from '../../models/disk-offering.model';
import { ServiceOfferingViewModel } from '../../models/service-offering.view-model';
import { CreateVmComputeService } from '../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../services/create-vm-network.service';
import { CreateVmService } from '../../services/create-vm-service';
import { CreateVmStorageService } from '../../services/create-vm-storage.service';
import { CreateVmWizardNetworkComponent } from './network.component';

describe('CreateVmWizardNetworkComponent', () => {

    let component: CreateVmWizardNetworkComponent;
    let fixture: ComponentFixture<CreateVmWizardNetworkComponent>;
    let mockCreateVmComputeService: jasmine.SpyObj<CreateVmComputeService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockCreateNetworkService: jasmine.SpyObj<CreateNetworkService>;
    let mockCreateVmNetworkService: jasmine.SpyObj<CreateVmNetworkService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;
    let mockVmManagementPermissionService: jasmine.SpyObj<VmManagementPermissionService>;

    const mockVmNetwork: VmNetwork = {
        id: '1',
        name: 'Test Network',
        cidr: '***********/24',
        type: 'Private',
        vpcname: 'Test VPC',
        ipaddress: '***********',
        macaddress: '00:11:22:33:44:55',
        gateway: '*************'
    };

    const mockVmNetwork2: VmNetwork = {
        id: '2',
        name: 'Test Network 2',
        cidr: '***********/24',
        type: 'Private',
        vpcname: 'Test VPC 2',
        ipaddress: '***********',
        macaddress: '00:11:22:33:44:56',
        gateway: '*************'
    };

    const zones: ZoneViewModel[] = [
        {
            id: '1',
            name: 'Zone 1'
        },
        {
            id: '2',
            name: 'Zone 2'
        },
        {
            id: '3',
            name: 'Zone 3'
        }
    ];

    const mockServiceOfferings: ServiceOfferingViewModel[] = [
        {
            name: 'name 1',
            id: '1',
            cpuNumber: 4,
            memory: 4096,
            isCustom: false
        }, {
            name: 'name 2',
            id: '2',
            cpuNumber: 2,
            memory: 2048,
            isCustom: false
        },
        {
            name: 'name 3',
            id: '3',
            cpuNumber: null,
            memory: null,
            isCustom: true
        }
    ];

    const zone1ISOs: TemplateViewModel[] = [
        {
            name: 'CentOS 7.8',
            id: '1',
            size: 123456,
            description: 'CentOS 7.8',
        }
    ];

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    beforeEach(() => {

        const mockZoneDomainAccountStore = { ...getMockZoneDomainAccountStore(), zones: signal(zones) };

        TestBed.configureTestingModule({
            imports: [CreateVmWizardNetworkComponent],
            providers: [
                provideMock(CreateNetworkService),
                provideMock(CreateVmComputeService),
                provideMock(VmMediaService),
                provideMock(CreateNetworkService),
                provideMock(UserContextService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(CloudInfraPermissionService),
                provideMock(CreateVmNetworkService),
                provideMock(CreateVmService),
                provideMock(CreateVmStorageService),
                provideMock(VmManagementPermissionService),
                provideMock(NotificationService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
                CreateVMWizardStore
            ]
        });
        mockCreateVmComputeService = TestBed.inject(CreateVmComputeService) as jasmine.SpyObj<CreateVmComputeService>;
        mockCreateVmComputeService.getServiceOfferings.and.returnValue(of(mockServiceOfferings));

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.getFeaturedISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyISOsByZoneId.and.returnValue(of(zone1ISOs));

        mockVmMediaService.getFeaturedTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyTemplatesByZoneId.and.returnValue(of(zone1ISOs));

        mockCreateVmNetworkService = TestBed.inject(CreateVmNetworkService) as jasmine.SpyObj<CreateVmNetworkService>;
        mockCreateVmNetworkService.getNetworks.and.returnValue(of([mockVmNetwork, mockVmNetwork2]));

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of([]));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of([]));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'test-account',
                domainId: 'test-domain-id',
                cpuCustomOfferingMaxValue: 4,
                memoryCustomOfferingMaxValue: 8192
            }
        } as UserContext;

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

        mockCreateNetworkService = TestBed.inject(CreateNetworkService) as jasmine.SpyObj<CreateNetworkService>;
        mockCreateNetworkService.getLayer2NetworkOfferings.and.returnValue(of([
            { id: '1', name: 'L2 Network Offering', forVPC: false, specifyVLan: false },
            { id: '2', name: 'L2 Network Offering 2', forVPC: false, specifyVLan: true }
        ]));
        mockCreateNetworkService.getIsolatedNetworkOfferings.and.returnValue(of([
            { id: '10', name: 'Isolated Network Offering', forVPC: false, specifyVLan: false },
            { id: '11', name: 'Isolated Network Offering 2', forVPC: true, specifyVLan: false }
        ]));
        mockCreateNetworkService.getSharedNetworkOfferings.and.returnValue(of([{ id: '100', name: 'Shared Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getVpcOfferings.and.returnValue(of([{ id: '1000', name: 'VPC Offering', cidr: '' }]));

        mockVmManagementPermissionService = TestBed.inject(VmManagementPermissionService) as jasmine.SpyObj<VmManagementPermissionService>;
        mockVmManagementPermissionService.canCreateNetwork.and.returnValue(true);

        fixture = TestBed.createComponent(CreateVmWizardNetworkComponent);
        component = fixture.componentInstance;

        component.store.setTemplate({ zone: zones[0] }, true);
        component.store.loadDataByZoneId();

        fixture.detectChanges();
    });

    describe('Network Form', () => {

        beforeEach(() => {
            spyOn(component.store, 'addSelectedNetwork').and.callThrough();
            spyOn(component.store, 'addNetwork').and.callThrough();
        });

        it('Add button network should be disabled and form invalid if network not selected', () => {
            const buttonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement;
            expect(buttonElement.disabled).toBeTrue();
        });

        it('Add button network should be enabled and form valid', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            const ipAddressInput = fixture.debugElement.query(By.css('#ipAddress')).nativeElement as HTMLInputElement;
            ipAddressInput.value = '***********';
            ipAddressInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const macAddressInput = fixture.debugElement.query(By.css('#macAddress')).nativeElement as HTMLInputElement;
            macAddressInput.value = '11:22:33:44:55:66';
            macAddressInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const buttonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement as HTMLButtonElement;
            expect(buttonElement.disabled).toBeFalse();
        }));

        it('Add button network should be enabled if ip and mac are empty', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            const buttonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement as HTMLButtonElement;
            expect(buttonElement.disabled).toBeFalse();
        }));

        it('Add button network should be disabled if mac invalid', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            const macAddressInput = fixture.debugElement.query(By.css('#macAddress')).nativeElement as HTMLInputElement;
            macAddressInput.value = 'invalid mac';
            macAddressInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const buttonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement as HTMLButtonElement;
            expect(buttonElement.disabled).toBeTrue();
        }));

    });

    describe('add and remove networks', () => {

        let addSelectedNetworkSpy: jasmine.Spy;
        let removeSelectedNetworkSpy: jasmine.Spy;

        beforeEach(() => {
            addSelectedNetworkSpy = spyOn(component.store, 'addSelectedNetwork').and.callThrough();
            removeSelectedNetworkSpy = spyOn(component.store, 'removeSelectedNetwork').and.callThrough();
        });

        it('Should add network with custom ip and mac addresses to store', fakeAsync(() => {

            expect(component.store.networkStep.isValid()).toBeFalse();

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            const ipAddressInput = fixture.debugElement.query(By.css('#ipAddress')).nativeElement as HTMLInputElement;
            ipAddressInput.value = '***********';
            ipAddressInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const macAddressInput = fixture.debugElement.query(By.css('#macAddress')).nativeElement as HTMLInputElement;
            macAddressInput.value = '11:22:33:44:55:66';
            macAddressInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const buttonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement as HTMLButtonElement;
            buttonElement.click();
            fixture.detectChanges();

            expect(addSelectedNetworkSpy).toHaveBeenCalledWith({
                id: '1',
                name: 'Test Network',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC',
                ipaddress: '***********',
                macaddress: '11:22:33:44:55:66',
                gateway: '*************'
            });

            expect(component.store.networkStep.selectedNetworks()).toEqual([{
                id: '1',
                name: 'Test Network',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC',
                ipaddress: '***********',
                macaddress: '11:22:33:44:55:66',
                gateway: '*************'
            }]);

            expect(component.store.networkStep.isValid()).toBeTrue();

        }));

        it('Should add network to the store', fakeAsync(() => {

            expect(component.store.networkStep.isValid()).toBeFalse();

            const buttonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement as HTMLButtonElement;
            expect(buttonElement.disabled).toBeTrue();

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            expect(buttonElement.disabled).toBeFalse();

            buttonElement.click();
            fixture.detectChanges();

            expect(addSelectedNetworkSpy).toHaveBeenCalledWith({
                id: '1',
                name: 'Test Network',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            });

            expect(component.store.networkStep.selectedNetworks()).toEqual([{
                id: '1',
                name: 'Test Network',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            }]);

            expect(component.store.networkStep.isValid()).toBeTrue();

        }));

        it('Should add two networks to the store', fakeAsync(() => {

            expect(component.store.networkStep.isValid()).toBeFalse();

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            const buttonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement as HTMLButtonElement;
            buttonElement.click();
            fixture.detectChanges();

            expect(component.store.networkStep.isValid()).toBeTrue();

            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            buttonElement.click();
            fixture.detectChanges();

            expect(component.store.networkStep.isValid()).toBeTrue();

            expect(addSelectedNetworkSpy).toHaveBeenCalledWith({
                id: '1',
                name: 'Test Network',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            });

            expect(addSelectedNetworkSpy).toHaveBeenCalledWith({
                id: '2',
                name: 'Test Network 2',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC 2',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            });

            expect(component.store.networkStep.selectedNetworks()).toEqual([{
                id: '1',
                name: 'Test Network',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            }, {
                id: '2',
                name: 'Test Network 2',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC 2',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            }]);

        }));

        it('Should add and remove networks from the store', fakeAsync(() => {

            expect(component.store.networkStep.isValid()).toBeFalse();

            const addButtonElement = fixture.debugElement.query(By.css('.add-network-btn')).nativeElement as HTMLButtonElement;
            expect(addButtonElement.disabled).toBeTrue();

            selectOption(fixture, 'ng-select', 0, true, 0);
            fixture.detectChanges();

            addButtonElement.click();
            fixture.detectChanges();

            expect(component.store.networkStep.isValid()).toBeTrue();

            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            expect(addButtonElement.disabled).toBeFalse();

            addButtonElement.click();
            fixture.detectChanges();

            expect(component.store.networkStep.isValid()).toBeTrue();

            expect(addButtonElement.disabled).toBeTrue();

            const removeButtonElements = fixture.debugElement.queryAll(By.css('.remove-network'));
            const firstRemoveButton = removeButtonElements[0].nativeElement as HTMLButtonElement;
            const secondRemoveButton = removeButtonElements[1].nativeElement as HTMLButtonElement;

            expect(firstRemoveButton.disabled).toBeFalse();
            expect(secondRemoveButton.disabled).toBeFalse();

            expect(component.store.networkStep.selectedNetworks()).toEqual([{
                id: '1',
                name: 'Test Network',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            }, {
                id: '2',
                name: 'Test Network 2',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC 2',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            }]);

            firstRemoveButton.click();
            fixture.detectChanges();

            expect(component.store.networkStep.isValid()).toBeTrue();

            expect(removeSelectedNetworkSpy).toHaveBeenCalledWith('1');
            expect(component.store.networkStep.selectedNetworks()).toEqual([{
                id: '2',
                name: 'Test Network 2',
                cidr: '***********/24',
                type: 'Private',
                vpcname: 'Test VPC 2',
                ipaddress: null,
                macaddress: null,
                gateway: '*************'
            }]);

            secondRemoveButton.click();
            fixture.detectChanges();

            expect(removeSelectedNetworkSpy).toHaveBeenCalledWith('2');
            expect(component.store.networkStep.selectedNetworks()).toEqual([]);

            expect(component.store.networkStep.isValid()).toBeFalse();

        }));

    });

});
