import { NetworkingDetailService } from './networking-detail.service';
import { Injectable, inject } from '@angular/core';
import { Resolve, ActivatedRouteSnapshot } from '@angular/router';
import { Network } from '@app/modules/cloud-infrastructure/models/network';
import { map, Observable, of } from 'rxjs';
import { NetworkingService } from './networking.service';

@Injectable({ providedIn: 'root' })
export class NetworkDetailResolverService implements Resolve<string> {

    private networkService = inject(NetworkingService);
    private networkingDetailService = inject(NetworkingDetailService);

    resolve(route: ActivatedRouteSnapshot) : Observable<string> {
        const id = route.paramMap.get('id') ?? null;
        if (!id) {
            this.networkingDetailService.selectedNetwork.set(null);
            return of('');
        }

        return this.networkService.getNetworkById(id).pipe(map((network: Network | null) => {
            this.networkingDetailService.selectedNetwork.set(network);
            return network?.name ?? '';
        }));
    }
}
