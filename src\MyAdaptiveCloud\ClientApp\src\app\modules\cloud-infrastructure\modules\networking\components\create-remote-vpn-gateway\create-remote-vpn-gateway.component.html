<div class="modal-header">
    <h4 class="modal-title">Add Remote VPN Gateway</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form class="form-horizontal" [formGroup]="form">
        <div class="row mb-3">
            <label for="name" class="col-3 col-form-label">Name<span class="required-asterisk">*</span></label>
            <div class="col">
                <input class="form-control" data-testid="name-input" formControlName="name" id="name"
                    [class]="{ 'is-invalid': form.controls.name.invalid && form.controls.name.dirty }"
                    autocomplete="off" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="gateway" class="col-3 col-form-label">Gateway<span class="required-asterisk">*</span></label>
            <div class="col">
                <input class="form-control" data-testid="gateway-input" formControlName="gateway" id="gateway"
                    [class]="{ 'is-invalid': form.controls.gateway.invalid && form.controls.gateway.dirty }" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="cidrList" class="col-3 col-form-label">CIDR List<span class="required-asterisk">*</span>
                <i class="ms-1 fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Guest CIDR list of the customer gateway. Multiple entries must be separated by a single comma character (,).'"
                    triggers="hover" placement="right" container="body">
                </i>
            </label>
            <div class="col">
                <input class="form-control" data-testid="cidrList-input" formControlName="cidrList" id="cidrList"
                    [class]="{ 'is-invalid': form.controls.cidrList.invalid && form.controls.cidrList.dirty }" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="ipsSecurityPreSharedKey" class="col-3 col-form-label">IPSec Pre-Shared Key<span
                    class="required-asterisk">*</span>
                <i class="ms-1 fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'IPsec Preshared-Key of the customer gateway. Cannot contain newline or double quotes.'"
                    triggers="hover" placement="right" container="body">
                </i>
            </label>
            <div class="col">
                <input class="form-control" data-testid="ipSecurityPreSharedKey-input"
                    formControlName="ipSecurityPreSharedKey" id="ipSecurityPreSharedKey"
                    [class]="{ 'is-invalid': form.controls.ipSecurityPreSharedKey.invalid && form.controls.ipSecurityPreSharedKey.dirty }" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-form-label" for="ikeEncryption">IKE Encryption</label>
            </div>
            <div class="col-9">
                <ng-select [items]="encryptionMethods" id="ikeEncryption" formControlName="ikeEncryption"
                    data-testid="ike-encryption-select" [clearable]="false" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-form-label" for="ikeHash">IKE Hash</label>
            </div>
            <div class="col-9">
                <ng-select [items]="hashes" id="ikeHash" formControlName="ikeHash" data-testid="ike-hash-select"
                    [clearable]="false" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-form-label" for="ikeVersion">IKE Version
                    <i class="ms-1 fa-solid fa-circle-info text-secondary"
                        [ngbPopover]="'Which IKE Version to use. Connections marked with ike will use ikev2 when initiating, but accept any protocol version when responding.'"
                        triggers="hover" placement="right" container="body">
                    </i>
                </label>
            </div>
            <div class="col-9">
                <ng-select [items]="ikeVersions" id="ikeVersion" formControlName="ikeVersion"
                    data-testid="ike-version-select" [clearable]="false" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-form-label" for="ikeDH">IKE DH</label>
            </div>
            <div class="col-9">
                <ng-select [items]="dhGroupList" id="ikeDH" formControlName="ikeDH" data-testid="ike-dh-select" bindValue="value"
                    [clearable]="true">
                    <ng-template ng-option-tmp let-item="item">
                        <span>{{ item.name }} ({{ item.value }})</span>
                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                        <span>{{ item.name }} ({{ item.value }})</span>
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-form-label" for="espEncryption">ESP Encryption</label>
            </div>
            <div class="col-9">
                <ng-select [items]="encryptionMethods" id="espEncryption" formControlName="espEncryption"
                    data-testid="esp-encryption-select" [clearable]="false" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-form-label" for="espHash">ESP Hash</label>
            </div>
            <div class="col-9">
                <ng-select [items]="hashes" id="espHash" formControlName="espHash" data-testid="esp-hash-select"
                    [clearable]="false" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-form-label" for="perfectForwardSecrecy">Perfect Forward Secrecy</label>
            </div>
            <div class="col-9">
                <ng-select [items]="dhGroupList" id="perfectForwardSecrecy" formControlName="perfectForwardSecrecy"
                    data-testid="perfect-forward-secrecy-select" [clearable]="true" bindValue="value">
                    <ng-template ng-option-tmp let-item="item">
                        {{ item.name }} ({{ item.value }})
                    </ng-template>
                    <ng-template ng-label-tmp let-item="item">
                        {{ item.name }} ({{ item.value }})
                    </ng-template>
                </ng-select>
            </div>
        </div>
        <div class="row mb-3">
            <label for="ikeLifetime" class="col-3 col-form-label">IKE Lifetime (seconds)
                <i class="ms-1 fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Lifetime of phase 1 VPN connection to the customer gateway in seconds. Must be between 1 and 86400 seconds.'"
                    triggers="hover" placement="right" container="body">
                </i>
            </label>
            <div class="col">
                <input class="form-control" data-testid="ike-lifetime-input" formControlName="ikeLifetime"
                    id="ikeLifetime"
                    [class]="{ 'is-invalid': form.controls.ikeLifetime.invalid && form.controls.ikeLifetime.dirty }" />
            </div>
        </div>
        <div class="row mb-3">
            <label for="espLifetime" class="col-3 col-form-label">ESP Lifetime (seconds)
                <i class="ms-1 fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Lifetime of phase 2 VPN connection to the customer gateway in seconds. Must be between 1 and 86400 seconds.'"
                    triggers="hover" placement="right" container="body">
                </i>
            </label>
            <div class="col">
                <input class="form-control" data-testid="esp-lifetime-input" formControlName="espLifetime"
                    id="espLifetime"
                    [class]="{ 'is-invalid': form.controls.espLifetime.invalid && form.controls.espLifetime.dirty }" />
            </div>
        </div>
        <div class="form-check row pt-2 pb-3">
            <label class="form-check-label clickable">Dead Peer Detection
                <i class="fa-solid fa-circle-info text-secondary" [ngbPopover]="'If DPD is enabled for VPN connection.'"
                    triggers="hover" placement="right" container="body">
                </i>
                <input type="checkbox" class="form-check-input clickable" formControlName="deadPeerDetection"
                    data-testid="dead-peer-detection-checkbox" />
            </label>
        </div>
        <div class="form-check row pt-2 pb-3">
            <label class="form-check-label clickable">Split Connections
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'For IKEv2, whether to split multiple right subnet cidrs into multiple connection statements.'"
                    triggers="hover" placement="right" container="body">
                </i>
                <input type="checkbox" class="form-check-input clickable" formControlName="splitConnections"
                    data-testid="split-connections-checkbox" />
            </label>
        </div>
        <div class="form-check row pt-2 pb-3">
            <label class="form-check-label clickable">Force UDP encapsulation of ESP packets
                <i class="fa-solid fa-circle-info text-secondary"
                    [ngbPopover]="'Force Encapsulation for NAT traversal.'" triggers="hover" placement="right"
                    container="body">
                </i>
                <input type="checkbox" class="form-check-input clickable" formControlName="forceEncapsulation"
                    data-testid="force-encapsulation-checkbox" />
            </label>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!form?.valid" (submitClickEvent)="submitForm()">OK
    </app-btn-submit>
</div>
