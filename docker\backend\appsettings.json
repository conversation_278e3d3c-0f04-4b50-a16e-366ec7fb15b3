{"ConnectionStrings": {"DefaultConnection": "Server=myacdb;User Id=root;Database=myadaptivecloud; Connection Timeout=1000000", "ACAgentConnection": "Server=agentdb;User Id=root;Database=acagent;", "LogsConnection": "Server=myaclogsdb;User Id=root;Database=myadaptivecloudlogs;", "BillingConnection": "Server=billingdb;User Id=root;Database=billing;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "MYAC": "Information"}}}