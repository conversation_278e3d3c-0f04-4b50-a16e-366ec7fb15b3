import { CdkDragDrop, CdkDragEnter, CdkDragExit, CdkDropList } from '@angular/cdk/drag-drop';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, Signal, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { DeviceManagementRoutingService } from '@app/shared/services/device-management-routing.service';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { DEVICES_ROUTES_SEGMENTS } from '../../../../shared/constants/routes-segments';
import { DeviceFolderTreeNode } from '../../models/device-folder-tree-node';
import { DeviceTreeNode } from '../../models/device-tree-node';
import { DeviceType } from '../../modules/device-folders/models/device-type.enum';
import { FolderDevicesManagementService } from '../../services/folder-devices-management.service';
import { FoldersTreeStore } from '../../store/folders-tree.store';

@Component({
    selector: 'app-device-folders-tree',
    imports: [CdkDropList, NgbTooltip, NgTemplateOutlet],
    templateUrl: './folder-tree.component.html',
    styleUrl: './folder-tree.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceFolderTreeComponent {
    public readonly foldersTreeStore = inject(FoldersTreeStore);
    private readonly folderDevicesManagementService = inject(FolderDevicesManagementService);
    private readonly deviceManagementRoutingService = inject(DeviceManagementRoutingService);
    private readonly router = inject(Router);
    private readonly route = inject(ActivatedRoute);

    private readonly tooltip = viewChild<NgbTooltip>('t');

    readonly folder = input.required<DeviceFolderTreeNode>();
    readonly showDevices = input(false);
    readonly showDevicesType = input(DeviceType.All);
    readonly showCounts = input(false);
    readonly showDiscoveredDevicesFolders = input(false);
    readonly allowMultiDeviceSelection = input(false);
    readonly allowDragAndDrop = input(false);
    readonly allowNavigation = input(false);

    protected readonly deviceType = DeviceType;

    private readonly selectedRootFolder = toSignal(this.foldersTreeStore.selectedRootFolder$);
    protected readonly allowMultiSelect = toSignal(this.foldersTreeStore.allowMultiSelect$);
    private readonly foldersRoot = toSignal(this.foldersTreeStore.foldersRoot$);
    protected readonly isTooltipEnabled = toSignal(this.foldersTreeStore.isTooltipEnabled$);
    protected readonly folderMovementText = toSignal(this.foldersTreeStore.folderMovementText$);
    protected readonly selectedFolderUniqueId = toSignal(this.foldersTreeStore.selectedFolderUniqueId$);
    protected readonly selectedFolder = toSignal(this.foldersTreeStore.selectedFolder$);
    protected readonly selectedDevices = toSignal(this.foldersTreeStore.selectedDevices$);
    protected readonly selectedDevice = toSignal(this.foldersTreeStore.selectedDevice$);

    protected readonly isFoldersRoot = computed(() => this.folder().getUniqueStringId() === this.foldersRoot()?.getUniqueStringId());

    protected readonly canToggle = computed(() => {
        const hasSubfolders = this.folder().hasSubfolders();
        const hasDevicesAndCanShow = this.folder().hasAnyDevice() && this.showDevices() && !this.folder().isOrganizationFolder();
        const isOrganizationFolderWithDevices = this.folder().isOrganizationFolder() && this.folder().deviceCountOnlySelf() > 0 && this.showDiscoveredDevicesFolders();

        return hasSubfolders || hasDevicesAndCanShow || isOrganizationFolderWithDevices;
    });

    protected readonly css: Signal<'org-selected' | 'org-selected bg-tree-secondary' | 'text-secondary'> = computed(() => (this.allowMultiSelect() ? this.getMultiSelectCss() : this.getSingleSelectCss()));

    private getMultiSelectCss(): 'org-selected' | 'org-selected bg-tree-secondary' | 'text-secondary' {

        // Multiselect scenario and current folder is the selected one
        const isMultiSelectAndFolderSelected = this.allowMultiSelect() && this.folder().isSelected();
        if (isMultiSelectAndFolderSelected) {
            return 'org-selected';
        }

        // If there are no devices selected and the current folder is selected, and the current folder is not root
        const isCurrentFolderSelectedAndNotRoot = !this.selectedDevices()?.length && (this.selectedFolder() && this.folder().getUniqueStringId() === this.selectedFolder()?.getUniqueStringId()) && this.folder().level() > 1;
        if (isCurrentFolderSelectedAndNotRoot) {
            return 'org-selected bg-tree-secondary';
        }

        return 'text-secondary';
    }

    private getSingleSelectCss(): 'org-selected' | 'org-selected bg-tree-secondary' | 'text-secondary' {
        const folder = this.folder();
        const selectedFolder = this.selectedFolder();
        const selectedRootFolder = this.selectedRootFolder();
        const selectedDevice = this.selectedDevice();

        const isUnassignedDevicesFolder = folder.isUnassignedDevicesFolder()
            && selectedFolder
            && selectedFolder.isUnassignedDevicesFolder()
            && selectedFolder.getUniqueStringId() === folder.getUniqueStringId();
        if (isUnassignedDevicesFolder) {
            if (folder.level() > 1) {
                return 'org-selected bg-tree-secondary';
            }
            return 'org-selected';
        }

        const isSingleSelectAndRootFolderSelected =
            !this.allowMultiSelect() &&
            folder.getUniqueStringId() === selectedRootFolder?.getUniqueStringId();

        if (isSingleSelectAndRootFolderSelected) {
            return 'org-selected';
        }

        const isCurrentFolderSelectedAndNotRoot =
            selectedFolder &&
            folder.getUniqueStringId() === selectedFolder.getUniqueStringId() &&
            folder.level() > 1;

        if (isCurrentFolderSelectedAndNotRoot) {
            return 'org-selected bg-tree-secondary';
        }

        const isFolderSelectedOrDeviceParentMatches =
            folder.isSelected() ||
            (selectedDevice && selectedDevice.parent().getUniqueStringId() === folder.getUniqueStringId());

        if (isFolderSelectedOrDeviceParentMatches || folder.getUniqueStringId() === selectedFolder?.getUniqueStringId()) {
            return folder.isRootOrganization() ? 'org-selected' : 'org-selected bg-tree-secondary';
        }

        return 'text-secondary';
    }

    protected selectDevice(device: DeviceTreeNode) {
        device.toggleSelected();
        if (device.isSelected()) {
            this.foldersTreeStore.selectDevice({ device, allowMultiDeviceSelection: this.allowMultiDeviceSelection() });
            const uniqueId = device.parent()?.getUniqueStringId();
            if (this.allowNavigation()) {
                if (this.router.url.includes(DEVICES_ROUTES_SEGMENTS.SOFTWARE_INVENTORY) && !device?.parent()?.isUnassignedDevicesFolder()) {
                    this.router.navigate([`${DEVICES_ROUTES_SEGMENTS.DEVICES}/${uniqueId}/device/${device.agentId}/${DEVICES_ROUTES_SEGMENTS.SOFTWARE_INVENTORY}`], { relativeTo: this.route });
                } else {
                    this.router.navigate([`${DEVICES_ROUTES_SEGMENTS.DEVICES}/${uniqueId}/device/`, device.agentId], { relativeTo: this.route });
                }
            }
            this.folderDevicesManagementService.hideInstalledSoftware();
        } else if (this.selectedDevice()?.agentId === device.agentId) {
            this.foldersTreeStore.unselectDevice();
        }
    }

    protected selectFolder() {
        if (this.allowNavigation()) {
            if (!this.folder().isUnassignedDevicesFolder()) {
                const segment = this.deviceManagementRoutingService.getSegmentFromURL(this.router.url);
                this.router.navigate([segment, this.folder().getUniqueStringId()], { relativeTo: this.route });
            } else {
                this.deviceManagementRoutingService.setFolderIdFromUniqueId(this.folder().getUniqueStringId());
                this.router.navigate([`/${DEVICES_ROUTES_SEGMENTS.BASE_DEVICES_MANAGEMENT}/devices/${this.folder().getUniqueStringId()}`]);
            }
        } else {
            this.foldersTreeStore.selectFolder(this.folder());
        }
    }

    protected drop(event: CdkDragDrop<DeviceFolderTreeNode[], DeviceFolderTreeNode[], DeviceFolderTreeNode[] | DeviceTreeNode[]>) {
        const destinationFolder = event.container.data[0];
        this.foldersTreeStore.moveItemsTo(destinationFolder);
        event.container.element.nativeElement.classList.remove('drop-area');
        this.tooltip().close();
    }

    protected dropListEntered(event: CdkDragEnter<DeviceFolderTreeNode[], DeviceFolderTreeNode[] | DeviceTreeNode[]>) {
        event.container.element.nativeElement.classList.add('drop-area');
        const destinationFolder: DeviceFolderTreeNode = event.container.data[0];
        this.foldersTreeStore.setDestinationFolderMovementText(destinationFolder);
    }

    protected dropListExited(event: CdkDragExit<DeviceFolderTreeNode[]>) {
        event.container.element.nativeElement.classList.remove('drop-area');
        this.foldersTreeStore.clearDestinationFolderMovementText();
    }

    protected onToggleFolder(folder: DeviceFolderTreeNode) {
        if (this.canToggle()) {
            if (this.showDevices() && !folder.isOrganizationFolder()) {
                this.foldersTreeStore.toggleFolderWithDevices(folder);
            } else {
                this.foldersTreeStore.toggleFolder(folder);
            }
        }
    }

    protected shouldShowDevice(device: DeviceTreeNode): boolean {
        const showDevicesType = this.showDevicesType();
        return showDevicesType === this.deviceType.All ||
            (showDevicesType === this.deviceType.Vm && device.cloudStackVM);
    }
}
