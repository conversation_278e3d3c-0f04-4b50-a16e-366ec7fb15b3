<h4><PERSON>ert Thresholds</h4>
@if (form) {
<div>
  <div class="row">
    <div class="col-xs-12 col-sm-6 col-md-6 pe-2">

      <div class="card card-default">
        <div class="card-body bg-secondary-5">
          <app-device-component-thresholds [componentName]="'CPU'"
            [useCurrentLevelForInheritance]="useCurrentLevelForInheritance"
            [deviceThresholdLevel]="deviceThresholds.deviceThresholdLevel" [thresholdsEntityId]="thresholdsEntityId"
            [intervals]="intervals" [inheritanceTypes]="inheritanceTypes" [form]="form.controls.cpu"
            [component]="deviceThresholds.cpuThresholds" />
        </div>
      </div>

      <div class="card card-default">
        <div class="card-body bg-secondary-5">
          <app-device-component-thresholds [componentName]="'Memory'"
            [useCurrentLevelForInheritance]="useCurrentLevelForInheritance"
            [deviceThresholdLevel]="deviceThresholds.deviceThresholdLevel" [thresholdsEntityId]="thresholdsEntityId"
            [intervals]="intervals" [inheritanceTypes]="inheritanceTypes" [form]="form.controls.memory"
            [component]="deviceThresholds.memoryThresholds" />
        </div>
      </div>

      <div class="card card-default">
        <div class="card-body bg-secondary-5">
          <app-device-component-thresholds [componentName]="'Agent Unreachable'"
            [useCurrentLevelForInheritance]="useCurrentLevelForInheritance"
            [deviceThresholdLevel]="deviceThresholds.deviceThresholdLevel" [thresholdsEntityId]="thresholdsEntityId"
            [intervals]="intervals" [inheritanceTypes]="inheritanceTypes" [form]="form.controls.agent"
            [component]="deviceThresholds.agentThresholds" [useIntervalsForMetrics]="true" />
        </div>
      </div>
    </div>

    <div class="col-xs-12 col-sm-12 col-md-6 ps-2">
      <div class="border rounded bg-secondary-5">
        <div class="disks-list overflow-auto">
          <h6 class="d-flex align-items-center all-drives-title card-title p-2 ms-2">Drives
            @if (showDeviceCount()) {
            <span class="mx-2 badge text-bg-secondary">
              {{ form.controls.disks.controls.length - 1 }}
            </span>
            }
          </h6>

          @for (diskForm of form.controls.disks.controls; track diskForm; let i = $index; let isFirst = $first; let
          isLast = $last) {
          <div class="border-top p-3" [class]="getClasses(diskForm.value.name, isFirst, isLast)">
            <app-device-component-thresholds [deviceThresholdLevel]="deviceThresholds.deviceThresholdLevel"
              [useCurrentLevelForInheritance]="useCurrentLevelForInheritance" [thresholdsEntityId]="thresholdsEntityId"
              [intervals]="intervals" [inheritanceTypes]="inheritanceTypes" [form]="diskForm"
              [component]="deviceThresholds.diskThresholds[i]" [isDiskUsage]="true"
              [componentName]="diskForm.value.name === ALL_DRIVES_NAME_CONSTANT ? ALL_DRIVES_NAME_CONSTANT : diskForm.value?.name?.replace('/','').replace('\\','')"
              (thresholdDiskInheritanceTypeChanged)="updateOtherComponentDisksInheritanceType($event)"
              (thresholdIntervalChanged)="updateOtherComponentIntervals($event)"
              (thresholdWarningChanged)="updateOtherComponentMetrics($event, diskForm.value.deviceAlertThresholdType)" />
          </div>
          }
        </div>
      </div>
    </div>
  </div>
</div>
}
