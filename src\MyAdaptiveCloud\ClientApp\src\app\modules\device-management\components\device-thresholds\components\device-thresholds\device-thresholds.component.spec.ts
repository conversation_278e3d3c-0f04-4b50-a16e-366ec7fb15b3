import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, FormGroup } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { DeviceAlertTypeEnum } from '@app/modules/device-management/models/device-alert-type.enum';
import { DeviceThresholds } from '@app/modules/device-management/models/device-thresholds';
import { DeviceFolderService } from '@app/modules/device-management/services/device-folder.service';
import { DevicesService } from '@app/modules/device-management/services/devices.service';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { Organization } from '@app/shared/models/organization.model';
import { UserContext } from '@app/shared/models/user-context.model';
import { OrganizationSharedService } from '@app/shared/services/organization-shared.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { DeviceThresholdsComponent } from '../../../device-thresholds/components/device-thresholds/device-thresholds.component';
import { DeviceThresholdInheritanceTypeEnum } from '../../../device-thresholds/models/device-threshold-inheritance-type.enum';
import { OrganizationThresholdsService } from '../../../device-thresholds/services/device-thresholds.service';
import { DeviceComponentThresholdsForm } from '../../forms/device-component-thresholds.form';
import { DeviceThresholdIntervalsForm } from '../../forms/device-threshold-intervals.form';
import { DeviceThresholdsMetricsForm } from '../../forms/device-threshold-metrics-form';
import { DeviceAlertThresholdTypeEnum } from '../../models/device-alert-threshold-type.enum';
import { DeviceThresholdInheritanceType } from '../../models/device-threshold-inheritance-type';
import { DeviceThresholdInterval } from '../../models/device-threshold-interval';
import { DeviceThresholdLevel } from '../../models/device-threshold-level.enum';
import { DeviceComponentThresholdsComponent } from '../device-component-thresholds/device-component-thresholds.component';
import { DeviceThresholdsMetricsComponent } from '../device-thresholds-metrics/device-thresholds-metrics.component';

describe('DeviceThresholdsComponent', () => {

    let component: DeviceThresholdsComponent;
    let fixture: ComponentFixture<DeviceThresholdsComponent>;
    let mockDeviceThresholdsService: jasmine.SpyObj<OrganizationThresholdsService>;
    let mockDeviceService: jasmine.SpyObj<DevicesService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockDeviceFolderService: jasmine.SpyObj<DeviceFolderService>;
    let mockOrganizationService: jasmine.SpyObj<OrganizationSharedService>;

    const organization: Organization = {
        organizationId: 1,
        name: 'No Root',
        allowSubOrg: true,
        parentOrganizationId: 3,
        parentOrganizationName: null,
        allowWhiteLabel: false,
        isPartner: false,
        organizationParentFullPath: 'No Root'
    };

    const getOrganizationResult: ApiDataResult<Organization> = {
        data: organization,
        message: 'success'
    };

    const deviceThresholds: DeviceThresholds = {
        deviceThresholdLevel: DeviceThresholdLevel.AgentOverride,
        agentThresholds: {
            deviceAlertThresholdType: DeviceAlertThresholdTypeEnum.Percentage_Used,
            deviceAlertThresholdTypes: [{ id: 2, maxValue: 100, name: '%' }, { id: 1, maxValue: 100, name: 'GB' }],
            deviceAlertType: DeviceAlertTypeEnum.Heartbeat,
            inheritanceType: DeviceThresholdInheritanceTypeEnum.Inherit,
            inheritFrom: 'Folder 1',
            inheritedFromPath: 'Folder',
            intervals: {
                clear: 1,
                trigger: 300
            },
            metrics: {
                critical: 2000,
                warning: 1000,
                error: 1500
            },
            name: ''
        },
        cpuThresholds: {
            deviceAlertThresholdType: DeviceAlertThresholdTypeEnum.Percentage_Used,
            deviceAlertThresholdTypes: [{ id: 2, maxValue: 100, name: '%' }, { id: 1, maxValue: 100, name: 'GB' }],
            deviceAlertType: DeviceAlertTypeEnum.CPU,
            inheritanceType: DeviceThresholdInheritanceTypeEnum.Override,
            inheritFrom: '',
            inheritedFromPath: 'Folder',
            intervals: {
                clear: 1,
                trigger: 300
            },
            metrics: {
                critical: 20,
                warning: 10,
                error: 15
            },
            name: ''
        },
        diskThresholds: [{
            deviceAlertThresholdType: DeviceAlertThresholdTypeEnum.Percentage_Used,
            deviceAlertThresholdTypes: [{ id: 2, maxValue: 100, name: '%' }, { id: 1, maxValue: 100, name: 'GB' }],
            deviceAlertType: DeviceAlertTypeEnum.DiskUsage,
            inheritanceType: DeviceThresholdInheritanceTypeEnum.Inherit,
            inheritFrom: 'System',
            inheritedFromPath: '',
            intervals: {
                clear: 1,
                trigger: 300
            },
            metrics: {
                critical: 2000,
                warning: 1000,
                error: 1500
            },
            name: 'C:'
        }, {
            deviceAlertThresholdType: DeviceAlertThresholdTypeEnum.Percentage_Used,
            deviceAlertThresholdTypes: [{ id: 2, maxValue: 100, name: '%' }, { id: 1, maxValue: 100, name: 'GB' }],
            deviceAlertType: DeviceAlertTypeEnum.DiskUsage,
            inheritanceType: DeviceThresholdInheritanceTypeEnum.Inherit,
            inheritFrom: 'System',
            inheritedFromPath: '',
            intervals: {
                clear: 300,
                trigger: 600
            },
            metrics: {
                critical: 4000,
                warning: 2000,
                error: 3000
            },
            name: 'D:'
        }],
        memoryThresholds: {
            deviceAlertThresholdType: DeviceAlertThresholdTypeEnum.Percentage_Used,
            deviceAlertThresholdTypes: [{ id: 2, maxValue: 100, name: '%' }, { id: 1, maxValue: 100, name: 'GB' }],
            deviceAlertType: DeviceAlertTypeEnum.Memory,
            inheritanceType: DeviceThresholdInheritanceTypeEnum.Inherit,
            inheritFrom: 'System',
            inheritedFromPath: '',
            intervals: {
                clear: 600,
                trigger: 900
            },
            metrics: {
                critical: 11000,
                warning: 5000,
                error: 10000
            },
            name: ''
        }
    };

    const intervals: DeviceThresholdInterval[] = [{
        value: 1,
        name: 'Immediate'
    }, {
        value: 300,
        name: '5 minutes'
    },
    {
        value: 600,
        name: '10 minutes'
    },
    {
        value: 900,
        name: '15 minutes'
    }];

    const inheritanceTypes: DeviceThresholdInheritanceType[] = [
        {
            id: 1,
            name: 'Inherit',
            typeId: DeviceThresholdInheritanceTypeEnum.Inherit
        },
        {
            id: 2,
            name: 'Override',
            typeId: DeviceThresholdInheritanceTypeEnum.Override
        },
        {
            id: 3,
            name: 'Disable',
            typeId: DeviceThresholdInheritanceTypeEnum.Disable
        },
    ];

    let cpuFormGroup: FormGroup<DeviceComponentThresholdsForm>;
    let cpuMetricsFormGroup: FormGroup<DeviceThresholdsMetricsForm>;
    let cpuIntervalFormGroup: FormGroup<DeviceThresholdIntervalsForm>;

    let memoryFormGroup: FormGroup<DeviceComponentThresholdsForm>;
    let memoryMetricsFormGroup: FormGroup<DeviceThresholdsMetricsForm>;
    let memoryIntervalFormGroup: FormGroup<DeviceThresholdIntervalsForm>;

    let agentFormGroup: FormGroup<DeviceComponentThresholdsForm>;
    let agentMetricsFormGroup: FormGroup<DeviceThresholdsMetricsForm>;
    let agentIntervalFormGroup: FormGroup<DeviceThresholdIntervalsForm>;

    let disk1FormGroup: FormGroup<DeviceComponentThresholdsForm>;
    let disk1MetricsFormGroup: FormGroup<DeviceThresholdsMetricsForm>;
    let disk1IntervalFormGroup: FormGroup<DeviceThresholdIntervalsForm>;

    let disk2FormGroup: FormGroup<DeviceComponentThresholdsForm>;
    let disk2MetricsFormGroup: FormGroup<DeviceThresholdsMetricsForm>;
    let disk2IntervalFormGroup: FormGroup<DeviceThresholdIntervalsForm>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                FormBuilder,
                provideMock(UserContextService),
                provideMock(OrganizationThresholdsService),
                provideMock(DevicesService),
                provideMock(DeviceFolderService),
                provideMock(OrganizationSharedService)
            ],
            imports: [
                DeviceThresholdsComponent
            ]
        });

        mockOrganizationService = TestBed.inject(OrganizationSharedService) as jasmine.SpyObj<OrganizationSharedService>;
        mockOrganizationService.getOrganizationById.and.returnValue(of(getOrganizationResult));

        mockDeviceFolderService = TestBed.inject(DeviceFolderService) as jasmine.SpyObj<DeviceFolderService>;
        mockDeviceFolderService.getOrganizationFolders.and.returnValue(of({ data: null, message: '' }));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1
        } as UserContext;

        mockDeviceThresholdsService = TestBed.inject(OrganizationThresholdsService) as jasmine.SpyObj<OrganizationThresholdsService>;
        mockDeviceThresholdsService.getThresholdForDeviceComponent.and.returnValue(of({ data: deviceThresholds.cpuThresholds, message: '' }));
        mockDeviceThresholdsService.getThresholdsMetadata.and.returnValue(of({
            data: { intervals, inheritanceTypes }, message: 'sucess'
        }));

        mockDeviceService = TestBed.inject(DevicesService) as jasmine.SpyObj<DevicesService>;
        mockDeviceService.updateThresholds.and.returnValue(of({ message: '' }));
        mockDeviceService.getThresholds.and.returnValue(of({
            data: deviceThresholds,
            message: ''
        }));

        mockDeviceService.getDefaultThresholds.withArgs(1, { deviceAlertTypeId: DeviceAlertTypeEnum.CPU, name: '', useCurrentLevel: false }).and.returnValue(of({ data: deviceThresholds.cpuThresholds, message: '' }));
        mockDeviceService.getDefaultThresholds.withArgs(1, { deviceAlertTypeId: DeviceAlertTypeEnum.DiskUsage, name: 'C:', useCurrentLevel: false }).and.returnValue(of({ data: deviceThresholds.diskThresholds[0], message: '' }));
        mockDeviceService.getDefaultThresholds.withArgs(1, { deviceAlertTypeId: DeviceAlertTypeEnum.DiskUsage, name: 'D:', useCurrentLevel: false }).and.returnValue(of({ data: deviceThresholds.diskThresholds[1], message: '' }));
        mockDeviceService.getDefaultThresholds.withArgs(1, { deviceAlertTypeId: DeviceAlertTypeEnum.Memory, name: '', useCurrentLevel: false }).and.returnValue(of({ data: deviceThresholds.memoryThresholds, message: '' }));
        mockDeviceService.getDefaultThresholds.withArgs(1, { deviceAlertTypeId: DeviceAlertTypeEnum.Heartbeat, name: '', useCurrentLevel: false }).and.returnValue(of({ data: deviceThresholds.agentThresholds, message: '' }));

        fixture = TestBed.createComponent(DeviceThresholdsComponent);
        component = fixture.componentInstance;
        component.deviceThresholds = deviceThresholds;
        component.thresholdsEntityId = 1;
        component.useCurrentLevelForInheritance = false;

        fixture.detectChanges();

        cpuFormGroup = component.form.controls.cpu;
        cpuIntervalFormGroup = cpuFormGroup.controls.intervals;
        cpuMetricsFormGroup = cpuFormGroup.controls.metrics;

        memoryFormGroup = component.form.controls.memory;
        memoryIntervalFormGroup = memoryFormGroup.controls.intervals;
        memoryMetricsFormGroup = memoryFormGroup.controls.metrics;

        agentFormGroup = component.form.controls.agent;
        agentIntervalFormGroup = agentFormGroup.controls.intervals;
        agentMetricsFormGroup = agentFormGroup.controls.metrics;

        disk1FormGroup = component.form.controls.disks.controls[0];
        disk1IntervalFormGroup = disk1FormGroup.controls.intervals;
        disk1MetricsFormGroup = disk1FormGroup.controls.metrics;

        disk2FormGroup = component.form.controls.disks.controls[1];
        disk2IntervalFormGroup = disk2FormGroup.controls.intervals;
        disk2MetricsFormGroup = disk2FormGroup.controls.metrics;
    });

    describe('Initialize', () => {

        it('should build the form and initialize default values', () => {
            expect(component.deviceThresholds).toEqual(deviceThresholds);
            expect(component.inheritanceTypes).toEqual(inheritanceTypes);
            expect(component.intervals).toEqual(intervals);

            expect(cpuFormGroup.controls.name.value).toEqual(deviceThresholds.cpuThresholds.name);
            expect(cpuFormGroup.controls.deviceAlertType.value).toEqual(deviceThresholds.cpuThresholds.deviceAlertType);
            expect(cpuFormGroup.controls.inheritanceType.value).toEqual(deviceThresholds.cpuThresholds.inheritanceType);
            expect(cpuFormGroup.controls.deviceAlertThresholdType.value).toEqual(deviceThresholds.cpuThresholds.deviceAlertThresholdType);

            expect(cpuIntervalFormGroup.controls.clear.value).toEqual(deviceThresholds.cpuThresholds.intervals.clear);
            expect(cpuIntervalFormGroup.controls.trigger.value).toEqual(deviceThresholds.cpuThresholds.intervals.trigger);

            expect(cpuMetricsFormGroup.controls.warning.value).toEqual(deviceThresholds.cpuThresholds.metrics.warning);
            expect(cpuMetricsFormGroup.controls.error.value).toEqual(deviceThresholds.cpuThresholds.metrics.error);
            expect(cpuMetricsFormGroup.controls.critical.value).toEqual(deviceThresholds.cpuThresholds.metrics.critical);

            expect(memoryFormGroup.controls.name.value).toEqual(deviceThresholds.memoryThresholds.name);
            expect(memoryFormGroup.controls.deviceAlertType.value).toEqual(deviceThresholds.memoryThresholds.deviceAlertType);
            expect(memoryFormGroup.controls.inheritanceType.value).toEqual(deviceThresholds.memoryThresholds.inheritanceType);
            expect(memoryFormGroup.controls.deviceAlertThresholdType.value).toEqual(deviceThresholds.memoryThresholds.deviceAlertThresholdType);
            expect(memoryIntervalFormGroup.controls.clear.value).toEqual(deviceThresholds.memoryThresholds.intervals.clear);
            expect(memoryIntervalFormGroup.controls.trigger.value).toEqual(deviceThresholds.memoryThresholds.intervals.trigger);
            expect(memoryMetricsFormGroup.controls.warning.value).toEqual(deviceThresholds.memoryThresholds.metrics.warning);
            expect(memoryMetricsFormGroup.controls.error.value).toEqual(deviceThresholds.memoryThresholds.metrics.error);
            expect(memoryMetricsFormGroup.controls.critical.value).toEqual(deviceThresholds.memoryThresholds.metrics.critical);

            expect(agentFormGroup.controls.name.value).toEqual(deviceThresholds.agentThresholds.name);
            expect(agentFormGroup.controls.deviceAlertType.value).toEqual(deviceThresholds.agentThresholds.deviceAlertType);
            expect(agentFormGroup.controls.inheritanceType.value).toEqual(deviceThresholds.agentThresholds.inheritanceType);
            expect(agentFormGroup.controls.deviceAlertThresholdType.value).toEqual(deviceThresholds.agentThresholds.deviceAlertThresholdType);
            expect(agentIntervalFormGroup.controls.clear.value).toEqual(deviceThresholds.agentThresholds.intervals.clear);
            expect(agentIntervalFormGroup.controls.trigger.value).toEqual(deviceThresholds.agentThresholds.intervals.trigger);
            expect(agentMetricsFormGroup.controls.warning.value).toEqual(deviceThresholds.agentThresholds.metrics.warning);
            expect(agentMetricsFormGroup.controls.error.value).toEqual(deviceThresholds.agentThresholds.metrics.error);
            expect(agentMetricsFormGroup.controls.critical.value).toEqual(deviceThresholds.agentThresholds.metrics.critical);

            expect(disk1FormGroup.controls.name.value).toEqual(deviceThresholds.diskThresholds[0].name);
            expect(disk1FormGroup.controls.deviceAlertType.value).toEqual(deviceThresholds.diskThresholds[0].deviceAlertType);
            expect(disk1FormGroup.controls.inheritanceType.value).toEqual(deviceThresholds.diskThresholds[0].inheritanceType);
            expect(disk1FormGroup.controls.deviceAlertThresholdType.value).toEqual(deviceThresholds.diskThresholds[0].deviceAlertThresholdType);
            expect(disk1IntervalFormGroup.controls.clear.value).toEqual(deviceThresholds.diskThresholds[0].intervals.clear);
            expect(disk1IntervalFormGroup.controls.trigger.value).toEqual(deviceThresholds.diskThresholds[0].intervals.trigger);
            expect(disk1MetricsFormGroup.controls.warning.value).toEqual(deviceThresholds.diskThresholds[0].metrics.warning);
            expect(disk1MetricsFormGroup.controls.error.value).toEqual(deviceThresholds.diskThresholds[0].metrics.error);
            expect(disk1MetricsFormGroup.controls.critical.value).toEqual(deviceThresholds.diskThresholds[0].metrics.critical);

            expect(disk2FormGroup.controls.name.value).toEqual(deviceThresholds.diskThresholds[1].name);
            expect(disk2FormGroup.controls.deviceAlertType.value).toEqual(deviceThresholds.diskThresholds[1].deviceAlertType);
            expect(disk2FormGroup.controls.inheritanceType.value).toEqual(deviceThresholds.diskThresholds[1].inheritanceType);
            expect(disk2FormGroup.controls.deviceAlertThresholdType.value).toEqual(deviceThresholds.diskThresholds[1].deviceAlertThresholdType);
            expect(disk2IntervalFormGroup.controls.clear.value).toEqual(deviceThresholds.diskThresholds[1].intervals.clear);
            expect(disk2IntervalFormGroup.controls.trigger.value).toEqual(deviceThresholds.diskThresholds[1].intervals.trigger);
            expect(disk2MetricsFormGroup.controls.warning.value).toEqual(deviceThresholds.diskThresholds[1].metrics.warning);
            expect(disk2MetricsFormGroup.controls.error.value).toEqual(deviceThresholds.diskThresholds[1].metrics.error);
            expect(disk2MetricsFormGroup.controls.critical.value).toEqual(deviceThresholds.diskThresholds[1].metrics.critical);
            expect(component.form.valid).toBeTrue();
        });

    });

    describe('CPU Metrics interaction', () => {

        let cpuDeviceThresholdsMetricsComponent: DebugElement;
        let warningInput: HTMLInputElement;
        let errorInput: HTMLInputElement;
        let criticalInput: HTMLInputElement;

        beforeEach(() => {
            cpuDeviceThresholdsMetricsComponent = fixture.debugElement.query(By.directive(DeviceThresholdsMetricsComponent));
            warningInput = cpuDeviceThresholdsMetricsComponent.queryAll(By.css('.form-control'))[0].nativeElement;
            errorInput = cpuDeviceThresholdsMetricsComponent.queryAll(By.css('.form-control'))[1].nativeElement;
            criticalInput = cpuDeviceThresholdsMetricsComponent.queryAll(By.css('.form-control'))[2].nativeElement;
        });

        it('should make the form invalid when warning is greater than error and critical when % is selected', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(DeviceAlertThresholdTypeEnum.Percentage_Used);
            warningInput.value = '80';
            errorInput.value = '10';
            criticalInput.value = '70';
            cpuMetricsFormGroup.markAllAsTouched();
            warningInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger')).nativeElement;
            expect(component.form.valid).toBeFalse();
            expect(error.textContent.toString().trim()).toBe('Warning must be less than Error value.');
        });

        it('should make the form invalid when error is greater than warning when GB is selected', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(DeviceAlertThresholdTypeEnum.GB_Free);
            warningInput.value = '10';
            errorInput.value = '80';
            criticalInput.value = '5';

            cpuMetricsFormGroup.markAllAsTouched();
            warningInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger')).nativeElement;
            expect(component.form.valid).toBeFalse();
            expect(error.textContent.toString().trim()).toBe('Error must be less than Warning value.');
        });

        it('should make the form invalid when critical is greater than warning when GB is selected', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(DeviceAlertThresholdTypeEnum.GB_Free);
            warningInput.value = '10';
            errorInput.value = '12';
            criticalInput.value = '15';

            cpuMetricsFormGroup.markAllAsTouched();
            warningInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger')).nativeElement;
            expect(component.form.valid).toBeFalse();
            expect(error.textContent.toString().trim()).toBe('Error must be less than Warning value.');
        });

        it('should make the form invalid when warning is greater than maximum value', () => {
            warningInput.value = '101';
            errorInput.value = '12';
            criticalInput.value = '15';
            cpuMetricsFormGroup.markAllAsTouched();

            warningInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger')).nativeElement;
            expect(component.form.valid).toBeFalse();
            expect(error.textContent.toString().trim()).toBe('Warning value must be smaller than 100.');
        });

        it('should make the form invalid when warning is not a number', () => {
            warningInput.value = 'ABC';
            errorInput.value = '12';
            criticalInput.value = '15';
            cpuMetricsFormGroup.markAllAsTouched();
            warningInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            expect(component.form.valid).toBeTrue();
        });

        it('should make the form invalid when warning is greater than error', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(DeviceAlertThresholdTypeEnum.Percentage_Used);
            warningInput.value = '25';
            errorInput.value = '10';
            criticalInput.value = '30';
            cpuMetricsFormGroup.markAllAsTouched();
            warningInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger')).nativeElement;
            expect(component.form.valid).toBeFalse();
            expect(error.innerText).toBe('Warning must be less than Error value.');
        });

        it('should make the form invalid when error is greater than critical', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(DeviceAlertThresholdTypeEnum.Percentage_Used);
            warningInput.value = '30';
            errorInput.value = '40';
            criticalInput.value = '35';
            cpuMetricsFormGroup.markAllAsTouched();
            warningInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger')).nativeElement;
            expect(component.form.valid).toBeFalse();
            expect(error.innerText).toBe('Error must be less than Critical value.');
        });

        it('should make the form invalid when critical is less than error', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(DeviceAlertThresholdTypeEnum.Percentage_Used);
            warningInput.value = '8';
            criticalInput.value = '7';
            errorInput.value = '10';
            cpuMetricsFormGroup.markAllAsTouched();
            criticalInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            warningInput.dispatchEvent(new Event('input'));

            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger')).nativeElement;
            expect(component.form.valid).toBeFalse();
            expect(error.innerText).toBe('Error must be less than Critical value.');
        });

        it('should make the form valid when critical is changed from invalid to valid', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(2);
            errorInput.value = '25';
            criticalInput.value = '13';
            warningInput.value = '10';

            cpuMetricsFormGroup.markAllAsTouched();
            criticalInput.dispatchEvent(new Event('input'));
            errorInput.dispatchEvent(new Event('input'));
            warningInput.dispatchEvent(new Event('input'));

            fixture.detectChanges();
            expect(component.form.valid).toBeFalse();

            criticalInput.value = '30';
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger'));
            expect(component.form.valid).toBeTrue();
            expect(error).toBeNull();
        });

        it('should make the form valid when critical exceeds max', () => {
            criticalInput.value = '101';
            cpuMetricsFormGroup.markAllAsTouched();
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(component.form.valid).toBeFalse();
        });

        it('should make the form valid when values change to a new valid range', () => {
            cpuFormGroup.controls.deviceAlertThresholdType.setValue(DeviceAlertThresholdTypeEnum.Percentage_Used);
            warningInput.value = '10';
            criticalInput.value = '40';
            errorInput.value = '30';
            cpuMetricsFormGroup.markAllAsTouched();
            errorInput.dispatchEvent(new Event('input'));
            warningInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            expect(component.form.valid).toBeTrue();

            warningInput.value = '20';
            criticalInput.value = '31';
            errorInput.value = '30';
            errorInput.dispatchEvent(new Event('input'));
            warningInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));

            fixture.detectChanges();
            expect(component.form.valid).toBeTrue();

            warningInput.value = '25';
            criticalInput.value = '31';
            errorInput.value = '30';
            errorInput.dispatchEvent(new Event('input'));
            warningInput.dispatchEvent(new Event('input'));
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const error = cpuDeviceThresholdsMetricsComponent.query(By.css('.text-danger'));
            expect(component.form.valid).toBeTrue();
            expect(error).toBeNull();
        });

        it('should make the form invalid when warning is not a number', () => {
            warningInput.value = 'invalid';
            warningInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(warningInput.value).toEqual('');
            expect(component.form.valid).toBeTrue();
        });

        it('should make the form invalid when error is not a number', () => {
            errorInput.value = 'invalid';
            errorInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(errorInput.value).toEqual('');
            expect(component.form.valid).toBeTrue();
        });

        it('should make the form invalid when critical is not a number', () => {
            criticalInput.value = 'invalid';
            criticalInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            expect(criticalInput.value).toEqual('');
            expect(component.form.valid).toBeTrue();
        });
    });

    describe('CPU Inheritance interaction', () => {

        let inheritanceInput: HTMLSelectElement;
        let cpuDeviceComponentThresholdsComponent: DebugElement;
        let updateFormSpy: jasmine.Spy;

        beforeEach(() => {
            cpuDeviceComponentThresholdsComponent = fixture.debugElement.query(By.directive(DeviceComponentThresholdsComponent));
            inheritanceInput = cpuDeviceComponentThresholdsComponent.queryAll(By.css('.form-select'))[0].nativeElement;
            updateFormSpy = spyOn(cpuDeviceComponentThresholdsComponent.componentInstance, 'updateForm').and.callThrough();
        });

        it('should clear and disable the form when Inheritance is Disable', () => {
            inheritanceInput.selectedIndex = 2;
            inheritanceInput.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(component.form.valid).toBeTrue();

            expect(cpuMetricsFormGroup.controls.warning.disabled).toBeTrue();
            expect(cpuMetricsFormGroup.controls.error.disabled).toBeTrue();
            expect(cpuMetricsFormGroup.controls.critical.disabled).toBeTrue();
            expect(cpuIntervalFormGroup.controls.clear.disabled).toBeTrue();
            expect(cpuIntervalFormGroup.controls.trigger.disabled).toBeTrue();

            expect(cpuMetricsFormGroup.controls.warning.value).toBe(null);
            expect(cpuMetricsFormGroup.controls.error.value).toBe(null);
            expect(cpuMetricsFormGroup.controls.critical.value).toBe(null);
            expect(cpuIntervalFormGroup.controls.clear.value).toBe(null);
            expect(cpuIntervalFormGroup.controls.trigger.value).toBe(null);

            expect(updateFormSpy).not.toHaveBeenCalled();
        });

        it('should update the form when Inheritance is Inherit', () => {

            mockDeviceThresholdsService.getThresholdForDeviceComponent.and.returnValue(of({
                data: deviceThresholds.cpuThresholds,
                message: ''
            }));

            inheritanceInput.selectedIndex = 1;
            inheritanceInput.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            inheritanceInput.selectedIndex = 0;
            inheritanceInput.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(component.form.valid).toBeTrue();

            expect(cpuMetricsFormGroup.controls.warning.disabled).toBeTrue();
            expect(cpuMetricsFormGroup.controls.error.disabled).toBeTrue();
            expect(cpuMetricsFormGroup.controls.critical.disabled).toBeTrue();
            expect(cpuIntervalFormGroup.controls.clear.disabled).toBeTrue();
            expect(cpuIntervalFormGroup.controls.trigger.disabled).toBeTrue();

            expect(updateFormSpy).toHaveBeenCalledTimes(1);
            expect(mockDeviceService.getDefaultThresholds).toHaveBeenCalledWith(1, {
                deviceAlertTypeId: deviceThresholds.cpuThresholds.deviceAlertType,
                name: deviceThresholds.cpuThresholds.name,
                useCurrentLevel: false
            });
            expect(cpuFormGroup.controls.name.value).toEqual(deviceThresholds.cpuThresholds.name);
            expect(cpuFormGroup.controls.deviceAlertType.value).toEqual(deviceThresholds.cpuThresholds.deviceAlertType);

            expect(cpuIntervalFormGroup.controls.clear.value).toEqual(deviceThresholds.cpuThresholds.intervals.clear);
            expect(cpuIntervalFormGroup.controls.trigger.value).toEqual(deviceThresholds.cpuThresholds.intervals.trigger);

            expect(cpuMetricsFormGroup.controls.warning.value).toEqual(deviceThresholds.cpuThresholds.metrics.warning);
            expect(cpuMetricsFormGroup.controls.error.value).toEqual(deviceThresholds.cpuThresholds.metrics.error);
            expect(cpuMetricsFormGroup.controls.critical.value).toEqual(deviceThresholds.cpuThresholds.metrics.critical);
        });
    });

    describe('Memory Inheritance interaction', () => {

        let inheritanceInput: HTMLSelectElement;

        beforeEach(() => {
            const thresholdsComponents = fixture.debugElement.queryAll(By.directive(DeviceComponentThresholdsComponent));
            const ramDeviceComponentThresholdsComponent = thresholdsComponents[1];

            inheritanceInput = ramDeviceComponentThresholdsComponent.queryAll(By.css('.form-select'))[0].nativeElement;
        });

        it('should clear and disable the form when Inheritance is Disable', () => {
            inheritanceInput.selectedIndex = 2;
            inheritanceInput.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(component.form.valid).toBeTrue();

            expect(memoryMetricsFormGroup.controls.warning.disabled).toBeTrue();
            expect(memoryMetricsFormGroup.controls.error.disabled).toBeTrue();
            expect(memoryMetricsFormGroup.controls.critical.disabled).toBeTrue();
            expect(memoryIntervalFormGroup.controls.clear.disabled).toBeTrue();
            expect(memoryIntervalFormGroup.controls.trigger.disabled).toBeTrue();

            expect(memoryMetricsFormGroup.controls.warning.value).toBe(null);
            expect(memoryMetricsFormGroup.controls.error.value).toBe(null);
            expect(memoryMetricsFormGroup.controls.critical.value).toBe(null);
            expect(memoryIntervalFormGroup.controls.clear.value).toBe(null);
            expect(memoryIntervalFormGroup.controls.trigger.value).toBe(null);
        });
    });

    describe('Agent Unreachable Inheritance interaction', () => {

        let inheritanceInput: HTMLSelectElement;

        beforeEach(() => {
            const thresholdsComponents = fixture.debugElement.queryAll(By.directive(DeviceComponentThresholdsComponent));
            const agentDownDeviceComponentThresholdsComponent = thresholdsComponents[2];

            inheritanceInput = agentDownDeviceComponentThresholdsComponent.queryAll(By.css('.form-select'))[0].nativeElement;
        });

        it('should clear and disable the form when Inheritance is Disable', () => {
            inheritanceInput.selectedIndex = 2;
            inheritanceInput.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(component.form.valid).toBeTrue();
            expect(agentIntervalFormGroup.controls.trigger.disabled).toBeTrue();
            expect(agentIntervalFormGroup.controls.clear.disabled).toBeTrue();
            expect(agentIntervalFormGroup.controls.trigger.value).toBe(null);
            expect(agentIntervalFormGroup.controls.trigger.value).toBe(null);
        });
    });

    describe('Disk C Inheritance interaction', () => {

        let inheritanceInput: HTMLSelectElement;

        beforeEach(() => {
            const thresholdsComponents = fixture.debugElement.queryAll(By.directive(DeviceComponentThresholdsComponent));
            const diskDeviceComponentThresholdsComponent = thresholdsComponents[3];

            inheritanceInput = diskDeviceComponentThresholdsComponent.queryAll(By.css('.form-select'))[0].nativeElement;
        });

        it('should clear and disable the form when Inheritance is Disable', () => {
            inheritanceInput.selectedIndex = 2;
            inheritanceInput.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(component.form.valid).toBeTrue();

            expect(disk1MetricsFormGroup.controls.warning.disabled).toBeTrue();
            expect(disk1MetricsFormGroup.controls.error.disabled).toBeTrue();
            expect(disk1MetricsFormGroup.controls.critical.disabled).toBeTrue();
            expect(disk1IntervalFormGroup.controls.clear.disabled).toBeTrue();
            expect(disk1IntervalFormGroup.controls.trigger.disabled).toBeTrue();

            expect(disk1MetricsFormGroup.controls.warning.value).toBe(null);
            expect(disk1MetricsFormGroup.controls.error.value).toBe(null);
            expect(disk1MetricsFormGroup.controls.critical.value).toBe(null);
            expect(disk1IntervalFormGroup.controls.clear.value).toBe(null);
            expect(disk1IntervalFormGroup.controls.trigger.value).toBe(null);
        });
    });

    describe('Disk D Inheritance interaction', () => {

        let inheritanceInput: HTMLSelectElement;

        beforeEach(() => {
            const thresholdsComponents = fixture.debugElement.queryAll(By.directive(DeviceComponentThresholdsComponent));
            const diskDeviceComponentThresholdsComponent = thresholdsComponents[4];

            inheritanceInput = diskDeviceComponentThresholdsComponent.queryAll(By.css('.form-select'))[0].nativeElement;
        });

        it('should clear and disable the form when Inheritance is Disable', () => {
            inheritanceInput.selectedIndex = 2;
            inheritanceInput.dispatchEvent(new Event('change'));
            fixture.detectChanges();

            expect(component.form.valid).toBeTrue();

            expect(disk2MetricsFormGroup.controls.warning.disabled).toBeTrue();
            expect(disk2MetricsFormGroup.controls.error.disabled).toBeTrue();
            expect(disk2MetricsFormGroup.controls.critical.disabled).toBeTrue();
            expect(disk2IntervalFormGroup.controls.clear.disabled).toBeTrue();
            expect(disk2IntervalFormGroup.controls.trigger.disabled).toBeTrue();

            expect(disk2MetricsFormGroup.controls.warning.value).toBe(null);
            expect(disk2MetricsFormGroup.controls.error.value).toBe(null);
            expect(disk2MetricsFormGroup.controls.critical.value).toBe(null);
            expect(disk2IntervalFormGroup.controls.clear.value).toBe(null);
            expect(disk2IntervalFormGroup.controls.trigger.value).toBe(null);
        });
    });
});
