import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { CloudInfraAccountService } from '@app/modules/cloud-infrastructure/services/cloud-infra-account.service';
import { CloudInfraDomainService } from '@app/modules/cloud-infrastructure/services/cloud-infra-domain.service';
import { CloudInfraZoneService } from '@app/modules/cloud-infrastructure/services/cloud-infra-zone.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { ConfirmationDialogComponent } from '@app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { VpnUser } from '../../models/vpn-user';
import { NetworkingPermissionService } from '../../services/networking-permission.service';
import { VpnUsersService } from '../../services/vpn-users.service';
import { CreateVpnUserComponent } from '../create-vpn-user/create-vpn-user.component';
import { ListRemoteAccessVpnUsersComponent } from './list-remote-access-vpn-users.component';

describe('RemoteAccessVpnUsersComponent', () => {

    let fixture: ComponentFixture<ListRemoteAccessVpnUsersComponent>;
    let component: ListRemoteAccessVpnUsersComponent;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVpnUsersService: jasmine.SpyObj<VpnUsersService>;
    let mockNetworkingPermissionService: jasmine.SpyObj<NetworkingPermissionService>;
    let mockModalService: jasmine.SpyObj<ModalService>;
    let mockCloudInfraZonesService: jasmine.SpyObj<CloudInfraZoneService>;
    let vpnUsersResponse: VpnUser[];

    beforeEach(() => {

        vpnUsersResponse = [
            {
                account: 'account1',
                domainid: 'domain1',
                domain: 'domain 1',
                id: '1',
                state: 'Active',
                username: 'vpnuser 1',
            },
            {
                account: 'account2',
                domainid: 'domain2',
                domain: 'domain 2',
                id: '2',
                state: 'Add',
                username: 'vpnuser 2',
            },
            {
                account: 'account3',
                domainid: 'domain3',
                domain: 'domain 3',
                id: '3',
                state: 'Add',
                username: 'vpnuser 3',
            },

            {
                account: 'account3',
                domainid: 'domain3',
                domain: 'domain 4',
                id: '4',
                state: 'Revoke',
                username: 'vpnuser 4',
            },
        ];

        TestBed.configureTestingModule({
            imports: [
                ListRemoteAccessVpnUsersComponent
            ],
            providers: [
                provideMock(VpnUsersService),
                provideMock(UserContextService),
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraAccountService),
                provideMock(CloudInfraDomainService),
                provideMock(ModalService),
                provideMock(NetworkingPermissionService),
                provideMock(ModalService),
                provideMock(NgbActiveModal),
                provideMock(CloudInfraZoneService),
                ZoneDomainAccountStore,
                CreateVpnUserComponent,
                ConfirmationDialogComponent
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        mockUserContextService.currentUser = {
            organizationId: 1,
            cloudInfraUserContext: {
                accountName: 'account',
                domainId: 'domainId',
                hasMappedDomain: true,
            } as CloudInfraUserContext
        } as UserContext;

        mockVpnUsersService = TestBed.inject(VpnUsersService) as jasmine.SpyObj<VpnUsersService>;
        mockVpnUsersService.getVpnUsers.and.returnValue(of(vpnUsersResponse));

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;
        mockNetworkingPermissionService = TestBed.inject(NetworkingPermissionService) as jasmine.SpyObj<NetworkingPermissionService>;

        mockCloudInfraZonesService = TestBed.inject(CloudInfraZoneService) as jasmine.SpyObj<CloudInfraZoneService>;
        mockCloudInfraZonesService.getZones.and.returnValue(of([{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }]));

        fixture = TestBed.createComponent(ListRemoteAccessVpnUsersComponent);
        component = fixture.componentInstance;
    });

    describe('Initialization', () => {

        it('should call getNetworks without account when context have a mapped domain', () => {
            fixture.detectChanges();
            expect(mockVpnUsersService.getVpnUsers).toHaveBeenCalledOnceWith();
        });

        it('should have the same amount of rows as data', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(vpnUsersResponse.length);
        });
    });

    describe('Data Binding', () => {

        it('should display the vpn user state and icon in the 1 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.circle.text-bg-success me-1')).toBeDefined();
            expect(rows[1].querySelector('.circle.text-bg-secondary.me-1')).toBeDefined();
            expect(rows[2].querySelector('.circle.text-bg-secondary.me-1')).toBeDefined();
            expect(rows[1].querySelector('.circle.text-bg-danger.me-1')).toBeDefined();
        });

        it('should display the vpn user name in the 2 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(vpnUsersResponse[0].username);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(vpnUsersResponse[1].username);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(vpnUsersResponse[2].username);
            expect(rows[3].querySelector('.datatable-body-cell:nth-child(2)').textContent.trim()).toEqual(vpnUsersResponse[3].username);
        });

        it('should display the vpn user domain in the 3 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(vpnUsersResponse[0].domain);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(vpnUsersResponse[1].domain);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(vpnUsersResponse[2].domain);
            expect(rows[3].querySelector('.datatable-body-cell:nth-child(3)').textContent.trim()).toEqual(vpnUsersResponse[3].domain);
        });

        it('should display the vpn user account in the 4 column', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows[0].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(vpnUsersResponse[0].account);
            expect(rows[1].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(vpnUsersResponse[1].account);
            expect(rows[2].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(vpnUsersResponse[2].account);
            expect(rows[3].querySelector('.datatable-body-cell:nth-child(4)').textContent.trim()).toEqual(vpnUsersResponse[3].account);
        });

    });

    describe('Create', () => {

        it('should open create modal when user has permission and an account is selected', () => {
            mockNetworkingPermissionService.canAddVpnUser.and.returnValue(true);
            component.store.setSelectedAccount({ id: 'account-id', name: 'Account 1', domainId: 'domain-id-123' });
            const modalRef = {
                closed: of(true),
                componentInstance: TestBed.inject(CreateVpnUserComponent)
            } as NgbModalRef;
            mockModalService.openModalComponent.and.returnValue(modalRef);

            fixture.detectChanges();

            const addButton = fixture.debugElement.query(By.css('[data-testid="add-vpn-user-button"]')).nativeElement as HTMLButtonElement;
            addButton.click();
            fixture.detectChanges();

            expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
        });

        it('should not open create modal when user has permission and an account is not selected', () => {
            mockNetworkingPermissionService.canAddVpnUser.and.returnValue(true);

            fixture.detectChanges();

            const addButton = fixture.debugElement.query(By.css('[data-testid="add-vpn-user-button"]')).nativeElement as HTMLButtonElement;
            addButton.click();
            fixture.detectChanges();

            expect(mockModalService.openModalComponent).not.toHaveBeenCalled();
        });

        it('should not display to create button when the user does not have permission', () => {
            mockNetworkingPermissionService.canAddVpnUser.and.returnValue(false);
            fixture.detectChanges();

            const addButton = fixture.debugElement.query(By.css('[data-testid="add-vpn-user-button"]'));
            expect(addButton).toBeNull();
        });

    });

    describe('Delete', () => {

        it('should not display action when the user does not have permission', () => {
            mockNetworkingPermissionService.canDeleteVpnUser.and.returnValue(true);
            const modalRef = {
                closed: of(true),
                componentInstance: TestBed.inject(ConfirmationDialogComponent)
            } as NgbModalRef;
            mockModalService.openDeleteConfirmationDialog.and.returnValue(modalRef);
            fixture.detectChanges();

            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));

            const deleteButtons = dataTableDebugElement.queryAll(By.directive(TableActionComponent));

            const deleteButton1 = deleteButtons[0].query(By.css('.table-action-container')).nativeElement as HTMLButtonElement;
            const deleteButton2 = deleteButtons[1].query(By.css('.table-action-container')).nativeElement as HTMLButtonElement;

            deleteButton1.click();

            fixture.detectChanges();

            expect(deleteButton2).toBeDefined();
            expect(mockModalService.openDeleteConfirmationDialog).toHaveBeenCalledOnceWith('Delete VPN User', 'Are you sure you want to delete this VPN user?', 'delete');
        });

        it('should not display action when the user does not have permission', () => {
            mockNetworkingPermissionService.canDeleteVpnUser.and.returnValue(false);
            fixture.detectChanges();

            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const deleteButtons = dataTableDebugElement.queryAll(By.directive(TableActionComponent));

            expect(deleteButtons.length).toBe(0);
        });

    });

});
