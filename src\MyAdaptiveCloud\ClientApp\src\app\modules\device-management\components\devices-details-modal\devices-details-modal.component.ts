import { ChangeDetectionStrategy, Component, Input, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { Observable, of } from 'rxjs';
import { DeviceTreeNode } from '../../models/device-tree-node';

@Component({
    selector: 'app-devices-details-modal',
    imports: [NgxDatatableModule, AutoSearchBoxComponent],
    templateUrl: './devices-details-modal.component.html',
    styleUrl: './devices-details-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})

export class DevicesDetailsModalComponent extends BaseListClientComponent<DeviceTreeNode> implements OnInit {
    protected readonly activeModal = inject(NgbActiveModal);

    readonly hostNameTemplate = viewChild<TemplateRef<never>>('hostNameTemplate');
    readonly pathTemplate = viewChild<TemplateRef<never>>('pathTemplate');

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) devices: DeviceTreeNode[];

    ngOnInit(): void {
        const columns: TableColumn[] = [
            {
                name: 'Device Name',
                prop: 'hostname',
                cellTemplate: this.hostNameTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: true,
            },
            {
                name: 'Path',
                prop: 'path',
                cellTemplate: this.pathTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: true,
            }
        ];

        super.initialize(() => this.getListData$(), columns);
    }

    getListData$(): Observable<ApiDataResult<DeviceTreeNode[]>> {
        this.sortDevicesAlphabetically();

        return of({
            data: this.devices,
            message: ''
        });
    }

    close() {
        this.activeModal.close();
    }

    private sortDevicesAlphabetically(): void {
        this.devices?.sort((a, b) => a.hostname.localeCompare(b.hostname));
    }
}
