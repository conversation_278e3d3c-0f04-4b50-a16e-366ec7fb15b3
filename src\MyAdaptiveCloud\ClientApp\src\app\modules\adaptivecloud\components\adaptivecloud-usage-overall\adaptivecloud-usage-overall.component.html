<div class="content-heading">
  Cloud Infrastructure - Usage
</div>

<div class="content-sub-heading">
  <form [formGroup]="form" class="form-horizontal">
    <ng-select placeholder="Select period" [clearable]="false" [searchable]="false" formControlName="period">
      <ng-template ng-label-tmp let-item="item">
        <span>{{ item | period }}</span>
      </ng-template>
      @for (period of periods$ | async; track period) {
        <ng-option [value]="period">
          <span>{{ period | period }}</span>
        </ng-option>
      }
    </ng-select>
  </form>
  <div class="action-buttons">
    <button [disabled]="form?.invalid" class="btn btn-primary" type="button" (click)="getSummary()">
      Download CSV
    </button>
  </div>
</div>

<div class="card card-default">
  <div class="card-body">
    <ngx-datatable #table class="table bootstrap no-detail-row" />
  </div>
</div>

<ng-template #normalCell let-row="row" let-column="column">
  <ng-template #popLicenseContent>
    @for (item of row[column.prop]['summary']; track item) {
      <div>
        <span>{{ item }}</span>
      </div>
    }
  </ng-template>
  @if (column.prop !== 'totals') {
    <div [ngbPopover]="popLicenseContent" popoverTitle="{{ column.name }}"
      triggers="hover" container="body">
      @if (row[column.prop]) {
        {{ row[column.prop]['cost'] ? (row[column.prop]['cost'] | currency) : '-' }}
      }
    </div>
  } @else {
    @if (row[column.prop]) {
      {{ row[column.prop]['cost'] ? (row[column.prop]['cost'] | currency) : '-' }}
    }
  }
</ng-template>

<ng-template #summaryCell let-column="column">
  <div class="summary-container">
    <div class="chip">
      <b><span class="chip-content">{{ totalsRow[column.prop] | currency }}</span></b>
    </div>
  </div>
</ng-template>
