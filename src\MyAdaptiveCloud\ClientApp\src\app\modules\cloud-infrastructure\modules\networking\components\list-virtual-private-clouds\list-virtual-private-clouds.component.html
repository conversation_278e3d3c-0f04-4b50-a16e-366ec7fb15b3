<div class="card-body">
    <div class="content-sub-heading d-flex justify-content-between">
        <div class="search-bar">
            <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" [dataItemName]="'searchTerm'" />
        </div>
        <div class="action-buttons">
            <app-filters-form [filters]="filters" (filtersApplied)="onFilterFormChanged()"
                (filtersReset)="onFilterFormChanged()">
                <app-list-virtual-private-clouds-filter [filters]="filters" [zones]="store.zones()" />
            </app-filters-form>
            @if (networkingPermissionService.canCreateVirtualPrivateCloud()) {
                <div class="d-inline" [title]="!store.getAccount() ? 'Select an account to create a Virtual Private Cloud' : ''">
                    <button class="btn btn-primary" [disabled]="!store.getAccount()"
                        (click)="openAddVirtualPrivateCloudModal()">
                        Create VPC
                    </button>
                </div>
            }
        </div>
    </div>

    <div class="content-sub-heading pt-0">
        <app-pill-filter [appliedFilters]="appliedFilters | async"
            (filterRemoved)="onFilterRemoved($event)" />
    </div>

    <div class="card card-default">
        <div class="card-body">
            <ngx-datatable #table class='table bootstrap no-detail-row' (sort)="onSorting($event)"
                (page)="onPageChanged($event)" />
        </div>
    </div>

    <ng-template #statusRow let-row="row">
        @if (toItem(row); as row) {
            @switch (row.state) {
                @case ('Enabled') {
                    <div>
                        <i [class]="'fa fa-check-circle text-success me-1 status-icon'" data-testid="status-icon"></i>
                        {{ row.state }}
                    </div>
                }
                @case ('Inactive') {
                    <div>
                        <i [class]="'fa fa-check-circle text-secondary me-1 status-icon'" data-testid="status-icon"></i>
                        {{ row.state }}
                    </div>
                }
            }
        }
    </ng-template>

    <ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
        <span (click)="sort()" class="clickable">
            {{ column.name }}
            <span
                [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
            </span>
        </span>
    </ng-template>

    <ng-template #actionsTemplate let-row="row">
        @if (toItem(row); as row) {
            @if (networkingPermissionService.canEditVirtualPrivateCloud()) {
                <app-table-action [icon]="'far fa-edit'" [title]="'Edit Virtual Private Cloud'" (clickHandler)="editVirtualPrivateCloud(row)" />
            }
            @if (networkingPermissionService.canRestartVirtualPrivateCloud()) {
                <app-table-action [icon]="'fa fa-solid fa-arrow-rotate-left'" [title]="'Restart Virtual Private Cloud'" (clickHandler)="restartVirtualPrivateCloud(row)" />
            }
            @if (networkingPermissionService.canDeleteVirtualPrivateCloud()) {
                <app-table-action [icon]="'far fa-trash-can'" [title]="'Delete Virtual Private Cloud'" (clickHandler)="deleteVirtualPrivateCloud(row.id)" />
            }
        }
    </ng-template>

</div>
