import { Agent } from './agent.model';
import { CssClassTextKeyPair } from '../../../shared/models/cssclass-text-keypair';

export class AgentViewModel extends Agent {
    public selected: boolean;
    public uptimeTooltip: string;
    public uptimeClass: string;
    public uptimeClassInline?: string;
    public cpuProgressBarColor: string;
    public ramProgressBarColor: string;
    public statusTooltipText: CssClassTextKeyPair[];
    public statusIconClass: string;
    public statusColorClass: string;

    public diskNameColorClass: Record<string, string> = {};
    public diskTextClass: Record<string, string> = {};
    public diskTextMessage: Record<string, string> = {};

    public scheduleDowntimeText: string;
    public organization: string;
}
