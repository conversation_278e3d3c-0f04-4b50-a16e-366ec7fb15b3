<div class="content-heading">
    Networking
</div>
<div class="d-flex">
    <div class="w-25">
        <div class="card card-default tree-container overflow-auto ms-0 rounded-0 me-4 pt-2">
            <app-domain-account-tree [domain]="store.mainDomain()" />
        </div>
    </div>
    <div class="w-75">
        <app-information-banner>
            Cloud Infrastructure Virtual Machines and its associated features are in a technical preview, which is for
            conducting
            testing, to obtain feedback and is provided on an �as is� basis.
        </app-information-banner>
        <div class="card-body">
            <div class="border-bottom mb-2">
                <app-breadcrumbs-nav></app-breadcrumbs-nav>
            </div>
            <ul class="manage-nav nav nav-underline">
                @if (networkingPermissionService.canViewNetworkList()) {
                    <li class="nav-item">
                        <a class="nav-link px-5" data-testid="networking-tab" aria-current="page"
                            [routerLink]="[NETWORKING_ROUTE_SEGMENTS.NETWORKS]" routerLinkActive="active"
                            [routerLinkActiveOptions]="{exact:false}">Networks</a>
                    </li>
                }
                @if (networkingPermissionService.canViewVirtualPrivateCloudList()) {
                    <li class="nav-item">
                        <a class="nav-link px-5" data-testid="vpc-tab"
                            [routerLink]="[NETWORKING_ROUTE_SEGMENTS.VIRTUAL_PRIVATE_CLOUDS]"
                            routerLinkActive="active">Virtual Private Clouds
                            <i class="ms-1 fa fa-question-circle"
                                title="VPCs allow for advanced networking functionality such as tiered networks and site-to-site VPNs"></i>
                        </a>
                    </li>
                }
                @if (networkingPermissionService.canViewRemoteVpnGatewaysList()) {
                    <li class="nav-item">
                        <a class="nav-link px-5" data-testid="remote-vpn-gateways-tab"
                            [routerLink]="[NETWORKING_ROUTE_SEGMENTS.REMOTE_VPN_GATEWAYS]" routerLinkActive="active">Remote
                            VPN Gateways
                        </a>
                    </li>
                }
                @if (networkingPermissionService.canViewVpnUserList()) {
                    <li class="nav-item">
                        <a class="nav-link px-5" data-testid="remote-access-vpn-tab"
                            [routerLink]="[NETWORKING_ROUTE_SEGMENTS.REMOTE_ACCESS_VPN_USERS]"
                            routerLinkActive="active">Remote Access VPN Users</a>
                    </li>
                }
            </ul>
            @if(store.zones()?.length) {
                <router-outlet />
            }
        </div>
    </div>
</div>
