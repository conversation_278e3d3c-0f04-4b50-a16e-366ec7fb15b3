@if (filters()?.form) {
    <form [formGroup]="filters().form">
        <div id="zone-group">
            <h6 class="card-title mb-3 fw-normal h5">{{filters().filterGroupOptions[0].groupName}}</h6>
            <div class="card-body">
                @for (zone of zones(); track zone.id) {
                    <div class="form-check mb-2">
                        <label class="form-check-label clickable" for="{{zone.id}}">{{
                            zone.name }}
                            <input type="radio" [checked]="true" class="form-check-input clickable" id="{{zone.id}}"
                                value="{{zone.id}}" formControlName="zoneId" /></label>
                    </div>
                }
            </div>
            <hr>
        </div>
    </form>
}
