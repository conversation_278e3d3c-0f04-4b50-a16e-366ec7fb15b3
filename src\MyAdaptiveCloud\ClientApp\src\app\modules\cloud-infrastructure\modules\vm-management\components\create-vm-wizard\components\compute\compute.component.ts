import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CPU_CUSTOM_OFFERING_MIN_VALUE, MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { UserContextService } from '@app/shared/services/user-context.service';
import { filter } from 'rxjs';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { ComputeStepCustomOfferingForm, ComputeStepForm } from '../../forms/compute-step.form';
import { CreateVmWizardConstants } from '../../models/create-vm-wizard.constants';
import { customOfferCpuNumberValidator, customOfferMemoryValidator } from './compute.component.validators';

@Component({
    selector: 'app-create-vm-wizard-compute',
    imports: [ReactiveFormsModule],
    templateUrl: './compute.component.html',
    styleUrls: ['../../create-vm-wizard-common.scss', './compute.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreateVmWizardComputeComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);
    public readonly store = inject(CreateVMWizardStore);
    private readonly userContextService = inject(UserContextService);

    protected readonly label = CreateVmWizardConstants.ComputeLabel;
    protected form: FormGroup<ComputeStepForm>;
    protected readonly presetServiceOfferings = this.store.computeStep.presetServiceOfferings;
    protected customServiceOffering = this.store.computeStep.customServiceOffering;
    protected readonly cpuCoresCustomOfferingMinValue = CPU_CUSTOM_OFFERING_MIN_VALUE;
    protected readonly cpuCoresCustomOfferingMaxValue = this.userContextService.currentUser.cloudInfraUserContext.cpuCustomOfferingMaxValue;
    protected readonly memoryCustomOfferingMinValue = MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB;
    protected readonly memoryCustomOfferingMaxValue = this.userContextService.currentUser.cloudInfraUserContext.memoryCustomOfferingMaxValue;

    ngOnInit(): void {
        this.buildForm();
    }

    private buildForm(): void {
        this.form = this.formBuilder.group<ComputeStepForm>({
            serviceOffering: this.formBuilder.control<string | null>(this.store.computeStep.form.serviceOffering()?.id, Validators.required),
            customServiceOffering: this.formBuilder.group<ComputeStepCustomOfferingForm>({
                cpuNumber: this.formBuilder.control<number | null>(
                    { value: this.customServiceOffering()?.cpuNumber, disabled: !this.customServiceOffering() || this.store.computeStep.form.serviceOffering()?.id !== this.customServiceOffering()?.id },
                    [customOfferCpuNumberValidator(this.cpuCoresCustomOfferingMaxValue), Validators.required]
                ),
                memory: this.formBuilder.control<number | null>(
                    { value: this.customServiceOffering()?.memory, disabled: !this.customServiceOffering() || this.store.computeStep.form.serviceOffering()?.id !== this.customServiceOffering()?.id },
                    [customOfferMemoryValidator(this.memoryCustomOfferingMaxValue), Validators.required]
                )
            })
        });

        this.form.controls.serviceOffering.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(value => {
                if (value === this.customServiceOffering()?.id) {
                    if (this.customServiceOffering()) {
                        this.form.controls.customServiceOffering.enable();
                        this.form.controls.customServiceOffering.updateValueAndValidity({ emitEvent: false });
                    }
                    this.store.setComputeStepFormValue({ serviceOffering: this.customServiceOffering() }, this.form.valid);
                } else {
                    if (this.customServiceOffering()) {
                        this.form.controls.customServiceOffering.disable();
                        this.form.controls.customServiceOffering.updateValueAndValidity({ emitEvent: false });
                    }
                    this.store.setComputeStepFormValue({ serviceOffering: this.presetServiceOfferings().find(so => so.id === value) }, true);
                }
            });

        this.form.controls.customServiceOffering.valueChanges
            .pipe(
                filter(() => !!this.customServiceOffering()),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(value => {
                this.form.controls.customServiceOffering.updateValueAndValidity({ emitEvent: false });
                this.store.setComputeStepCustomOfferFormValue(value.cpuNumber, value.memory);
                this.store.setComputeStepFormValue({ serviceOffering: this.customServiceOffering() }, this.form.valid);
            });
    }
}
