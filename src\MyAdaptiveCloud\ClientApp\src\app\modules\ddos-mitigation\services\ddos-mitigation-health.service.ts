import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiService } from '@app/shared/services/api.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { Observable } from 'rxjs';
import { DDOS_MITIGATION_ENDPOINT_SEGMENTS } from './ddos-mitigation-endpoint-segments';

@Injectable({
    providedIn: 'root'
})
export class DDoSMitigationHealthService {

    private readonly apiService = inject(ApiService);
    private readonly userContext = inject(UserContextService);

    healthcheck(): Observable<ApiDataResult<boolean>> {
        return this.apiService.get(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/health/${this.userContext.currentUser.organizationId}`);
    }

}

