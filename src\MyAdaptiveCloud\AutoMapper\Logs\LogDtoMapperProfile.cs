﻿using AutoMapper;
using MyAdaptiveCloud.Api.ViewModel.Logs;
using MyAdaptiveCloud.Services.DTOs.Logs;

namespace MyAdaptiveCloud.Api.AutoMapper.Logs
{
    public class LogDtoMapperProfile : Profile
    {
        public LogDtoMapperProfile()
        {
            CreateMap<ExtraInformationDTO, ExtraInformationViewModel>();
            CreateMap<ExtraInformationViewModel, ExtraInformationDTO>();
            CreateMap<LogEntryDTO, LogEntryViewModel>();
            CreateMap<LogEntryDetailDTO, LogEntryDetailViewModel>()
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.Level.ToString()));
            CreateMap<LogEntryDetailDTO, LogEntryDetailEntityViewModel>()
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.Level.ToString()));
        }
    }
}