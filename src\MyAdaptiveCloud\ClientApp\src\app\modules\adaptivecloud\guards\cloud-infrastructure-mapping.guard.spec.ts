import { TestBed } from '@angular/core/testing';
import { PermissionService } from '@app/shared/services/permission.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { CloudInfrastructureMappingGuard } from './cloud-infrastructure-mapping.guard';

describe('CloudInfrastructureMappingGuard', () => {
    let guard: CloudInfrastructureMappingGuard;
    let mockPermissionService: jasmine.SpyObj<PermissionService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(PermissionService),
                CloudInfrastructureMappingGuard
            ]
        });

        mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
        guard = TestBed.inject(CloudInfrastructureMappingGuard);
    });

    it('should return false when canViewCloudInfraMappings is not allowed ', () => {
        mockPermissionService.canViewCloudInfraMappings.and.returnValue(false);
        const expected = guard.canActivate();
        expect(expected).toBeFalse();
    });

    it('should return false when canViewCloudInfraMappings is not allowed ', () => {
        mockPermissionService.canViewCloudInfraMappings.and.returnValue(true);
        expect(guard.canActivate()).toBeTrue();
    });

});
