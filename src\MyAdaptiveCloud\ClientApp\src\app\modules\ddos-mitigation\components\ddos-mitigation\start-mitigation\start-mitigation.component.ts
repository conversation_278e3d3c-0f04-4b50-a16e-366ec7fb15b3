import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MitigationTypeEnum } from '@app/modules/ddos-mitigation/models/mitigation-type.enum';
import { MitigationValidation } from '@app/modules/ddos-mitigation/models/mitigation-validation.model';
import { StartDDoSMitigationForm } from '@app/modules/ddos-mitigation/models/start-ddos-mitigation.form';
import { DDoSMitigationBlackholeService } from '@app/modules/ddos-mitigation/services/ddos-mitigation-blackhole-service';
import { DDoSMitigationScrubService } from '@app/modules/ddos-mitigation/services/ddos-mitigation-scrub.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { filter } from 'rxjs';

@Component({
    selector: 'app-start-ddos-mitigation',
    imports: [
        BtnSubmitComponent,
        ReactiveFormsModule,
        NgbPopover
    ],
    templateUrl: './start-mitigation.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class StartMitigationComponent implements OnInit {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly formBuilder = inject(FormBuilder);
    private readonly ddosMitigationScrubService = inject(DDoSMitigationScrubService);
    private readonly ddosMitigationBlackholeService = inject(DDoSMitigationBlackholeService);
    private readonly destroyRef = inject(DestroyRef);
    private readonly notificationService = inject(NotificationService);

    protected readonly mitigationTypeEnum = MitigationTypeEnum;
    readonly mitigationType = signal<MitigationTypeEnum>(null);
    public readonly validatedCidr = signal<MitigationValidation | null>(null);
    public readonly isCidrValid = computed(() => !!this.validatedCidr()?.cidr);
    protected readonly cidrValidationInProgress = signal<boolean>(false);
    protected readonly mitigationInProgress = signal<boolean>(false);

    protected form: FormGroup<StartDDoSMitigationForm>;

    ngOnInit(): void {
        this.form = this.formBuilder.group({
            cidr: this.formBuilder.control<string>('', [Validators.required, Validators.maxLength(18)]),
            simulate: this.formBuilder.control<boolean>({ value: true, disabled: true })
        });

        this.form.controls.cidr.valueChanges.pipe(
            filter(() => !!this.validatedCidr()),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(() => {
            this.validatedCidr.set(null);
        });
    }

    protected validateCidr() {
        this.cidrValidationInProgress.set(true);
        if (this.form.controls.cidr.valid) {
            const service = this.mitigationType() === MitigationTypeEnum.Scrub ?
                this.ddosMitigationScrubService.validate(this.form.controls.cidr.value) :
                this.ddosMitigationBlackholeService.validate(this.form.controls.cidr.value);
            service
                .subscribe(res => {
                    if (res.data.cidr) {
                        this.form.controls.cidr.setValue(res.data.cidr);
                    }
                    this.validatedCidr.set(res.data);
                    if (this.isCidrValid()) {
                        this.form.controls.simulate.enable({ emitEvent: false });
                    } else {
                        this.form.controls.simulate.disable({ emitEvent: false });
                    }
                    this.cidrValidationInProgress.set(false);
                });
        }
    }

    protected submitForm() {
        if (this.validatedCidr()) {
            this.mitigationInProgress.set(true);
            const service = this.mitigationType() === MitigationTypeEnum.Scrub ?
                this.ddosMitigationScrubService.start(this.form.controls.cidr.value, this.form.controls.simulate.value) :
                this.ddosMitigationBlackholeService.start(this.form.controls.cidr.value, this.form.controls.simulate.value);
            service.subscribe(res => {
                this.notificationService.notify(res.message);
                this.activeModal.close({ taskId: res.data.taskId, cidr: this.form.controls.cidr.value, message: res.data.message, simulate: this.form.controls.simulate.value });
            });
        }
    }

}
