<div class="content-heading">
    Cloud Infrastructure - Usage Billing Run Logs
</div>

<div class="content-sub-heading">
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap no-detail-row" (sort)="onSorting($event)"
            (page)="onPageChanged($event)" />
    </div>
</div>
<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
    <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" [placeholder]="column.name"
        [dataItemName]="column.prop" />
</ng-template>

<ng-template #dateCellTemplate let-value="value">
    <span>
        {{ value ? (value | date: 'yyyy-MM-dd':'UTC') : '-' }}
    </span>
</ng-template>

<ng-template #errorCount let-value="value">
    @if (value > 0) {
    <span style="color:red;">
        {{ value}}
    </span>
    }
    @if (value <= 0) { <span>
        {{ value }}
        </span>
        }
</ng-template>


<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        <app-table-action [icon]="'fa-solid fa-eye'" [title]="'View'" (click)="viewDetails(row.id)" />
    }
</ng-template>
