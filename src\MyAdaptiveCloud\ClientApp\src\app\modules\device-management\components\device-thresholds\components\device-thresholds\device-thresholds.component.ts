import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, Input, OnInit, inject, input, output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DeviceAlertTypeEnum } from '@app/modules/device-management/models/device-alert-type.enum';
import { DeviceComponentThresholds } from '@app/modules/device-management/models/device-component-thresholds';
import { DeviceThresholds } from '@app/modules/device-management/models/device-thresholds';
import { Organization } from '@app/shared/models/organization.model';
import { OrganizationSharedService } from '@app/shared/services/organization-shared.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { distinctUntilChanged, forkJoin, map, of } from 'rxjs';
import { DeviceComponentThresholdsForm } from '../../forms/device-component-thresholds.form';
import { DeviceThresholdIntervalsForm } from '../../forms/device-threshold-intervals.form';
import { DeviceThresholdsMetricsForm } from '../../forms/device-threshold-metrics-form';
import { DeviceThresholdsForm } from '../../forms/device-thresholds.form';
import { ALL_DRIVES_NAME_CONSTANT } from '../../models/constants';
import { DeviceThresholdInheritanceType } from '../../models/device-threshold-inheritance-type';
import { DeviceThresholdInheritanceTypeEnum } from '../../models/device-threshold-inheritance-type.enum';
import { DeviceThresholdInterval } from '../../models/device-threshold-interval';
import { DeviceThresholdIntervals } from '../../models/device-threshold-intervals';
import { DeviceThresholdLevel } from '../../models/device-threshold-level.enum';
import { DeviceThresholdMetrics } from '../../models/device-threshold-metrics';
import { OrganizationThresholdsService } from '../../services/device-thresholds.service';
import { thresholdIntervalValidator, thresholdMetricValidator } from '../../validators/metrics.validator';
import { DeviceComponentThresholdsComponent } from '../device-component-thresholds/device-component-thresholds.component';

@Component({
    selector: 'app-device-thresholds',
    imports: [DeviceComponentThresholdsComponent],
    templateUrl: './device-thresholds.component.html',
    styleUrl: './device-thresholds.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DeviceThresholdsComponent implements OnInit {
    private readonly deviceThresholdsService = inject(OrganizationThresholdsService);
    private readonly userContextService = inject(UserContextService);
    private readonly organizationService = inject(OrganizationSharedService);
    private readonly formBuilder = inject(FormBuilder);
    private readonly cdr = inject(ChangeDetectorRef);
    private readonly destroyRef = inject(DestroyRef);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) deviceThresholds: DeviceThresholds;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) thresholdsEntityId: number;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) useCurrentLevelForInheritance: boolean;
    readonly showDeviceCount = input(true);
    readonly formStatusIsValidEvent = output<boolean>();
    public form: FormGroup<DeviceThresholdsForm>;
    public intervals: DeviceThresholdInterval[];
    public inheritanceTypes: DeviceThresholdInheritanceType[];
    ALL_DRIVES_NAME_CONSTANT = ALL_DRIVES_NAME_CONSTANT;

    ngOnInit(): void {
        const organizationId = this.userContextService.currentUser.organizationId;
        forkJoin([this.deviceThresholdsService.getThresholdsMetadata(organizationId), this.deviceThresholds?.deviceThresholdLevel === DeviceThresholdLevel.Organization ? this.organizationService.getOrganizationById(this.thresholdsEntityId) : of(null)])
            .pipe(map(([metadata, organizationResponse]) => ({ metadata: metadata.data, organization: organizationResponse?.data })))
            .subscribe(({ metadata, organization }) => {
                this.intervals = metadata.intervals;
                this.inheritanceTypes = metadata.inheritanceTypes.filter(inheritanceType => this.filterInheritTypeForOrganizationRoot(organization, inheritanceType));
                this.form = this.buildForm(this.deviceThresholds);
                this.form?.statusChanges.pipe(
                    distinctUntilChanged(),
                    takeUntilDestroyed(this.destroyRef)
                ).subscribe(() => {
                    this.formStatusIsValidEvent.emit(this.form.valid);
                });
                this.cdr.detectChanges();
            });
    }

    private filterInheritTypeForOrganizationRoot(organization: Organization, inheritanceType: DeviceThresholdInheritanceType) {
        return this.useCurrentLevelForInheritance || inheritanceType.id !== DeviceThresholdInheritanceTypeEnum.Inherit || this.deviceThresholds.deviceThresholdLevel !== DeviceThresholdLevel.Organization || (organization.parentOrganizationId ?? null) !== null;
    }

    private buildForm(deviceThresholds: DeviceThresholds): FormGroup {
        return this.formBuilder.group<DeviceThresholdsForm>({
            cpu: this.buildComponentForm(deviceThresholds.cpuThresholds),
            memory: this.buildComponentForm(deviceThresholds.memoryThresholds),
            agent: this.buildComponentForm(deviceThresholds.agentThresholds),
            disks: this.formBuilder.array(deviceThresholds.diskThresholds.map(diskThreshold => this.buildComponentForm(diskThreshold))),
        });
    }

    private buildIntervalsForm(deviceComponentThresholds: DeviceComponentThresholds): FormGroup<DeviceThresholdIntervalsForm> {
        const triggerRequired = deviceComponentThresholds.deviceAlertType !== DeviceAlertTypeEnum.Heartbeat;
        return this.formBuilder.group<DeviceThresholdIntervalsForm>({
            clear: this.formBuilder.control(deviceComponentThresholds.intervals?.clear, thresholdIntervalValidator(true)),
            trigger: this.formBuilder.control(deviceComponentThresholds.intervals?.trigger, thresholdIntervalValidator(triggerRequired))
        });
    }

    private buildComponentForm(deviceComponentThresholds: DeviceComponentThresholds): FormGroup<DeviceComponentThresholdsForm> {

        const form = this.formBuilder.group<DeviceComponentThresholdsForm>({
            name: this.formBuilder.control(deviceComponentThresholds.name),
            deviceAlertType: this.formBuilder.control(deviceComponentThresholds.deviceAlertType),
            inheritanceType: this.formBuilder.control(deviceComponentThresholds.inheritanceType, Validators.required),
            intervals: this.buildIntervalsForm(deviceComponentThresholds),
            inheritFrom: this.formBuilder.control(deviceComponentThresholds.inheritFrom),
            metrics: this.buildMetricsForm(deviceComponentThresholds)
        });

        form.addControl('deviceAlertThresholdType', this.formBuilder.control(deviceComponentThresholds.deviceAlertThresholdType));

        return form;
    }

    private buildMetricsForm(deviceComponentThresholds: DeviceComponentThresholds): FormGroup<DeviceThresholdsMetricsForm> {
        const maxValue = deviceComponentThresholds.deviceAlertThresholdTypes.find(t => t.id === deviceComponentThresholds.deviceAlertThresholdType)?.maxValue;
        const selectedThresholdId = deviceComponentThresholds.deviceAlertThresholdType;

        const form = this.formBuilder.group<DeviceThresholdsMetricsForm>({
            critical: this.formBuilder.control(deviceComponentThresholds.metrics.critical),
            error: this.formBuilder.control(deviceComponentThresholds.metrics.error),
            warning: this.formBuilder.control(deviceComponentThresholds.metrics.warning)
        }, { validators: thresholdMetricValidator(selectedThresholdId, maxValue) });

        return form;
    }

    public updateOtherComponentMetrics(updatedValue: DeviceThresholdMetrics, deviceAlertThresholdType: number) {
        let ind = 0;
        const disksArray = this.form.controls.disks;

        this.deviceThresholds.diskThresholds.forEach(d => {
            if (d.name !== ALL_DRIVES_NAME_CONSTANT) {
                const diskFormGroup = disksArray.at(ind);
                if (diskFormGroup && +diskFormGroup.controls.inheritanceType.value === DeviceThresholdInheritanceTypeEnum.Inherit) {
                    diskFormGroup.controls.deviceAlertThresholdType.setValue(deviceAlertThresholdType);
                    const metricsFormGroup = diskFormGroup.controls.metrics;
                    metricsFormGroup.setValue(updatedValue);
                }
            }

            ind += 1;
        });
    }

    public updateOtherComponentIntervals(updatedValue: DeviceThresholdIntervals) {
        let ind = 0;
        const disksArray = this.form.controls.disks;

        this.deviceThresholds.diskThresholds.forEach(d => {
            if (d.name !== ALL_DRIVES_NAME_CONSTANT) {
                const diskFormGroup = disksArray.at(ind);
                if (diskFormGroup && +diskFormGroup.controls.inheritanceType.value === DeviceThresholdInheritanceTypeEnum.Inherit) {
                    const intervalsFormGroup = diskFormGroup.controls.intervals;
                    intervalsFormGroup.setValue(updatedValue);
                }
            }

            ind += 1;
        });
    }

    public updateOtherComponentDisksInheritanceType(deviceComponentThresholds: DeviceComponentThresholds) {
        if (deviceComponentThresholds.name === ALL_DRIVES_NAME_CONSTANT) {
            this.form.controls.disks.controls.forEach(diskFormGroup => {
                if (diskFormGroup.value.name !== ALL_DRIVES_NAME_CONSTANT) {
                    if (+deviceComponentThresholds.inheritanceType === DeviceThresholdInheritanceTypeEnum.Override
                        && +diskFormGroup.value.inheritanceType === DeviceThresholdInheritanceTypeEnum.Inherit) {
                        diskFormGroup.controls.inheritFrom.setValue(`'${ALL_DRIVES_NAME_CONSTANT}'`);
                    } else if (+diskFormGroup.value.inheritanceType === DeviceThresholdInheritanceTypeEnum.Inherit) {
                        diskFormGroup.controls.deviceAlertThresholdType.setValue(deviceComponentThresholds.deviceAlertThresholdType);
                        diskFormGroup.controls.inheritFrom.setValue(deviceComponentThresholds.inheritFrom);
                        diskFormGroup.controls.metrics.patchValue(deviceComponentThresholds.metrics);
                        diskFormGroup.controls.intervals.patchValue(deviceComponentThresholds.intervals);
                    }

                    if (+deviceComponentThresholds.inheritanceType === DeviceThresholdInheritanceTypeEnum.Disable) {
                        diskFormGroup.controls.inheritFrom.setValue(ALL_DRIVES_NAME_CONSTANT);
                    }
                }
            });
        } else if (+deviceComponentThresholds.inheritanceType === DeviceThresholdInheritanceTypeEnum.Inherit) {
            const defaultValues = this.getAllDrivesFormValues();
            const diskForm = this.form.controls.disks.controls.find(d => d.value.name === deviceComponentThresholds.name);
            const allDiskValues = this.form.controls.disks.controls.find(d => d.value.name === ALL_DRIVES_NAME_CONSTANT).value;
            const inheritFrom = +allDiskValues.inheritanceType !== DeviceThresholdInheritanceTypeEnum.Inherit
                ? `'${ALL_DRIVES_NAME_CONSTANT}'` : +defaultValues.value.inheritanceType === DeviceThresholdInheritanceTypeEnum.Override
                    ? `'${ALL_DRIVES_NAME_CONSTANT}'` : defaultValues.value.inheritFrom;
            diskForm.controls.deviceAlertThresholdType.setValue(defaultValues.controls.deviceAlertThresholdType.value);
            diskForm.controls.inheritFrom.setValue(inheritFrom);
            diskForm.controls.metrics.patchValue(defaultValues.controls.metrics?.value);
            diskForm.controls.intervals.patchValue(defaultValues.controls.intervals?.value);
        }
    }

    private getAllDrivesFormValues(): FormGroup<DeviceComponentThresholdsForm> {
        const disksArray = this.form.controls.disks;
        const allDrives = disksArray.controls.find(diskThreshold => diskThreshold.value.name === ALL_DRIVES_NAME_CONSTANT);
        return allDrives;
    }

    getClasses(name: string, isFirst: boolean, isLast: boolean) {
        const showDeviceCount = this.showDeviceCount();
        if (name !== ALL_DRIVES_NAME_CONSTANT && showDeviceCount) {
            return 'border px-3 mx-3 mb-2';
        }

        if (showDeviceCount && isFirst) {
            return 'mb-3 pb-3 px-3 border-bottom';
        }

        if (showDeviceCount && isLast) {
            return 'p-2 default border-bottom';
        }

        return 'p-2 default border-0';
    }
}
