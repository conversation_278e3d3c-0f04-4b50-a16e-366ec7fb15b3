import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { Bgp } from '../../models/bgp.model';
import { CommunitiesPipe } from '../../pipes/communities.pipe';

@Component({
    selector: 'app-ddos-mitigation-bgp-advertisements-details',
    imports: [NgbPopover, CommunitiesPipe],
    templateUrl: './ddos-mitigation-bgp-advertisements-details.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush

})
export class DdosMitigationBgpAdvertisementsDetailsComponent {
    protected readonly activeModal = inject(NgbActiveModal);
    readonly bgpDetails = signal<Bgp>(null);
}
