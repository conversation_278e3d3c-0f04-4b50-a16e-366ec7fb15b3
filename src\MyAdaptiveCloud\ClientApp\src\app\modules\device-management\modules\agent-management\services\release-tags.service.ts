import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';
import { ReleaseTag } from '../models/release-tag.model';
import { ServiceType } from '../models/service-type.model';
import { UpdateReleaseTagRequest } from '../requests/update-release-tag.request';
import { ReleaseTagId } from '../models/release-tag-id.model';
import { AGENT_MSI_ENDPOINT_FIRST_SEGMENT } from './agent-msi-endpoint.constants';

@Injectable({
    providedIn: 'root'
})
export class ReleaseTagsService {

    private readonly apiService = inject(ApiService);

    private readonly endpoint = `${AGENT_MSI_ENDPOINT_FIRST_SEGMENT}/release-tags`;

    getList(): Observable<ApiDataResult<ReleaseTag[]>> {
        return this.apiService.get<ApiDataResult<ReleaseTag[]>>(`${this.endpoint}`);
    }

    getAgents(): Observable<ApiDataResult<ServiceType[]>> {
        return this.apiService.get<ApiDataResult<ServiceType[]>>(`${this.endpoint}/agent`);
    }

    getWatchdogs(): Observable<ApiDataResult<ServiceType[]>> {
        return this.apiService.get<ApiDataResult<ServiceType[]>>(`${this.endpoint}/watchdog`);
    }

    update(request: UpdateReleaseTagRequest[]): Observable<ApiResult> {
        return this.apiService.post<ApiResult, UpdateReleaseTagRequest[]>(`${this.endpoint}`, request);
    }

    getIdList(organizationId: number): Observable<ApiDataResult<ReleaseTagId[]>> {
        return this.apiService.get<ApiDataResult<ReleaseTagId[]>>(`${this.endpoint}/${organizationId}/release-tags-id`);
    }
}

