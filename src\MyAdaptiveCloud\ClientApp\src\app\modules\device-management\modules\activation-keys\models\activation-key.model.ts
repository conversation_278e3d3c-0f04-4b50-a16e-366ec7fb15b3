import { IUserAction } from '@app/shared/models/user-actions/user-action';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';

export class ActivationKey implements IUserAction {
    activationKeyId: number;
    key: string;
    creationDate: Date;
    deactivationDate: Date;
    isActive: boolean;
    canView: UserActionState;
    canCreate: UserActionState;
    canEdit: UserActionState;
    canDelete: UserActionState;
    organizationId: number;
}
