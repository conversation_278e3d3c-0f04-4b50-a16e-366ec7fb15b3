import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VpnUsersService } from '../../services/vpn-users.service';
import { CreateVpnUserComponent } from './create-vpn-user.component';

describe('CreateVpnUserComponent', () => {

    let fixture: ComponentFixture<CreateVpnUserComponent>;
    let component: CreateVpnUserComponent;

    let mockVpnUsersService: jasmine.SpyObj<VpnUsersService>;

    let submitButton: HTMLButtonElement;
    let userNameInput: HTMLInputElement;
    let passwordInput: HTMLInputElement;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [CreateVpnUserComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VpnUsersService)
            ]
        });

        mockVpnUsersService = TestBed.inject(VpnUsersService) as jasmine.SpyObj<VpnUsersService>;
        mockVpnUsersService.createVpnUser.and.returnValue(of('job-id'));

        fixture = TestBed.createComponent(CreateVpnUserComponent);
        component = fixture.componentInstance;
        component.account.set('Account 1');
        component.domainId.set('domain-id-123');

        fixture.detectChanges();

        submitButton = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
        userNameInput = fixture.debugElement.query(By.css('[data-testid="vpn-user-name-input"]')).nativeElement as HTMLInputElement;
        passwordInput = fixture.debugElement.query(By.css('[data-testid="vpn-password-input"]')).nativeElement as HTMLInputElement;

    });

    describe('Form interaction', () => {

        it('should not submit with user name too short', () => {

            userNameInput.value = 'a';
            userNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            passwordInput.value = 'Pass';
            passwordInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();
            expect(mockVpnUsersService.createVpnUser).not.toHaveBeenCalled();
        });

        it('should not submit with password too short', () => {

            userNameInput.value = 'username';
            userNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            passwordInput.value = 'p';
            passwordInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();
            expect(mockVpnUsersService.createVpnUser).not.toHaveBeenCalled();
        });

        it('should not submit with user name too long', () => {

            userNameInput.value = 'aasdasdasdarewvrwgrawhrerg--__35424234g3ga5gragwesrwgrwg25rg3rg2g';
            userNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            passwordInput.value = 'Pass';
            passwordInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();
            expect(mockVpnUsersService.createVpnUser).not.toHaveBeenCalled();
        });

        it('should not submit with password too long', () => {

            userNameInput.value = 'username';
            userNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            passwordInput.value = 'aasdasdasdarewvrwgra2g__--2312323';
            passwordInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();
            expect(mockVpnUsersService.createVpnUser).not.toHaveBeenCalled();
        });

        it('should not submit with user name with invalid characters', () => {

            userNameInput.value = 'aasdasdasdarewvrwgrawhrergewr23^&';
            userNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            passwordInput.value = 'Pass';
            passwordInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();
            expect(mockVpnUsersService.createVpnUser).not.toHaveBeenCalled();
        });

        it('should not submit with password with invalid characters', () => {

            userNameInput.value = 'username';
            userNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            passwordInput.value = 'aasdasdasdar*@#';
            passwordInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(submitButton.disabled).toBeTrue();
            expect(mockVpnUsersService.createVpnUser).not.toHaveBeenCalled();
        });

        it('should submit with valid user name and password', () => {

            userNameInput.value = 'aasdasdasdarewvrwgrawhrerg--__35424234g3ga5gragwesrwgrwg25rg3rg2';
            userNameInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            passwordInput.value = 'aasdasdasdarewvrwgra2g__--231233';
            passwordInput.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            submitButton.click();
            fixture.detectChanges();

            expect(submitButton.disabled).toBeFalse();
            expect(mockVpnUsersService.createVpnUser).toHaveBeenCalledOnceWith(userNameInput.value, passwordInput.value, component.domainId(), component.account());
        });

    });

});
