@import "custom-variables.scss";

$nav-logo-width: 40px;
$navbar-padding-left: 5px;

.navbar {
    background-color: $white-label-primary-color;
    @include gradient-x($white-label-primary-color, $white-label-primary-color);
    height: $navbar-height;
    padding-left: $navbar-padding-left;
    position: fixed;
    width: 100%;
    z-index: $navbar-z-index;

    .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: $aside-width-expanded;

        img {
            max-width: calc(#{$aside-width-expanded} - #{$navbar-padding-left * 2});
        }
    }
}