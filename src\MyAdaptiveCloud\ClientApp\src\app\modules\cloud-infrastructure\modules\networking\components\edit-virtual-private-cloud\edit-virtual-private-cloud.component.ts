import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { EditVirtualPrivateCloudForm } from '../../forms/edit-virtual-private.form';
import { VirtualPrivateCloudListViewModel } from '../../models/virtual-private-cloud-list.view-model';
import { VirtualPrivateCloudService } from '../../services/virtual-private-cloud.service';

@Component({
    selector: 'app-edit-virtual-private-cloud',
    imports: [BtnSubmitComponent, ReactiveFormsModule],
    templateUrl: './edit-virtual-private-cloud.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class EditVirtualPrivateCloudComponent implements OnInit {

    protected readonly activeModal = inject(NgbActiveModal);
    private readonly formBuilder = inject(FormBuilder);
    private readonly virtualPrivateCloudService = inject(VirtualPrivateCloudService);

    readonly virtualPrivateCloud = signal<VirtualPrivateCloudListViewModel | null>(null);

    protected form: FormGroup<EditVirtualPrivateCloudForm>;

    ngOnInit(): void {
        this.form = this.formBuilder.group<EditVirtualPrivateCloudForm>({
            description: this.formBuilder.control<string | null>(this.virtualPrivateCloud().description, Validators.maxLength(255)),
            name: this.formBuilder.control<string>(this.virtualPrivateCloud().name, [Validators.required, Validators.maxLength(255)]),
        });
    }

    protected submit() {
        if (this.form.valid) {
            this.virtualPrivateCloudService.editVirtualPrivateCloud(this.virtualPrivateCloud().id, this.form.value.name, this.form.value.description)
                .subscribe(() => {
                    this.activeModal.close();
                });
        }
    }

}
