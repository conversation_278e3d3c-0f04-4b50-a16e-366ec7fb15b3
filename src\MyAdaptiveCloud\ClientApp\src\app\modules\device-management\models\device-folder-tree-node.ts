import { computed, signal } from '@angular/core';
import { DEVICE_FOLDER_ID_PREFIX } from '@app/shared/constants/shared-folder-devices-constants';
import { Device } from './device';
import { DeviceFolderTreeNodeDto } from './device-folder-tree-node-dto';
import { DeviceTreeNode } from './device-tree-node';
import { ItemsSelection } from './items-selection';

export class DeviceFolderTreeNode {

    protected readonly subFolders = signal<DeviceFolderTreeNode[]>([]);
    readonly parent = signal<DeviceFolderTreeNode | null>(null);
    readonly path = signal<string>(null);
    readonly isExpanded = signal<boolean>(false);
    readonly level = signal<number>(0);
    readonly isSelected = signal<boolean>(false);
    readonly isSelectedForAction = signal<boolean>(false);

    protected readonly devices = signal<DeviceTreeNode[]>([]);
    readonly name = signal<string>('');
    readonly description = signal<string>('');

    folderId: number | null;
    protected readonly parentFolderId = signal<number | null>(null);
    readonly hasSubfolders = signal<boolean>(false);
    protected readonly _deviceCountSelfAndChildren = signal<number>(0);
    protected readonly _deviceCountOnlySelf = signal<number>(0);

    constructor(deviceFolder?: Partial<DeviceFolderTreeNodeDto>, level?: number, parent?: DeviceFolderTreeNode, subFolders?: DeviceFolderTreeNode[]) {
        if (parent && subFolders && subFolders.some(subfolder => parent.contains(subfolder))) {
            throw new Error('A subfolder is already contained in the parent folder');
        }

        this.folderId = deviceFolder?.folderId;

        if (deviceFolder?.parentFolderId !== null && deviceFolder?.parentFolderId !== undefined) {
            this.parentFolderId.set(deviceFolder.parentFolderId);
        }

        if (level) {
            this.level.set(level);
        }

        if (parent) {
            this.parent.set(parent);
        }

        if (subFolders) {
            this.addSubfolders(subFolders);
        }

        if (deviceFolder?.name) {
            this.name.set(deviceFolder.name);
        }

        if (deviceFolder?.description) {
            this.description.set(deviceFolder.description);
        }

        if (deviceFolder?.deviceCountCurrentFolder) {
            this._deviceCountOnlySelf.set(deviceFolder.deviceCountCurrentFolder);
        }

        if (deviceFolder?.deviceCount) {
            this._deviceCountSelfAndChildren.set(deviceFolder.deviceCount);
        }

        this.hasSubfolders.set(!!deviceFolder?.hasSubfolders);
    }

    isPartnerOrganization(): boolean {
        return false;
    }

    isRootOrganization() {
        return false;
    }

    /**
     * Since the ids of folders and organizations are not unique between them, we add a prefix to make them unique.
    */
    getUniqueStringId(): string {
        return `${DEVICE_FOLDER_ID_PREFIX}${this.getId().toString()}`;
    }

    getId(): number {
        return this.folderId;
    }

    readonly getParentId = computed(() => this.parentFolderId());

    readonly folderDevices = computed(() => this.devices());

    isOrganizationFolder(): boolean {
        return false;
    }

    isUnassignedDevicesFolder(): boolean {
        return false;
    }

    deselectSubfolders() {
        this.subFolders().forEach(f => f.isSelected.set(false));
    }

    deselectDevices() {
        this.devices()?.forEach(d => {
            d.isSelected.set(false);
            d.isSelectedForAction.set(false);
        });
    }

    /**
     * Deselects all the devices except the exceptAgentId.
     */
    deselectDevicesExcept(exceptAgentId: number) {
        this.devices()?.filter(d => d.agentId !== exceptAgentId)
            .forEach(d => {
                d.isSelected.set(false);
                d.isSelectedForAction.set(false);
            });
    }

    toggleSelected() {
        this.isSelected.update(value => !value);
    }

    toggleSelectedForAction() {
        this.isSelectedForAction.update(value => !value);
    }

    findFolderById(folderId: number): DeviceFolderTreeNode {
        if ((this.getId() ?? null) === folderId) {
            return this;
        }

        for (const subFolder of this.subFolders()) {
            const foundFolder = subFolder.findFolderById(folderId);
            if (foundFolder) {
                return foundFolder;
            }
        }

        return null;
    }

    findFolderByUniqueId(uniqueFolderId: string): DeviceFolderTreeNode {
        if ((this.getUniqueStringId() ?? null) === uniqueFolderId) {
            return this;
        }

        for (const subFolder of this.subFolders()) {
            const foundFolder = subFolder.findFolderByUniqueId(uniqueFolderId);
            if (foundFolder) {
                return foundFolder;
            }
        }

        return null;
    }

    findDeviceById(deviceId: number): DeviceTreeNode {
        let result: DeviceTreeNode = null;
        const stack: DeviceFolderTreeNode[] = [this];

        while (stack.length && !result) {
            const currentFolder = stack.pop();
            result = currentFolder.devices()?.find(d => d.agentId === deviceId);
            stack.push(...currentFolder.subFolders());
        }
        return result;
    }

    /**
     * Returns the organization this folder belongs to: the first ancestor that is OrganizationFolder.
     */
    getOrganization(): DeviceFolderTreeNode {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        let currentElement: DeviceFolderTreeNode = this;
        let parentOrganization: DeviceFolderTreeNode = null;

        while (currentElement && !parentOrganization) {
            if (currentElement.isOrganizationFolder()) {
                parentOrganization = currentElement;
            }
            currentElement = currentElement.parent();
        }
        return parentOrganization;
    }

    readonly pathFromRoot = computed(() => {
        let currentElement: DeviceFolderTreeNode = this.parent();
        const pathToRoot: DeviceFolderTreeNode[] = [this];

        while (currentElement) {
            pathToRoot.push(currentElement);
            currentElement = currentElement.parent();
        }
        return pathToRoot.reverse();
    });

    pathFromRootString(charToLink = '\\') {
        return this.pathFromRoot().map(a => a.name())
            .join(charToLink);
    }

    findRootFolder(): DeviceFolderTreeNode {
        return this.pathFromRoot()[0];
    }

    setDevices(devices: Device[]) {
        if (!devices) {
            throw new Error('Devices can\'t be null.');
        }

        this.devices.set([...devices.map(device => new DeviceTreeNode(device, this))]);
    }

    removeDevice(device: DeviceTreeNode) {
        this.removeDevices([device]);
    }

    removeDevices(devices: DeviceTreeNode[]) {
        if (!devices.length) {
            throw new Error('No devices provided.');
        }

        const devicesToRemoveIds = this.devices().filter(device => devices.map(d => d.agentId).includes(device.agentId))
            .map(d => d.agentId);
        const devicesToRemove = this.devices().filter(device => devicesToRemoveIds.includes(device.agentId));
        devicesToRemove.forEach(device => device.parent.set(null));
        const newDevices = [...this.devices().filter(device => !devicesToRemoveIds.includes(device.agentId))];

        this.devices.set([...newDevices]);
        this.updateDeviceCount(-devicesToRemoveIds.length);
    }

    removeSubfolder(folder: DeviceFolderTreeNode) {
        this.removeSubfolders([folder]);
    }

    removeSubfolders(folders: DeviceFolderTreeNode[]) {
        if (!folders.length) {
            throw new Error('No folders provided.');
        }

        this.subFolders.update(value => {
            const foldersToRemoveIds = folders.map(f => f.getUniqueStringId());
            return [...value.filter(folder => !foldersToRemoveIds.includes(folder.getUniqueStringId()))];
        });

        if (!this.subFolders().length) {
            this.hasSubfolders.set(false);
            this.isExpanded.set(false);
        }
    }

    readonly shouldLoadSubfolders = computed(() => !!this.hasSubfolders() && !this.subFolders().length);

    readonly shouldLoadDevices = computed(() => this._deviceCountOnlySelf() !== this.devices()?.length);

    /**
     * Returns true if the folder contains a device at any level
     */
    readonly hasAnyDevice = computed(() => this._deviceCountOnlySelf() > 0 || this._deviceCountSelfAndChildren() > 0 || this.devices()?.length > 0);

    addDevice(deviceToAdd: DeviceTreeNode) {
        this.addDevices([deviceToAdd]);
    }

    addDevices(devices: DeviceTreeNode[]) {
        if (!devices) {
            throw new Error('No devices provided.');
        }

        // Check if the devices are already in the folder and only aded the one that are not
        const devicesToAdd = devices.filter(device => !this.devices().map(d => d.agentId)
            .includes(device.agentId));

        // Update all devices with this folder as parent
        devicesToAdd.forEach(device => device.parent.set(this));

        this.devices.update(value => [...value, ...devicesToAdd].sort((a, b) => a.hostname.localeCompare(b.hostname)));
        this.updateDeviceCount(devicesToAdd.length);
    }

    sortSubfolders() {
        if (this.subFolders()?.length) {
            const organizationFolders = this.subFolders().filter(folder => folder.isOrganizationFolder())
                .sort((a, b) => a.name().localeCompare(b.name()));

            const notOrganizationFolders = this.subFolders().filter(folder => !folder.isOrganizationFolder())
                .sort((a, b) => (a.isUnassignedDevicesFolder() ? -1 : b.isUnassignedDevicesFolder() ? 1 : a.name().localeCompare(b.name())));

            this.subFolders.set(organizationFolders.concat(notOrganizationFolders));
        }
    }

    setSubfolders(folders: DeviceFolderTreeNode[]) {
        this.subFolders.set([]);

        if (folders) {
            this.addSubfolders(folders);
        }
    }

    addSubfolders(folders: DeviceFolderTreeNode[]) {
        if (!folders) {
            throw new Error('No folders provided.');
        }

        folders.forEach(folder => {
            folder.parent.set(this);
            folder.parentFolderId.set(this.folderId);
            folder.forEach((f, currentLevel) => f.level.set(this.level() + 1 + currentLevel));
        });

        const updatedFolders = ([...this.subFolders(), ...folders]);

        const organizationFolders = updatedFolders.filter(folder => folder.isOrganizationFolder())
            .sort((a, b) => a.name().localeCompare(b.name()));

        const notOrganizationFolders = updatedFolders.filter(folder => !folder.isOrganizationFolder())
            .sort((a, b) => (a.isUnassignedDevicesFolder() ? -1 : b.isUnassignedDevicesFolder() ? 1 : a.name().localeCompare(b.name())));

        this.subFolders.set([...organizationFolders.concat(notOrganizationFolders)]);

        this.sortSubfolders();
        this.hasSubfolders.set(this.subFolders().length > 0);
    }

    addSubfolder(folderToAdd: DeviceFolderTreeNode) {
        this.addSubfolders([folderToAdd]);
    }

    readonly getSubFolders = computed(() => this.subFolders());

    readonly deviceCountSelfAndChildren = computed(() => (this.shouldLoadDevices() ? this._deviceCountSelfAndChildren() : this.deviceCountOnlySelf() + this.calculateDeviceCountOfSubfolders()));

    readonly deviceCountOnlySelf = computed(() => (this._deviceCountOnlySelf()));

    private calculateDeviceCountOfSubfolders(): number {
        let total = 0;

        function countDevicesInSubfolder(subfolder: DeviceFolderTreeNode): number {
            let count: number = subfolder._deviceCountOnlySelf();

            if (subfolder.subFolders()?.length) {
                for (const nestedSubfolder of subfolder.subFolders()) {
                    count += countDevicesInSubfolder(nestedSubfolder);
                }
            }

            return count;
        }

        for (const sf of this.subFolders()) {
            total += countDevicesInSubfolder(sf);
        }

        return total;
    }

    updateSubfoldersAndDevices(folder: DeviceFolderTreeNode) {
        this.devices.set([...folder.devices() ? [...folder.devices()] : []]);
        this.subFolders.set([...folder?.subFolders()?.length ? [...folder.subFolders()] : []]);
    }

    /*
    * Updates the device count of the folder and its subfolders.
    * @param count The number of devices to add or remove to this folder.
    * @private This method must not be called directly. Use addDevices or removeDevices instead.
    * @returns {number} The updated device count.
    */
    protected updateDeviceCount(count: number) {
        this._deviceCountOnlySelf.update(value => value + count);
        this._deviceCountSelfAndChildren.update(value => (value + count) + this.calculateDeviceCountOfSubfolders());
    }

    protected folderBelongsToTheOrganization(folder: DeviceFolderTreeNode): boolean {
        return this.getOrganization() === folder.getOrganization();
    }

    protected deviceBelongsToTheOrganization(device: DeviceTreeNode): boolean {
        return device.orgId === this.getOrganization().getId();
    }

    protected movingThisFolderCreatesACycle(folderToMove: DeviceFolderTreeNode): boolean {
        return this.pathFromRoot().includes(folderToMove);
    }

    canReceiveItems(movingItems: ItemsSelection): boolean {
        return movingItems.devices.every(deviceToMove => this.getId() !== deviceToMove.parent()?.getId() && this.deviceBelongsToTheOrganization(deviceToMove)) &&
            movingItems.folders.every(folderToMove => this.folderBelongsToTheOrganization(folderToMove) &&
                this.getId() !== folderToMove.getParentId() && !this.movingThisFolderCreatesACycle(folderToMove));
    }

    contains(node: DeviceFolderTreeNode): boolean {
        return this.reduce((res, currentNode) => res || currentNode.getId() === node.getId(), false);
    }

    /**
     * Collapses the node. Cleaning the subFolders means that they will be api loaded again on expand.
     */
    collapse(cleanSubFolders = false): void {
        if (cleanSubFolders) {
            this.subFolders.set([]);
        }
        this.isExpanded.set(false);
    }

    /**
     * Performs the specified action for each node in the tree.
     * @param callbackfn  forEach calls the callbackfn function one time for each node in the tree.
     * level is 0 for the current node, 1 for node children, 2 for children of children, etc.
     */
    forEach(callbackfn: (node: DeviceFolderTreeNode, level: number) => void): void {
        const stack: { folder: DeviceFolderTreeNode, level: number }[] = [];
        stack.push({ folder: this, level: 0 });

        while (stack.length > 0) {
            const current = stack.pop();
            callbackfn(current.folder, current.level);

            // eslint-disable-next-line @typescript-eslint/prefer-for-of
            for (let i = 0; i < current.folder.subFolders().length; i++) {
                stack.push({ folder: current.folder.subFolders()[i], level: current.level + 1 });
            }
        }
    }

    /**
 * Calls the specified callback function for all the elements in the tree. The return value of the callback function is the accumulated result, and is provided as an argument in the next call to the callback function.
 * @param callbackfn A function that accepts two arguments. The reduce method calls the callbackfn function one time for each element in the tree.
 * @param initialValue InitialValue is used as the initial value to start the accumulation. The first call to the callbackfn function provides this value as an argument.
 */
    reduce<T>(callbackfn: (acum: T, currentValue: DeviceFolderTreeNode) => T, initialValue: T): T {
        const stack: DeviceFolderTreeNode[] = [];
        let accumulator: T = initialValue;

        stack.push(this);

        while (stack.length > 0) {
            const current = stack.pop();
            accumulator = callbackfn(accumulator, current);

            // eslint-disable-next-line @typescript-eslint/prefer-for-of
            for (let i = 0; i < current.subFolders().length; i++) {
                stack.push(current.subFolders()[i]);
            }
        }

        return accumulator;
    }

    getAllSelectedFoldersAndDevices(): ItemsSelection {
        const initialSelection = new ItemsSelection(null, this.isSelected() ? [this] : null);

        return this.reduce((selectedFoldersAndDevices, currentFolder) => {
            selectedFoldersAndDevices.addDevices(currentFolder.devices()?.filter(d => d.isSelected()) ?? []);
            selectedFoldersAndDevices.addFolders(currentFolder.subFolders().filter(f => f.isSelected()));
            return selectedFoldersAndDevices;
        }, initialSelection);
    }
}
