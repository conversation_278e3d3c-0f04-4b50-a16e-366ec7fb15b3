<div class="content-heading">
    Invoices
</div>

<div class="content-sub-heading">
    <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap no-detail-row" />
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #dateCellTemplate let-value="value">
    <span>
        {{ (value) ? (value | date: 'yyyy-MM-dd':'UTC') : '-' }}
    </span>
</ng-template>

<ng-template #currencyCellTemplate let-value="value" ngx-datatable-cell-template>
    <ng-container>
        <div class="w-100 text-end">
            {{ value | currency }}
        </div>
    </ng-container>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        <app-table-action [icon]="'fas fa-download'" [enabled]="row.canView === userActionState.Allowed"
            [title]="'Download Invoice'" (clickHandler)="downloadInvoice(row)" />
    }
</ng-template>
