import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { AuthService } from '@app/shared/services/auth.service';
import { UserContextService } from '@app/shared/services/user-context.service';

@Component({
    selector: 'app-logout',
    imports: [],
    templateUrl: './logout.component.html',
    styleUrl: './logout.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class LogoutComponent implements OnInit {
    private readonly authService = inject(AuthService);
    private readonly userContextService = inject(UserContextService);

    ngOnInit(): void {
        this.userContextService.logout();
    }

    protected redirectToSSOLogin() {
        this.authService.redirectToSSOLoginWithPrompt(false);
    }

}
