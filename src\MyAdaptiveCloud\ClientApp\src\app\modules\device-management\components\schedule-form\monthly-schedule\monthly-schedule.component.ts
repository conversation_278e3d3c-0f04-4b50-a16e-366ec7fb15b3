import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MonthlyScheduleState, ScheduleStore } from '@app/modules/device-management/components/schedule-form/schedule.component.store';
import { ScheduleMonthlyForm } from '@app/modules/device-management/models/schedule/schedule-monthly.form';
import { ScheduleMonthsType } from '@app/modules/device-management/models/schedule/schedule-months-type.enum';
import { MonthDaysForm } from '@app/modules/device-management/modules/policy/models/month-days.form';
import { MonthWeekForm } from '@app/modules/device-management/modules/policy/models/month-week.form';
import { MonthWeekModel } from '@app/modules/device-management/modules/policy/models/month-week.model';
import { ScheduleMonthDay } from '@app/modules/device-management/modules/policy/models/schedule-month-day.model';
import { ScheduleMonth } from '@app/modules/device-management/modules/policy/models/schedule-month.model';
import { DayOfWeek } from '@app/shared/models/day-of-week.enum';
import { WeekNumber } from '@app/shared/models/week-number.enum';
import { NgSelectModule } from '@ng-select/ng-select';
import { debounceTime, distinctUntilChanged, take } from 'rxjs';

@Component({
    selector: 'app-monthly-schedule',
    imports: [
        ReactiveFormsModule,
        FormsModule,
        NgSelectModule
    ],
    templateUrl: './monthly-schedule.component.html',
    styleUrl: './monthly-schedule.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class MonthlyScheduleComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    private readonly scheduleStore = inject(ScheduleStore);
    private readonly destroyRef = inject(DestroyRef);

    protected monthlyScheduleForm: FormGroup<ScheduleMonthlyForm>;
    protected readonly scheduleTypeMonthsTypeEnum = ScheduleMonthsType;

    protected months: ScheduleMonth[] = [
        { name: 'January', value: 1 },
        { name: 'February', value: 2 },
        { name: 'March', value: 3 },
        { name: 'April', value: 4 },
        { name: 'May', value: 5 },
        { name: 'June', value: 6 },
        { name: 'July', value: 7 },
        { name: 'August', value: 8 },
        { name: 'September', value: 9 },
        { name: 'October', value: 10 },
        { name: 'November', value: 11 },
        { name: 'December', value: 12 }
    ];

    protected monthDays: ScheduleMonthDay[] = [
        { name: '1', value: 1 },
        { name: '2', value: 2 },
        { name: '3', value: 3 },
        { name: '4', value: 4 },
        { name: '5', value: 5 },
        { name: '6', value: 6 },
        { name: '7', value: 7 },
        { name: '8', value: 8 },
        { name: '9', value: 9 },
        { name: '10', value: 10 },
        { name: '11', value: 11 },
        { name: '12', value: 12 },
        { name: '13', value: 13 },
        { name: '14', value: 14 },
        { name: '15', value: 15 },
        { name: '16', value: 16 },
        { name: '17', value: 17 },
        { name: '18', value: 18 },
        { name: '19', value: 19 },
        { name: '20', value: 20 },
        { name: '21', value: 21 },
        { name: '22', value: 22 },
        { name: '23', value: 23 },
        { name: '24', value: 24 },
        { name: '25', value: 25 },
        { name: '26', value: 26 },
        { name: '27', value: 27 },
        { name: '28', value: 28 },
        { name: '29', value: 29 },
        { name: '30', value: 30 },
        { name: '31', value: 31 },
        { name: 'Last', value: 32 }
    ];

    protected weekDays: MonthWeekModel[] = [
        { name: DayOfWeek[DayOfWeek.Sunday], value: DayOfWeek.Sunday },
        { name: DayOfWeek[DayOfWeek.Monday], value: DayOfWeek.Monday },
        { name: DayOfWeek[DayOfWeek.Tuesday], value: DayOfWeek.Tuesday },
        { name: DayOfWeek[DayOfWeek.Wednesday], value: DayOfWeek.Wednesday },
        { name: DayOfWeek[DayOfWeek.Thursday], value: DayOfWeek.Thursday },
        { name: DayOfWeek[DayOfWeek.Friday], value: DayOfWeek.Friday },
        { name: DayOfWeek[DayOfWeek.Saturday], value: DayOfWeek.Saturday }
    ];

    protected monthWeeks: MonthWeekModel[] = [
        { name: WeekNumber[WeekNumber.First], value: WeekNumber.First },
        { name: WeekNumber[WeekNumber.Second], value: WeekNumber.Second },
        { name: WeekNumber[WeekNumber.Third], value: WeekNumber.Third },
        { name: WeekNumber[WeekNumber.Fourth], value: WeekNumber.Fourth },
        { name: WeekNumber[WeekNumber.Last], value: WeekNumber.Last }
    ];

    public enable() {
        this.monthlyScheduleForm.enable();
    }

    public disable() {
        this.monthlyScheduleForm.disable();
    }

    ngOnInit(): void {
        this.monthlyScheduleForm = this.formBuilder.group<ScheduleMonthlyForm>({
            months: new FormControl([], [Validators.required]),
            selectByDaysOrWeeksRadios: new FormControl(ScheduleMonthsType.Days),
            monthDaysGroup: this.formBuilder.group<MonthDaysForm>({
                monthDays: new FormControl([], [Validators.required]),
            }),
            weeksOnMonthGroup: this.formBuilder.group<MonthWeekForm>({
                monthOnWeek: new FormControl([], [Validators.minLength(1), Validators.required]),
                monthOnWeekNumber: new FormControl([], [Validators.minLength(1), Validators.required]),
            }),
        });
        this.subscribeToScheduleState();
        this.scheduleStore.getScheduleTypeFormState$.pipe(take(1))
            .subscribe(scheduleTypeDailyForm => {
                if (scheduleTypeDailyForm.isReadOnly) {
                    this.monthlyScheduleForm.disable();
                } else {
                    this.subscribeToFormValueChanges();
                }
            });
    }

    private subscribeToFormValueChanges() {
        this.monthlyScheduleForm.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe(value => {
                this.toggleSetByDaysOrWeeks();

                const _scheduleTypeMonthlyForm: MonthlyScheduleState = {
                    scheduleMonthsType: value.selectByDaysOrWeeksRadios,
                    months: value.months,
                    monthDays: this.monthlyScheduleForm.controls.monthDaysGroup.controls.monthDays.value,
                    monthWeeks: this.monthlyScheduleForm.controls.weeksOnMonthGroup.controls.monthOnWeek.value,
                    monthWeeksDays: this.monthlyScheduleForm.controls.weeksOnMonthGroup.controls.monthOnWeekNumber.value,
                    isValid: this.monthlyScheduleForm.valid
                };
                this.scheduleStore.updateScheduleTypeMonthlyFormState(_scheduleTypeMonthlyForm);
            });
    }

    private subscribeToScheduleState() {
        this.scheduleStore.getScheduleTypeMonthlyFormState$
            .pipe(take(1))
            .subscribe((_scheduleTypeMonthlyFormState: MonthlyScheduleState) => {
                this.initializeScheduleTypeMonthlyFormValue(_scheduleTypeMonthlyFormState);
            });
    }

    private initializeScheduleTypeMonthlyFormValue(_scheduleTypeMonthlyFormState: MonthlyScheduleState) {
        this.monthlyScheduleForm.setValue({
            months: _scheduleTypeMonthlyFormState.months,
            selectByDaysOrWeeksRadios: _scheduleTypeMonthlyFormState.scheduleMonthsType,
            monthDaysGroup: {
                monthDays: _scheduleTypeMonthlyFormState.monthDays
            },
            weeksOnMonthGroup: {
                monthOnWeek: _scheduleTypeMonthlyFormState.monthWeeks,
                monthOnWeekNumber: _scheduleTypeMonthlyFormState.monthWeeksDays
            }
        }, { emitEvent: false });

        this.toggleSetByDaysOrWeeks();
    }

    private toggleSetByDaysOrWeeks() {
        if (this.monthlyScheduleForm.controls.selectByDaysOrWeeksRadios.value === ScheduleMonthsType.Days) {
            this.setDaysSelectionEnabled();
        } else {
            this.setDaysSelectionDisabled();
        }
        if (this.monthlyScheduleForm.controls.selectByDaysOrWeeksRadios.value === ScheduleMonthsType.On) {
            this.setWeeksOnMonthGroupEnabled();
        } else {
            this.setWeeksOnMonthGroupDisabled();
        }
    }

    private setWeeksOnMonthGroupDisabled() {
        this.monthlyScheduleForm.controls.weeksOnMonthGroup.disable({ emitEvent: false });
        this.monthlyScheduleForm.controls.weeksOnMonthGroup.controls.monthOnWeek.patchValue([], { emitEvent: false });
        this.monthlyScheduleForm.controls.weeksOnMonthGroup.controls.monthOnWeekNumber.patchValue([], { emitEvent: false });
    }

    private setWeeksOnMonthGroupEnabled() {
        this.monthlyScheduleForm.controls.weeksOnMonthGroup.enable({ emitEvent: false });
    }

    private setDaysSelectionDisabled() {
        this.monthlyScheduleForm.controls.monthDaysGroup.controls.monthDays.disable({ emitEvent: false });
        this.monthlyScheduleForm.controls.monthDaysGroup.controls.monthDays.patchValue([], { emitEvent: false });
    }

    private setDaysSelectionEnabled() {
        this.monthlyScheduleForm.controls.monthDaysGroup.controls.monthDays.enable({ emitEvent: false });
    }

    /**
     * Helper method to update selection of multiselect dropdowns
     * @param dropDownName name of the dropdown
     */
    public onSelectAll(dropDownName: string, dropdownOptionsName: string) {
        // TODO: Fix typing
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        const selected = this[dropdownOptionsName].map(item => item.value);
        this.monthlyScheduleForm.get(dropDownName).patchValue(selected);
    }

    /**
     * Helper method to clear selection of multiselect dropdowns
     * @param dropDownName name of the dropdown
     */
    public onClearAll(dropDownName: string) {
        this.monthlyScheduleForm.get(dropDownName).patchValue([]);
    }

}
