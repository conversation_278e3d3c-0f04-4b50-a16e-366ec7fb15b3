﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class AdaptiveCloudDriveFolderAuthorize : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public AdaptiveCloudDriveFolderAuthorize(IEntityAuthorizationService _entityAuthorizationService,
            IIdentityService identityService, IUserContextService userContextService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            this._entityAuthorizationService = _entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int folderId);

            var adaptiveCloudDriveFolder = await _entityAuthorizationService.GetAdaptiveCloudDriveFolder(folderId);

            if (adaptiveCloudDriveFolder != null)
            {
                // we need to send distance one to  check higher organization herarchies
                var distance = adaptiveCloudDriveFolder.IsHidden ? 1 : _distance;

                if (_perms != null && !_userContextService.HasPermission(userId, adaptiveCloudDriveFolder.OrganizationId, distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, adaptiveCloudDriveFolder.OrganizationId);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class AdaptiveCloudDriveFolderAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public AdaptiveCloudDriveFolderAuthorizeAttribute(params Perms[] perms) : base(typeof(AdaptiveCloudDriveFolderAuthorize), perms)
        {
            Name = "folderId";
        }
    }
}