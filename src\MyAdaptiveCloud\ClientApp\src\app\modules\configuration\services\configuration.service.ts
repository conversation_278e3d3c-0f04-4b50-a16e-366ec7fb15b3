import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';
import { Configuration } from '../models/configuration.model';
import { EditConfigurationRequest } from '../requests/edit-configuration.request';

@Injectable({
    providedIn: 'root'
})
export class ConfigurationService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'configuration';

    editConfigurationValue(request: EditConfigurationRequest): Observable<ApiResult> {
        return this.apiService.post<ApiResult, EditConfigurationRequest>(this.endpoint, request);
    }

    getList(): Observable<ApiDataResult<Configuration[]>> {
        return this.apiService.get<ApiDataResult<Configuration[]>>(`${this.endpoint}/list`);
    }

}
