@import 'custom-variables.scss';

.storage-list-layout {
    display: grid;
    grid-template-columns: min-content 13px 318px 30px auto ;
    align-items: center;
    padding-top: 9px;
    padding-bottom: 9px;

    .column-1 {
        grid-column: 1;
    }

    .column-2 {
        grid-column: 3;
    }

    .column-3 {
        grid-column: 5;
    }
}

.storage-item-disk-size-input {
    width: 80px !important;
}

