import { IUserAction } from '@app/shared/models/user-actions/user-action';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';

export class AppliedPolicy implements IUserAction {
    public policyId: number;
    public scheduleId?: number;
    public organizationId: number;
    public name: string;
    public description: string;
    public inheritedFrom: string;
    public inheritedFromFolderId?: number;
    public inheritedFromOrganizationId?: number;
    public isEnabled: boolean;
    public canEdit: UserActionState;
    public canDelete: UserActionState;
    public canCreate: UserActionState;
    public canView: UserActionState;
    public isInherited: boolean;
    public isEmpty: boolean;
}
