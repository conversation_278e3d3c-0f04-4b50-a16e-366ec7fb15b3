<div class="content-heading">
  Cloud Infrastructure - Virtual Machine to ConnectWise Product Mappings
</div>

<div class="content-sub-heading">
  @if (usagePeriods) {
    <app-month-year-picker [(period)]="pagination['month']" [periods]="usagePeriods"
    (periodChange)="onChangePeriod()" />
  }

  <div class="action-buttons">
    @if (permissionService.canManageCloudInfraMappings()) {
      <button class="btn btn-primary" type="button"
        (click)="createMapping()">
        Create Mapping
      </button>
    }
  </div>
</div>

<div class="card card-default">
  <div class="card-body">
    <ngx-datatable #table class="table bootstrap no-detail-row">
      <ngx-datatable-group-header [rowHeight]="50">
        <ng-template let-group="group" let-expanded="expanded" ngx-datatable-group-header-template>
          <div class="ps-2">
            <strong>{{ group.value[0].productMap.cwProductName }}</strong>
          </div>
        </ng-template>
      </ngx-datatable-group-header>
    </ngx-datatable>
  </div>
</div>

<ng-template #dashCellTemplate let-value="value">
  <span>
    {{ value ? value : '-' }}
  </span>
</ng-template>

<ng-template #dateCellTemplate let-value="value">
  <span>
    {{ (value) ? (value | date: 'yyyy-MM-dd':'UTC') : '-' }}
  </span>
</ng-template>

<ng-template #valueCellTemplate let-row="row" let-value="value">
  <span>
    {{ (row.isFormula) ? 'Calculated' : value }}
  </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
  @if (toItem(row); as row) {
    <app-table-action [icon]="'far fa-edit'" [enabled]="row.canEdit === userActionState.Allowed" [title]="'Mapping'"
      (clickHandler)="editMapping(row)" />
    <app-table-action [icon]="'far fa-trash-alt'" [enabled]="row.canDelete === userActionState.Allowed"
      [title]="'Delete'" (clickHandler)="deleteMapping(row)" />
  }
</ng-template>
