import { ComponentFixture, discardPeriodicTasks, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { StartVmComponent } from './start-vm.component';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';

describe('StartVmComponent', () => {

    let component: StartVmComponent;
    let fixture: ComponentFixture<StartVmComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [StartVmComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                provideMock(CloudInfraPermissionService),
                FormBuilder
            ]
        })
            .compileComponents();

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.startVirtualMachine.and.returnValue(of('jobId1'));
        mockVmManagementService.getClusterList.and.returnValue(of([{ name: 'Cluster 1', id: 'C1' }], [{ name: 'Cluster 2', id: 'C2' }]));
        mockVmManagementService.getHostList.and.returnValue(of([{ name: 'Host 1', id: 'H1' }], [{ name: 'Host 2', id: 'H2' }]));
        mockVmManagementService.getPodList.and.returnValue(of([{ name: 'Pod 1', id: 'P1' }], [{ name: 'Pod 2', id: 'P2' }]));

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        fixture = TestBed.createComponent(StartVmComponent);
        component = fixture.componentInstance;

        component.virtualMachineId = 'test-id';
        component.zoneId = 'ZONE1';
    });

    describe('Initializtion', () => {

        it('should not load options when user is not Root Admin', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);
            fixture.detectChanges();

            expect(mockVmManagementService.getPodList).not.toHaveBeenCalled();
            expect(mockVmManagementService.getClusterList).not.toHaveBeenCalled();
            expect(mockVmManagementService.getHostList).not.toHaveBeenCalled();
        });

        it('should load options when user is Root Admin', () => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);
            fixture.detectChanges();

            expect(mockVmManagementService.getPodList).toHaveBeenCalledOnceWith('ZONE1');
            expect(mockVmManagementService.getClusterList).toHaveBeenCalledOnceWith('ZONE1');
            expect(mockVmManagementService.getHostList).toHaveBeenCalledOnceWith('ZONE1', 'Routing', 'Up');
        });

    });

    describe('Submit', () => {

        it('should close modal on cancel', () => {

            fixture.detectChanges();
            const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should call the service and close modal when submitting a valid form', () => {
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.startVirtualMachine).toHaveBeenCalledOnceWith('test-id', '', '', '', 0);
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should not submit the form with an invalid boot delay value', () => {
            fixture.detectChanges();
            const bootDelayControl = fixture.debugElement.query(By.css('#boot-delay')).nativeElement as HTMLInputElement;
            bootDelayControl.value = '100';
            bootDelayControl.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.startVirtualMachine).not.toHaveBeenCalled();
            expect(activeModal.close).not.toHaveBeenCalled();
        });

        it('should submit the form with all optional values', fakeAsync(() => {
            mockCloudInfraPermissionService.isRootAdmin.and.returnValue(true);

            fixture.detectChanges();

            const bootDelayControl = fixture.debugElement.query(By.css('#boot-delay')).nativeElement as HTMLInputElement;
            bootDelayControl.value = '50';
            bootDelayControl.dispatchEvent(new Event('input'));
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 0);
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 2, true, 1);
            fixture.detectChanges();

            selectOption(fixture, 'ng-select', 1, true, 2);
            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.startVirtualMachine).toHaveBeenCalledWith('test-id', 'P2', '', 'H2', 50);
            expect(activeModal.close).toHaveBeenCalledTimes(1);

            flush();
            discardPeriodicTasks();
        }));

    });

});

