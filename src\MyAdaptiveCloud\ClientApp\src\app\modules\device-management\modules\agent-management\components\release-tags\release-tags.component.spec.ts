import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { Msi } from '../../models/msi.model';
import { ReleaseTag } from '../../models/release-tag.model';
import { ServiceTypeEnum } from '../../models/service-type.enum';
import { ReleaseTagsService } from '../../services/release-tags.service';
import { ReleaseTagsComponent } from './release-tags.component';

describe('ReleaseTagsComponent', () => {
    let fixture: ComponentFixture<ReleaseTagsComponent>;
    let mockReleaseTagsService: jasmine.SpyObj<ReleaseTagsService>;

    const releaseTags: ReleaseTag[] = [
        {
            agentMandatory: true,
            agentServiceId: 1,
            agentVisible: true,
            id: 1,
            releaseTagName: 'Reccomended',
            watchdogMandatory: true,
            watchdogServiceId: 1,
            watchdogVisible: true,
            description: 'Description 1',
        },
        {
            agentMandatory: true,
            agentServiceId: 2,
            agentVisible: true,
            id: 2,
            releaseTagName: 'Latest',
            watchdogMandatory: true,
            watchdogServiceId: 4,
            watchdogVisible: true,
            description: 'Description 1',
        },
    ];

    const agentVersions: Msi[] = [
        {
            name: 'adaptive-cloud-agent',
            serviceId: 1,
            releaseDate: new Date('2023-01-01'),
            version: '1.0.0',
            checksum: '', canDelete: true, checksumType: '',
            agentCount: 0,
            releaseTag: '',
            serviceType: ServiceTypeEnum.Agent,
            isObsolete: false,
            canMarkObsolete: false
        },
        {
            name: 'adaptive-cloud-agent',
            serviceId: 2,
            releaseDate: new Date('2024-01-01'),
            version: '1.2.0',
            checksum: '', canDelete: true, checksumType: '',
            agentCount: 0,
            releaseTag: '',
            serviceType: ServiceTypeEnum.Agent,
            isObsolete: false,
            canMarkObsolete: false
        }
    ];

    const watchdogVersions: Msi[] = [
        {
            name: 'adaptive-cloud-watchdog',
            serviceId: 3,
            releaseDate: new Date('2023-02-02'),
            version: '1.30.0', checksum: '', canDelete: true, checksumType: '',
            agentCount: 0,
            releaseTag: '',
            serviceType: ServiceTypeEnum.Watchdog,
            isObsolete: false,
            canMarkObsolete: false
        },
        {
            name: 'adaptive-cloud-watchdog',
            serviceId: 4,
            releaseDate: new Date('2024-06-05'),
            version: '1.32.0', checksum: '', canDelete: true, checksumType: '',
            agentCount: 0,
            releaseTag: '',
            serviceType: ServiceTypeEnum.Watchdog,
            isObsolete: false,
            canMarkObsolete: false
        }
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ReleaseTagsComponent],
            providers: [
                provideMock(ReleaseTagsService)
            ]
        })
            .compileComponents();

        mockReleaseTagsService = TestBed.inject(ReleaseTagsService) as jasmine.SpyObj<ReleaseTagsService>;
        mockReleaseTagsService.getList.and.returnValue(of({ data: releaseTags, message: '' }));
        mockReleaseTagsService.getAgents.and.returnValue(of({ data: agentVersions, message: '' }));
        mockReleaseTagsService.getWatchdogs.and.returnValue(of({ data: watchdogVersions, message: '' }));

        fixture = TestBed.createComponent(ReleaseTagsComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should call services', () => {
            expect(mockReleaseTagsService.getAgents).toHaveBeenCalledTimes(1);
            expect(mockReleaseTagsService.getAgents).toHaveBeenCalledTimes(1);
            expect(mockReleaseTagsService.getList).toHaveBeenCalledTimes(1);
        });
    });

});
