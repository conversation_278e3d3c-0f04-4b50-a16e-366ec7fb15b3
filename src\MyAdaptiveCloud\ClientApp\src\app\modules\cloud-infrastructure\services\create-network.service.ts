import { inject, Injectable } from '@angular/core';
import { NetworkOfferingViewModel } from '@app/modules/cloud-infrastructure/models/network-offering.view-model';
import { VpcViewModel } from '@app/modules/cloud-infrastructure/models/vpc.view-model';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { map, Observable } from 'rxjs';
import { Network } from '../models/network';
import { NetworkOffering } from '../models/network-offering';
import { SecondaryVlanType } from '../models/secondary-vlan-type.enum';
import { CreateIsolatedNetworkRequest } from '../requests/create-isolated-network.request';
import { CreateLayer2NetworkRequest } from '../requests/create-layer2-network.request';
import { CreateNetworkResponse } from '../responses/create-network.response';
import { ListNetworkOfferingResponse } from '../responses/list-network-offerings.response';
import { ListVirtualPrivateCloudResponse } from '../responses/list-virtual-private-cloud.response';
import { SharedNetworkRequest } from '../requests/create-shared-network';

@Injectable({
    providedIn: 'root',
})

export class CreateNetworkService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    getIsolatedNetworkOfferings(zoneId: string, domainId: string): Observable<NetworkOfferingViewModel[]> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listNetworkOfferings,
            supportedServices: 'SourceNat',
            state: 'Enabled',
            response: 'json',
            zoneid: zoneId,
            guestiptype: 'Isolated'
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;

        return this.cloudInfraApiService.get<ListNetworkOfferingResponse>(params)
            .pipe(
                map(res => res?.listnetworkofferingsresponse?.networkoffering ?? []),
                map(networkoffering => networkoffering
                    .sort((a, b) => a.displaytext.localeCompare(b.displaytext))
                    .map(network => this.mapNetworkOffering(network)))
            );
    }

    getLayer2NetworkOfferings(zoneId: string, domainId: string): Observable<NetworkOfferingViewModel[]> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listNetworkOfferings,
            state: 'Enabled',
            response: 'json',
            zoneid: zoneId,
            guestiptype: 'L2'
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;

        return this.cloudInfraApiService.get<ListNetworkOfferingResponse>(params)
            .pipe(
                map(res => res?.listnetworkofferingsresponse?.networkoffering ?? []),
                map(networkoffering => networkoffering
                    .sort((a, b) => a.displaytext.localeCompare(b.displaytext))
                    .map(network => this.mapNetworkOffering(network)))
            );
    }

    getSharedNetworkOfferings(zoneId: string, domainId: string): Observable<NetworkOfferingViewModel[]> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listNetworkOfferings,
            state: 'Enabled',
            response: 'json',
            zoneid: zoneId,
            guestiptype: 'Shared'
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;

        return this.cloudInfraApiService.get<ListNetworkOfferingResponse>(params)
            .pipe(
                map(res => res?.listnetworkofferingsresponse?.networkoffering ?? []),
                map(networkoffering => networkoffering
                    .sort((a, b) => a.displaytext.localeCompare(b.displaytext))
                    .map(network => this.mapNetworkOffering(network)))
            );
    }

    getVpcOfferings(zoneId: string, domainId: string, account: string): Observable<VpcViewModel[]> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listVPCs,
            response: 'json',
            zoneid: zoneId,
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListVirtualPrivateCloudResponse>(params)
            .pipe(map(response => (response?.listvpcsresponse?.vpc ?? [])
                .sort((a, b) => a.name.localeCompare(b.name))
                .map(vpc => ({
                    id: vpc.id,
                    name: vpc.name
                }))));
    }

    createIsolatedNetwork(zoneId: string, domainId: string, account: string, createIsolatedNetworkRequest: CreateIsolatedNetworkRequest): Observable<Network> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createNetwork,
            networkOfferingId: createIsolatedNetworkRequest.networkOfferingId,
            response: 'json',
            zoneid: zoneId,
            displayText: createIsolatedNetworkRequest.description,
            name: createIsolatedNetworkRequest.name
        };

        if (createIsolatedNetworkRequest.vpc) {
            params['vpcid'] = createIsolatedNetworkRequest.vpc;
        }

        if (createIsolatedNetworkRequest.gateway) {
            params['gateway'] = createIsolatedNetworkRequest.gateway;
        }

        if (createIsolatedNetworkRequest.netmask) {
            params['netmask'] = createIsolatedNetworkRequest.netmask;
        }

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<CreateNetworkResponse>(params)
            .pipe(map(response => response.createnetworkresponse?.network));
    }

    createLayer2Network(zoneId: string, domainId: string, account: string, createIsolatedNetworkRequest: CreateLayer2NetworkRequest): Observable<Network> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createNetwork,
            networkOfferingId: createIsolatedNetworkRequest.networkOfferingId,
            response: 'json',
            zoneid: zoneId,
            displayText: createIsolatedNetworkRequest.description,
            name: createIsolatedNetworkRequest.name
        };

        if (createIsolatedNetworkRequest.vLan) {
            params['vlan'] = createIsolatedNetworkRequest.vLan;

            params['bypassvlanoverlapcheck'] = createIsolatedNetworkRequest.bypassVLanId ? 'true' : 'false';

            if (createIsolatedNetworkRequest.secondaryVLanType && createIsolatedNetworkRequest.secondaryVLanType !== SecondaryVlanType.None) {
                {
                    params['isolatedpvlantype'] = createIsolatedNetworkRequest.secondaryVLanType.toLowerCase();
                    if (createIsolatedNetworkRequest.secondaryVLanType === SecondaryVlanType.Community || createIsolatedNetworkRequest.secondaryVLanType === SecondaryVlanType.Isolated) {
                        params['isolatedpvlan'] = createIsolatedNetworkRequest.secondaryVLanID;
                    }
                }
            }
        }

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<CreateNetworkResponse>(params)
            .pipe(map(response => response.createnetworkresponse?.network));
    }

    createSharedNetwork(zoneId: string, domainId: string, account: string, request: SharedNetworkRequest) {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createNetwork,
            networkOfferingId: request.networkofferingid,
            response: 'json',
            zoneid: zoneId,
            displayText: request.displaytext,
            name: request.name,
            vlan: request.vlan,
            [CloudInfraParamsEnum.DOMAIN_ID]: domainId,
            [CloudInfraParamsEnum.ACCOUNT]: account
        };

        if (request.gateway) {
            params['gateway'] = request.gateway;
        }
        if (request.netmask) {
            params['netmask'] = request.netmask;
        }
        if (request.startip) {
            params['startip'] = request.startip;
        }
        if (request.endip) {
            params['endip'] = request.endip;
        }

        if (request.gatewayv6) {
            params['gatewayv6'] = request.gatewayv6;
        }
        if (request.cidr) {
            params['cidr'] = request.cidr;
        }
        if (request.startipv6) {
            params['startipv6'] = request.startipv6;
        }
        if (request.endipv6) {
            params['endipv6'] = request.endipv6;
        }

        if (request.networkdomain) {
            params['networkdomain'] = request.networkdomain;
        }
        if (request.hideipaddressusage) {
            params['hideipaddressusage'] = request.hideipaddressusage;
        }

        return this.cloudInfraApiService.get<CreateNetworkResponse>(params)
            .pipe(map(response => response.createnetworkresponse?.network));
    }

    private mapNetworkOffering(networkOffering: NetworkOffering): NetworkOfferingViewModel {
        return {
            id: networkOffering.id,
            name: networkOffering.displaytext ?? networkOffering.name,
            forVPC: networkOffering.forvpc,
            specifyVLan: networkOffering.specifyvlan
        };
    }

}
