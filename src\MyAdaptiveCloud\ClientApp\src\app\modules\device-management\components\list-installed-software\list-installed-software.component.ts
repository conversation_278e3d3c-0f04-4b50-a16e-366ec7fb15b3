import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { NotificationService } from '@app/shared/services/notification.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, map, switchMap, take, tap } from 'rxjs';
import { InstalledSoftwareModel } from '../../modules/devices/models/installed-software-model';
import { SoftwareInventoryStatus } from '../../modules/devices/models/software-inventory-status.enum';
import { InstalledSoftwareListRequest } from '../../modules/devices/requests/installed-software.request';
import { DevicesService } from '../../services/devices.service';
import { FoldersTreeStore } from '../../store/folders-tree.store';
import { ModalService } from './../../../../shared/services/modal.service';
import { SoftwareInventoryService } from './../../modules/devices/services/software-inventory-service';

@Component({
    selector: 'app-list-installed-software',
    imports: [NgxDatatableModule, AutoSearchBoxComponent, TableActionComponent, DatePipe],
    templateUrl: './list-installed-software.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ListInstalledSoftwareComponent extends BaseListComponent<InstalledSoftwareModel> implements OnInit {

    private readonly userContextService = inject(UserContextService);
    private readonly devicesService = inject(DevicesService);
    private readonly foldersTreeStore = inject(FoldersTreeStore);
    private readonly modalService = inject(ModalService);
    private readonly softwareInventoryService = inject(SoftwareInventoryService);
    private readonly route = inject(ActivatedRoute);
    private readonly notificationService = inject(NotificationService);
    protected readonly permissionService = inject(PermissionService);

    private readonly dateCell = viewChild<TemplateRef<never>>('dateCellTemplate');

    readonly agentId = signal<number>(null);

    protected readonly softwareInventoryStatus = SoftwareInventoryStatus;
    protected readonly hostName = toSignal(this.route.parent?.data?.pipe(map(res => res.selectedDevice?.name as string)));

    constructor() {
        super();
        this.pagination = new InstalledSoftwareListRequest();
    }

    ngOnInit(): void {
        this.route.parent?.params
            .pipe(
                map(params => params['deviceId'] as number),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(deviceId => {
                this.agentId.set(deviceId);
                this.getData();

            });
    }

    private getData() {
        const columns: TableColumn[] = [
            {
                name: 'Name',
                prop: 'application',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150,
                headerTemplate: this.headerTemplateSortable()
            },
            {
                name: 'Version',
                prop: 'version',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150,
                headerTemplate: this.headerTemplateSortable()
            },
            {
                name: 'Installed On',
                prop: 'installedOn',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150,
                cellTemplate: this.dateCell(),
                headerTemplate: this.headerTemplateSortable()
            },
        ];

        if (this.userContextService.getFeatureFlagState(FeatureFlag.FeatureFlagUninstallSoftwareInventory)) {
            columns.push({
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false
            });
        }

        super.initialize(this.devicesService.getInstalledSoftware.bind(this.devicesService, this.agentId()), columns);
    }

    protected exportData() {
        this.devicesService.exportCSV(this.agentId());
    }

    protected uninstallSoftwareInventory(item: InstalledSoftwareModel) {
        if (item.softwareInventoryStatus !== SoftwareInventoryStatus.MarkedForDeleted) {
            this.modalService.openDeleteConfirmationDialog(
                'Software Inventory Uninstall',
                `<span>Remove <b>${item.application}</b> from <b>${this.hostName()}</b>?</span>`,
                'uninstall', null, true
            ).closed
                .pipe(
                    filter(res => !!res),
                    take(1),
                    switchMap(() => this.foldersTreeStore.selectedDevice$.pipe(
                        takeUntilDestroyed(this.destroyRef),
                        switchMap(selectedDevice => this.softwareInventoryService.uninstallSoftwareInventory(
                            selectedDevice.orgId,
                            {
                                softwareName: item.application,
                                agentId: this.agentId(),
                                version: item.version
                            }
                        ))
                    )),
                    tap(res => {
                        this.notificationService.notify(res.message);
                        this.loadData();
                    })
                )
                .subscribe();
        }
    }

}

