import { FormControl, FormGroup } from '@angular/forms';
import { DeviceAlertTypeEnum } from '../../../models/device-alert-type.enum';
import { DeviceThresholdInheritanceTypeEnum } from '../models/device-threshold-inheritance-type.enum';
import { DeviceThresholdIntervalsForm } from './device-threshold-intervals.form';
import { DeviceThresholdsMetricsForm } from './device-threshold-metrics-form';

export class DeviceComponentThresholdsForm {
    public inheritanceType: FormControl<DeviceThresholdInheritanceTypeEnum>;
    public deviceAlertThresholdType?: FormControl<number>;
    public metrics: FormGroup<DeviceThresholdsMetricsForm>;
    public intervals?: FormGroup<DeviceThresholdIntervalsForm>;
    public deviceAlertType: FormControl<DeviceAlertTypeEnum>;
    public name: FormControl<string>;
    public inheritFrom: FormControl<string>;
}
