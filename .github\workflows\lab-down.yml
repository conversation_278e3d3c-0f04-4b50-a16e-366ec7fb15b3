name: jen<PERSON>-<PERSON><PERSON>

# Controls when the workflow will run
on:
  workflow_dispatch
# on: delete

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    name: Build
    runs-on: ubuntu-latest
    env:
      JSO<PERSON>_BRANCH: "${{ toJSON('BRANCH') }}:"
      JSON_PARAMS: ${{ toJSON(github.event.ref) }}
    steps:
      - name: Trigger jenkins job
        uses: joshlk/jenkins-githubaction@master
        with:
          url: https://jenkins.ippathways.com/jenkins
          job_name: myadaptivecloud-dynamiclab-teardown
          username: g<PERSON><PERSON>
          api_token: ${{ secrets.JENKINSAPITOKEN }}
          wait: false
          parameters: ${{ format('{{ {0} {1} }}', env.JSON_BRANCH, env.JSO<PERSON>_PARAMS) }}
