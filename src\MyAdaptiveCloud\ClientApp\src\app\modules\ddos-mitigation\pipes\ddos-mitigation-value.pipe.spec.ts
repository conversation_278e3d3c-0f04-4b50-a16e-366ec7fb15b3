import { DDosMitigationValuePipe } from './ddos-mitigation-value.pipe';

describe('DDosMitigationValuePipe', () => {
    let pipe: DDosMitigationValuePipe;

    beforeEach(() => {
        pipe = new DDosMitigationValuePipe();
    });

    it('should return number array', () => {
        const input = [123, 231.23];
        const result = pipe.transform(input);
        expect(result).toEqual([123, 231.23]);
    });

    it('should return number with decimals', () => {
        const input = 123.21;
        const result = pipe.transform(input);
        expect(result).toBe(123.21);
    });

    it('should return string value', () => {
        const input = 'this value';
        const result = pipe.transform(input);
        expect(result).toBe('this value');
    });

    it('should return string array value', () => {
        const input = ['this value', 'this value 2'];
        const result = pipe.transform(input);
        expect(result).toEqual(['this value', 'this value 2']);
    });

    it('should return empty string for null input', () => {
        expect(pipe.transform(null)).toBe('-');
    });

    it('should return empty string for null input', () => {
        expect(pipe.transform([])).toBe('-');
    });

    it('should return empty string for undefined input', () => {
        expect(pipe.transform(undefined)).toBe('-');
    });

    it('should return empty string for empty input', () => {
        expect(pipe.transform('')).toBe('-');
    });

});
