import { Clipboard } from '@angular/cdk/clipboard';
import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, TemplateRef, inject, viewChild } from '@angular/core';
import { ActivationKey } from '@app/modules/device-management/modules/activation-keys/models/activation-key.model';
import { BtnSwitchComponent } from '@app/shared/components/btn-switch/btn-switch.component';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { NotificationService } from '@app/shared/services/notification.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { ActivationKeysService } from '../../services/activation-keys.service';

@Component({
    selector: 'app-activation-keys-list',
    imports: [AutoSearchBoxComponent, NgxDatatableModule, DatePipe, NgbPopover, BtnSwitchComponent],
    templateUrl: './activation-keys-list.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ActivationKeysListComponent extends BaseListClientComponent<ActivationKey> implements OnInit {
    private readonly activationKeysService = inject(ActivationKeysService);
    private readonly userContextService = inject(UserContextService);
    protected readonly permissionService = inject(PermissionService);
    private readonly notificationService = inject(NotificationService);
    private readonly clipboard = inject(Clipboard);

    readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');
    readonly dateCell = viewChild<TemplateRef<never>>('dateCellTemplate');
    readonly keyCell = viewChild<TemplateRef<never>>('keyCellTemplate');

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                cellTemplate: this.keyCell(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 50
            },
            {
                name: 'Key',
                prop: 'key',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 250,
            },
            {
                name: 'Creation Date',
                prop: 'creationDate',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150,
                cellTemplate: this.dateCell()
            },
            {
                name: 'Deactivation Date',
                prop: 'deactivationDate',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150,
                cellTemplate: this.dateCell()
            },
            {
                name: 'Active',
                prop: 'isActive',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.actionsTemplate(),
                width: 200,
                sortable: true,
                resizeable: false,
                canAutoResize: false
            },
        ];

        super.initialize(this.activationKeysService.getActivationKeysForOrg.bind(this.activationKeysService, this.userContextService.currentUser.organizationId), columns);

        this.table().sorts = [{ prop: 'isActive', dir: 'desc' }, { prop: 'creationDate', dir: 'desc' }];
    }

    protected createActivationKey(): void {
        this.activationKeysService.createActivationKey(this.userContextService.currentUser.organizationId)
            .subscribe(res => {
                this.notificationService.notify(res.message);
                this.loadData();
            });
    }

    protected toggleActivationKey(activationKey: ActivationKey): void {
        this.activationKeysService.toggleActivationKey(activationKey.activationKeyId)
            .subscribe(res => {
                activationKey.deactivationDate = res.data.deactivationDate;
                activationKey.isActive = res.data.isActive;
                this.notificationService.notify(res.message);
            });
    }

    protected copyActivationKey(key: string): void {
        if (this.clipboard.copy(key)) {
            this.notificationService.notify('Key copied');
        }
    }
}
