import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isIpInCidr } from '@app/shared/utils/helpers';
import { VmNetwork } from '../../../../models/vm-network.model';

export function vmIpCidrValidator(networks: VmNetwork[], ipControlName = 'ipAddress', networkSelectedIdControl = 'networkSelectedId'): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
        const ip = formGroup.get(ipControlName);
        const networkSelectedId = formGroup.get(networkSelectedIdControl);

        if (!networkSelectedId.value) {
            return null; // No error if network is not selected
        }

        // Find the network with the selected ID
        const selectedNetwork = networks.find(network => network.id === networkSelectedId.value);

        if (!selectedNetwork) {
            return null;
        }

        const cidr = selectedNetwork.cidr;
        const gateway = selectedNetwork.gateway;

        if (ip?.value === null || ip?.value === '') {
            return null;
        }

        const isIpDifferentFromGateway = ip?.value !== gateway;

        const isValid = isIpInCidr(ip.value, cidr);

        return isValid && isIpDifferentFromGateway ? null : { invalidIpAddress: true };
    };
}

/**
 * Custom Validator for MAC Address
 * @returns A ValidatorFn that checks the MAC address format.
 */
export function macAddressValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const macRegex = /^([0-9A-Fa-f]{2}([-:])){5}([0-9A-Fa-f]{2})$/;
        const value = control.value;

        if (!value) {
            return null; // No error if value is empty (handled by required validator if needed)
        }

        const isValid = macRegex.test(value);
        return isValid ? null : { invalidMacAddress: true };
    };
}
