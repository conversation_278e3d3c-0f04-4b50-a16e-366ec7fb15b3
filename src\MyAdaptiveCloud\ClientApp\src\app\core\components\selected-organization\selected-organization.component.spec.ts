import { TestBed } from '@angular/core/testing';
import { SelectableOrganizationService } from '@app/core/services/selectable-organization.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectedOrganizationComponent } from './selected-organization.component';

describe('SelectedOrganizationComponent', () => {
    let component: SelectedOrganizationComponent;

    beforeEach(() => {

        TestBed.configureTestingModule({
            imports: [
                SelectedOrganizationComponent
            ],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(SelectableOrganizationService),
                provideMock(UserContextService)
            ]
        });

        component = TestBed.createComponent(SelectedOrganizationComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
