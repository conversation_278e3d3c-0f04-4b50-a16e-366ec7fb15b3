import { TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AcusageRunlogDetailsComponent } from './acusage-runlog-details.component';

describe('AcusageRunlogDetailsComponent', () => {
    let component: AcusageRunlogDetailsComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ActivatedRoute),
                provideMock(AdaptiveCloudUsageService)
            ],
            imports: [
                AcusageRunlogDetailsComponent
            ]
        });

        component = TestBed.createComponent(AcusageRunlogDetailsComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
