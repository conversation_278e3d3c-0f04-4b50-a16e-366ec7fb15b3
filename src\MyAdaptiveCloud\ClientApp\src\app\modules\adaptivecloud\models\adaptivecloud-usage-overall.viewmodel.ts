export interface AdaptiveCloudUsageOverallViewModel {
    id: string;
    name: string;
    totals: {
        cost: number;
        summary: string;
    };
    vCPUs: {
        cost: number;
        summary: string[];
    };
    ram: {
        cost: number;
        summary: string[];
    };
    ipAddresses: {
        cost: number;
        summary: string[];
    };
    networkBytes: {
        cost: number;
        summary: string[];
    };
    primaryStorage: {
        cost: number;
        summary: string[];
    };
    secondaryStorage: {
        cost: number;
        summary: string[];
    };
    licensing: {
        cost: number;
        summary: string[];
    };
}
