import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { distinctUntilChanged, Observable, of, tap } from 'rxjs';
import { ReinstallVmForm } from '../../forms/reinstall-vm.form';
import { OsTypeFilter } from '../../models/os-type-filter.enum';
import { OsType } from '../../models/os-type.enum';
import { TemplateViewModel } from '../../models/template.view.model';
import { VmManagementService } from '../../services/vm-management.service';
import { VmMediaService } from '../../services/vm-media-service';
import { MediaSelectorComponent } from '../media-selector/media-selector.component';

@Component({
    selector: 'app-reinstall-vm',
    imports: [ReactiveFormsModule, BtnSubmitComponent, MediaSelectorComponent, AsyncPipe],
    templateUrl: './reinstall-vm.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ReinstallVmComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);
    private readonly vmMediaService = inject(VmMediaService);
    private readonly destroyRef = inject(DestroyRef);

    readonly inputData = signal<{
        virtualMachineId: string;
        virtualMachineZoneId: string;
        virtualMachineHasIso: boolean;
        domainId: string;
        account: string;
    }>(null);

    protected readonly isSubmitting = signal<boolean>(false);
    protected readonly form = this.formBuilder.group<ReinstallVmForm>({
        media: this.formBuilder.control<TemplateViewModel | null>(null),
        osTypeFilter: this.formBuilder.control<OsTypeFilter | null>(null)
    });
    protected media$: Observable<TemplateViewModel[]>;
    protected readonly mediaTypeLabel = computed(() => (this.inputData().virtualMachineHasIso ? 'ISO' : 'template'));
    protected readonly osType = OsType;

    private featuredMedia: TemplateViewModel[];
    private publicMedia: TemplateViewModel[];
    private myMedia: TemplateViewModel[];

    ngOnInit(): void {

        this.form.controls.osTypeFilter.valueChanges
            .pipe(
                distinctUntilChanged(),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(value => {
                if (value === OsTypeFilter.Featured) {
                    const service = this.inputData().virtualMachineHasIso ?
                        this.vmMediaService.getFeaturedISOsByZoneId(this.inputData().virtualMachineZoneId) :
                        this.vmMediaService.getFeaturedTemplatesByZoneId(this.inputData().virtualMachineZoneId);
                    this.media$ = this.featuredMedia ? of(this.featuredMedia) : service.pipe(tap(media => this.featuredMedia = media));
                } else if (value === OsTypeFilter.Public) {
                    const service = this.inputData().virtualMachineHasIso ?
                        this.vmMediaService.getPublicISOsByZoneId(this.inputData().virtualMachineZoneId, this.inputData().domainId, this.inputData().account) :
                        this.vmMediaService.getPublicTemplatesByZoneId(this.inputData().virtualMachineZoneId, this.inputData().domainId, this.inputData().account);
                    this.media$ = this.publicMedia ? of(this.publicMedia) : service.pipe(tap(media => this.publicMedia = media));
                } else if (value === OsTypeFilter.MyTemplates) {
                    const service = this.inputData().virtualMachineHasIso ?
                        this.vmMediaService.getMyISOsByZoneId(this.inputData().virtualMachineZoneId, this.inputData().domainId, this.inputData().account) :
                        this.vmMediaService.getMyTemplatesByZoneId(this.inputData().virtualMachineZoneId, this.inputData().domainId, this.inputData().account);
                    this.media$ = this.myMedia ? of(this.myMedia) : service.pipe(tap(media => this.myMedia = media));
                }
            });
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected reinstallVm() {
        this.isSubmitting.set(true);
        if (this.form.valid) {

            this.vmManagementService.reinstallVirtualMachine(
                this.inputData().virtualMachineId,
                this.form.controls.media.value?.id
            )
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
