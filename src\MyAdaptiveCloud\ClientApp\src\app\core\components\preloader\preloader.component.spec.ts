import { TestBed } from '@angular/core/testing';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { PreloaderComponent } from './preloader.component';

describe('PreloaderComponent', () => {
    let component: PreloaderComponent;

    beforeEach(() => {

        TestBed.configureTestingModule({
            providers: [
                provideMock(UserContextService)
            ],
            imports: [
                PreloaderComponent
            ]
        });

        component = TestBed.createComponent(PreloaderComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
