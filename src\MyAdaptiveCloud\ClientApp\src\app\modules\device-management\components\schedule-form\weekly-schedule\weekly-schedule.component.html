@if (form) {
  <form class="form-horizontal" [formGroup]="form" novalidate>
    <div class="mb-3">
      <div class="col-xl-12">
        <label class="mx-1">Recur every:</label>
        <input formControlName="freqRecurrenceFactor" placeholder="# of weeks" class="form-control d-inline w-15" />
        <label class="mx-1">weeks on:</label>
      </div>
    </div>
    <div class="mb-3 row" formArrayName="weeklyDays">
      @for (day of weekDays; track day; let i = $index) {
        <div class="col-3">
          <div class="form-check">
            <input
              id="day-{{i}}"
              class="form-check-input"
              type="checkbox"
              formControlName="{{i}}"
              [value]="day.value"/>
            <label for="day-{{i}}" class="form-check-label">
              {{day.name}}
            </label>
          </div>
        </div>
      }
    </div>
  </form>
}
