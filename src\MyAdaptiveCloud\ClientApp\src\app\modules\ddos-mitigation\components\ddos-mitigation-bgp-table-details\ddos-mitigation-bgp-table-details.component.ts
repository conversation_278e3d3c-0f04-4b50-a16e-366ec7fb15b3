import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Bgp } from '../../models/bgp.model';
import { CommunitiesPipe } from '../../pipes/communities.pipe';

@Component({
    selector: 'app-ddos-mitigation-bgp-table-details',
    imports: [CommunitiesPipe],
    templateUrl: './ddos-mitigation-bgp-table-details.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DdosMitigationBgpTableDetailsComponent {
    protected readonly activeModal = inject(NgbActiveModal);
    readonly bgpDetails = signal<Bgp>(null);
}
