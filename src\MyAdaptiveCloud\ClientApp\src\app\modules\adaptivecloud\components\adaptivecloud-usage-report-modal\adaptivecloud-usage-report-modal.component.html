<div class="modal-header">
    <h4 class="modal-title">Generate Usage Report</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form class="form-horizontal" [formGroup]="form">
        <div class="mb-3 row">
            <div class="col-xl-12">
                <label class="form-label">Report Type</label>
                <ng-select placeholder="Select a Report Type" [items]="reportTypes" formControlName="reportType"
                    [clearable]="false" [searchable]="false" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-xl-12">
                <label class="form-label">Report Format</label>
                <ng-select placeholder="Select a Report Format" [items]="reportFormats" formControlName="reportFormat"
                    [clearable]="false" [searchable]="false" />
            </div>
        </div>
        <hr />
        <div class="mb-3 row">
            <div class="col-xl-12">
                <label class="form-label">Select Accounts</label>
                <app-elements-selector class="w-100" [elements]="accounts" [selectedElements]="selectedAccounts"
                    (updateSelectedEvent)="updateSelectedAccounts($event)" />
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
    <app-btn-submit [btnClasses]="'ms-2 btn-primary'" [disabled]="form?.invalid" (submitClickEvent)="generateReport()">
        Generate Report</app-btn-submit>
</div>