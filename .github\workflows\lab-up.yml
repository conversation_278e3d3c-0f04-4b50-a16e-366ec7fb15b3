name: jen<PERSON>-<PERSON><PERSON>

# Controls when the workflow will run
on: workflow_dispatch
  # Triggers the workflow on push or pull request events but only for the main branch
  #push:
  #  branches: [ main ]
  #pull_request:
  #  branches: [ master ]
    #types: [ opened ]

jobs:
  lab-deploy:
    env:
      JSON_BRANCH: "${{ toJSON('BRANCH') }}:"
      JSON_PARAMS: ${{ toJSON(github.head_ref || github.ref_name) }}
    #needs: [ build-dotnet, build-angular ]
    name: Deploy to the lab
    runs-on: ubuntu-latest
    steps:
      - name: Trigger jenkins deploy job
        uses: joshlk/jenkins-githubaction@master
        with:
          url: https://jenkins.ippathways.com/jenkins
          job_name: myadaptivecloud-dynamiclab-deploy
          username: ggood<PERSON>
          api_token: ${{ secrets.JENKINSAPITOKEN }}
          parameters: ${{ format('{{ {0} {1} }}', env.JSON_BRANCH, env.JSO<PERSON>_PARAMS) }}
          timeout: "1000"
