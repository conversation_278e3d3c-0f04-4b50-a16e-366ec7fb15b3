import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UpdateReleaseTagsModalComponent } from './update-release-tags-confirmation-modal.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

describe('UpdateReleaseTagsModalComponent', () => {
    let component: UpdateReleaseTagsModalComponent;
    let fixture: ComponentFixture<UpdateReleaseTagsModalComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [UpdateReleaseTagsModalComponent],
            providers: [
                NgbActiveModal
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(UpdateReleaseTagsModalComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
