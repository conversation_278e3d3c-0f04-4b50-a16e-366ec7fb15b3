<div class="card-body">
    <div class="content-sub-heading d-flex justify-content-between">
        <div class="search-bar">
            <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)"
                [dataItemName]="'searchTerm'" />
        </div>
        <div class="action-buttons">
            <app-filters-form [filters]="filters" (filtersApplied)="onFilterFormChanged()"
                (filtersReset)="onFilterFormChanged()">
                <app-list-networks-filter [filters]="filters" [zones]="store.zones()" />
            </app-filters-form>
            @if (networkingPermissionService.canCreateNetwork()) {
            <div class="d-inline" [title]="!store.getAccount() ? 'Select an account to create a network' : ''">
                <button class="btn btn-primary" [disabled]="!store.getAccount()" (click)="openAddNetworkModal()">
                    Create Network
                </button>
            </div>
            }
        </div>
    </div>

    <div class="content-sub-heading pt-0">
        <app-pill-filter [appliedFilters]="appliedFilters | async"
            (filterRemoved)="onFilterRemoved($event)" />
    </div>

    <div class="card card-default">
        <div class="card-body">
            <ngx-datatable #table class='table bootstrap no-detail-row' (sort)="onSorting($event)"
                (page)="onPageChanged($event)" />
        </div>
    </div>

    <ng-template #statusRow let-row="row">
        @if (toItem(row); as row) {
            @switch (row.state) {
                @case ('Allocated') {
                <div title="Network is allocated and will be fully setup once a VM is assigned to it">
                    <i [class]="'fa fa-check-circle text-primary me-1 status-icon'"></i>
                    {{ row.state }}
                </div>
                }
                @case ('Implemented') {
                <div title="Network is implemented and fully setup">
                    <i [class]="'fa fa-check-circle text-success me-1 status-icon'"></i>
                    {{ row.state }}
                </div>
                }
                @case ('Implementing') {
                <div class="d-flex" title="Network is being implemented">
                    <i [class]="'icon-network-implementing status-icon me-1'"></i>
                    {{ row.state }}
                </div>
                }
                @case ('Setup') {
                <div title="Network was pre-setup and is ready for use">
                    <i [class]="'fa fa-check-circle text-success me-1 status-icon'"></i>
                    {{ row.state }}
                </div>
                }
            }
        }
    </ng-template>
    <ng-template #detailNetworkRow let-row="row">
        @if (toItem(row); as row) {
        <div class="d-flex align-items-center">
            <a [routerLink]="[NETWORKING_ROUTE_SEGMENTS.NETWORK, row.id, NETWORKING_ROUTE_SEGMENTS.NETWORK_DETAIL]" [relativeTo]="activateRoute.parent"
                class="text-decoration-none">
                {{ row.name }}
            </a>
        </div>
        }
    </ng-template>
    <ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
        <span (click)="sort()" class="clickable">
            {{ column.name }}
            <span
                [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
            </span>
        </span>
    </ng-template>

    <ng-template #actionsTemplate let-row="row">
        @if (toItem(row); as row) {
            @if (networkingPermissionService.canEditNetwork()) {
                <app-table-action [icon]="'far fa-edit'" [title]="'Edit'" [enabled]="row.type !== 'Shared'"
                    (clickHandler)="editNetwork(row)" />
            }
            @if (networkingPermissionService.canRestartNetwork()) {
                @let restartEnabled = row.state === 'Setup' || row.state === 'Implemented';
                <app-table-action [icon]="'fa fa-solid fa-arrow-rotate-left'" [enabled]="restartEnabled"
                    [title]="restartEnabled ? 'Restart' : 'Network is not in the right state to be restarted. Correct states are: Implemented, Setup'"
                    (clickHandler)="restartNetwork(row)" />
            }
            @if (networkingPermissionService.canDeleteNetwork()) {
                <app-table-action [icon]="'far fa-trash-can'" [enabled]="!row.isSystem"
                    [title]="!row.isSystem ? 'Delete' : 'System networks cannot be deleted'"
                    (clickHandler)="deleteNetwork(row)" />
            }
        }
    </ng-template>

</div>
