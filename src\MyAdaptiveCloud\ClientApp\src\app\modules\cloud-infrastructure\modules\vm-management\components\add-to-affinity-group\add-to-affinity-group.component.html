<div class="modal-header">
    <h4 class="modal-title">Add to Affinity Group</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.close()"></button>
</div>
<div class="modal-body">

    @if (vmManagementPermissionService.canCreateAffinityGroup()) {
        <p>Create new affinity group or select an existing affinity group</p>
        <form id="useNewOrExistingForm" class="form-horizontal g-3 mb-3" [formGroup]="form">
            <div class="form-check form-check-inline mb-3">
                <input class="form-check-input" id="useNew" type="radio" formControlName="useExisting"
                    [value]="false">
                <label class="form-check-label" for="useNew">
                    New
                </label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" id="useExisting" type="radio" formControlName="useExisting"
                    [value]="true">
                <label class="form-check-label" for="useExisting">
                    Existing
                </label>
            </div>
        </form>
    }

    @if (!form.controls.useExisting.value) {
        <form [formGroup]="createNewForm" id="createNewForm" class="row g-3">
            <div class="row mb-3">
                <label for="name" class="col-3 col-form-label">Name<span class="required-asterisk">*</span></label>
                <div class="col">
                    <input id="name" type="text" class="form-control" formControlName="name">
                </div>
            </div>
            <div class="row mb-3">
                <label for="description" class="col-3 col-form-label">Description</label>
                <div class="col">
                    <input id="description" type="text" class="form-control" formControlName="description">
                </div>
            </div>
            <div class="row mb-3">
                <label for="affinity-group-types" class="col-3 col-form-label">Type
                    <span class="required-asterisk">*</span>
                </label>
                <div class="col">
                    <ng-select id="affinity-group-types" [items]="affinityGroupTypes()" bindLabel="type" bindValue="type"
                        formControlName="type" />
                </div>
            </div>
        </form>
    } @else {
        <form [formGroup]="useExistingForm" id="useExistingForm" class="row g-3">
            <div class="row mb-3">
                <label for="affinity-groups" class="col-3 col-form-label">Affinity Group
                </label>
                <div class="col">
                    <ng-select id="affinity-groups" [items]="availableAffinityGroups()" bindLabel="name" bindValue="id"
                        formControlName="id" />
                </div>
            </div>
        </form>
    }
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="activeModal.close()">Cancel</button>
    <app-btn-submit
        [disabled]="isSubmitting() || (form.controls.useExisting.value && !useExistingForm.valid) || (!form.controls.useExisting.value && !createNewForm.valid)"
        [btnClasses]="'btn-primary'" (submitClickEvent)="submit()">Save</app-btn-submit>
</div>
