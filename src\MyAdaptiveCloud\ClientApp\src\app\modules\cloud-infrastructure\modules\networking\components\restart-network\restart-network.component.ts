import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { RestartNetworkForm } from '../../forms/restart-network.form';
import { NetworkingService } from '../../services/networking.service';

@Component({
    selector: 'app-restart-network',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgbPopover],
    templateUrl: './restart-network.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class RestartNetworkComponent {
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly networkingService = inject(NetworkingService);

    readonly networkId = signal<string | null>(null);

    protected form = this.formBuilder.group<RestartNetworkForm>({
        cleanup: this.formBuilder.control<boolean>(false),
        makeRedundant: this.formBuilder.control<boolean>(false)
    });

    protected readonly isSubmitting = signal<boolean>(false);

    protected cancel() {
        this.activeModal.close();
    }

    protected restartNetwork() {
        if (this.form.valid) {
            this.isSubmitting.set(true);
            this.networkingService.restartNetwork(this.networkId(), this.form.controls.cleanup.value, this.form.controls.makeRedundant.value)
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
