import { Injectable, inject } from '@angular/core';
import { ApiService } from '@app/shared/services/api.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { DDOS_MITIGATION_ENDPOINT_SEGMENTS } from './ddos-mitigation-endpoint-segments';

@Injectable({
    providedIn: 'root'
})
export class DDoSMitigationTasksService {

    private readonly apiService = inject(ApiService);
    private readonly userContext = inject(UserContextService);

    getList() {
        return this.apiService.get(`${DDOS_MITIGATION_ENDPOINT_SEGMENTS.ROOT}/${DDOS_MITIGATION_ENDPOINT_SEGMENTS.TASKS}/${this.userContext.currentUser.organizationId}`);
    }

}

