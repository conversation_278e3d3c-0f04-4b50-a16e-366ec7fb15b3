import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { CloudInfraDomainService } from '../../services/cloud-infra-domain.service';
import { getMockZoneDomainAccountStore } from '../../store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '../../store/zone-domain-account-store';
import { DomainAccountTreeComponent } from './domain-account-tree.component';

describe('DomainAccountTreeComponent', () => {
    let component: DomainAccountTreeComponent;
    let fixture: ComponentFixture<DomainAccountTreeComponent>;

    beforeEach(async () => {

        const mockMockZoneDomainAccountStore = getMockZoneDomainAccountStore();

        await TestBed.configureTestingModule({
            imports: [DomainAccountTreeComponent],
            providers: [
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraDomainService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockMockZoneDomainAccountStore,
                }]
        })
            .compileComponents();

        fixture = TestBed.createComponent(DomainAccountTreeComponent);
        fixture.componentRef.setInput('domain', mockMockZoneDomainAccountStore.selectedDomain());

        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should display domain name', () => {
        const domainNameElement = fixture.debugElement.query(By.css('[data-testid="domain-name"]'));
        expect(domainNameElement.nativeElement.textContent).toContain('ROOT');
    });

    it('should display subdomains and accounts when expanded', () => {
        component.domain().isExpanded = true;
        fixture.detectChanges();

        const accountElement = fixture.debugElement.query(By.css('[data-testid="account-name"]'));
        expect(accountElement.nativeElement.textContent).toContain('Account 1');
    });
});
