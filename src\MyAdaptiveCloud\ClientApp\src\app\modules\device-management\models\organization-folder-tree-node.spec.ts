import { TestBed } from '@angular/core/testing';
import { DeviceFolderTreeNode } from './device-folder-tree-node';
import { OrganizationFolderTreeNode } from './organization-folder-tree-node';
import { UnassignedDevicesFolderTreeNode } from './unassigned-devices-folder-tree-node';

const ROOT_FOLDER_ID = 0;
describe('OrganizationFolderTreeNode', () => {

    beforeEach(() => {

        TestBed.configureTestingModule({
            providers: [OrganizationFolderTreeNode]
        });
    });

    describe('folder class', () => {
        let rootFolder: OrganizationFolderTreeNode;

        beforeEach(() => {
            rootFolder = new OrganizationFolderTreeNode({ name: 'Root Folder', organizationId: 1 });
        });

        it('should return correct UniqueStringId', () => {
            const result = rootFolder.getUniqueStringId();
            expect(result).toEqual('O1');
        });

        it('should return organization Id when getId is called', () => {
            const result = rootFolder.getId();
            expect(result).toEqual(1);
        });

        it('should return true when isOrganizationFolder() is called', () => {
            const result = rootFolder.isOrganizationFolder();
            expect(result).toBeTrue();
        });

        it('should return false when isUnassignedDevicesFolder() is called', () => {
            const result = rootFolder.isUnassignedDevicesFolder();
            expect(result).toBeFalse();
        });

        it('should return false when shouldLoadDevices() is called', () => {
            const result = rootFolder.shouldLoadDevices();
            expect(result).toBeFalse();
        });

        it('should add the unassigned devices folder and has subfolders should be false', () => {
            const unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(ROOT_FOLDER_ID);
            rootFolder.addSubfolder(unassignedDevicesFolder);

            expect(rootFolder.getSubFolders().includes(unassignedDevicesFolder)).toBeTrue();
            expect(rootFolder.hasSubfolders()).toBeFalse();
            expect(unassignedDevicesFolder.parent()).toBe(rootFolder);
            expect(unassignedDevicesFolder.level()).toBe(rootFolder.level() + 1);
        });

        it('should add the unassigned devices folder and has subfolders should be true', () => {
            const unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(ROOT_FOLDER_ID);
            const subOrgA = new OrganizationFolderTreeNode({ organizationId: 2, name: 'A' });
            rootFolder.addSubfolders([unassignedDevicesFolder, subOrgA]);

            expect(rootFolder.getSubFolders().includes(unassignedDevicesFolder)).toBeTrue();
            expect(rootFolder.hasSubfolders()).toBeTrue();
            expect(unassignedDevicesFolder.parent()).toBe(rootFolder);
            expect(unassignedDevicesFolder.level()).toBe(rootFolder.level() + 1);
        });

        it('should set UnassignedDevicesFolderTreeNode when there are other subfolders present', () => {
            const subFolder3 = new DeviceFolderTreeNode(({ folderId: 4, name: 'Folder 3' }));
            const subFolder4 = new DeviceFolderTreeNode(({ folderId: 5, name: 'Folder 4' }));

            rootFolder.addSubfolders([subFolder3, subFolder4]);

            const unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(ROOT_FOLDER_ID);

            rootFolder.setSubfolders([unassignedDevicesFolder]);

            expect(rootFolder.getSubFolders().includes(unassignedDevicesFolder)).toBe(true);
            expect(rootFolder.hasSubfolders()).toBe(false);
            expect(rootFolder.getSubFolders().length).toBe(1);
            expect(unassignedDevicesFolder.parent()).toBe(rootFolder);
            expect(unassignedDevicesFolder.getParentId()).toBe(rootFolder.folderId);
            expect(unassignedDevicesFolder.level()).toBe(rootFolder.level() + 1);
        });

    });

    describe('lazy load', () => {
        let rootFolder: OrganizationFolderTreeNode;

        beforeEach(() => {
            rootFolder = new OrganizationFolderTreeNode({ name: 'Root Folder', organizationId: 0 });
        });

        it('should return false when shouldLoadSubfolders is called over a root organization', () => {
            const result = rootFolder.shouldLoadSubfolders();
            expect(result).toBeFalse();
        });

        it('should return false when shouldLoadSubfolders is called over a non root organization and has no folders', () => {
            const folder = new OrganizationFolderTreeNode({ name: 'Folder', organizationId: 1, parentOrganizationId: 0 });
            rootFolder.addSubfolder(folder);

            const result = folder.shouldLoadSubfolders();
            expect(result).toBeFalse();
        });

        it('should return true when shouldLoadSubfolders is called has no folders but has devices', () => {
            const folder = new OrganizationFolderTreeNode({ name: 'Folder', organizationId: 1, parentOrganizationId: 0, deviceCountCurrentFolder: 2 });
            rootFolder.addSubfolder(folder);

            const result = folder.shouldLoadSubfolders();
            expect(result).toBeTrue();
        });

        it('should return false when shouldLoadDevices is called over a root organization', () => {
            const result = rootFolder.shouldLoadDevices();
            expect(result).toBeFalse();
        });

        it('should return false when shouldLoadDevices is called over a non root organization', () => {
            const folder = new OrganizationFolderTreeNode({ name: 'Folder', organizationId: 1, parentOrganizationId: 0 });
            rootFolder.addSubfolder(folder);

            const result = folder.shouldLoadDevices();
            expect(result).toBeFalse();
        });

    });
});
