import { DebugElement } from '@angular/core';
import { ComponentFixture, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { UserAuthenticator } from '@app/core/models/user-authenticator';
import { TwoFactorAuthenticationService } from '@app/core/services/two-factor-authentication.service';
import { AuthService } from '@app/shared/services/auth.service';
import { ModalService } from '@app/shared/services/modal.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { ListTwoFactorAuthenticationComponent } from './list-two-factor-authentication.component';

describe('ListTwoFactorAuthenticationComponent', () => {
    let component: ListTwoFactorAuthenticationComponent;
    let fixture: ComponentFixture<ListTwoFactorAuthenticationComponent>;
    let mockTwoFactorAuthenticationService: jasmine.SpyObj<TwoFactorAuthenticationService>;
    let mockModalService: jasmine.SpyObj<ModalService>;
    let mockAuthService: jasmine.SpyObj<AuthService>;

    const data: UserAuthenticator[] = [
        {
            id: '1',
            name: 'Greg'
        },
        {
            id: '2',
            name: 'John'
        },
        {
            id: '3',
            name: 'Juan'
        },
        {
            id: '4',
            name: 'Isaac'
        },
    ];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                ListTwoFactorAuthenticationComponent
            ],
            providers: [
                provideMock(TwoFactorAuthenticationService),
                provideMock(AuthService),
                provideMock(ModalService)
            ]
        });
        mockTwoFactorAuthenticationService = TestBed.inject(TwoFactorAuthenticationService) as jasmine.SpyObj<TwoFactorAuthenticationService>;
        mockTwoFactorAuthenticationService.getUserAuthenticatorsList.and.returnValue(of({ data, message: 'success', totalCount: data.length }));
        mockTwoFactorAuthenticationService.addAuthenticator.and.returnValue(of({ message: 'success' }));
        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;
        mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

        fixture = TestBed.createComponent(ListTwoFactorAuthenticationComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('Create', () => {
        it('should be created', () => {
            expect(component).toBeTruthy();
        });
    });

    describe('OnInit', () => {

        it('should call TwoFactorAuthenticationService', () => {
            expect(mockTwoFactorAuthenticationService.getUserAuthenticatorsList).toHaveBeenCalled();
        });

        it('should have the right amount of data', () => {
            expect(component.table().count).toEqual(4);
            expect(component.table().rows).toEqual(data);
        });
    });

    describe('Component Interaction', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
            fixture.detectChanges();
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });

        it('should call the delete function on click', () => {
            spyOn(component, 'deleteAuthenticator');
            const rows = dataTable.querySelectorAll<HTMLElement>('.fa-minus-circle');
            const button = rows[0];
            button.click();
            expect(component.deleteAuthenticator).toHaveBeenCalled();
        });

        it('should display the correct data', () => {
            const rows = dataTable.querySelectorAll<HTMLElement>('datatable-row-wrapper');
            const thirdRow = rows[2];
            expect(thirdRow.innerText).toEqual(data[2].name);
        });
    });

    describe('Add Authenticator', () => {

        const addAuthenticatorDialogData = {
            title: 'Add authenticator',
            content: 'To add another authenticator to your account, you will need to log out and log back in.',
            confirmButtonText: 'Add authenticator',
            showCancelButton: true
        };

        const confirmAddAuthenticatorDialogData = {
            title: 'Confirm',
            content: 'Would you like to log out now and add an authenticator? If not, this option will be available at your next login automatically.',
            showCancelButton: true,
            cancelButtonText: 'Later',
            confirmButtonText: 'Now'
        };

        beforeEach(() => {
            fixture.detectChanges();
        });

        it('should call addAuthenticator and redirect to login when modal both modals are confirmed', fakeAsync(() => {
            mockModalService.openConfirmationDialog.withArgs(addAuthenticatorDialogData).and.returnValue({ closed: of(true) } as NgbModalRef);
            mockModalService.openConfirmationDialog.withArgs(confirmAddAuthenticatorDialogData).and.returnValue({ closed: of(true) } as NgbModalRef);
            fixture.detectChanges();

            const actionButtons = fixture.debugElement.query(By.css('.action-buttons'));
            const addButton = actionButtons.nativeElement.querySelector('.btn.btn-primary');

            addButton.click();
            fixture.detectChanges();
            flush();

            expect(addButton.innerText).toBe('Add Authenticator');
            expect(mockModalService.openConfirmationDialog).toHaveBeenCalledWith(addAuthenticatorDialogData);
            expect(mockModalService.openConfirmationDialog).toHaveBeenCalledWith(confirmAddAuthenticatorDialogData);
            expect(mockTwoFactorAuthenticationService.addAuthenticator).toHaveBeenCalledTimes(1);
            expect(mockAuthService.logoutAndRedirectToLogin).toHaveBeenCalledTimes(1);
        }));

        it('should call addAuthenticator when first modal is confirmed and not redirect to login', fakeAsync(() => {
            mockModalService.openConfirmationDialog.withArgs(addAuthenticatorDialogData).and.returnValue({ closed: of(true) } as NgbModalRef);
            mockModalService.openConfirmationDialog.withArgs(confirmAddAuthenticatorDialogData).and.returnValue({ closed: of(false) } as NgbModalRef);
            fixture.detectChanges();

            const actionButtons = fixture.debugElement.query(By.css('.action-buttons'));
            const addButton = actionButtons.nativeElement.querySelector('.btn.btn-primary');

            addButton.click();
            fixture.detectChanges();
            flush();

            expect(addButton.innerText).toBe('Add Authenticator');
            expect(mockModalService.openConfirmationDialog).toHaveBeenCalledWith(addAuthenticatorDialogData);
            expect(mockModalService.openConfirmationDialog).toHaveBeenCalledWith(confirmAddAuthenticatorDialogData);
            expect(mockTwoFactorAuthenticationService.addAuthenticator).toHaveBeenCalledTimes(1);
            expect(mockAuthService.logoutAndRedirectToLogin).not.toHaveBeenCalled();
        }));

        it('should not call addAuthenticator or redirect to login when first modal is canceled', fakeAsync(() => {
            mockModalService.openConfirmationDialog.withArgs(addAuthenticatorDialogData).and.returnValue({ closed: of(false) } as NgbModalRef);
            mockModalService.openConfirmationDialog.withArgs(confirmAddAuthenticatorDialogData).and.returnValue({ closed: of(false) } as NgbModalRef);
            fixture.detectChanges();

            const actionButtons = fixture.debugElement.query(By.css('.action-buttons'));
            const addButton = actionButtons.nativeElement.querySelector('.btn.btn-primary');

            addButton.click();
            fixture.detectChanges();
            flush();

            expect(addButton.innerText).toBe('Add Authenticator');
            expect(mockModalService.openConfirmationDialog).toHaveBeenCalledWith(addAuthenticatorDialogData);
            expect(mockModalService.openConfirmationDialog).not.toHaveBeenCalledWith(confirmAddAuthenticatorDialogData);
            expect(mockTwoFactorAuthenticationService.addAuthenticator).not.toHaveBeenCalled();
            expect(mockAuthService.logoutAndRedirectToLogin).not.toHaveBeenCalled();
        }));

    });

});

