import { DebugElement, DestroyRef, signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { ZoneViewModel } from '@app/modules/cloud-infrastructure/models/zone.view-model';
import { CPU_CUSTOM_OFFERING_MIN_VALUE, MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { CreateNetworkService } from '../../../../../../services/create-network.service';
import { OsTypeFilter } from '../../../../models/os-type-filter.enum';
import { OsType } from '../../../../models/os-type.enum';
import { TemplateViewModel } from '../../../../models/template.view.model';
import { VmNetwork } from '../../../../models/vm-network.model';
import { VmAffinityGroupsService } from '../../../../services/vm-affinity-groups.service';
import { VmManagementService } from '../../../../services/vm-management.service';
import { VmMediaService } from '../../../../services/vm-media-service';
import { CreateVMWizardStore } from '../../create-vm-wizard-store';
import { CreateVmWizardStepEnum } from '../../models/create-vm-wizard-steps.enum';
import { DiskOffering } from '../../models/disk-offering.model';
import { ServiceOfferingViewModel } from '../../models/service-offering.view-model';
import { CreateVmComputeService } from '../../services/create-vm-compute.service';
import { CreateVmNetworkService } from '../../services/create-vm-network.service';
import { CreateVmService } from '../../services/create-vm-service';
import { CreateVmStorageService } from '../../services/create-vm-storage.service';
import { CreateVmWizardComputeComponent } from './compute.component';

describe('CreateVMWizardComputeComponent', () => {

    const zones: ZoneViewModel[] = [
        {
            id: '1',
            name: 'Zone 1'
        },
        {
            id: '2',
            name: 'Zone 2'
        },
        {
            id: '3',
            name: 'Zone 3'
        }
    ];

    const mockServiceOfferings: ServiceOfferingViewModel[] = [
        {
            name: 'name 1',
            id: '1',
            cpuNumber: 4,
            memory: 4096,
            isCustom: false
        }, {
            name: 'name 2',
            id: '2',
            cpuNumber: 2,
            memory: 2048,
            isCustom: false
        },
        {
            name: 'name 3',
            id: '3',
            cpuNumber: null,
            memory: null,
            isCustom: true
        }
    ];

    const zone1ISOs: TemplateViewModel[] = [
        {
            name: 'CentOS 7.8',
            id: '1',
            size: 123456,
            description: 'CentOS 7.8',
        }
    ];

    const mockVmNetwork: VmNetwork = {
        id: '2',
        name: 'Test Network 2',
        cidr: '***********/24',
        type: 'Private',
        vpcname: 'Test VPC 2',
        ipaddress: '***********',
        macaddress: '00:11:22:33:44:56',
        gateway: '*************'
    };

    const mockOfferingsResponse: DiskOffering[] = [
        {
            id: '1',
            offeringName: 'Offering 1',
            diskSize: 10,
            description: 'Offering 1 description',
            isCustomized: false
        },
        {
            id: '2',
            offeringName: 'Offering 2',
            diskSize: 20,
            description: 'Offering 2 description',
            isCustomized: false
        }
    ];

    let component: CreateVmWizardComputeComponent;
    let fixture: ComponentFixture<CreateVmWizardComputeComponent>;
    let mockCreateVmComputeService: jasmine.SpyObj<CreateVmComputeService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockCreateNetworkService: jasmine.SpyObj<CreateNetworkService>;
    let mockCreateVmNetworkService: jasmine.SpyObj<CreateVmNetworkService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmAffinityGroupsService: jasmine.SpyObj<VmAffinityGroupsService>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockCloudInfraPermissionService: jasmine.SpyObj<CloudInfraPermissionService>;
    let mockCreateVmStorageService: jasmine.SpyObj<CreateVmStorageService>;

    beforeEach(() => {

        const mockZoneDomainAccountStore = { ...getMockZoneDomainAccountStore(), zones: signal(zones) };

        TestBed.configureTestingModule({
            imports: [CreateVmWizardComputeComponent],
            providers: [
                CreateVMWizardStore,
                provideMock(CreateVmComputeService),
                provideMock(CreateNetworkService),
                provideMock(CreateVmNetworkService),
                provideMock(VmMediaService),
                provideMock(UserContextService),
                provideMock(VmAffinityGroupsService),
                provideMock(VmManagementService),
                provideMock(CloudInfraPermissionService),
                provideMock(CreateVmStorageService),
                provideMock(CreateVmService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                },
                DestroyRef,
                FormBuilder
            ]
        });
        mockCreateVmComputeService = TestBed.inject(CreateVmComputeService) as jasmine.SpyObj<CreateVmComputeService>;
        mockCreateVmComputeService.getServiceOfferings.and.returnValue(of(mockServiceOfferings));

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.getFeaturedISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicISOsByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyISOsByZoneId.and.returnValue(of(zone1ISOs));

        mockVmMediaService.getFeaturedTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getPublicTemplatesByZoneId.and.returnValue(of(zone1ISOs));
        mockVmMediaService.getMyTemplatesByZoneId.and.returnValue(of(zone1ISOs));

        mockCreateVmNetworkService = TestBed.inject(CreateVmNetworkService) as jasmine.SpyObj<CreateVmNetworkService>;
        mockCreateVmNetworkService.getNetworks.and.returnValue(of([mockVmNetwork]));

        mockCreateNetworkService = TestBed.inject(CreateNetworkService) as jasmine.SpyObj<CreateNetworkService>;
        mockCreateNetworkService.getLayer2NetworkOfferings.and.returnValue(of([{ id: '1', name: 'L2 Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getIsolatedNetworkOfferings.and.returnValue(of([{ id: '10', name: 'Isolated Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getSharedNetworkOfferings.and.returnValue(of([{ id: '100', name: 'Shared Network Offering', forVPC: false, specifyVLan: false }]));
        mockCreateNetworkService.getVpcOfferings.and.returnValue(of([{ id: '1000', name: 'VPC Offering', cidr: '' }]));

        mockCreateVmStorageService = TestBed.inject(CreateVmStorageService) as jasmine.SpyObj<CreateVmStorageService>;
        mockCreateVmStorageService.getDiskOfferings.and.returnValue(of(mockOfferingsResponse));

        mockVmAffinityGroupsService = TestBed.inject(VmAffinityGroupsService) as jasmine.SpyObj<VmAffinityGroupsService>;
        mockVmAffinityGroupsService.getAffinityGroups.and.returnValue(of([]));

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getKeyPairList.and.returnValue(of([]));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            cloudInfraUserContext: {
                accountName: 'test-account',
                domainId: 'test-domain-id',
                cpuCustomOfferingMaxValue: 4,
                memoryCustomOfferingMaxValue: 8192
            }
        } as UserContext;

        mockCloudInfraPermissionService = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;
        mockCloudInfraPermissionService.isRootAdmin.and.returnValue(false);

        fixture = TestBed.createComponent(CreateVmWizardComputeComponent);
        component = fixture.componentInstance;

        component.store.setZone(zones[0]);
        fixture.detectChanges();
        component.store.setTemplate({ zone: zones[0], osType: OsType.Template, osTypeFilter: OsTypeFilter.Featured, template: zone1ISOs[0], virtualMachineName: 'Test VM' }, true);
        fixture.detectChanges();
        component.store.navigateToStep(CreateVmWizardStepEnum.Compute);

        fixture.detectChanges();
    });

    describe('Initialize', () => {

        let serviceContainer: DebugElement;
        let serviceDetails: DebugElement[];

        beforeEach(() => {
            serviceContainer = fixture.debugElement.query(By.css('table'));
            serviceDetails = serviceContainer.queryAll(By.css('tr'));
        });

        it('should have called the initialization services and populated the service offering list', () => {
            expect(serviceDetails.length).toBe(mockServiceOfferings.length);
            expect(mockCreateVmComputeService.getServiceOfferings).toHaveBeenCalledOnceWith('1', 'domain-id', 'account');
        });

    });

    describe('Form Interaction', () => {

        let serviceOfferingRadioButtons: DebugElement[];

        beforeEach(() => {
            const serviceContainer = fixture.debugElement.query(By.css('table'));
            serviceOfferingRadioButtons = serviceContainer.queryAll(By.css('.form-check-input'));
        });

        it('should not have an service offering selected by default', () => {
            expect(serviceOfferingRadioButtons.every(radio => !radio.nativeElement.checked)).toBeTrue();
            expect(component.store.computeStep.isValid()).toBeFalse();
        });

        it('should select service offering and be valid', () => {
            const selectedServiceOffering = serviceOfferingRadioButtons[1].nativeElement as HTMLInputElement;
            selectedServiceOffering.click();
            selectedServiceOffering.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            expect(component.store.computeStep.isValid()).toBeTrue();
        });

        it('should select custom service offering and be invalid', () => {
            const selectedServiceOffering = serviceOfferingRadioButtons[2].nativeElement as HTMLInputElement;
            selectedServiceOffering.click();
            selectedServiceOffering.dispatchEvent(new Event('input'));
            fixture.detectChanges();
            expect(component.store.computeStep.isValid()).toBeFalse();
        });

        describe('Custom service offering', () => {

            let cpuCoreInput: HTMLInputElement;
            let memoryInput: HTMLInputElement;

            beforeEach(() => {
                const selectedServiceOffering = serviceOfferingRadioButtons[2].nativeElement as HTMLInputElement;
                selectedServiceOffering.click();
                selectedServiceOffering.dispatchEvent(new Event('input'));
                fixture.detectChanges();

                cpuCoreInput = fixture.debugElement.query(By.css('.cpu-input')).nativeElement as HTMLInputElement;
                memoryInput = fixture.debugElement.query(By.css('.memory-input')).nativeElement as HTMLInputElement;
            });

            it('should be valid when valid values have been entered for CPU and memory', () => {
                cpuCoreInput.value = '4';
                cpuCoreInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeFalse();

                memoryInput.value = '4096';
                memoryInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeTrue();
            });

            it('should be invalid when an invalid memory value is entered', () => {
                cpuCoreInput.value = (CPU_CUSTOM_OFFERING_MIN_VALUE).toString();
                cpuCoreInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeFalse();

                memoryInput.value = (MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB - 1).toString();
                memoryInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeFalse();

                memoryInput.value = (mockUserContextService.currentUser.cloudInfraUserContext.memoryCustomOfferingMaxValue + 1).toString();
                memoryInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeFalse();
            });

            it('should be invalid when an invalid cpu value is entered', () => {

                memoryInput.value = MEMORY_CUSTOM_OFFERING_MIN_VALUE_MB.toString();
                memoryInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeFalse();

                cpuCoreInput.value = (CPU_CUSTOM_OFFERING_MIN_VALUE - 1).toString();
                cpuCoreInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeFalse();

                cpuCoreInput.value = (mockUserContextService.currentUser.cloudInfraUserContext.cpuCustomOfferingMaxValue + 1).toString();
                cpuCoreInput.dispatchEvent(new Event('input'));
                fixture.detectChanges();
                expect(component.store.computeStep.isValid()).toBeFalse();
            });

        });

    });

});
