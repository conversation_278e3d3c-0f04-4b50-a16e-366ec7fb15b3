﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class ServiceAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public ServiceAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService, IUserContextService userContextService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int serviceId);

            var serviceOrganizationId = await _entityAuthorizationService.GetServiceOrganizationId(serviceId);
            if (serviceOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, serviceOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, serviceOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    /// <summary>
    /// Specifies what minimum Role is required within the target Organization to access this endpoint.
    /// The target Organization is determined via organizationId or parentOrganizationId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class ServiceAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public ServiceAuthorizeAttribute(params Perms[] perms) : base(typeof(ServiceAuthorizeFilter), perms)
        {
            Name = "serviceId";
        }
    }
}