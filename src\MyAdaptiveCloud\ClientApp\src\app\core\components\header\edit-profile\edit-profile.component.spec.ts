import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiR<PERSON>ult } from '@app/shared/models/api-service/api.result';
import { PasswordPolicy } from '@app/shared/models/password-policy';
import { Person } from '@app/shared/models/person.model';
import { EditProfileRequest } from '@app/shared/models/profile/edit-profile.request';
import { UserContext } from '@app/shared/models/user-context.model';
import { NotificationService } from '@app/shared/services/notification.service';
import { ProfileService } from '@app/shared/services/profile.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { EditProfileComponent } from './edit-profile.component';

describe('EditProfileComponent', () => {
    let component: EditProfileComponent;
    let fixture: ComponentFixture<EditProfileComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockProfileService: jasmine.SpyObj<ProfileService>;

    const user: Person = {
        firstName: 'gaston',
        lastName: 'pauls',
        userId: 1,
        email: '<EMAIL>',
        fullName: 'Gaston Pauls'
    };

    let newPassword = 'Aa12345678';

    let confirmNewPassword = 'Aa12345678';

    const passwordPolicyRequest: PasswordPolicy = {
        length: '8',
        digits: '1',
        lowerCase: '1',
        upperCase: '1',
        username: '<EMAIL>',
        passwordHistory: '3'
    };

    const passwordPolicyApiResult: ApiDataResult<PasswordPolicy> = {
        data: passwordPolicyRequest,
        message: 'success'
    };

    const editProfileRequest: EditProfileRequest = {
        firstName: 'jhon',
        lastName: 'munera'
    };

    const apiResult: ApiResult = { message: 'success' };
    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideMock(ProfileService),
                provideMock(NgbActiveModal),
                provideMock(NotificationService),
                FormBuilder,
                provideMock(UserContextService)
            ],
            imports: [
                EditProfileComponent
            ]
        });

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            userId: 1,
            organizationId: 50
        } as UserContext;
        mockProfileService = TestBed.inject(ProfileService) as jasmine.SpyObj<ProfileService>;
        mockProfileService.getCurrentProfile.and.returnValue(of({ data: user, message: '' }));
        mockProfileService.editProfile.and.returnValue(of(apiResult));
        mockProfileService.getPasswordPolicy.and.returnValue(of(passwordPolicyApiResult));
        mockProfileService.resetPassword.and.returnValue(of(apiResult));
        fixture = TestBed.createComponent(EditProfileComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    describe('Creation', () => {
        it('should create', () => {
            expect(component).toBeTruthy();
        });

        it('should call the userService', () => {
            expect(mockProfileService.getCurrentProfile).toHaveBeenCalledTimes(1);
        });

        it('should fill the form', () => {
            expect(component.form.controls.firstName.value).toEqual(user.firstName);
            expect(component.form.controls.lastName.value).toEqual(user.lastName);
        });

        it('should create the passwordForm', () => {
            expect(component.passwordForm.controls.newPassword.value).toEqual('');
            expect(component.passwordForm.controls.confirmNewPassword.value).toEqual('');
        });

    });

    describe('submitForms', () => {

        it('should submit the form', () => {
            fixture.detectChanges();

            component.form.controls.firstName.setValue(editProfileRequest.firstName);
            component.form.controls.lastName.setValue(editProfileRequest.lastName);
            fixture.detectChanges();

            component.submitForm();

            expect(mockProfileService.editProfile).toHaveBeenCalled();
        });

        it('should submit the passwordForm', () => {
            fixture.detectChanges();

            newPassword = 'Aa12345678';
            confirmNewPassword = 'Aa12345678';

            component.passwordForm.controls.newPassword.setValue(newPassword);
            component.passwordForm.controls.confirmNewPassword.setValue(confirmNewPassword);
            fixture.detectChanges();

            component.submitPasswordForm();
            fixture.detectChanges();

            expect(mockProfileService.getPasswordPolicy).toHaveBeenCalled();
            expect(mockProfileService.resetPassword).toHaveBeenCalled();
        });
    });

    describe('validatePasswordPolicy', () => {

        it('should not submit the passwordForm on different passwords', () => {
            fixture.detectChanges();

            const wrongNewPassword = 'Aa1234567';

            const newPasswordInput = fixture.debugElement.query(By.css('#newPasswordInput'));
            const confirmPasswordInput = fixture.debugElement.query(By.css('#confirmPasswordInput'));
            newPasswordInput.nativeElement.value = wrongNewPassword;
            confirmPasswordInput.nativeElement.value = confirmNewPassword;
            fixture.detectChanges();

            const compiled = fixture.debugElement.nativeElement;
            const form = fixture.debugElement.query(By.css('#passwordForm'));
            form.triggerEventHandler('submit', compiled);

            fixture.detectChanges();

            expect(mockProfileService.getPasswordPolicy).toHaveBeenCalled();
            expect(mockProfileService.resetPassword).not.toHaveBeenCalled();
        });

        it('should not submit the passwordForm on no uppercase', () => {
            fixture.detectChanges();

            passwordPolicyRequest.upperCase = '1';

            newPassword = 'aa12345678';

            confirmNewPassword = 'aa12345678';

            component.passwordForm.controls.newPassword.setValue(newPassword);
            component.passwordForm.controls.confirmNewPassword.setValue(confirmNewPassword);
            fixture.detectChanges();

            component.submitPasswordForm();
            fixture.detectChanges();

            expect(mockProfileService.getPasswordPolicy).toHaveBeenCalled();
            expect(mockProfileService.resetPassword).not.toHaveBeenCalled();
        });

        it('should not submit the passwordForm on no lowercase', () => {
            fixture.detectChanges();

            passwordPolicyRequest.lowerCase = '1';

            newPassword = 'AA12345678';

            confirmNewPassword = 'AA12345678';

            component.passwordForm.controls.newPassword.setValue(newPassword);
            component.passwordForm.controls.confirmNewPassword.setValue(confirmNewPassword);
            fixture.detectChanges();

            component.submitPasswordForm();
            fixture.detectChanges();

            expect(mockProfileService.getPasswordPolicy).toHaveBeenCalled();
            expect(mockProfileService.resetPassword).not.toHaveBeenCalled();
        });

        it('should not submit the passwordForm on no digits', () => {
            fixture.detectChanges();

            passwordPolicyRequest.digits = '1';

            newPassword = 'AAaaaaaaa';

            confirmNewPassword = 'AAaaaaaaa';

            component.passwordForm.controls.newPassword.setValue(newPassword);
            component.passwordForm.controls.confirmNewPassword.setValue(confirmNewPassword);
            fixture.detectChanges();

            component.submitPasswordForm();
            fixture.detectChanges();

            expect(mockProfileService.getPasswordPolicy).toHaveBeenCalled();
            expect(mockProfileService.resetPassword).not.toHaveBeenCalled();
        });

        it('should not submit the passwordForm on no length', () => {
            fixture.detectChanges();

            passwordPolicyRequest.length = '8';

            newPassword = 'AAa';

            confirmNewPassword = 'AAa';

            component.passwordForm.controls.newPassword.setValue(newPassword);
            component.passwordForm.controls.confirmNewPassword.setValue(confirmNewPassword);
            fixture.detectChanges();

            component.submitPasswordForm();
            fixture.detectChanges();

            expect(mockProfileService.getPasswordPolicy).toHaveBeenCalled();
            expect(mockProfileService.resetPassword).not.toHaveBeenCalled();
        });

        it('should not submit the passwordForm on no length', () => {
            fixture.detectChanges();

            passwordPolicyRequest.username = '<EMAIL>';

            newPassword = '<EMAIL>';

            confirmNewPassword = '<EMAIL>';

            component.passwordForm.controls.newPassword.setValue(newPassword);
            component.passwordForm.controls.confirmNewPassword.setValue(confirmNewPassword);
            fixture.detectChanges();

            component.submitPasswordForm();
            fixture.detectChanges();

            expect(mockProfileService.getPasswordPolicy).toHaveBeenCalled();
            expect(mockProfileService.resetPassword).not.toHaveBeenCalled();
        });
    });
});
