import { KeyValuePipe } from '@angular/common';
import { Component, DestroyRef, inject, Input, input, OnInit, output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, filter } from 'rxjs';
import { DeviceThresholdsMetricsForm } from '../../forms/device-threshold-metrics-form';
import { DeviceThresholdInterval } from '../../models/device-threshold-interval';
import { DeviceThresholdMetrics } from '../../models/device-threshold-metrics';
import { DeviceThresholdsMetricsErrorsComponent } from '../device-thresholds-metrics-errors/device-thresholds-metrics-errors.component';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-device-thresholds-metrics',
    imports: [ReactiveFormsModule, DeviceThresholdsMetricsErrorsComponent, KeyValuePipe],
    templateUrl: './device-thresholds-metrics.component.html',
    styleUrl: './device-thresholds-metrics.component.scss'
})
export class DeviceThresholdsMetricsComponent implements OnInit {

    private readonly destroyRef = inject(DestroyRef);

    // TODO: Skipped for migration because:
    //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
    //  and migrating would break narrowing currently.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() form: FormGroup<DeviceThresholdsMetricsForm>;
    readonly deviceAlertThresholdTypeName = input<string>(undefined);
    readonly deviceAlertTypeName = input<string>(undefined);
    readonly useIntervalsForMetrics = input<boolean>(undefined);
    readonly intervals = input.required<DeviceThresholdInterval[]>();

    readonly thresholdWarningChanged = output<DeviceThresholdMetrics>();

    ngOnInit() {
        this.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(values => {
            for (const key in values) {
                if (values[key] !== null && typeof values[key] === 'string') {
                    const originalValue = values[key];

                    let finalValue = values[key].replace(/[^0-9.]/g, '');
                    const intValue = parseInt(finalValue, 10);
                    finalValue = isNaN(intValue) ? '' : intValue.toString();

                    if (originalValue.toLocaleString() !== finalValue.toLocaleString()) {
                        this.form.get(key)?.patchValue(finalValue, { emitEvent: false });
                    }
                }

                if (values[key] === '' || values[key] === 'null') {
                    this.form.get(key)?.patchValue(null, { emitEvent: false });
                }
            }
        });

        this.form.valueChanges.pipe(filter(() => !this.form.pristine && this.form.touched && this.form.valid), debounceTime(500), takeUntilDestroyed(this.destroyRef)).subscribe(value => {
            this.thresholdWarningChanged.emit(value as DeviceThresholdMetrics);
        });
    }
}

