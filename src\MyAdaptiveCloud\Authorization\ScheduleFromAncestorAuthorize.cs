using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
	public class ScheduleFromAncestorAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
		Perms[] perms, int distance, string name) : BaseAsyncAuthorizationFilter(perms, distance, name)
	{
		private readonly IIdentityService _identityService = identityService;
		private readonly IEntityAuthorizationService _entityAuthorizationService = entityAuthorizationService;

		/// <summary>
		/// Grants authorization to a user from a descendant organization of Organization A to access the schedule of Organization A.
		/// </summary>
		public override async Task OnAuthorizationAsync(AuthorizationFilterContext context)
		{
			int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
			if (userId == 0)
				context.Result = new UnauthorizedResult();
			else
			{
				string scheduleIdValue = AuthorizeFilterHelpers.GetEntityValue(context, _name);
				string organizationIdValue = AuthorizeFilterHelpers.GetEntityValue(context, "organizationId");
				_ = int.TryParse(scheduleIdValue, out int policyId);
				_ = int.TryParse(organizationIdValue, out int organizationId);

				var scheduleOrganizationId = await _entityAuthorizationService.GetScheduleOrganizationId(policyId);

				if (scheduleOrganizationId.HasValue)
				{
					// A user wants to see a schedule from an organization that is ancestor of the user's organization
					var scheduleOrganizationIsAnAncestor = _entityAuthorizationService.IsOrganizationWithinOrganizationHierarchy(scheduleOrganizationId.Value, organizationId);
					var scheduleOrganizationIsADescendant = _entityAuthorizationService.IsOrganizationWithinOrganizationHierarchy(organizationId, scheduleOrganizationId.Value);

					if (!scheduleOrganizationIsAnAncestor && !scheduleOrganizationIsADescendant)
						context.Result = new ForbidResult(); //the user organization is not a descendant of the organization the policy belongs to
				}
				else
					context.Result = new BadRequestResult();
			}
		}
	}

	public class ScheduleFromAncestorAuthorizeAttribute : BaseAuthorizeAttribute
	{
		public ScheduleFromAncestorAuthorizeAttribute(params Perms[] perms) : base(typeof(ScheduleFromAncestorAuthorizeFilter), perms)
		{
			Name = "scheduleId";
		}
	}
}