@if (navbarExpanded()) {
    <div class="aside-inner">
        <nav class="sidebar">
            <div class="sidebar-nav d-flex align-items-center ">
                <div class="text-center flex-grow-1">
                    <p class="nav-heading m-0">
                        {{ userContext().name }}
                    </p>
                </div>
                <button (click)="toggleNavbar(false)" class="toggle-nav-btn">
                    <i class="icon-left-nav-expand"></i>
                </button>
            </div>
            <!-- START sidebar nav-->
             @if (menuItems()) {
                 <ul class="sidebar-nav">
                     @for (item of menuItems(); track $index; let i = $index) {
                         <li routerLinkActive="active">
                             @if (!item.submenu?.length) {
                                 <!-- external links -->
                                 @if (item.externalLink) {
                                     <a href target="_blank" [attr.href]="item.externalLink"
                                         title="{{item.title || item.label}}" class="text-decoration-none">
                                         <em class="{{item.icon}}"></em>
                                         <span>{{ item.label}}</span>
                                     </a>
                                 } @else {
                                     <!-- single menu item -->
                                     <a [routerLink]="item.route" (click)="topLevelNavigate()"
                                         title="{{item.title || item.label}}" class="text-decoration-none">
                                         <em class="{{item.icon}}"></em>
                                         <span>{{ item.label}}</span>
                                     </a>
                                 }

                             } @else {
                                 <a title="{{item.title || item.label}}" class="d-flex align-items-center w-100 text-decoration-none"
                                     (click)="toggleSubmenu(i)">
                                     <em class="{{item.icon}} w-10"></em>
                                     <span class="w-70">{{ item.label}}</span>
                                     @if (item.reminderTooltip) {
                                         <i
                                             class="reminder-icon ms-2 float-right w-10 {{item.reminderIcon}}"
                                             [ngbPopover]="item.reminderTooltip"
                                             triggers="hover"
                                             container="body">
                                         </i>
                                        }
                                     <i class="float-right fa-solid w-10"
                                        [class]="{
                                            'fa-chevron-right': expandedSubMenuId() !== i,
                                            'fa-chevron-down': expandedSubMenuId() === i
                                         }">
                                     </i>
                                 </a>

                                 <ul #collapse="ngbCollapse" class="sidebar-nav sidebar-subnav" [id]="i"
                                     [ngbCollapse]="isSubMenuCollapsed(i)">
                                     @for (subitem of item.submenu; track $index) {
                                         <li routerLinkActive="active">
                                             @if (!subitem.submenu && subitem.externalLink) {
                                                 <a href target="_blank"
                                                     [attr.href]="subitem.externalLink" title="{{subitem.title || subitem.label}}"
                                                     class="text-decoration-none w-100 d-flex align-items-center"
                                                     routerLinkActive="active-submenu">
                                                     @if (subitem.icon) {
                                                         <em class="{{subitem.icon}} w-10"></em>
                                                     }
                                                     <span class="w-70">{{ subitem.label}}</span>
                                                     <span class="w-10">&nbsp;</span>
                                                 </a>
                                             }

                                             @if (!subitem.submenu && !subitem.externalLink) {
                                                 <a href [routerLink]="subitem.route"
                                                     routerLinkActive="active-submenu" title="{{subitem.title || subitem.label}}"
                                                     class="text-decoration-none w-100 d-flex align-items-center">
                                                     @if (subitem.icon) {
                                                         <em class="{{subitem.icon}} w-10"></em>
                                                     }
                                                     <span class="w-70">{{subitem.label}}</span>
                                                     @if (subitem.hubFunctionName) {
                                                         <app-hub-count class="w-10" [hubFunctionName]="subitem.hubFunctionName" />
                                                     }
                                                 </a>
                                             }
                                         </li>
                                     }
                                 </ul>
                             }
                         </li>
                     }
                 </ul>
             }
        </nav>
    </div>
} @else {
    <div class="aside-inner collapsed">
        <nav class="sidebar">
            <div class="sidebar-nav align-items-center ">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="text-center user-initial d-flex justify-content-center align-items-center">
                        <h3 class="m-0 d-inline-block fw-bold">
                            {{ userContext().name[0] | uppercase }}
                        </h3>
                    </div>
                </div>
                <button (click)="toggleNavbar(true)" class="toggle-nav-btn p-0 w-100">
                    <i class="icon-left-nav-expand"></i>
                </button>
            </div>

            @if (menuItems()) {
                <ul class="sidebar-nav">
                    @for (item of menuItems(); track $index; let i = $index) {
                        <li class="text-center">
                            <a title="{{item.title || item.label}}">
                                <i
                                    class="{{item.icon}} color-secondary collapsed-nav-item-icon w-100 mx-0"
                                    [class.active-icon]="currentRoute() === item.route">
                                </i>
                            </a>
                        </li>
                    }
                </ul>
            }
        </nav>
    </div>
}
