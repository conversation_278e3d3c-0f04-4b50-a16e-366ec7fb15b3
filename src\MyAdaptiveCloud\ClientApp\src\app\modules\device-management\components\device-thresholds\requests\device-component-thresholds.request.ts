import { DeviceThresholdInheritanceTypeEnum } from '../models/device-threshold-inheritance-type.enum';
import { DeviceThresholdIntervalsRequest } from './device-threshold-intervals.request';
import { DeviceThresholdsMetricsRequest } from './device-threshold-metrics.request';

export class DeviceComponentThresholdsRequest {
    public inheritanceType: DeviceThresholdInheritanceTypeEnum;
    public deviceAlertThresholdType: number;
    public metrics: DeviceThresholdsMetricsRequest;
    public intervals: DeviceThresholdIntervalsRequest;
}
