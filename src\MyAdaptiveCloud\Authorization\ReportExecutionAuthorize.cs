using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    /// Verifies that the current user's Role is authorized to access the target Organization's information
    /// </summary>
    public class ReportExecutionAuthorize : BaseAsyncAuthorizationFilter
    {
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public ReportExecutionAuthorize(
            IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name, bool includeInactiveOrganizations = false) : base(perms, distance, name)
        {
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                var rxecutioId = AuthorizeFilterHelpers.GetEntityValue(context, _name);
                _ = int.TryParse(rxecutioId, out int reportExecutionId);

                var reportExecutionExists = await _entityAuthorizationService.HasReportExecutionId(reportExecutionId);
                if (!reportExecutionExists)
                {
                    context.Result = new BadRequestResult();
                    return;
                }

                await Task.CompletedTask;
            }
        }
    }

    public class ReportExecutionAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public ReportExecutionAuthorizeAttribute(params Perms[] perms) : base(typeof(ReportExecutionAuthorize), perms)
        {
            Name = "reportExecutionId";
        }
    }
}