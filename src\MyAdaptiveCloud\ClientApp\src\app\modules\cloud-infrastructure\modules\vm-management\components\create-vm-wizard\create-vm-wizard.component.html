<div class="wizard-container">
    <div class="wizard-content">
        <div class="header">
            <div class="title">Create Virtual Machine</div>
            <div class="ms-auto">
                <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
                <app-create-vm-wizard-stepper class="ms-2"/>
            </div>
        </div>

        <div class="content">
            <ng-container *ngComponentOutlet="steps[store.currentStep()]" />
        </div>

        <div class="footer">
            <app-create-vm-wizard-stepper class="ms-auto"/>
        </div>
    </div>

    <div class="summary-content">
        <app-create-vm-wizard-summary />
    </div>
</div>
