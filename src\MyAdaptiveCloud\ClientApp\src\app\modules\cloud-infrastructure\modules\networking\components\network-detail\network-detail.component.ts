import { Component, ChangeDetectionStrategy, inject, OnInit } from '@angular/core';
import { NetworkingDetailService } from '../../services/networking-detail.service';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NetworkDetailForm } from '../../models/network-detail-form';

@Component({
    selector: 'app-network-detail',
    imports: [ReactiveFormsModule],
    templateUrl: './network-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})

export class NetworkDetailComponent implements OnInit {

    protected readonly networkingDetailService = inject(NetworkingDetailService);
    protected form: FormGroup<NetworkDetailForm>;
    private readonly formBuilder = inject(FormBuilder);

    ngOnInit(): void {
        this.form = this.formBuilder.group<NetworkDetailForm>({
            cidr: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().cidr, disabled: true }),
            networkdomain: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().networkdomain, disabled: true }),
            zonename: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().zonename, disabled: true }),
            domain: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().domain, disabled: true }),
            account: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().account, disabled: true }),
            type: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().type, disabled: true }),
            id: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().id, disabled: true }),
            networkofferingname: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().networkofferingname, disabled: true }),
            restartrequired: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().restartrequired ? 'Yes' : 'No', disabled: true }),
            redundantrouter: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().redundantrouter ? 'Yes' : 'No', disabled: true })
        });
    }
}
