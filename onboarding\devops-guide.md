# DevOps & Infrastructure Guide

## Table of Contents
- [Infrastructure Overview](#infrastructure-overview)
- [Docker Configuration](#docker-configuration)
- [Jenkins CI/CD Pipelines](#jenkins-cicd-pipelines)
- [Deployment Processes](#deployment-processes)
- [Environment Configurations](#environment-configurations)
- [Testing Automation](#testing-automation)
- [Monitoring & Logging](#monitoring--logging)

## Infrastructure Overview

The application uses a containerized microservices architecture with comprehensive CI/CD pipelines:

```
┌─────────────────────────────────────────────────────────────┐
│                    GitHub Repository                        │
├─────────────────────────────────────────────────────────────┤
│                   Jenkins CI/CD                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │   Build     │ │    Test     │ │      Deploy         │   │
│  │   Pipeline  │ │  Automation │ │     Pipeline        │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                Docker Container Registry                   │
├─────────────────────────────────────────────────────────────┤
│              Production Environment                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │   Backend   │ │   Worker    │ │     Databases       │   │
│  │  Container  │ │  Services   │ │   (MySQL/MariaDB)   │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Key Infrastructure Components
- **Docker**: Containerization platform
- **Jenkins**: CI/CD automation
- **GitHub Container Registry**: Container image storage
- **MySQL/MariaDB**: Database systems
- **Liquibase**: Database migration management

## Docker Configuration

### Multi-Service Architecture
```yaml
# docker/compose.yaml.jinja
services:
  backend:
    container_name: "${COMPOSE_PROJECT_NAME}-backend"
    build:
      context: ./
      dockerfile: ./docker/dockerfile
    working_dir: /home/<USER>/publish
    command: ./startup.sh
    restart: always
    secrets:
      - myacdb-password
      - agentdb-password
      - myaclogsdb-password
      - billingdb-password
    volumes:
      - type: bind
        source: ./
        target: /home/<USER>
    ports:
      - 127.0.0.1:0:8000
    depends_on:
      myacdb:
        condition: service_healthy

  worker-services:
    container_name: "${COMPOSE_PROJECT_NAME}-worker-services"
    build:
      context: ./
      dockerfile: ./docker/dockerfile
    working_dir: /home/<USER>/services
    command: dotnet MyAdaptiveCloud.WorkerServices.dll
    restart: always
    depends_on:
      myacdb:
        condition: service_healthy
```

### Database Services
```yaml
  myacdb:
    image: mariadb:10.11
    container_name: "${COMPOSE_PROJECT_NAME}-myacdb"
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD_FILE: /run/secrets/myacdb-password
      MYSQL_DATABASE: myadaptivecloud
      MYSQL_USER: myac
      MYSQL_PASSWORD_FILE: /run/secrets/myacdb-password
    secrets:
      - myacdb-password
    volumes:
      - myacdb-data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      start_period: 10s
      interval: 10s
      timeout: 5s
      retries: 3

  myacdbliquibase:
    image: liquibase/liquibase:4.17
    command: '/liquibase/liquibase --search-path=/liquibase/changelog/ --defaults-file=/run/secrets/liquibase-properties update --contexts="test,main"'
    restart: 'no'
    secrets:
      - myacdb-password
      - liquibase-properties
    volumes:
      - type: bind
        source: publish/sql/myadaptivecloud
        target: /liquibase/changelog
    depends_on:
      myacdb:
        condition: service_healthy
```

### Dockerfile Structure
```dockerfile
# Base .NET runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0

# Set working directory
WORKDIR /app

# Copy published application
COPY publish/ .

# Expose port
EXPOSE 8080

# Set entry point
ENTRYPOINT ["dotnet", "MyAdaptiveCloud.dll"]
```

### Docker Secrets Management
- **Database Passwords**: Stored as Docker secrets
- **API Keys**: Environment-specific secret files
- **SSL Certificates**: Mounted as volumes
- **Configuration Files**: Template-based generation

## Jenkins CI/CD Pipelines

### Main Build Pipeline (Jenkinsfile)
```groovy
pipeline {
    agent none
    environment {
        DOTNET_CLI_HOME = "/tmp/DOTNET_CLI_HOME"
    }
    stages {
        stage('build') {
            agent {
                docker {
                    image 'ghcr.io/ippathways/dotnet-angular:dotnet90angular-latest'
                    registryUrl 'https://ghcr.io/'
                    registryCredentialsId 'jenkins-docker-packages'
                }
            }
            steps {
                // Clean previous builds
                sh 'rm -rf src/MyAdaptiveCloud/bin'
                sh 'rm -rf src/MyAdaptiveCloud.WorkerServices/bin'
                
                // Build Angular frontend
                dir("src/MyAdaptiveCloud/ClientApp") {
                    sh "npm install"
                    sh "npm run ng -- build --configuration=production"
                }
                
                // Build .NET backend
                sh "dotnet publish -c Release --version-suffix ${env.BUILD_NUMBER}"
            }
        }
    }
}
```

### Test Automation Pipeline (Jenkinsfile.testAutomation)
```groovy
pipeline {
    agent {
        docker {
            image "ghcr.io/ippathways/playwright-python-docker:playwright-python-noble-v1.54.0"
            registryUrl 'https://ghcr.io/'
            registryCredentialsId 'jenkins-docker-packages'
        }
    }
    stages {
        stage('prep') {
            steps {
                script {
                    createPoetryEnvironment()
                    displayPoetryInfo()
                }
            }
        }
        
        stage('Lint and Scan') {
            steps {
                script {
                    dir('testAutomation/MyAdaptiveCloud.Tests') {
                        sh "poetry run ./code_hygiene.sh"
                    }
                }
            }
        }
        
        stage('Run API Tests') {
            steps {
                script {
                    retry(2) {
                        executeAPIStage(apiAttempts)
                    }
                }
            }
        }
        
        stage('Run UI Tests') {
            parallel {
                stage('Chrome Tests') {
                    steps {
                        script {
                            executeUIStage('chrome', chromeAttempts)
                        }
                    }
                }
                stage('Firefox Tests') {
                    steps {
                        script {
                            executeUIStage('firefox', firefoxAttempts)
                        }
                    }
                }
            }
        }
    }
}
```

### Dynamic Lab Deployment (Jenkinsfile.dynamiclab.up)
```groovy
pipeline {
    agent any
    environment {
        registry = 'localhost:5000'
        imagename = "myadaptivecloud/${BRANCH}"
        DOCKER_BUILDKIT = 1
        NODE_OPTIONS = '--max_old_space_size=8192 --max-semi-space-size=512'
    }
    stages {
        stage('run-tests') {
            parallel {
                stage('angular-test') {
                    steps {
                        dir('src/MyAdaptiveCloud/ClientApp/') {
                            sh 'npm install'
                            sh 'npm run lint'
                            sh 'npm run test-ci'
                        }
                    }
                }
                stage('dotnet-test') {
                    steps {
                        sh 'dotnet test --logger trx --results-directory TestResults/'
                    }
                }
            }
        }
        
        stage('build') {
            steps {
                dir("src/MyAdaptiveCloud/ClientApp") {
                    sh "npm install"
                    sh "npm run ng -- build --configuration=production"
                }
                sh "dotnet publish -c Release --version-suffix ${env.BUILD_NUMBER}"
            }
        }
        
        stage('deploy') {
            steps {
                script {
                    sh "docker build -t ${registry}/${imagename}:${BUILD_NUMBER} ."
                    sh "docker push ${registry}/${imagename}:${BUILD_NUMBER}"
                }
            }
        }
    }
}
```

## Deployment Processes

### Environment-Specific Deployments
```bash
# Development deployment
docker-compose -f docker/compose.yaml up -d

# Production deployment
docker-compose -f docker/prod/docker-compose.yaml up -d

# Worker services only
docker-compose -f docker/prod/docker-compose-worker.yaml up -d
```

### Deployment Scripts
```python
# docker/deployproject.py
class Config(dict):
    def load(self, filename=None):
        if filename and os.path.exists(filename):
            with open(filename, 'r') as f:
                config_data = json.load(f)
                self.update(config_data)

# Load configuration
config = Config(name=f'{scriptpath}/deployproject.json')
config.load()

# DNS and service configuration
dnsApikey = config.getValue('PowerDNS', 'apikey')
ngunitHost = config.getValue('NginxUnit', 'host')
```

### Blue-Green Deployment Strategy
1. **Build**: Create new container images
2. **Test**: Run automated test suites
3. **Deploy**: Deploy to staging environment
4. **Validate**: Run smoke tests
5. **Switch**: Update load balancer to new version
6. **Monitor**: Watch for issues and rollback if needed

## Environment Configurations

### Development Environment
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=myadaptivecloud_dev;",
    "ACAgentConnection": "Server=localhost;Database=acagent_dev;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Information"
    }
  }
}
```

### Production Environment
```yaml
# Production docker-compose.yaml
services:
  api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
    volumes:
      - /home/<USER>/home/<USER>
      - ./docker_passwd:/etc/passwd
      - ./docker_group:/etc/group
      - /mnt/dsm1_adaptivecloud_drive01:/mnt/dsm1_adaptivecloud_drive01
    logging:
      driver: syslog
      options:
        tag: "{{.Name}}"
```

### Configuration Management
- **Environment Variables**: Runtime configuration
- **Secret Files**: Sensitive data management
- **Volume Mounts**: Configuration file injection
- **Template Processing**: Jinja2 templates for dynamic config

## Testing Automation

### Python Test Framework
```toml
# testAutomation/MyAdaptiveCloud.Tests/pyproject.toml
[project]
dependencies = [
    "playwright == 1.54.0",
    "pytest",
    "requests",
    "tenacity"
]

[tool.poetry.group.pytest.dependencies]
pytest = "^8.3.4"
pytest-playwright = "^0.7.0"
pytest-xdist = "^3.6.1"
pytest-html = "^4.1.1"

[tool.poetry.group.lint.dependencies]
ruff = "*"
black = "^25.1.0"
isort = "^6.0.0"
flake8 = "^7.1.1"
mypy = "^1.15.0"
```

### Test Execution
```bash
# Code quality checks
poetry run ./code_hygiene.sh

# API tests
poetry run pytest tests/api

# UI tests with parallel execution
poetry run pytest tests/ui -n 4 --browser chrome
poetry run pytest tests/ui -n 4 --browser firefox
```

### Test Docker Container
```dockerfile
# testAutomation/Dockerfile
FROM mcr.microsoft.com/playwright/python:v1.54.0-noble

# Install Poetry
RUN pip3 install poetry

# Set working directory
WORKDIR /app

# Copy dependencies
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry config virtualenvs.create false && \
    poetry install --no-root && \
    poetry run playwright install

# Copy test files
COPY . .

CMD ["poetry", "run", "pytest", "tests/ui/tests", "-n", "4"]

## Monitoring & Logging

### Application Logging
```csharp
// Custom logging middleware
public class HttpRequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<HttpRequestLoggingMiddleware> _logger;

    public async Task Invoke(HttpContext context)
    {
        _logger.LogInformation("Request: {Method} {Path}",
            context.Request.Method, context.Request.Path);

        await _next(context);

        _logger.LogInformation("Response: {StatusCode}",
            context.Response.StatusCode);
    }
}
```

### Database Logging
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning",
      "MYAC": "Information"
    }
  }
}
```

### Container Logging
```yaml
# Production logging configuration
services:
  api:
    logging:
      driver: syslog
      options:
        tag: "{{.Name}}"
        syslog-address: "tcp://log-server:514"
```

### Health Checks
```yaml
# Database health check
healthcheck:
  test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
  start_period: 10s
  interval: 10s
  timeout: 5s
  retries: 3
```

## Security Considerations

### Container Security
- **Non-root User**: Containers run as non-root user
- **Secret Management**: Docker secrets for sensitive data
- **Network Isolation**: Separate networks for different services
- **Image Scanning**: Automated vulnerability scanning

### SSL/TLS Configuration
```yaml
# SSL certificate mounting
volumes:
  - ./certs:/etc/ssl/certs:ro
  - ./private:/etc/ssl/private:ro
```

### Access Control
- **Jenkins Credentials**: Secure credential management
- **GitHub Packages**: Private container registry
- **Database Access**: Role-based database permissions
- **Network Policies**: Firewall and network segmentation

## Troubleshooting

### Common Issues
1. **Build Failures**: Check Node.js memory limits
2. **Test Timeouts**: Increase timeout values for slow tests
3. **Database Connections**: Verify connection strings and credentials
4. **Container Startup**: Check health check configurations

### Debugging Commands
```bash
# View container logs
docker logs <container-name>

# Execute commands in container
docker exec -it <container-name> /bin/bash

# Check container health
docker inspect <container-name> | grep Health

# View Docker Compose logs
docker-compose logs -f <service-name>
```

### Performance Monitoring
- **Container Metrics**: CPU, memory, disk usage
- **Application Metrics**: Response times, error rates
- **Database Performance**: Query performance, connection pools
- **Network Monitoring**: Bandwidth usage, latency

## Best Practices

### CI/CD Pipeline
- **Fail Fast**: Stop pipeline on first failure
- **Parallel Execution**: Run tests in parallel when possible
- **Artifact Management**: Store build artifacts for debugging
- **Rollback Strategy**: Maintain ability to rollback deployments

### Container Management
- **Image Optimization**: Multi-stage builds, minimal base images
- **Resource Limits**: Set appropriate CPU and memory limits
- **Health Monitoring**: Implement comprehensive health checks
- **Log Aggregation**: Centralized logging for troubleshooting
```
