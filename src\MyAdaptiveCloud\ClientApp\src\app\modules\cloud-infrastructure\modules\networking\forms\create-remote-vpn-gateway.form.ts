import { FormControl } from '@angular/forms';

export interface CreateRemoteVpnGatewayForm {
    cidrList: FormControl<string>;
    deadPeerDetection: FormControl<boolean>;
    espLifetime: FormControl<number | null>;
    espHash: FormControl<string | null>;
    espEncryption: FormControl<string>;
    forceEncapsulation: FormControl<boolean>;
    gateway: FormControl<string>;
    ikeDH: FormControl<string>;
    ikeEncryption: FormControl<string>;
    ikeHash: FormControl<string>;
    ikeLifetime: FormControl<number | null>;
    ikeVersion: FormControl<string>;
    ipSecurityPreSharedKey: FormControl<string>;
    perfectForwardSecrecy: FormControl<string | null>;
    name: FormControl<string>;
    splitConnections: FormControl<boolean>;
}
