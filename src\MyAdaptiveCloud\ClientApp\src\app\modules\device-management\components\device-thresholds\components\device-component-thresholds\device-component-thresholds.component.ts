import { ChangeDetectorRef, Component, DestroyRef, Input, OnInit, inject, input, output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DeviceAlertTypeEnum } from '@app/modules/device-management/models/device-alert-type.enum';
import { DeviceComponentThresholds } from '@app/modules/device-management/models/device-component-thresholds';
import { DeviceThresholdsDefaultRequest } from '@app/modules/device-management/requests/device-thresholds-default.request';
import { DeviceFolderService } from '@app/modules/device-management/services/device-folder.service';
import { DevicesService } from '@app/modules/device-management/services/devices.service';
import { filter, map, startWith } from 'rxjs';
import { DeviceComponentThresholdsForm } from '../../forms/device-component-thresholds.form';
import { ALL_DRIVES_NAME_CONSTANT } from '../../models/constants';
import { DeviceAlertThresholdType } from '../../models/device-alert-threshold-type';
import { DeviceThresholdInheritanceType } from '../../models/device-threshold-inheritance-type';
import { DeviceThresholdInheritanceTypeEnum } from '../../models/device-threshold-inheritance-type.enum';
import { DeviceThresholdInterval } from '../../models/device-threshold-interval';
import { DeviceThresholdIntervals } from '../../models/device-threshold-intervals';
import { DeviceThresholdLevel } from '../../models/device-threshold-level.enum';
import { DeviceThresholdMetrics } from '../../models/device-threshold-metrics';
import { OrganizationThresholdsService } from '../../services/device-thresholds.service';
import { thresholdMetricValidator } from '../../validators/metrics.validator';
import { DeviceThresholdsIntervalsComponent } from '../device-thresholds-intervals/device-thresholds-intervals.component';
import { DeviceThresholdsMetricsComponent } from '../device-thresholds-metrics/device-thresholds-metrics.component';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-device-component-thresholds',
    imports: [ReactiveFormsModule, DeviceThresholdsIntervalsComponent, DeviceThresholdsMetricsComponent],
    templateUrl: './device-component-thresholds.component.html',
    styleUrl: './device-component-thresholds.component.scss'
})
export class DeviceComponentThresholdsComponent implements OnInit {
    private readonly deviceFoldersService = inject(DeviceFolderService);
    private readonly devicesService = inject(DevicesService);
    private readonly deviceThresholdsService = inject(OrganizationThresholdsService);
    private readonly cdr = inject(ChangeDetectorRef);
    private readonly destroyRef = inject(DestroyRef);

    public selectedDeviceAlertThresholdType: DeviceAlertThresholdType;
    public readonly inheritanceType = DeviceThresholdInheritanceTypeEnum;

    readonly deviceThresholdLevel = input.required<DeviceThresholdLevel>();
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) component: DeviceComponentThresholds;
    readonly form = input.required<FormGroup<DeviceComponentThresholdsForm>>();
    readonly thresholdsEntityId = input.required<number>();
    readonly intervals = input.required<DeviceThresholdInterval[]>();
    readonly inheritanceTypes = input.required<DeviceThresholdInheritanceType[]>();
    readonly useCurrentLevelForInheritance = input.required<boolean>();
    readonly componentName = input<string>(undefined);
    readonly isDiskUsage = input<boolean>(undefined);

    readonly useIntervalsForMetrics = input<boolean>(undefined);

    readonly thresholdWarningChanged = output<DeviceThresholdMetrics>();
    readonly thresholdIntervalChanged = output<DeviceThresholdIntervals>();
    readonly thresholdDiskInheritanceTypeChanged = output<DeviceComponentThresholds>();

    ALL_DRIVES_NAME_CONSTANT = ALL_DRIVES_NAME_CONSTANT;

    ngOnInit() {
        this.selectedDeviceAlertThresholdType = this.component.deviceAlertThresholdTypes?.find(type => type.id === this.component.deviceAlertThresholdType);
        this.form().controls.inheritanceType.valueChanges
            .pipe(
                startWith(this.form().controls.inheritanceType.value),
                map(value => Number(value)),
                takeUntilDestroyed(this.destroyRef),
            )
            .subscribe(value => {
                this.handleOverrideForms(+value);
                if (this.component.deviceAlertType === DeviceAlertTypeEnum.DiskUsage) {
                    this.updateDiskInheritance();
                }
            });

        const form = this.form();
        form.controls.deviceAlertThresholdType?.valueChanges
            .pipe(
                filter(value => !!value),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(deviceAlertType => {
                this.resetForm(deviceAlertType);
            });

        if (this.component.deviceAlertType === DeviceAlertTypeEnum.DiskUsage && this.component.name === ALL_DRIVES_NAME_CONSTANT) {
            form.controls.metrics?.valueChanges
                .pipe(
                    filter(value => !!value),
                    takeUntilDestroyed(this.destroyRef)
                )
                .subscribe(metrics => {
                    if (+this.form().controls.inheritanceType.value === DeviceThresholdInheritanceTypeEnum.Override) {
                        this.updateOtherComponentMetrics(metrics as DeviceThresholdMetrics);
                    }
                });

            form.controls.intervals?.valueChanges
                .pipe(
                    filter(value => !!value),
                    takeUntilDestroyed(this.destroyRef)
                )
                .subscribe(intervals => {
                    if (+this.form().controls.inheritanceType.value === DeviceThresholdInheritanceTypeEnum.Override) {
                        this.updateOtherComponentIntervals(intervals as DeviceThresholdIntervals);
                    }
                });
        }
        form.controls.intervals?.valueChanges
            .pipe(
                filter(value => !!value),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(intervals => {
                if (+this.form().controls.inheritanceType.value === DeviceThresholdInheritanceTypeEnum.Override) {
                    this.updateOtherComponentIntervals(intervals as DeviceThresholdIntervals);
                }
            });
    }

    handleOverrideForms(inheritanceTypeId: number) {
        const typeId = this.inheritanceTypes().find(inheritanceType => +inheritanceType.id === +inheritanceTypeId).typeId;
        if (typeId !== DeviceThresholdInheritanceTypeEnum.Override) {
            this.setFormNotOverriddenState(typeId);
        } else {
            this.setFormOverriddenState();
        }
    }

    updateForm() {
        const request: DeviceThresholdsDefaultRequest =
        {
            deviceAlertTypeId: this.component.deviceAlertType,
            name: this.component.name ?? '',
            useCurrentLevel: this.useCurrentLevelForInheritance()
        };

        switch (this.deviceThresholdLevel()) {
            case DeviceThresholdLevel.Folder:
                this.deviceFoldersService.getDefaultThresholds(this.thresholdsEntityId(), request).subscribe(res => {
                    this.updateFormState(res.data);
                });
                break;
            case DeviceThresholdLevel.AgentOverride:
                if (!(this.component.deviceAlertType === DeviceAlertTypeEnum.DiskUsage && this.component.name !== ALL_DRIVES_NAME_CONSTANT)) {
                    this.devicesService.getDefaultThresholds(this.thresholdsEntityId(), request).subscribe(res => {
                        this.updateFormState(res.data);
                    });
                } else {
                    this.updateDiskInheritance();
                }
                break;
            case DeviceThresholdLevel.Organization:
                this.deviceThresholdsService.getThresholdForDeviceComponent(this.thresholdsEntityId(), request).subscribe(res => {
                    this.updateFormState(res.data);
                });
                break;
            default:
                break;
        }
    }

    public updateDiskInheritance() {
        const form = this.form();
        if (!form.pristine) {
            const deviceComponentThresholds = new DeviceComponentThresholds();
            deviceComponentThresholds.inheritanceType = form.controls.inheritanceType.value;
            deviceComponentThresholds.deviceAlertThresholdType = form.controls.deviceAlertThresholdType.value;
            deviceComponentThresholds.name = form.controls.name.value;
            deviceComponentThresholds.deviceAlertType = this.component.deviceAlertType;
            deviceComponentThresholds.deviceAlertThresholdTypes = this.component.deviceAlertThresholdTypes;
            deviceComponentThresholds.inheritFrom = this.component.inheritFrom;
            deviceComponentThresholds.metrics = form.controls.metrics.value as DeviceThresholdMetrics;
            deviceComponentThresholds.intervals = form.controls.intervals.value as DeviceThresholdIntervals;
            this.thresholdDiskInheritanceTypeChanged.emit(deviceComponentThresholds);
        }
    }

    public updateOtherComponentMetrics(metric: DeviceThresholdMetrics) {
        this.thresholdWarningChanged.emit(metric);
    }

    public updateOtherComponentIntervals(intervals: DeviceThresholdIntervals) {
        this.thresholdIntervalChanged.emit(intervals);
    }

    private updateFormState(deviceComponentThresholds: DeviceComponentThresholds) {
        if (deviceComponentThresholds.deviceAlertThresholdType) {
            this.resetForm(deviceComponentThresholds.deviceAlertThresholdType);
        }

        this.form().patchValue(deviceComponentThresholds, { emitEvent: false });

        this.component = deviceComponentThresholds;
        if (this.component.deviceAlertType === DeviceAlertTypeEnum.DiskUsage) {
            this.updateDiskInheritance();
        }
        this.cdr.detectChanges();
    }

    private setFormOverriddenState() {
        const form = this.form();
        form.controls.metrics?.enable();
        form.controls.deviceAlertThresholdType?.enable();
        form.controls.intervals.enable();

        if (!form.controls.deviceAlertThresholdType?.value) {
            form.controls.deviceAlertThresholdType?.setValue(this.component.deviceAlertThresholdType ?? this.component.deviceAlertThresholdTypes[0]?.id);
        }
    }

    private setFormNotOverriddenState(typeId: DeviceThresholdInheritanceTypeEnum) {
        const form = this.form();
        form.controls.metrics?.disable();
        form.controls.deviceAlertThresholdType?.disable();
        form.controls.intervals.disable();
        if (+typeId === DeviceThresholdInheritanceTypeEnum.Inherit) {
            this.updateForm();
        } else {
            form.controls.metrics?.reset();
            form.controls.intervals.reset();
        }
    }

    private resetForm(deviceAlertType: number) {
        const deviceAlertThresholdType = this.component.deviceAlertThresholdTypes.find(type => type.id === deviceAlertType);
        this.form().controls.metrics.reset();
        this.form().controls.metrics.setValidators(thresholdMetricValidator(deviceAlertThresholdType.id, deviceAlertThresholdType.maxValue));
        this.form().controls.metrics.updateValueAndValidity();
        this.selectedDeviceAlertThresholdType = deviceAlertThresholdType;
    }
}
