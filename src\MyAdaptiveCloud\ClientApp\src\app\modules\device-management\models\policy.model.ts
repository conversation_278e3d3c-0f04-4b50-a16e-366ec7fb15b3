import { IUserAction } from '@app/shared/models/user-actions/user-action';
import { UserActionState } from '@app/shared/models/user-actions/user-action-state.enum';

export class Policy implements IUserAction {
    public policyId: number;
    public organizationId: number;
    public scheduleId: number | null;
    public name: string;
    public description: string;
    public isEnabled: boolean;
    public canEdit: UserActionState;
    public canDelete: UserActionState;
    public canCreate: UserActionState;
    public canView: UserActionState;
    public releaseTagName: string;
}
