import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { CloudInfraAccountViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-domain.view-model';
import { CloudInfraZoneService } from '@app/modules/cloud-infrastructure/services/cloud-infra-zone.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { BreadcrumbNavService } from '@app/shared/services/breadcrumb-nav.service';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { CloudInfraAccountService } from '../../../../services/cloud-infra-account.service';
import { CloudInfraDomainService } from '../../../../services/cloud-infra-domain.service';
import { VmDetailsContainerComponent } from '../vm-details-container/vm-details-container.component';
import { VmListComponent } from '../vm-list/vm-list.component';
import { VmManagementComponent } from './vm-management.component';

describe('VmManagementComponent', () => {
    let fixture: ComponentFixture<VmManagementComponent>;
    let mockCloudInfraAccountService: jasmine.SpyObj<CloudInfraAccountService>;
    let mockCloudInfraDomainService: jasmine.SpyObj<CloudInfraDomainService>;
    let mockCloudInfrastructureSessionService: jasmine.SpyObj<CloudInfrastructureSessionService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockCloudInfraZoneService: jasmine.SpyObj<CloudInfraZoneService>;

    const account1: CloudInfraAccountViewModel = {
        id: '1',
        name: 'Account 1',
        domainId: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
    };

    const domain1: CloudInfraDomainViewModel = {
        accounts: [account1],
        hasChild: true,
        id: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
        isExpanded: false,
        level: 0,
        name: 'ROOT',
        subDomains: []
    };

    const mockZones = [{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [VmManagementComponent],
            providers: [
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraAccountService),
                provideMock(CloudInfraDomainService),
                provideMock(UserContextService),
                provideMock(ActivatedRoute),
                provideMock(BreadcrumbNavService),
                ZoneDomainAccountStore,
                provideMock(CloudInfraZoneService)
            ]
        });

        mockCloudInfraAccountService = TestBed.inject(CloudInfraAccountService) as jasmine.SpyObj<CloudInfraAccountService>;
        mockCloudInfraDomainService = TestBed.inject(CloudInfraDomainService) as jasmine.SpyObj<CloudInfraDomainService>;
        mockCloudInfrastructureSessionService = TestBed.inject(CloudInfrastructureSessionService) as jasmine.SpyObj<CloudInfrastructureSessionService>;
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        mockCloudInfraAccountService.getAccountById.and.returnValue(of([account1]));
        mockCloudInfraDomainService.getDomainList.and.returnValue(of([domain1]));

        mockCloudInfraZoneService = TestBed.inject(CloudInfraZoneService) as jasmine.SpyObj<CloudInfraZoneService>;
        mockCloudInfraZoneService.getZones.and.returnValue(of(mockZones));

        const cloudInfraContext: CloudInfraUserContext = {
            apiKey: 'Yre9R_RkK_1Vls2VUQfppp6NW140BIpaKtJH1K7hkMlTq7iU7MVNamrv0G9l8K2K6H1a2al4VVnay_nJKnH7uw',
            secretKey: 'NYjBP6PrsDX6tilLnveSTYllM-WEGTQy66Kz91klgkftDaj3-bTC6XAwz9fkCJcFlgBwM5g5fqE00f7HcaDog',
            permissions: [
                '*'
            ],
            roleName: 'Root Admin',
            roleType: 'Admin',
            accountName: 'Root',
            accountId: '59029ecf-c7e0-11eb-bbc8-005056b1c79a',
            domainId: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
            apiUrl: 'https://labmy.ippathways.us/client',
            apiVersion: '4.15',
            cpuCustomOfferingMaxValue: 4,
            memoryCustomOfferingMaxValue: 8192,
            diskSizeCustomOfferingMaxValue: 1024,
            diskSizeCustomOfferingMinValue: 1,
            hasMappedDomain: false
        };
        mockCloudInfrastructureSessionService.login.and.returnValue(of(cloudInfraContext));

        mockUserContextService.currentUser = { organizationId: 1, cloudInfraUserContext: cloudInfraContext } as UserContext;

        fixture = TestBed.createComponent(VmManagementComponent);
    });

    it('should load the VM list by default', () => {
        const cloudInfraContext: CloudInfraUserContext = {
            apiKey: 'Yre9R_RkK_1Vls2VUQfppp6NW140BIpaKtJH1K7hkMlTq7iU7MVNamrv0G9l8K2K6H1a2al4VVnay_nJKnH7uw',
            secretKey: 'NYjBP6PrsDX6tilLnveSTYllM-WEGTQy66Kz91klgkftDaj3-bTC6XAwz9fkCJcFlgBwM5g5fqE00f7HcaDog',
            permissions: [
                '*'
            ],
            roleName: 'Root Admin',
            roleType: 'Admin',
            accountName: 'Root',
            accountId: '59029ecf-c7e0-11eb-bbc8-005056b1c79a',
            domainId: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
            apiUrl: 'https://labmy.ippathways.us/client',
            apiVersion: '4.15',
            cpuCustomOfferingMaxValue: 4,
            memoryCustomOfferingMaxValue: 8192,
            diskSizeCustomOfferingMaxValue: 1024,
            diskSizeCustomOfferingMinValue: 1,
            hasMappedDomain: true
        };
        mockCloudInfrastructureSessionService.login.and.returnValue(of(cloudInfraContext));

        mockUserContextService.currentUser = { organizationId: 1, isPartner: true, cloudInfraUserContext: cloudInfraContext } as UserContext;
        fixture.detectChanges();
        expect(fixture.debugElement.query(By.directive(VmListComponent))).toBeDefined();
        expect(fixture.debugElement.query(By.directive(VmDetailsContainerComponent))).toBeNull();
    });
});
