<div class="content-wrapper">

  <div class="content-sub-heading">
    <div class="action-buttons">
      <button class="btn btn-primary" (click)="addAuthenticator()">Add Authenticator</button>
    </div>
  </div>

  <div class="card card-default">
    <div class="card-body">
      <ngx-datatable #table class="table bootstrap no-detail-row" />
    </div>
  </div>
</div>

<ng-template #actionsTemplate let-row="row">
  @if (toItem(row); as row) {
    <app-table-action [icon]="'fa fa-edit'" (clickHandler)="updateAuthenticator(row.id, row.name)"
      [title]="'Rename Authenticator'" />
    <app-table-action [icon]="'fa fa-minus-circle'" [title]="'Delete Authenticator'"
      (clickHandler)="deleteAuthenticator(row.id)" />
  }
</ng-template>