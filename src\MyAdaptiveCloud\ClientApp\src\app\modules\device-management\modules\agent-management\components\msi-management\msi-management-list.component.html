<div class="content-sub-heading">
    <div class="action-buttons">
        @if (isAgentAPIConnected()) {
            <button type="button" class="btn btn-primary" (click)="uploadMsi()">Upload</button>
        }
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap no-detail-row" />
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        @if (isAgentAPIConnected()) {
            <app-table-action [icon]="'fas fa-download'" [title]="'Download'" (clickHandler)="downloadMsi(row.serviceId)" />
        }
        @if (row.isObsolete) {
            <app-table-action [icon]="'fas fa-undo'" [title]="'Mark as active'" (clickHandler)="markAsActive(row)" />
        } @else {
            <app-table-action [icon]="'fas fa-archive'" [title]="'Mark as obsolete'" [enabled]="row.canMarkObsolete" (clickHandler)="markAsObsolete(row)" />
        }
        @if(isAgentAPIConnected())
        {
            <app-table-action [icon]="'far fa-trash-can'" [title]="'Delete'" (clickHandler)="deleteMsi(row.serviceId)" [enabled]="row.canDelete" />
        }
    }
</ng-template>

<ng-template #dateCellTemplate let-value="value">
    <span>
        {{ (value) ? (value | date: 'yyyy-MM-dd HH:mm:ss') : '-' }}
    </span>
</ng-template>

<ng-template #serviceTypeCellTemplate let-row="row">
    @if (toItem(row); as row) {
            @switch (row.serviceType) {
                @case (serviceType.Agent) {
                    Agent
                }
                @case (serviceType.Watchdog) {
                    Watchdog
            }
        }
    }
</ng-template>

<ng-template #checksumTemplate let-row="row">
    @if (toItem(row); as row) {
        <span>
            {{ row.checksumType }} - {{ row.checksum }}
        </span>
    }
</ng-template>

<ng-template #obsoleteTemplate let-row="row">
    @if (toItem(row); as row) {
    <span>
        {{ row.isObsolete ? 'Obsolete' : 'Active' }}
    </span>
    }
</ng-template>

