export interface Bgp {
    aggregator: unknown; // Adjust to a specific type if needed
    asPath: number[];
    atomicAggregate: boolean;
    clusterList: string[];
    communities: string[];
    extendedCommunities: string[];
    largeCommunities: string[];
    localPref: number;
    med: number;
    neighborIp: string;
    neighborRemoteAs: number;
    nextHop: string;
    origin: number;
    originatorId: string;
    prefix: string;
    simulate: boolean;
}
