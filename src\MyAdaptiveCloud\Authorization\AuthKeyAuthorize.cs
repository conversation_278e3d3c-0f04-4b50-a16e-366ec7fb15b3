using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    /// <summary>
    /// Verifies that the current user's Role is authorized to access the target Organization's information
    /// </summary>
    public class AuthKeyAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;


        public AuthKeyAuthorizeFilter(IUserContextService userContextService, IIdentityService identityService, IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                string apiKeyId = AuthorizeFilterHelpers.GetEntityValue(context, _name);
                _ = int.TryParse(apiKeyId, out int authenticationKeyId);
                var authenticationKeyPersonId = await _entityAuthorizationService.GetAuthenticationKeyPersonId(authenticationKeyId);

                if (authenticationKeyPersonId.HasValue)
                {
                    if (authenticationKeyPersonId.Value != userId)
                    {
                        // Gets the active organization ids where the target person is a contact, and
                        var organizationIds = await _entityAuthorizationService.GetPersonOrganizationIds(authenticationKeyPersonId.Value);
                        // If the target person is not a member of any active organization, then the caller user is not authorized
                        if (!organizationIds.Any())
                        {
                            context.Result = new ForbidResult();
                            return;
                        }

                        // check if the caller user has permissions in all of the organizations
                        var isAuthorized = true;
                        foreach (var organizationId in organizationIds)
                        {
                            if (_perms != null && !_userContextService.HasPermission(userId, organizationId, _distance, _perms))
                            {
                                isAuthorized = false;
                                break;
                            }
                        }

                        if (!isAuthorized)
                        {
                            context.Result = new ForbidResult();
                            return;
                        }
                    }
                }
                else
                {
                    context.Result = new BadRequestResult();
                }
            }

            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Specifies what minimum Role is required within the target Organization to access this endpoint.
    /// The target Organization is determined via authkeyId as a parameter or in the path.
    /// </summary>
    /// <param name="Distance">The minimum distance up the organization hierarchy that the role must be in order to qualify.</param>
    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class AuthKeyAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public AuthKeyAuthorizeAttribute(params Perms[] perms) : base(typeof(AuthKeyAuthorizeFilter), perms)
        {
            Name = "authenticationKeyId";
        }
    }
}