<div aria-live="polite" aria-atomic="true" class="notification-container">
    <div>
        @for (notification of notifications(); track notification) {
        <ngb-toast [autohide]="true" [delay]="delay(notification.type)" [header]="notification.header"
            [class]="notification.className" (hide)="remove(notification)">
            {{notification.message}}</ngb-toast>
        }
    </div>
</div>
