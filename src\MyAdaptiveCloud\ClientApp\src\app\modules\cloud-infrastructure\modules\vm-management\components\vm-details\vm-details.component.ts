import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { take } from 'rxjs';
import { TagsForm } from '../../forms/tags.form';
import { CloudInfrastructureTagsService } from '../../services/cloud-infra-tags.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';

@Component({
    selector: 'app-vm-details',
    imports: [ReactiveFormsModule],
    templateUrl: './vm-details.component.html',
    styleUrl: './vm-details.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmDetailsComponent implements OnInit {

    private readonly cloudInfrastructureTagsService = inject(CloudInfrastructureTagsService);
    private readonly formBuilder = inject(FormBuilder);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly vmDetailsStateService = inject(VmDetailsStateService);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);
    protected readonly vm = this.vmDetailsStateService.selectedVM;
    protected readonly isLoading = toSignal(this.vmDetailsStateService.isLoading$);

    protected readonly tagsForm = this.formBuilder.group<TagsForm>({
        tagKey: this.formBuilder.control('', Validators.required),
        tagValue: this.formBuilder.control('', Validators.required),
    });

    ngOnInit(): void {
        this.vmDetailsStateService.isLoading$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(isLoading => {
                if (isLoading && this.tagsForm.enabled) {
                    this.tagsForm.disable();
                    this.tagsForm.updateValueAndValidity();
                } else if (!isLoading && this.tagsForm.disabled) {
                    this.tagsForm.enable();
                    this.tagsForm.updateValueAndValidity();
                }
            });
    }

    protected createTags(): void {
        const tagKey = this.tagsForm.controls.tagKey.value;
        const tagValue = this.tagsForm.controls.tagValue.value;
        this.cloudInfrastructureTagsService.createTags(this.vm().id, [{ key: tagKey, value: tagValue }])
            .pipe(take(1))
            .subscribe(() => {
                this.vm().tags = [...this.vm().tags, ...[{ key: tagKey, value: tagValue }]];
                this.tagsForm.reset();
            });
    }

    protected deleteTags(key: string, value: string): void {
        this.cloudInfrastructureTagsService.deleteTags(this.vm().id, [{ key, value }])
            .pipe(take(1))
            .subscribe(() => {
                const index = this.vm().tags.findIndex(tag => tag.key === key && tag.value === value);
                if (index !== -1) {
                    this.vm().tags.splice(index, 1);
                    const newVM = { ...this.vm() };
                    this.vmDetailsStateService.selectedVM.set(newVM);
                }
            });
    }

}
