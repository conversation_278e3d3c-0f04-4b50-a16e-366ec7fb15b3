import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { map, Observable } from 'rxjs';
import { BillingStartDateStatus } from '../../models/ac-mapping-billing-start-date-status.model';
import { BillingStatus } from '../../models/ac-mapping-billing-status.model';
import { AcMapping } from '../../models/ac-mapping.model';
import { CwCompany } from '../../models/cw-company.model';
import { AcCwMappingListRequest } from '../../requests/ac-cw-mapping-list.request';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';
import { CreateAcToCwMappingComponent } from '../create-ac-to-cw-mapping/create-ac-to-cw-mapping.component';

@Component({
    selector: 'app-mapping-list',
    imports: [NgxDatatableModule, TableActionComponent, AutoSearchBoxComponent, NgbPopover, DatePipe],
    templateUrl: './ac-cw-mapping-list.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AcToCwMappingListComponent extends BaseListComponent<AcMapping> implements OnInit {
    private readonly mappingService = inject(AcToCwMappingService);
    private readonly modalService = inject(ModalService);

    private readonly billingTemplate = viewChild<TemplateRef<never>>('billingTemplate');
    private readonly billingDateTemplate = viewChild<TemplateRef<never>>('billingDateTemplate');
    private readonly nextInvoiceDateTemplate = viewChild<TemplateRef<never>>('nextInvoiceDateTemplate');
    private readonly cwCompanyNameTemplate = viewChild<TemplateRef<never>>('cwCompanyNameTemplate');
    private readonly cwAgreementNameTemplate = viewChild<TemplateRef<never>>('cwAgreementNameTemplate');
    private readonly acNameTemplate = viewChild<TemplateRef<never>>('acNameTemplate');

    private rows: AcMapping[] = [];
    protected BillingStatus = BillingStatus;
    protected BillingStartDateStatus = BillingStartDateStatus;

    constructor() {
        super();
        this.pagination = new AcCwMappingListRequest();
    }

    ngOnInit(): void {
        const columns: TableColumn[] = [
            {
                name: 'Billing',
                prop: 'billingStatus',
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 80,
                cellTemplate: this.billingTemplate(),
                headerTemplate: this.headerTemplateSortable()
            },
            {
                name: 'Cloud Infrastructure Entity Name',
                prop: 'acName',
                cellTemplate: this.acNameTemplate(),
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150
            },
            {
                name: 'Type',
                prop: 'acType',
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 70,
                minWidth: 70,
                maxWidth: 70,
            },
            {
                name: 'ConnectWise Company Name',
                prop: 'cwCompanyName',
                cellTemplate: this.cwCompanyNameTemplate(),
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150
            },
            {
                name: 'ConnectWise Agreement Name',
                prop: 'cwAgreementName',
                cellTemplate: this.cwAgreementNameTemplate(),
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 150
            },
            {
                name: 'Billing Start Date',
                prop: 'billingStartDate',
                cellTemplate: this.billingDateTemplate(),
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 140,
            },
            {
                name: 'Next Invoice Date',
                prop: 'nextInvoiceDate',
                cellTemplate: this.nextInvoiceDateTemplate(),
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 140,
            },
            {
                name: 'Actions',
                cellTemplate: this.actionsTemplate(),
                width: 100,
                sortable: false,
                resizeable: false,
                canAutoResize: false
            }
        ];
        super.initialize(() => this.mapData$(), columns);
    }

    private mapData$(): Observable<ApiDatasetResult<AcMapping[]>> {

        return this.mappingService.getMappings(this.pagination as AcCwMappingListRequest).pipe(
            takeUntilDestroyed(this.destroyRef),
            map(res => {
                res.data.forEach(mapping => {
                    const localBillStartDate = new Date(mapping.billingStartDate);
                    const cwBillStartDate = new Date(mapping.cwBillingStartDate);
                    // If the local billing start date is earlier than the CW date
                    if (localBillStartDate < cwBillStartDate) {
                        mapping.billingStartDateStatus = BillingStartDateStatus.LocalEarlierThanCW;
                    } else if (localBillStartDate > new Date()) {
                        // If the local billing start date is in the future
                        mapping.billingStartDateStatus = BillingStartDateStatus.LocalInTheFuture;
                    } else {
                        mapping.billingStartDateStatus = BillingStartDateStatus.Default;
                    }
                });
                return res;
            })
        );
    }

    protected addMap() {
        const modalRef = this.modalService.openModalComponent(CreateAcToCwMappingComponent);
        // Pass in an array of all existing mapped agreementIds so that the modal can disallow reuse of the same agreement id
        (modalRef.componentInstance as CreateAcToCwMappingComponent).mappedAgreements = this.rows.map(mapping => mapping.cwAgreementId);

        modalRef.closed.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
            if (res) {
                this.loadData();
            }
        });
    }

    protected onMap(row: AcMapping) {
        const modalRef = this.modalService.openModalComponent(CreateAcToCwMappingComponent);
        const componentInstance = (modalRef.componentInstance as CreateAcToCwMappingComponent);
        // Pass in an array of all existing mapped agreementIds so that the modal can disallow reuse of the same agreement id
        componentInstance.mappedAgreements = this.rows
            .map(mapping => mapping.cwAgreementId)
            .filter(id => id && id !== row.cwAgreementId);
        componentInstance.mappingId = row.id;
        componentInstance.acName = row.acName;
        componentInstance.acId = row.acId;
        componentInstance.isEnabled = row.enabled;
        if (row.acId) {
            componentInstance.selectedAccountDomain = {
                name: row.acName,
                id: row.acId,
                acType: row.acType
            };
        }
        if (row.cwCompanyId) {
            const selectedCompany: CwCompany = {
                name: row.cwCompanyName,
                id: row.cwCompanyId,
                identifier: row.cwCompanyIdentifier
            };
            componentInstance.selectedCompany = selectedCompany;
        }
        if (row.cwAgreementId) {
            componentInstance.selectedAgreement = {
                name: row.cwAgreementName,
                id: row.cwAgreementId,
                prorateFlag: false,
                agreementStatus: '',
                disabled: false
            };
        }

        componentInstance.billingStartDate = row.billingStartDate ? new Date(row.billingStartDate) : undefined;
        componentInstance.cwBillingStartDate = row.cwBillingStartDate ? new Date(row.cwBillingStartDate) : undefined;

        modalRef.closed.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
            if (res) {
                this.loadData();
            }
        });
    }

    protected onDelete(row: AcMapping) {
        this.modalService.openDeleteConfirmationDialog('Delete Mapping', 'Are you sure you want to delete this mapping?').closed
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(result => {
                if (result) {
                    this.mappingService.deleteMapping(row.id)
                        .pipe(takeUntilDestroyed(this.destroyRef))
                        .subscribe(() => {
                            this.loadData();
                        });
                }
            });
    }

}
