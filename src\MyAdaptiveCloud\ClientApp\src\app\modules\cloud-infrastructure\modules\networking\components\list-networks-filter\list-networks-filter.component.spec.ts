import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { CloudInfraZoneService } from '@app/modules/cloud-infrastructure/services/cloud-infra-zone.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { NETWORK_LIST_CONSTANTS } from '../../models/network-list.constants';
import { NetworkListFilters } from '../../requests/network-list.filter';
import { ListNetworksFilterComponent } from './list-networks-filter.component';

describe('ListNetworksFilterComponent', () => {
    let component: ListNetworksFilterComponent;
    let fixture: ComponentFixture<ListNetworksFilterComponent>;
    let mockZoneService: jasmine.SpyObj<CloudInfraZoneService>;
    let el: DebugElement;

    const mockZones = [{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ListNetworksFilterComponent],
            providers: [
                provideMock(CloudInfraZoneService)
            ]
        })
            .compileComponents();

        mockZoneService = TestBed.inject(CloudInfraZoneService) as jasmine.SpyObj<CloudInfraZoneService>;
        mockZoneService.getZones.and.returnValue(of(mockZones));

        fixture = TestBed.createComponent(ListNetworksFilterComponent);
        component = fixture.componentInstance;
        fixture.componentRef.setInput('filters', new NetworkListFilters(mockZones));
        fixture.componentRef.setInput('zones', mockZones);

        el = fixture.debugElement;

        fixture.detectChanges();
    });

    it('should set the Zone filter options correctly in the view', () => {
        const groupElement = fixture.debugElement.query(By.css('#zone-group'));
        const inputs = groupElement.queryAll(By.css('.form-check-input'));
        const labels = groupElement.queryAll(By.css('.form-check-label'));
        const zoneGroup = component.filterGroupOptions.find(group => group.key === mockZones[0].id);

        expect(inputs.length).toEqual(mockZones.length);
        expect('Zone').toEqual(zoneGroup.groupName);

        inputs.forEach((input, index) => {
            const radioInput = (input.nativeElement as HTMLInputElement).id;
            expect(radioInput).toEqual(mockZones[index].id);
        });

        labels.forEach((label, index) => {
            const labelText = (label.nativeElement as HTMLLabelElement).innerText;
            expect(labelText).toEqual(mockZones[index].name);
        });
    });

    it('should set the VPC filter options correctly in the view', () => {
        const groupElement = fixture.debugElement.query(By.css('#vpc-group'));
        const inputs = groupElement.queryAll(By.css('.form-check'));
        const groupSectionTitle = groupElement.query(By.css('h6')).nativeElement.innerText;

        expect(inputs.length).toEqual(2); // VPC and Non-VPC
        expect(groupSectionTitle).toEqual('Virtual Private Cloud');
    });

    it('should render the VPC checkbox with correct label', () => {
        const vpcCheckboxLabel = el.query(By.css('#vpc-group .form-check:nth-child(1) label'));
        const vpcCheckboxInput = el.query(By.css('#vpc-group .form-check:nth-child(1) input[type="checkbox"]'));

        expect((vpcCheckboxInput?.nativeElement as HTMLInputElement)?.checked).toBeTrue();
        expect((vpcCheckboxLabel?.nativeElement as HTMLLabelElement)?.textContent).toContain('VPC Networks ');
        expect(vpcCheckboxInput.nativeElement.getAttribute('data-testid')).toBe(NETWORK_LIST_CONSTANTS.vpcKey);
    });

    it('should render the Non-VPC checkbox with correct label', () => {
        const nonVpcCheckboxLabel = el.query(By.css('#vpc-group .form-check:nth-child(2) label'));
        const nonVpcCheckboxInput = el.query(By.css('#vpc-group .form-check:nth-child(2) input[type="checkbox"]'));

        expect((nonVpcCheckboxInput?.nativeElement as HTMLInputElement)?.checked).toBeTrue();
        expect((nonVpcCheckboxLabel?.nativeElement as HTMLLabelElement)?.textContent).toContain('Non-VPC Networks ');
        expect(nonVpcCheckboxInput.nativeElement.getAttribute('data-testid')).toBe(NETWORK_LIST_CONSTANTS.nonVpcKey);
    });

    it('should update form control when VPC checkbox is clicked', () => {
        const vpcCheckboxInput = el.query(By.css(`[data-testid=${NETWORK_LIST_CONSTANTS.vpcKey}]`)).nativeElement;
        expect(component.form.get(NETWORK_LIST_CONSTANTS.vpcKey)?.value).toBeTrue();

        vpcCheckboxInput.click();
        fixture.detectChanges();

        expect(component.form.get(NETWORK_LIST_CONSTANTS.vpcKey)?.value).toBeFalse();
    });

    it('should set the Type filter options correctly in the view', () => {
        const groupElement = fixture.debugElement.query(By.css('#type-group'));
        const inputs = groupElement.queryAll(By.css('.form-check'));
        const groupSectionTitle = groupElement.query(By.css('h6')).nativeElement.innerText;

        expect(inputs.length).toEqual(3); // Isolated and L2 and Shared
        expect(groupSectionTitle).toEqual('Type');

    });

    it('should render the Type Isolated checkbox with correct label', () => {
        const chkLabel = el.query(By.css('#type-group .form-check:nth-child(1) label'));
        const chkInput = el.query(By.css('#type-group .form-check:nth-child(1) input[type="checkbox"]'));

        expect((chkInput?.nativeElement as HTMLInputElement)?.checked).toBeTrue();

        expect((chkLabel?.nativeElement as HTMLLabelElement)?.textContent).toContain('Isolated ');
        expect(chkInput?.nativeElement.getAttribute('data-testid')).toBe(NETWORK_LIST_CONSTANTS.isolatedKey);
    });

    it('should render the L2 checkbox with correct label', () => {
        const chkLabel = el.query(By.css('#type-group .form-check:nth-child(2) label'));
        const chkInput = el.query(By.css('#type-group .form-check:nth-child(2) input[type="checkbox"]'));

        expect((chkInput?.nativeElement as HTMLInputElement)?.checked).toBeTrue();

        expect((chkLabel?.nativeElement as HTMLLabelElement)?.textContent).toContain('Layer-2 ');
        expect(chkInput.nativeElement.getAttribute('data-testid')).toBe(NETWORK_LIST_CONSTANTS.l2Key);
    });

    it('should update form control when Isolated Type checkbox is clicked', () => {
        const chkInput = el.query(By.css(`[data-testid=${NETWORK_LIST_CONSTANTS.isolatedKey}]`)).nativeElement;
        expect(component.form.get(NETWORK_LIST_CONSTANTS.isolatedKey)?.value).toBeTrue();

        chkInput.click();
        fixture.detectChanges();

        expect(component.form.get(NETWORK_LIST_CONSTANTS.isolatedKey)?.value).toBeFalse();
    });

    it('should update form control when l2 Type checkbox is clicked', () => {
        const chkInput = el.query(By.css(`[data-testid=${NETWORK_LIST_CONSTANTS.l2Key}]`)).nativeElement;
        expect(component.form.get(NETWORK_LIST_CONSTANTS.l2Key)?.value).toBeTrue();

        chkInput.click();
        fixture.detectChanges();

        expect(component.form.get(NETWORK_LIST_CONSTANTS.l2Key)?.value).toBeFalse();
    });
});
