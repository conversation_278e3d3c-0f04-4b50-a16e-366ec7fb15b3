import { Injectable, inject } from '@angular/core';
import { GetThresholdRequest } from '@app/modules/device-management/modules/device-folders/requests/get-threshold.request';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs';
import { DeviceComponentThresholds } from '../../../models/device-component-thresholds';
import { DeviceThresholds } from '../../../models/device-thresholds';
import { DeviceThresholdsDefaultRequest } from '../../../requests/device-thresholds-default.request';
import { DeviceThresholdsRequest } from '../../../requests/device-thresholds.request';
import { DeviceThresholdsMetadata } from '../models/device-threshold-metadata';

@Injectable({
    providedIn: 'root'
})
export class OrganizationThresholdsService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'organizations';

    getThresholdsMetadata(organizationId: number): Observable<ApiDataResult<DeviceThresholdsMetadata>> {
        return this.apiService.get<ApiDataResult<DeviceThresholdsMetadata>>(`${this.endpoint}/${organizationId}/thresholds/metadata`);
    }

    getThresholdForDeviceComponent(organizationId: number, request: DeviceThresholdsDefaultRequest): Observable<ApiDataResult<DeviceComponentThresholds>> {
        return this.apiService.get<ApiDataResult<DeviceComponentThresholds>>(`${this.endpoint}/${organizationId}/thresholds/${request.deviceAlertTypeId}`, request);
    }

    getThresholdsForDeviceComponents(organizationId: number, request: GetThresholdRequest): Observable<ApiDataResult<DeviceThresholds>> {
        return this.apiService.get<ApiDataResult<DeviceThresholds>>(`${this.endpoint}/${organizationId}/thresholds`, request);
    }

    updateOrganizationThresholds(organizationId: number, request: DeviceThresholdsRequest): Observable<ApiResult> {
        return this.apiService.post<ApiResult, DeviceThresholdsRequest>(`${this.endpoint}/${organizationId}/thresholds`, request);
    }
}
