<p class="title">{{ label }}</p>

<form [formGroup]="form">
    <div class="row mb-3">
        <div class="col-4">
            <label class="form-label" for="virtualMachineName">Virtual Machine Name</label>
            <input type="text" class="form-control" formControlName="virtualMachineName" id="virtualMachineName" data-testid="virtual-machine-name-input"
                [class]="{ 'is-invalid':  form.controls.virtualMachineName.dirty && form.controls.virtualMachineName.invalid }">
        </div>
    </div>
    <p class="subtitle">Select Zone<span class="required-asterisk">*</span></p>
    <p>A zone typically corresponds to a single datacenter. Multiple zones help make the cloud more reliable by
        providing
        physical isolation and redundancy.</p>
    <hr />
    <div class="list">
        @for (zone of store.zones(); track zone.id) {
            <label class="details">
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" [value]="zone" formControlName="zone"
                        [id]="'zone_' +  zone.id" />
                    <i class="ms-2 fas fa-building text-secondary"></i>
                    <span class="ms-2 text-truncate">{{ zone.name }}</span>
                </div>
            </label>
        }
    </div>
    <hr />
    @if (form.controls.zone.value) {
        <p class="subtitle">Select Template or ISO<span class="required-asterisk">*</span></p>
        <p>Templates and ISOs are used to create virtual machines. Templates are pre-configured images, while ISOs
            are disc images.</p>
        <p class="title">Select Template or ISO<span class="required-asterisk">*</span></p>
        <ul class="manage-nav nav nav-underline">
            <li class="nav-item">
                <label class="nav-link px-5"
                    [class]="form.controls.osType.value === osType.Template ? 'active' : ''">Templates
                    <input class="btn-check" type="radio" [value]="osType.Template" formControlName="osType"
                        id="ostype_templates" />
                </label>
            </li>
            <li class="nav-item">
                <label class="nav-link px-5" [class]="form.controls.osType.value === osType.ISO ? 'active' : ''">ISO
                    <input class="btn-check" type="radio" [value]="osType.ISO" formControlName="osType" id="ostype_iso" />
                </label>
            </li>
        </ul>
        <p class="mt-3">
            @if (form.controls.osType.value === osType.Template) {
                OS image that can be used to boot VMs.
            }
            @if (form.controls.osType.value === osType.ISO) {
                Disc image containing data or bootable media for OS.
            }
        </p>
        <app-vm-media-selector [media]="filteredTemplates()" [mediaControl]="form.controls.template"
            [selectedOsType]="form.controls.osType.value" [mediaTypeControl]="form.controls.osTypeFilter" />
    }
</form>
