export interface Cluster {
    allocationstate: string;
    clustertype: string;
    cpuovercommitratio: string;
    hypervisortype: string;
    id: string;
    managedstate: string;
    memoryovercommitratio: string;
    name: string;
    podid: string;
    podname: string;
    resourcedetails:
    {
        'cluster.cpu.allocated.capacity.disablethreshold': string;
        cpuOvercommitRatio: string;
        'drain.disabled.on.reboot': boolean;
        memoryOvercommitRatio: string;
    },
    zoneid: string;
    zonename: string;
}
