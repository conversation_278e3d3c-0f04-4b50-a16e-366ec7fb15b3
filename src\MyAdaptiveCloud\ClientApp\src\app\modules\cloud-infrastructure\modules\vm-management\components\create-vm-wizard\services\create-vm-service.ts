import { inject, Injectable } from '@angular/core';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/modules/vm-management/models/vm.constants';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { map, Observable } from 'rxjs';
import { CreateVirtualMachineRequest } from '../requests/create-virtual-machine.request';
import { CreateVirtualMachineResponse } from '../responses/create-virtual-machine.response';

@Injectable({
    providedIn: 'root'
})
export class CreateVmService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);
    private readonly userContextService = inject(UserContextService);

    createVirtualMachine(request: CreateVirtualMachineRequest, domainId: string, account: string): Observable<{ jobId: string, virtualMachineId: string }> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createVirtualMachine,
            response: 'json',
            serviceofferingid: request.serviceOffering.id,
            startvm: 'false',
            templateid: request.templateId,
            zoneid: request.zoneId,
            hypervisor: request.hypervisor,
            isdynamicallyscalable: 'true'
        };

        if (request.rootDiskSize) {
            params['rootdisksize'] = request.rootDiskSize.toString();
        }

        // Used for ISO VMs as the root disk. Template VMs use its default disk offering, and only the disk size should be overridable.
        if (request.diskOfferingId) {
            params['diskofferingid'] = request.diskOfferingId;
        }

        if (request.serviceOffering.isCustom) {
            if (request.serviceOffering.cpuNumber) {
                params['details[0].cpuNumber'] = request.serviceOffering.cpuNumber.toString();
                params['details[0].cpuSpeed'] = '1000';
            }
            if (request.serviceOffering.memory) {
                params['details[0].memory'] = request.serviceOffering.memory.toString();
            }
        }

        if (request.affinityGroups?.length) {
            params['affinitygroupids'] = request.affinityGroups.map(ag => ag).join(',');
        }

        if (request.keyboardLanguage) {
            params['keyboard'] = request.keyboardLanguage;
        }

        if (request.keypairs?.length) {
            if (this.userContextService.currentUser.cloudInfraUserContext.apiVersion === '4.15') {
                params['keypair'] = request.keypairs[0];
            } else {
                params['keypairs'] = request.keypairs.join(',');
            }
        }

        if (request.name) {
            params['name'] = request.name;
            params['displayname'] = request.name;
        }

        request.networkIds?.forEach((networkId, index) => {
            params[`iptonetworklist[${index}].networkid`] = networkId;
        });

        if (request.userdata) {
            params['userdata'] = btoa(request.userdata);
        }

        if (request.size) {
            params['size'] = request.size.toString();
        }

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<CreateVirtualMachineResponse>(params)
            .pipe(map(res => ({ jobId: res.deployvirtualmachineresponse?.jobid, virtualMachineId: res.deployvirtualmachineresponse?.id })));
    }

}
