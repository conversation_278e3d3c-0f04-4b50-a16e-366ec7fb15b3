<div class="modal-header">
    <h4 class="modal-title">Migrate VM to another host</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="cancel()"></button>
</div>
<div class="modal-body">

    <div class="content-sub-heading d-flex justify-content-between">
        <div class="search-bar mb-3">
            <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
        </div>
    </div>

    <div class="card card-default">
        <div class="card-body">
            <ngx-datatable #table class="table bootstrap no-detail-row" />
        </div>
    </div>

    <ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
        <span (click)="sort()" class="clickable">
            {{ column.name }}
            <span
                [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
            </span>
        </span>
    </ng-template>

    <ng-template #memoryUsedTemplate let-row="row">
        @if (toItem(row); as row) {
            {{ row.memoryUsed | fileSizeConverter:'B':'GB': false : false: 1 }}GB
        }
    </ng-template>

    <ng-template #actionsTemplate let-row="row">
        @if (toItem(row); as row) {
            @if (row.suitableForMigration) {
                <button class="btn btn-link p-0" (click)="selectHost(row)">Select
                    Host</button>
            }
        }
    </ng-template>

</div>
<div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="cancel()">Cancel</button>
    <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!table.selected?.length"
        (submitClickEvent)="migrateHost()">OK</app-btn-submit>
</div>
