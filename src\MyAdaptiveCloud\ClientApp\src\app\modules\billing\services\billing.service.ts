import { Injectable, inject } from '@angular/core';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiService } from '@app/shared/services/api.service';
import { Observable } from 'rxjs/internal/Observable';
import { Invoice } from '../models/invoice.model';

@Injectable({
    providedIn: 'root'
})
export class BillingService {
    private readonly apiService = inject(ApiService);

    private readonly endpoint = 'billing';

    getInvoicesByOrganization(organizationId: number): Observable<ApiDataResult<Invoice[]>> {
        return this.apiService.get<ApiDataResult<Invoice[]>>(`${this.endpoint}/organization/${organizationId}`, null);
    }

    downloadInvoice(invoiceId: number): void {
        this.apiService.downloadFile(`${this.endpoint}/invoice/${invoiceId}`);
    }
}
