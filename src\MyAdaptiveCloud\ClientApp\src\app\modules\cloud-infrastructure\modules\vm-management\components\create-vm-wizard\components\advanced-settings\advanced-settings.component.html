<div class="card">
    <div class="card-body">
        <p class="title">{{ label }}</p>

        <label class="me-2 color-secondary">Show advanced settings
            <app-btn-switch class="ms-1" [isActiveInput]="store.advancedSettingsStep.displaySettings()" (isActiveChange)="toggleAdvancedSettings($event)" />
        </label>

        @if (store.advancedSettingsStep.displaySettings()) {

        <form [formGroup]="advancedSettingsForm">
            <div class="settings-section">
                <h3 class="setting-title">Affinity Groups</h3>
                <p class="setting-description">Affinity groups control if virtual machines in a group should run on the
                    same physical host (host affinity) or should run on different physical hosts (host anti-affinity)
                </p>

                <div [formGroup]="affinityGroupsForm">
                    <table class="w-100 options-table">
                        <thead>
                            <th>
                                <div class="form-check d-inline-block">
                                    <input class="form-check-input" type="checkbox" id="selectAllGroups"
                                        formControlName="selectAllGroups">
                                    <label class="form-check-label" for="selectAllGroups">Affinity Groups</label>
                                </div>
                            </th>
                            <th>Description</th>
                            <th>Type</th>
                        </thead>
                        <ng-container [formGroup]="affinityGroupsOptions">
                            <tbody>
                                @for (group of affinityGroupsRes(); track group.id;) {
                                <tr>
                                    <td>
                                        <div class="form-check d-inline-block">
                                            <input class="form-check-input" type="checkbox" id="group-{{group.id}}"
                                                formControlName="{{group.id}}">
                                            <label class="form-check-label"
                                                for="group-{{group.id}}">{{group.name}}</label>
                                        </div>
                                    </td>
                                    <td>{{group.description}}</td>
                                    <td>{{group.type}}</td>
                                </tr>
                                }
                            </tbody>
                        </ng-container>
                    </table>
                </div>
            </div>

            <div class="settings-section">
                <h3 class="setting-title">SHH Key Pairs</h3>
                <p class="setting-description">If supported by virtual machine, add SSH key pairs to virtual machine for
                    authentication.</p>

                <div [formGroup]="sshKeyPairsForm">
                    <table class="w-100 options-table">
                        <thead>
                            <th class="headers-row">
                                <div class="form-check d-inline-block">
                                    <input class="form-check-input" type="checkbox" id="selectAllKeyPairs"
                                        formControlName="selectAllKeyPairs">
                                    <label class="form-check-label" for="selectAllKeyPairs">SHH Key Pairs</label>
                                </div>
                            </th>
                            <th class="headers-row">Account</th>
                            <th class="headers-row">Domain</th>
                        </thead>
                        <ng-container [formGroup]="sshKeyPairsOptions">
                            <tbody>
                                @for (keyPair of sshKeyPairsRes(); track keyPair.id;) {
                                <tr>
                                    <td>
                                        <div class="form-check d-inline-block">
                                            <input class="form-check-input" type="checkbox" id="key-pair{{keyPair.id}}"
                                                formControlName="{{keyPair.id}}">
                                            <label class="form-check-label"
                                                for="key-pair{{keyPair.id}}">{{keyPair.name}}</label>
                                        </div>

                                    </td>
                                    <td>{{keyPair.account}}</td>
                                    <td>{{keyPair.domain}}</td>
                                </tr>
                                }
                            </tbody>
                        </ng-container>
                    </table>
                </div>

                <div class="settings-section">
                    <h3 class="setting-title">Userdata</h3>
                    <textarea class="form-control" rows="5" formControlName="userData" name="userData"></textarea>
                </div>

                <div class="settings-section">
                    <h3 class="setting-title">Keyboard Language</h3>
                    <ng-select [items]="availableKeyboardValues" formControlName="keyboardLanguage"
                        [clearable]="false" />
                </div>
            </div>
        </form>
        }
    </div>
</div>
