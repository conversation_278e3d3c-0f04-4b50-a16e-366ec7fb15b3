import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, OnInit, TemplateRef, inject, input, output, signal, viewChild } from '@angular/core';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, SelectionType, TableColumn } from '@swimlane/ngx-datatable';
import { Observable, of } from 'rxjs';
import { AlertThresholdLevelEnum } from '../../models/alert-rule-threshold-level.model';
import { DeviceAlerts } from '../../models/device-alerts';
import { DeviceAlertsRequest } from '../../modules/devices/requests/device-alerts-list-request';
import { DeviceAlertsViewModel } from '../device-thresholds/models/device-alerts-view.model';

@Component({
    selector: 'app-alerted-devices-details',
    imports: [
        NgxDatatableModule,
        NgbTooltip,
        TableActionComponent,
    ],
    templateUrl: './alerted-devices-details.component.html',
    providers: [DatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AlertedDevicesDetailsComponent extends BaseListComponent<DeviceAlertsViewModel> implements OnInit {
    private readonly datePipe = inject(DatePipe);

    readonly agentId = input.required<number>();
    readonly agentSelected = input.required<boolean>();
    readonly selectable = input(true);

    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) activeAlerts: DeviceAlerts[] = [];

    private readonly expandedTemplate = viewChild<TemplateRef<never>>('expandedTemplate');
    private readonly dateCell = viewChild<TemplateRef<never>>('dateCellTemplate');

    readonly selectedAlertsChange = output<DeviceAlerts[]>();

    protected readonly selectionType = SelectionType;
    readonly selectedAlerts = signal<DeviceAlerts[]>([]);

    private readonly warningTextClass = 'fa fa-warning m-1 text-warning';
    private readonly errorTextClass = 'fa fa-warning m-1 text-error';
    private readonly criticalTextClass = 'fa fa-warning m-1 text-danger';

    readonly acknowledgedWarningClass = 'fa triangle-checked-warning ms-1 me-1 mt-1 text-warning';
    readonly acknowledgedErrorClass = 'fa triangle-checked ms-1 me-1 mt-1 text-error';
    readonly acknowledgedCriticalClass = 'fa triangle-checked-critical ms-1 me-1 mt-1 text-danger';

    private readonly scheduleDowntimeWarningClass = 'fa clock-with-dots-warning ms-1 me-1 mt-1 text-warning';
    private readonly scheduleDowntimeErrorClass = 'fa clock-with-dots-error ms-1 me-1 mt-1 text-error';
    private readonly scheduleDowntimeCriticalClass = 'fa clock-with-dots-critical ms-1 me-1 mt-1 text-danger';

    private readonly ackAndScheduledWarningClass = 'fa fa-circle-check m-1 text-warning';
    private readonly ackAndScheduledErrorClass = 'fa fa-circle-check m-1 text-error';
    private readonly ackAndScheduledCriticalClass = 'fa fa-circle-check m-1 text-danger';

    constructor() {
        super();
        this.pagination = new DeviceAlertsRequest();
    }

    ngOnInit(): void {
        const columns: TableColumn[] = [
            {
                name: 'Alert Type',
                prop: 'alertType',
                sortable: false,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.expandedTemplate()
            },
            {
                name: 'Alert Status',
                prop: 'alertStatus',
                sortable: false,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Acknowledged by',
                prop: 'acknowledgedBy',
                sortable: false,
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Acknowledged Date',
                prop: 'acknowledgedDate',
                sortable: false,
                resizeable: true,
                canAutoResize: true,
                cellTemplate: this.dateCell()
            },
            {
                name: 'Notes',
                cellTemplate: this.actionsTemplate(),
                width: 40,
                sortable: false,
                resizeable: true,
                canAutoResize: true
            }
        ];

        if (this.activeAlerts !== null) {
            this.activeAlerts = this.processDevicesAlerts(this.activeAlerts);
        }

        if (this.agentSelected()) {
            this.selectAllAlerts();
        }

        super.initialize(() => this.loadAlertData$(), columns);
        this.table().footerHeight = 0;
    }

    private loadAlertData$(): Observable<ApiDatasetResult<DeviceAlertsViewModel[]>> {
        const result: ApiDatasetResult<DeviceAlertsViewModel[]> = {
            data: this.activeAlerts as DeviceAlertsViewModel[],
            totalCount: this.activeAlerts.length
        };

        return of(result);
    }

    processDevicesAlerts(res: DeviceAlerts[]): DeviceAlertsViewModel[] {
        const resUI = [...res] as DeviceAlertsViewModel[];

        resUI.forEach(alert => {
            alert.acknowledged = !!alert.acknowledgedBy;
            alert.alertThresholdLevelClass = this.getIconClass(alert);
            alert.iconText = this.getIconText(alert);
        });

        return resUI;
    }

    private getIconClass(alert: DeviceAlertsViewModel): string {
        switch (alert.alertThresholdLevel) {
            case AlertThresholdLevelEnum.Warning:
                if (alert.acknowledged && alert.inScheduledDownTime) {
                    return this.ackAndScheduledWarningClass;
                } else if (alert.acknowledged) {
                    return this.acknowledgedWarningClass;
                } else if (alert.inScheduledDownTime) {
                    return this.scheduleDowntimeWarningClass;
                }

                return this.warningTextClass;
            case AlertThresholdLevelEnum.Error:
                if (alert.acknowledged && alert.inScheduledDownTime) {
                    return this.ackAndScheduledErrorClass;
                } else if (alert.acknowledged) {
                    return this.acknowledgedErrorClass;
                } else if (alert.inScheduledDownTime) {
                    return this.scheduleDowntimeErrorClass;
                }

                return this.errorTextClass;
            case AlertThresholdLevelEnum.Critical:
                if (alert.acknowledged && alert.inScheduledDownTime) {
                    return this.ackAndScheduledCriticalClass;
                } else if (alert.acknowledged) {
                    return this.acknowledgedCriticalClass;
                } else if (alert.inScheduledDownTime) {
                    return this.scheduleDowntimeCriticalClass;
                }

                return this.criticalTextClass;
        }
    }

    private getIconText(alert: DeviceAlertsViewModel): string {
        const tooltipBreakLine = '\n';
        let iconText = '';

        if (alert.acknowledged) {
            iconText += `Acknowledged by ${alert.acknowledgedBy} on ${this.formatOnlyDate(alert.acknowledgedDate)}${tooltipBreakLine}`;
        }

        if (alert.inScheduledDownTime) {
            iconText += `${alert.alertType} is in a scheduled downtime until ${this.formatOnlyTime(alert.scheduleDowntimeEndDate)} on ${this.formatOnlyDate(alert.scheduleDowntimeEndDate)}`;
        }

        return iconText;
    }

    protected setAlertsSelection(alerts: DeviceAlerts[]) {
        this.selectedAlerts.set([...alerts]);
        this.selectedAlertsChange.emit(this.selectedAlerts());
    }

    private selectAllAlerts() {
        const activeSelectableAlerts = this.activeAlerts.filter(w => w.selectable);

        activeSelectableAlerts.forEach(a => {
            a.selected = true;
        });

        this.setAlertsSelection(activeSelectableAlerts);
        this.selectedAlertsChange.emit(this.selectedAlerts());
    }

    protected toggleAlertSelection(row: DeviceAlertsViewModel) {
        row.selected = !row.selected;

        if (row.selected) {
            this.addAlertIfNotExist(row);
        } else {
            this.removeAlertIfSelectable(row);
        }

        this.selectedAlertsChange.emit(this.selectedAlerts());
    }

    protected formatDate(date: Date): string {
        return date ? this.datePipe.transform(date.toString(), 'MM/dd/yy | h:mm a').toString() : '';
    }

    private formatOnlyDate(date: Date): string {
        return date ? this.datePipe.transform(date.toString(), 'MM/dd/yy').toString() : '';
    }

    private formatOnlyTime(date: Date): string {
        return date ? this.datePipe.transform(date.toString(), 'h:mm a').toString() : '';
    }

    private addAlertIfNotExist(alert: DeviceAlertsViewModel) {
        if (!this.selectedAlerts().includes(alert) && alert.selectable) {
            this.selectedAlerts.update(value => {
                value.push(alert);
                return value;
            });
            alert.selected = true;
        }
    }

    private removeAlertIfSelectable(a: DeviceAlertsViewModel) {
        if (a.selectable) {
            const index = this.selectedAlerts().indexOf(a);
            if (index !== -1) {
                a.selected = false;
                this.selectedAlerts.update(value => value.splice(index, 1));
            }
        }
    }
}
