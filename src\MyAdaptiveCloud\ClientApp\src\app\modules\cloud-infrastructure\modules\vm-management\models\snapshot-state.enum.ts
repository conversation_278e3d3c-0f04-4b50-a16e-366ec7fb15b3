export enum SnapshotState {
    Allocated = 'Allocated', // ("The VM snapshot is allocated but has not been created yet.");
    Creating = 'Creating', // ("The VM snapshot is being created.");
    Ready = 'Ready', // ("The VM snapshot is ready to be used.");
    Reverting = 'Reverting', // ("The VM snapshot is being used to revert");
    Expunging = 'Expunging', // ("The volume is being expunging");
    Removed = 'Removed', // ("The volume is destroyed, and can't be recovered.");
    Error = 'Error', // ("The volume is in error state, and can't be recovered");
}
