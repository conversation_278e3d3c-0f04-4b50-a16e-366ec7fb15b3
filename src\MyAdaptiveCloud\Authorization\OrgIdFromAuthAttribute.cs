using System.Globalization;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class OrgIdFromAuthValueProvider : BindingSourceValueProvider
    {
        private readonly IDictionary<object, object> _values;
        private PrefixContainer _prefixContainer;

        public OrgIdFromAuthValueProvider(BindingSource bindingSource, IDictionary<object, object> values) : base(bindingSource)
        {
            _values = values;
        }

        protected PrefixContainer PrefixContainer
        {
            get
            {
                if (_prefixContainer == null)
                {
                    _prefixContainer = new PrefixContainer(_values.Keys.Select(key => key.ToString()).ToList());
                }

                return _prefixContainer;
            }
        }

        public override bool ContainsPrefix(string key)
        {
            return PrefixContainer.ContainsPrefix(key);
        }

        public override ValueProviderResult GetValue(string key)
        {
            if (key == null)
            {
                throw new ArgumentNullException(nameof(key));
            }

            if (key.Length == 0)
            {
                // Top level parameters will fall back to an empty prefix when the parameter name does not
                // appear in any value provider. This would result in the parameter binding to a route value
                // an empty key which isn't a scenario we want to support.
                // Return a "None" result in this event.
                return ValueProviderResult.None;
            }

            if (_values.TryGetValue(key, out var value))
            {
                var stringValue = value as string ?? Convert.ToString(value, CultureInfo.InvariantCulture) ?? string.Empty;
                return new ValueProviderResult(stringValue, CultureInfo.InvariantCulture);
            }
            else
            {
                return ValueProviderResult.None;
            }
        }
    }

    public class OrgIdFromAuthValueProviderFactory : IValueProviderFactory
    {
        public Task CreateValueProviderAsync(ValueProviderFactoryContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            var valueProvider = new OrgIdFromAuthValueProvider(
                new BindingSource("FromAuth", "FromAuth", false, false),
                context.ActionContext.HttpContext.Items);

            context.ValueProviders.Add(valueProvider);

            return Task.CompletedTask;
        }
    }

    [AttributeUsage(AttributeTargets.Parameter | AttributeTargets.Property, AllowMultiple = false, Inherited = true)]
    public class OrgIdFromAuthAttribute : Attribute, IBindingSourceMetadata, IModelNameProvider
    {
        private readonly BindingSource source = new BindingSource("FromAuth", "FromAuth", false, false);
        public BindingSource BindingSource => source;

        public string Name { get; set; } = "organizationId";
    }
}