import { FormGroup, FormBuilder, ValidationErrors } from '@angular/forms'; // Adjust imports as needed
import { thresholdMetricValidator } from './metrics.validator';
import { DeviceAlertThresholdTypeEnum } from '../models/device-alert-threshold-type.enum';

describe('Threshold Metric Validator', () => {
    let formBuilder: FormBuilder;

    beforeEach(() => {
        formBuilder = new FormBuilder();
    });

    it('should return null for valid input (percentage used)', () => {
        const formGroup: FormGroup = formBuilder.group({
            warning: '10',
            error: '5',
            critical: '2',
        });

        const validator = thresholdMetricValidator(1, 100); // Adjust parameters as needed
        const result: ValidationErrors | null = validator(formGroup);

        expect(result).toBeNull();
    });

    it('should return validation error for invalid decimal input.', () => {
        const formGroup: FormGroup = formBuilder.group({
            warning: '35.1',
            error: '37',
            critical: '45',
        });

        const validator = thresholdMetricValidator(DeviceAlertThresholdTypeEnum.Percentage_Used, 100, 1);
        const result: ValidationErrors | null = validator(formGroup);

        expect(result).toEqual({ validationError: 'Please enter a whole number.' });
    });

    it('should return validation error for invalid input. Percentage. Warning less error', () => {
        const formGroup: FormGroup = formBuilder.group({
            warning: '35',
            error: '30',
            critical: '45',
        });

        const validator = thresholdMetricValidator(DeviceAlertThresholdTypeEnum.Percentage_Used, 100, 1);
        const result: ValidationErrors | null = validator(formGroup);

        expect(result).toEqual({ validationError: 'Warning must be less than Error value.' });
    });

    it('should return validation error for invalid input. Percentage. Error less critical', () => {
        const formGroup: FormGroup = formBuilder.group({
            warning: '20',
            error: '30',
            critical: '25',
        });

        const validator = thresholdMetricValidator(DeviceAlertThresholdTypeEnum.Percentage_Used, 100, 1);
        const result: ValidationErrors | null = validator(formGroup);

        expect(result).toEqual({ validationError: 'Error must be less than Critical value.' });
    });

    it('should return validation error for invalid input. GB Free. Error less Warning', () => {
        const formGroup: FormGroup = formBuilder.group({
            warning: '25',
            error: '30',
            critical: '15',
        });

        const validator = thresholdMetricValidator(DeviceAlertThresholdTypeEnum.GB_Free, 100, 1);
        const result: ValidationErrors | null = validator(formGroup);

        expect(result).toEqual({ validationError: 'Error must be less than Warning value.' });
    });

    it('should return validation error for invalid input. GB Free. Critical less Error', () => {
        const formGroup: FormGroup = formBuilder.group({
            warning: '50',
            error: '30',
            critical: '35',
        });

        const validator = thresholdMetricValidator(DeviceAlertThresholdTypeEnum.GB_Free, 100, 1);
        const result: ValidationErrors | null = validator(formGroup);

        expect(result).toEqual({ validationError: 'Critical must be less than Error value.' });
    });
});
