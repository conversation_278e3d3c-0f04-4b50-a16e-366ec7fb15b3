<div class="content-heading">
    Cloud Infrastructure - Usage Type to ConnectWise Product Mappings
</div>
<div class="content-sub-heading">
</div>
<div class="card card-default">
    <div class="card-body">
        <div class="table-responsive">
            <ngx-datatable #table class="table bootstrap no-detail-row">
                <!-- Group Header Template -->
                <ngx-datatable-group-header [rowHeight]="50" #myGroupHeader>
                    <ng-template let-group="group" let-expanded="expanded" ngx-datatable-group-header-template>
                        <div class="ps-1">
                            <b>{{ group.value[0].usageType }}</b>
                        </div>
                    </ng-template>
                </ngx-datatable-group-header>

                <ng-template #checkCell ngx-datatable-cell-template let-value="value">
                    <span>
                        @if (value) {
                        <i class="fas fa-check text-success"></i>
                        } @else {
                        <i class="fas fa-times text-danger"></i>
                        }
                    </span>
                </ng-template>
                <ng-template #dashCellNumber ngx-datatable-cell-template let-value="value">
                    <span>
                        {{ value ? (value | number) : '-' }}
                    </span>
                </ng-template>
                <ng-template #dashCell ngx-datatable-cell-template let-value="value">
                    <span>
                        {{ value ? value : '-' }}
                    </span>
                </ng-template>
            </ngx-datatable>
        </div>
    </div>
</div>
