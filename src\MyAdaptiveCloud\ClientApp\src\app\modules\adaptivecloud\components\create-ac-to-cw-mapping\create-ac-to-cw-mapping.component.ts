import { Async<PERSON>ip<PERSON>, DatePipe } from '@angular/common';
import { Component, DestroyRef, Input, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule, NgForm } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NotificationService } from '@app/shared/services/notification.service';
import { NgbActiveModal, NgbDateStruct, NgbInputDatepicker } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable, Subject, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, map, switchMap, tap } from 'rxjs/operators';
import { AcUnmapped } from '../../models/ac-unmapped.model';
import { CwAgreement } from '../../models/cw-agreement.model';
import { CwCompany } from '../../models/cw-company.model';
import { AcCwMappingRequest } from '../../requests/ac-cw-mapping.request';
import { AcToCwMappingService } from '../../services/ac-cw-mapping.service';

// eslint-disable-next-line @angular-eslint/prefer-on-push-component-change-detection
@Component({
    selector: 'app-create-ac-to-cw-mapping',
    imports: [FormsModule, NgSelectModule, BtnSubmitComponent, NgbInputDatepicker, AsyncPipe, DatePipe],
    templateUrl: './create-ac-to-cw-mapping.component.html'
})
export class CreateAcToCwMappingComponent implements OnInit {
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly mappingService = inject(AcToCwMappingService);
    private readonly notificationService = inject(NotificationService);
    private readonly destroyRef = inject(DestroyRef);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() mappedAgreements: number[]; // / < Existing mapped agreements
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() mappingId?: number;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() acName?: string;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() acId?: string;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() isEnabled?: boolean;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() billingStartDate?: Date;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() cwBillingStartDate?: Date;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @angular-eslint/prefer-signals
    @Input() selectedAccountDomain: any;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() selectedCompany: CwCompany;
    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input() selectedAgreement: CwAgreement;

    ngbStartDate?: NgbDateStruct;
    ngbMinDate?: NgbDateStruct;
    accountsDomains: AcUnmapped[] = [];
    companies$: Observable<CwCompany[]>;
    agreements$: Observable<CwAgreement[]>;
    companyInput$ = new Subject<string>();
    companiesLoading = false;
    private loadedAgreementsForId = 0;

    ngOnInit(): void {
        const today = new Date();
        this.ngbStartDate = {
            year: today.getUTCFullYear(),
            month: today.getUTCMonth() + 1, // Month in Date is zero based, but NgbDate is 1 based
            day: today.getUTCDate()
        };
        if (!this.mappingId) {
            this.mappingService.getUnmappedAccountsDomains()
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(res => {
                    this.accountsDomains = res.data;
                });
        }

        if (this.billingStartDate) {
            this.ngbStartDate = {
                year: this.billingStartDate.getUTCFullYear(),
                month: this.billingStartDate.getUTCMonth() + 1,
                day: this.billingStartDate.getUTCDate()
            };
        }
        if (this.cwBillingStartDate) {
            this.ngbMinDate = {
                year: this.cwBillingStartDate.getUTCFullYear(),
                month: this.cwBillingStartDate.getUTCMonth() + 1,
                day: this.cwBillingStartDate.getUTCDate()
            };
        }
        this.companies$ = this.companyInput$.pipe(
            debounceTime(500),
            distinctUntilChanged(),
            tap(() => this.companiesLoading = true),
            takeUntilDestroyed(this.destroyRef),
            switchMap(companyInput => this.mappingService.searchCompanies({ keyword: companyInput }).pipe(
                takeUntilDestroyed(this.destroyRef),
                map(res => res.data.sort((a, b) => a.name.localeCompare(b.name))),
                finalize(() => this.companiesLoading = false)
            ))
        );
        this.loadAgreements();
    }

    onChangeCompany(): void {
        this.loadAgreements();
    }

    onClearCompany() {
        this.selectedAgreement = null;
        this.agreements$ = of([]);
    }

    onCloseCompany() {
        // For some reason, ng-select, when first searched and selected, doesn't seem to fire the change event, so make sure it gets loaded
        if (this.selectedCompany && (this.loadedAgreementsForId !== this.selectedCompany.id)) {
            this.loadAgreements();
        }
    }

    loadAgreements() {
        this.agreements$ = this.selectedCompany?.id ?
            this.mappingService.getAgreements({ cwCompanyId: this.selectedCompany.id, status: 'Active' })
                .pipe(
                    takeUntilDestroyed(this.destroyRef),
                    map(agreements => {
                        agreements.data.forEach(agreement => agreement.disabled = this.mappedAgreements.includes(agreement.id));
                        return agreements.data;
                    })
                )
            : of([]);
        this.loadedAgreementsForId = this.selectedCompany?.id;
    }

    onChangeAgreement() {
        this.cwBillingStartDate = this.selectedAgreement ? new Date(this.selectedAgreement?.billStartDate) : null;
        if (this.cwBillingStartDate) {
            this.ngbMinDate = {
                year: this.cwBillingStartDate.getUTCFullYear(),
                month: this.cwBillingStartDate.getUTCMonth() + 1,
                day: this.cwBillingStartDate.getUTCDate()
            };
            if (!this.billingStartDate) {
                this.ngbStartDate = {
                    year: this.cwBillingStartDate.getUTCFullYear(),
                    month: this.cwBillingStartDate.getUTCMonth() + 1,
                    day: this.cwBillingStartDate.getUTCDate()
                };
            }
        }
    }

    submitForm(form: NgForm) {
        if (form.valid) {
            const model: AcCwMappingRequest = {
                acId: this.selectedAccountDomain.id,
                acName: this.selectedAccountDomain.name,
                acType: this.selectedAccountDomain.acType,
                enabled: this.isEnabled,
                billingStartDate: this.isEnabled ? new Date(this.ngbStartDate.year, this.ngbStartDate.month - 1, this.ngbStartDate.day) : null
            };

            if (this.selectedCompany) {
                model.cwCompanyId = this.selectedCompany.id;
                model.cwCompanyName = this.selectedCompany.name;
                model.cwCompanyIdentifier = this.selectedCompany.identifier;
            }
            if (this.selectedAgreement) {
                model.cwAgreementId = this.selectedAgreement.id;
                model.cwAgreementName = this.selectedAgreement.name;
            }

            const service = this.mappingId ?
                this.mappingService.editMapping(this.mappingId, model) :
                this.mappingService.createMapping(model);

            service
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(res => {
                    this.notificationService.notify(res.message);
                    this.activeModal.close(res);
                });

        }
    }

}
