@if (vm()) {
    <div class="card card-default mt-4">
        <div class="cards-container">
            <ul class="tabs-nav nav nav-underline pt-2">
                <li class="nav-item">
                    <a class="nav-link px-5" aria-current="page" [routerLink]="[VM_ROUTE_SEGMENTS.DETAILS]"
                        routerLinkActive="active">Details</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link px-5" [routerLink]="[VM_ROUTE_SEGMENTS.NICS]" routerLinkActive="active">NICs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link px-5" [routerLink]="[VM_ROUTE_SEGMENTS.VOLUMES]"
                        routerLinkActive="active">Volumes</a>
                </li>
                @if (vmPermissionService.canListVmSnapshots()) {
                    <li class="nav-item">
                        <a class="nav-link px-5" [routerLink]="[VM_ROUTE_SEGMENTS.SNAPSHOTS]"
                            routerLinkActive="active">Snapshots</a>
                    </li>
                }
                @if (vmPermissionService.canListAffinityGroups()) {
                    <li class="nav-item">
                        <a class="nav-link px-5" [routerLink]="[VM_ROUTE_SEGMENTS.AFFINITY_GROUPS]"
                            routerLinkActive="active">Affinity
                            Groups</a>
                    </li>
                }
            </ul>
        </div>

        <div class="mt-2 p-3 border-bottom">
            @let vmRunning = vm().state === vmStateEnum.Running;
            @let vmDestroyed = vm().state === vmStateEnum.Destroyed;
            @let vmStopped = vm().state === vmStateEnum.Stopped;
            <div class="general-details-top d-flex">
                <div class="status col-1 d-flex">
                    @if (vmRunning || vmDestroyed || vmStopped) {
                        <div [ngbPopover]="statusPopTemplate" [popoverContext]="{ vm: vm }" triggers="hover" container="body"
                            class="d-flex text-secondary justify-content-center">
                            <i class="vm-icon-status" [class]="{
                                        'opacity-25': isLoading(),
                                        'icon-vm-running-with-agent': vmRunning && vm().isAgentInstalled,
                                        'icon-vm-running-without-agent': vmRunning && !vm().isAgentInstalled,
                                        'icon-vm-stopped-with-agent': vmStopped && vm().isAgentInstalled,
                                        'icon-vm-stopped-without-agent': vmStopped && !vm().isAgentInstalled,
                                        'icon-vm-destroyed-with-agent': vmDestroyed && vm().isAgentInstalled,
                                        'icon-vm-destroyed-without-agent': vmDestroyed && !vm().isAgentInstalled,
                                            }">
                            </i>
                        </div>

                        <ng-template #statusPopTemplate>
                            <div
                                [class]="{'text-secondary': isLoading(), 'text-success': !isLoading() && vmRunning, 'text-danger': !isLoading() && !vmRunning}">
                                <i [class]="{'fa-solid fa-check': vmRunning, 'fa-solid fa-stop': !vmRunning}"></i>
                                <span>&nbsp; {{ isLoading() ? 'Loading' :vm().state }}</span>
                            </div>
                        </ng-template>
                        }
                </div>
                <div class="details d-flex flex-column col-11">
                    <div class="first-row d-flex flex-row">
                        <div class="name mx-2 me-2">
                            <span class="text-primary fs-4">{{ vm().displayname }}</span>
                        </div>
                        <div class="status-pills d-flex ms-2">
                            @if(isLoading()) {
                                <div class="text-secondary pill-badge mx-1 px-2">
                                    <i class="spinner-border spinner-border-sm"></i>
                                    <span class="ms-2 pill-text">Loading</span>
                                </div>
                            } @else {
                                <div [class]="{'text-success': vmRunning, 'text-danger': !vmRunning}"
                                    class="pill-badge mx-1 px-2">
                                    <i [class]="vmRunning ? 'fa-solid fa-check' : 'fa-solid fa-stop'" class="pill-icon"></i>
                                    <span class="ms-2 pill-text">{{ vm().state }}</span>
                                </div>
                            }

                            <div [class]="{'text-success': vm().isAgentInstalled, 'text-danger': !vm().isAgentInstalled}"
                                class="pill-badge mx-1 px-2">
                                <i [class]="vm().isAgentInstalled ? 'fa-solid fa-check' : 'fa-solid fa-circle-xmark'"
                                    class="pill-icon"></i>
                                <span class="ms-2 pill-text">
                                    {{ vm().isAgentInstalled ? 'Agent installed' : 'Agent not installed' }}
                                </span>
                            </div>
                            <div [class]="{'text-success': vm().isoid, 'text-danger': !vm().isoid}"
                                class="pill-badge mx-1 px-2">
                                <i [class]="vm().isoid ? 'fa-solid fa-check' : 'fa-solid fa-circle-xmark'"
                                    class="pill-icon"></i>
                                <span class="ms-3 pill-text">{{ vm().isoid ? 'ISO attached' : 'ISO not attached' }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="second-row d-flex flex-row mt-3">
                        <div class="id-cell mx-2">
                            <span class="text-secondary fw-bold">ID: </span>
                            <span class="text-secondary">{{ vm().id }}</span>
                        </div>
                        <div class="created-cell mx-2">
                            <span class="text-secondary fw-bold">Created Date: </span>
                            <span class="text-secondary">{{ vm().created | date:'MM/dd/yyyy' }}</span>
                        </div>
                        @if(cloudInfraPermissionService.isRootAdmin()) {
                            <div class="internal-cell mx-2">
                                <span class="text-secondary fw-bold">Internal Name: </span>
                                <span class="text-secondary">{{ vm().name }}</span>
                            </div>
                        }
                    </div>

                </div>
                <fieldset [disabled]="isLoading()">
                    <div class="actions col-1 d-flex justify-content-end align-items-end">
                        @if(vmStopped) {
                            <button class="btn btn-primary me-1" type="button">
                                Edit
                            </button>

                            @if (vmPermissionService.canStartVirtualMachine()) {
                                <button (click)="vmActionsService.openStartVmModal(vm().id, vm().name, vm().zoneid)" type="button" data-testid="start-vm"
                                    title="Start VM" class="btn btn-outline-secondary me-1">
                                    <i class="fa-solid fa-play text-success"></i>
                                </button>
                            }
                        }
                        @else if (vmRunning) {
                            @if (vmPermissionService.canStopVirtualMachine()) {
                                <button (click)="vmActionsService.openStopVmModal(vm().id, vm().name)" type="button" title="Stop VM" data-testid="stop-vm"
                                    class="btn btn-outline-secondary me-1">
                                    <i class="fa-solid fa-circle-stop text-danger"></i>
                                </button>
                            }
                            @if (vmPermissionService.canRebootVirtualMachine()) {
                                <button (click)="vmActionsService.openRebootVmModal(vm().id, vm().name)" type="button" data-testid="reboot"
                                    title="Reboot VM" class="btn btn-outline-secondary me-1">
                                    <i class="fa-solid fa-arrow-rotate-left"></i>
                                </button>
                            }

                            @if (vmPermissionService.canAttachIso() && !vm().isoid) {
                                <button (click)="vmActionsService.openAttachIsoModal(vm().id, vm().zoneid, vm().domainid, vm().account, vm().name)" type="button" title="Attach ISO"
                                    class="btn btn-outline-secondary me-1 " data-testid="attach-iso">
                                    <i class="fa-solid fa-compact-disc"></i>
                                </button>
                            }

                            @if (vmPermissionService.canEjectIso() && vm().isoid) {
                                <button (click)="vmActionsService.openEjectIsoModal(vm().id, vm().name)" type="button"
                                    title="Eject ISO" class="btn btn-outline-secondary me-1" data-testid="eject-iso">
                                    <i class="fa-solid fa-eject"></i>
                                </button>
                            }

                            <button (click)="vmActionsService.openConsole(vm().id)" type="button" title="View Console"
                                class="btn btn-outline-secondary me-1" data-testid="view-console">
                                <i class="remote-control"></i>
                            </button>
                        }

                        @if (vmStopped || vmRunning || vmDestroyed) {
                            <span ngbDropdown class="dropdown" container="body">
                                <button class="btn btn-outline-secondary no-caret-btn" ngbDropdownToggle>
                                    <i class="fa-solid fa-ellipsis-vertical"></i>
                                </button>
                                <div ngbDropdownMenu class="custom-dropdown-menu">
                                    @if (vmRunning) {
                                        @if (vmPermissionService.canSnapshotVolume()) {
                                            <button class="dropdown-item" data-testid="snapshot-volume"
                                                (click)="vmActionsService.openVolumeSnapshotModal(vm().id, vm().domainid, vm().account, vm().name)">
                                                Take Volume snapshot
                                            </button>
                                        }
                                        @if (vmPermissionService.canSnapshotVirtualMachine()) {
                                            <button class="dropdown-item" data-testid="snapshot-vm"
                                                (click)="vmActionsService.openSnapshotVmModal(vm().id, vm().name)">
                                                Take VM snapshot
                                            </button>
                                        }
                                        @if (vmPermissionService.canMigrateVirtualMachineHost()) {
                                            <button class="dropdown-item" data-testid="migrate-host"
                                                (click)="vmActionsService.openMigrateHostModal(vm().id, vm().name)">Migrate VM host</button>
                                        }
                                        @if (vmPermissionService.canReinstallVirtualMachine()) {
                                            <button class="dropdown-item" data-testid="reinstall-vm"
                                                (click)="vmActionsService.openReinstallVirtualMachineModal(vm().id, vm().zoneid, vm().domainid, vm().account, vm().name, vm().isoid)">Reinstall VM</button>
                                        }
                                    }
                                    @if (vmStopped) {
                                        @if (vmPermissionService.canResetVirtualMachinePassword() && vm().passwordenabled) {
                                            <button class="dropdown-item" data-testid="reset-password"
                                                (click)="vmActionsService.openResetPasswordModal(vm().id, vm().name)">Reset
                                                Password</button>
                                            }
                                        @if (vmPermissionService.canResetSSHKeyPairForVirtualMachine()) {
                                            <button class="dropdown-item" data-testid="reset-ssh-pair"
                                                (click)="vmActionsService.openResetSSHKeyPairModal(vm().id, vm().domainid, vm().account, vm().name)">Reset SSH key pair</button>
                                        }
                                        @if (vmPermissionService.canReinstallVirtualMachine()) {
                                            <button class="dropdown-item" data-testid=" reinstall-vm"
                                                (click)="vmActionsService.openReinstallVirtualMachineModal(vm().id, vm().zoneid, vm().domainid, vm().account, vm().name, vm().isoid)">Reinstall VM</button>
                                        }
                                        @if (vmPermissionService.canDestroyVirtualMachine()) {
                                            <button class="dropdown-item" data-testid="destroy-vm"
                                                (click)="vmActionsService.openDestroyVmModal(vm().id, vm().domainid, vm().account, vm().name)">Destroy</button>
                                        }
                                    }
                                    @if (vmDestroyed) {
                                        @if (vmPermissionService.canExpungeVirtualMachine()) {
                                        <button class="dropdown-item" data-testid="expunge-vm"
                                            (click)="vmActionsService.openExpungeDestroyedVirtualMachineModal(vm().id, vm().name)">Expunge</button>
                                        }
                                        @if (vmPermissionService.canRecoverVirtualMachine()) {
                                        <button class="dropdown-item recover-vm" data-testid="recover-vm"
                                            (click)="vmActionsService.openRecoverVirtualMachineModal(vm().id, vm().name)">Recover</button>
                                        }
                                    }
                                </div>
                            </span>
                        }
                    </div>
                </fieldset>
            </div>
        </div>
        <div class="card-body">
            <router-outlet />
        </div>
    </div>
}
