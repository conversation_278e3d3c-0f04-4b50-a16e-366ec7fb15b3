import { NgComponentOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { ApiClientsComponent } from '@app/shared/components/api-clients/api-clients.component';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbActiveModal, NgbNav, NgbNavContent, NgbNavItem, NgbNavLink, NgbNavOutlet } from '@ng-bootstrap/ng-bootstrap';
import { EditProfileComponent } from '../edit-profile/edit-profile.component';
import { ManageSubscriptionsComponent } from '../manage-subsciptions/manage-subscriptions.component';
import { ListTwoFactorAuthenticationComponent } from '../two-factor-authentication/list-two-factor-authentication/list-two-factor-authentication.component';

@Component({
    selector: 'app-user-preferences-tab',
    imports: [<PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>avOutlet, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>av<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ompo<PERSON>Out<PERSON>],
    templateUrl: './user-preferences-tab.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserPreferencesTabComponent {
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly userContextService = inject(UserContextService);

    protected readonly editProfileTab = EditProfileComponent;
    protected readonly twoFactorAuthenticationTab = ListTwoFactorAuthenticationComponent;
    protected readonly manageSubscriptionsComponent = ManageSubscriptionsComponent;
    protected readonly apiClientsComponent = ApiClientsComponent;

    protected apliClientInputs = {
        userId: this.userContextService.currentUser.userId,
        isCreateEnabled: true,
        isEditingSelf: true
    };
}

