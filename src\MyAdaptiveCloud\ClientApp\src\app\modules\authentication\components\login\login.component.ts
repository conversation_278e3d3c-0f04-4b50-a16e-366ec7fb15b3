import { ChangeDetectionStrategy, Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserContextService } from '@app/shared/services/user-context.service';
import { LoginRequest } from '../../requests/login.request';
import { LocalAuthenticationService } from '../../services/local-authentication.service';
import { LoginForm } from './login.form';

@Component({
    selector: 'app-login',
    imports: [ReactiveFormsModule],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoginComponent {
    private readonly fb = inject(FormBuilder);
    private readonly userContextService = inject(UserContextService);
    private readonly authenticationService = inject(LocalAuthenticationService);
    private readonly router = inject(Router);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly form: FormGroup<LoginForm> = this.fb.group<LoginForm>({
        email: this.fb.control(null, [Validators.required, Validators.email]),
        password: this.fb.control(null, Validators.required)
    });

    protected submitForm(): void {
        if (this.form.valid) {
            const request = this.form.getRawValue() as LoginRequest;
            this.authenticationService.login(request)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                    this.userContextService.initializeUserContext().then((() => {
                        if (this.userContextService.currentUser) {
                            this.router.navigate(['/home']);
                        }
                    }));
                });
        }
    }

}
