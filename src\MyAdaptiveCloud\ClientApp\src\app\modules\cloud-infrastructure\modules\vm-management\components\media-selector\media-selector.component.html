<div class="mb-3">
    <label class="btn"
        [class]="mediaTypeControl().value === osTypeFilter.Featured ? 'btn-outline-secondary' : 'btn-myac-special-case'">Featured
        <input class="btn-check" type="radio" [value]="osTypeFilter.Featured" [formControl]="mediaTypeControl()"
            id="media_type_featured" />
    </label>

    <label class="ms-2 btn"
        [class]="mediaTypeControl().value === osTypeFilter.Public ? 'btn-outline-secondary' : 'btn-myac-special-case'">Public
        <input class="btn-check" type="radio" [value]="osTypeFilter.Public" [formControl]="mediaTypeControl()"
            id="media_type_public" />
    </label>

    <label class="ms-2 btn"
        [class]="mediaTypeControl().value === osTypeFilter.MyTemplates ? 'btn-outline-secondary' : 'btn-myac-special-case'">{{
        selectedOsType() === osType.Template ? 'My Templates' : 'My ISOs' }}
        <input class="btn-check" type="radio" [value]="osTypeFilter.MyTemplates" [formControl]="mediaTypeControl()"
            id="media_type_myiso" />
    </label>
</div>

<div class="list">
    @for (item of media(); track item.id) {
        <label class="details d-flex align-items-center">
            <div>
                <input class="form-check-input" type="radio" [value]="item" [formControl]="mediaControl()"
                    [id]="'media_' + item.id" />
                <i class="ms-2 fas fa-layer-group"></i>
            </div>
            <div class="ms-3">
                <div [id]="'media_' + item.id + '_name'">{{ item.name }} </div>
                <div [id]="'media_' + item.id + '_description'"><small class="text-secondary">{{ item.description }}</small>
                </div>
            </div>
        </label>
    }
</div>
