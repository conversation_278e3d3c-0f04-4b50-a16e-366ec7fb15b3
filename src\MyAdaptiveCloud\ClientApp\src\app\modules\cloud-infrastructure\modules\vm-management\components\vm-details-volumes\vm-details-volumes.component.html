<div class="content-sub-heading mt-0 pt-0">
    <div class="search-bar d-flex align-items-center gap-3">
        <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
    </div>

    <div class="action-buttons">
        <div class="btn-group" role="group">
            <button type="button" class="btn" [class.btn-primary]="viewMode() === 'volumes'"
                [class.btn-outline-primary]="viewMode() !== 'volumes'" (click)="toggleViewMode('volumes')"
                data-testid="volumes-toggle">
                Volumes
            </button>
            <button type="button" class="ms-0 btn" [class.btn-primary]="viewMode() === 'metrics'"
                [class.btn-outline-primary]="viewMode() !== 'metrics'" (click)="toggleViewMode('metrics')"
                data-testid="metrics-toggle">
                Metrics
            </button>
        </div>
        <button class="ms-3 btn btn-primary" data-testid="add-volume-button" (click)="onAddVolume()">
            Add Volume
        </button>
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class='table bootstrap no-detail-row'>
        </ngx-datatable>
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #stateTemplate let-row="row">
    @if (toItem(row); as row) {
        @let iconColor = {
        'text-warning': row.state === volumeState.UploadNotStarted,
        'text-danger': row.state === volumeState.UploadError,
        'text-secondary': row.state === volumeState.Uploaded,
        'text-success': row.state === volumeState.Ready,
        'text-primary': row.state === volumeState.Allocated
        };
        <i class='fa-solid fa-circle me-1 status-icon' [class]="iconColor"></i>
        {{ row.state }}
    }
</ng-template>

<ng-template #numberTemplate let-value="value">
    {{ value ? (value | number) : '-' }}
</ng-template>

<ng-template #gbTemplate let-value="value">
    {{ value ? value + ' GB' : '-' }}
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as volume) {
    <span ngbDropdown class="dropdown text-center ms-4" container="body">
        <button class="btn btn-link kebab-menu" ngbDropdownToggle>
            <i class="fa-solid fa-ellipsis-vertical"></i>
        </button>
        <div ngbDropdownMenu class="custom-dropdown-menu">
            <button disabled class="dropdown-item edit-volume">Edit</button>
        </div>
    </span>
    }
</ng-template>
