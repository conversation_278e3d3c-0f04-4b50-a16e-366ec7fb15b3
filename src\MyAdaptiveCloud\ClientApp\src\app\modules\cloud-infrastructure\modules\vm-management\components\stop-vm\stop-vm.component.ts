import { ChangeDetectionStrategy, Component, inject, Input, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { VmStopForm } from '../../forms/vm-stop.form';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-stop-vm',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgbPopover],
    templateUrl: './stop-vm.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class StopVmComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    private readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);

    protected form: FormGroup<VmStopForm>;
    protected readonly isSubmitting = signal<boolean>(false);

    // TODO: Skipped for migration because:
    //  Your application code writes to the input. This prevents migration.
    // eslint-disable-next-line @angular-eslint/prefer-signals
    @Input({ required: true }) virtualMachineId: string;

    ngOnInit(): void {
        this.form = this.formBuilder.group<VmStopForm>({
            forceStop: this.formBuilder.control<boolean>(false, Validators.required)
        });
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected stopVirtualMachine() {
        if (this.form.valid) {
            this.isSubmitting.set(true);
            this.vmManagementService.stopVirtualMachine(this.virtualMachineId, this.form.value.forceStop)
                .subscribe(res => {
                    this.isSubmitting.set(false);
                    if (res) {
                        this.activeModal.close(res);
                    }
                });
        }
    }

}
