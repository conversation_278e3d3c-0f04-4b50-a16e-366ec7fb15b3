﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class AdaptiveCloudDriveFileAuthorize : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public AdaptiveCloudDriveFileAuthorize(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService, IUserContextService userContextService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int fileAdministrationId);

            var adaptiveCloudDriveFileOrganizationId = await _entityAuthorizationService.GetAdaptiveCloudDriveFileOrganizationId(fileAdministrationId);
            if (adaptiveCloudDriveFileOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, adaptiveCloudDriveFileOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, adaptiveCloudDriveFileOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class AdaptiveCloudDriveAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public AdaptiveCloudDriveAuthorizeAttribute(params Perms[] perms) : base(typeof(AdaptiveCloudDriveFileAuthorize), perms)
        {
            Name = "fileAdministrationId";
        }
    }
}
