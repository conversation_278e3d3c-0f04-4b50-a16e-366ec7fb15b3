import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { VolumeState } from '@app/modules/cloud-infrastructure/models/volume-state';
import { getMockZoneDomainAccountStore, MockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { VmDetails } from '../../models/vm-detail.model';
import { Volume } from '../../models/volume';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmVolumesService } from '../../services/vm-volumes.service';
import { VmDetailsVolumesComponent } from './vm-details-volumes.component';

describe('VmDetailsVolumesComponent', () => {
    let fixture: ComponentFixture<VmDetailsVolumesComponent>;
    let vmDetailsStateService: VmDetailsStateService;
    let mockVmVolumesService: jasmine.SpyObj<VmVolumesService>;
    let volumes: Volume[];
    let mockZoneDomainAccountStore: MockZoneDomainAccountStore;

    let dataTableDebugElement: DebugElement;
    let dataTable: HTMLElement;

    const mockVm: VmDetails = {
        id: 'vm-123'
    } as VmDetails;

    beforeEach(() => {
        mockZoneDomainAccountStore = getMockZoneDomainAccountStore();

        volumes = [
            {
                id: 'vol-1',
                name: 'Root Volume',
                state: VolumeState.Ready,
                size: **********,
                diskioread: 500000,
                diskiowrite: 500000,
                physicalsize: ***********,
                utilization: '75%'
            } as Volume,
            {
                id: 'vol-2',
                name: 'Data Volume',
                state: VolumeState.Ready,
                size: ***********,
                diskioread: 60,
                diskiowrite: 60,
                physicalsize: **********,
                utilization: '70%'
            } as Volume,
            {
                id: 'vol-4',
                name: 'Root Volume 2',
                state: VolumeState.UploadInProgress,
                size: *********,
                diskioread: 0,
                diskiowrite: null,
                physicalsize: 0,
                utilization: '0%'
            } as Volume
        ];

        TestBed.configureTestingModule({
            imports: [VmDetailsVolumesComponent],
            providers: [
                VmDetailsStateService,
                provideMock(VmVolumesService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: mockZoneDomainAccountStore,
                }
            ]
        }).compileComponents();

        vmDetailsStateService = TestBed.inject(VmDetailsStateService);
        mockVmVolumesService = TestBed.inject(VmVolumesService) as jasmine.SpyObj<VmVolumesService>;

        mockVmVolumesService.getVolumeMetricsByVirtualMachine.and.returnValue(of(volumes));

        vmDetailsStateService.selectedVM.set(mockVm);

        fixture = TestBed.createComponent(VmDetailsVolumesComponent);

        dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
        dataTable = dataTableDebugElement?.nativeElement;
    });

    describe('Component Initialization', () => {

        it('should load volumes data on initialization', () => {
            fixture.detectChanges();
            expect(mockVmVolumesService.getVolumeMetricsByVirtualMachine).toHaveBeenCalledOnceWith(mockVm.id);
        });
    });

    describe('Data Display', () => {

        beforeEach(() => {
            fixture.detectChanges();
        });

        it('should display volumes in the data table', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toBe(volumes.length);
        });

        it('should display correct volume data in table cells', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            expect(firstRowCells.length).toBeGreaterThan(0);
        });

        it('should show the expected volume data in the grid by default', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            const secondRow = rows[1];
            const secondRowCells = secondRow.querySelectorAll('.datatable-body-cell');

            const thirdRow = rows[2];
            const thirdRowCells = thirdRow.querySelectorAll('.datatable-body-cell');

            expect(secondRowCells[0].textContent.trim()).toBe(volumes[0].state);
            expect(secondRowCells[1].textContent).toBe(volumes[0].name);
            expect(secondRowCells[2].textContent.trim()).toBe('4.68 GB');
            expect(secondRowCells[3].textContent.trim()).toBe('18.64 GB');
            expect(secondRowCells[4].textContent.trim()).toBe('75%');

            expect(firstRowCells[0].textContent.trim()).toBe(volumes[1].state);
            expect(firstRowCells[1].textContent).toBe(volumes[1].name);
            expect(firstRowCells[2].textContent.trim()).toBe('9.31 GB');
            expect(firstRowCells[3].textContent.trim()).toBe('4.25 GB');
            expect(firstRowCells[4].textContent.trim()).toBe('70%');

            expect(thirdRowCells[0].textContent.trim()).toBe(volumes[2].state);
            expect(thirdRowCells[1].textContent).toBe(volumes[2].name);
            expect(thirdRowCells[2].textContent.trim()).toBe('0.47 GB');
            expect(thirdRowCells[3].textContent.trim()).toBe('-');
            expect(thirdRowCells[4].textContent.trim()).toBe('0%');
        });

        it('should show the expected metrics data in the grid when selecting metrics in the toggle', () => {

            const toggle = fixture.debugElement.query(By.css('[data-testid="metrics-toggle"]')).nativeElement as HTMLButtonElement;
            toggle.click();
            fixture.detectChanges();

            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            const secondRow = rows[1];
            const secondRowCells = secondRow.querySelectorAll('.datatable-body-cell');

            const thirdRow = rows[2];
            const thirdRowCells = thirdRow.querySelectorAll('.datatable-body-cell');

            expect(secondRowCells[0].textContent.trim()).toBe(volumes[0].state);
            expect(secondRowCells[1].textContent).toBe(volumes[0].name);
            expect(secondRowCells[2].textContent.trim()).toBe('500,000');
            expect(secondRowCells[3].textContent.trim()).toBe('500,000');
            expect(secondRowCells[4].textContent.trim()).toBe('1,000,000');

            expect(firstRowCells[0].textContent.trim()).toBe(volumes[1].state);
            expect(firstRowCells[1].textContent).toBe(volumes[1].name);
            expect(firstRowCells[2].textContent.trim()).toBe('60');
            expect(firstRowCells[3].textContent.trim()).toBe('60');
            expect(firstRowCells[4].textContent.trim()).toBe('120');

            expect(thirdRowCells[0].textContent.trim()).toBe(volumes[2].state);
            expect(thirdRowCells[1].textContent).toBe(volumes[2].name);
            expect(thirdRowCells[2].textContent.trim()).toBe('-');
            expect(thirdRowCells[3].textContent.trim()).toBe('-');
            expect(thirdRowCells[4].textContent.trim()).toBe('-');
        });

    });

});
