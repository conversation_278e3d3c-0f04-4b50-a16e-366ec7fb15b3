﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class DeleteCompanyAssociationAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public DeleteCompanyAssociationAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _identityService = identityService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
            }
            else
            {
                string val = AuthorizeFilterHelpers.GetEntityValue(context, "organizationId");
                _ = int.TryParse(val, out int organizationId);

                if (!await _entityAuthorizationService.IsOrganizationInactive(organizationId))
                {
                    context.Result = new BadRequestResult();
                }
            }

            await Task.CompletedTask;
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class DeleteCompanyAssociationAuthorizeAttribute : TypeFilterAttribute
    {
        public DeleteCompanyAssociationAuthorizeAttribute() : base(typeof(DeleteCompanyAssociationAuthorizeFilter))
        {
        }
    }
}