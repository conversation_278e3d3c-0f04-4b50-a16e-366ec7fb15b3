import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { ReleaseTagId } from '@app/modules/device-management/modules/agent-management/models/release-tag-id.model';
import { ReleaseTagsService } from '@app/modules/device-management/modules/agent-management/services/release-tags.service';
import { ScheduleInfoForm } from '@app/modules/device-management/modules/policy/models/schedule-info.form';
import { DEFAULT_RELEASE_TAG, RELEASE_TAG_ORDER } from '@app/shared/constants/release-tags-constants';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectComponent } from '@ng-select/ng-select';
import { debounceTime, defer, distinctUntilChanged, filter, map } from 'rxjs';
import { ScheduleInfoStore, ScheduleStore } from '../schedule.component.store';

@Component({
    selector: 'app-schedule-info',
    imports: [ReactiveFormsModule, AsyncPipe, NgSelectComponent, NgbPopover],
    templateUrl: './schedule-info.component.html',
    providers: [],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ScheduleInfoComponent implements OnInit {

    private readonly formBuilder = inject(FormBuilder);
    private readonly scheduleStore = inject(ScheduleStore);
    private readonly destroyRef = inject(DestroyRef);
    private readonly releaseTagService = inject(ReleaseTagsService);
    private readonly userContextService = inject(UserContextService);

    private isReadOnly: boolean;
    private readonly minVersionToInstallUpdatesImmediate = signal<string>('');

    protected readonly form = this.formBuilder.group<ScheduleInfoForm>({
        name: this.formBuilder.control('', [Validators.required, Validators.maxLength(255)]),
        description: this.formBuilder.control('', [Validators.maxLength(255)]),
        hasMicrosoftUpdates: this.formBuilder.control(false),
        releaseTagName: this.formBuilder.control(DEFAULT_RELEASE_TAG)
    });

    protected readonly isAgentManagementFlagEnabled = this.userContextService.getFeatureFlagState(FeatureFlag.FeatureFlagAgentManagement);

    protected readonly allReleaseTags$ = defer(() => this.releaseTagService.getIdList(this.userContextService.currentUser.organizationId)
        .pipe(map(res => this.sortByReleaseTagOrder(res.data, RELEASE_TAG_ORDER))));

    ngOnInit(): void {
        this.subscribeToStateChanges();
        this.handleFormValueChanges();
    }

    private subscribeToStateChanges() {
        this.scheduleStore.getScheduleInfoFormState$
            .pipe(
                filter(scheduleInfoForm => scheduleInfoForm.isScheduleInfoFormValid),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe((scheduleInfoForm: ScheduleInfoStore) => {
                if (scheduleInfoForm.isReadOnly !== this.isReadOnly) {
                    this.isReadOnly = scheduleInfoForm.isReadOnly;
                    if (this.isReadOnly) {
                        this.form.disable();
                    } else {
                        this.form.enable();
                    }
                }

                if (!this.minVersionToInstallUpdatesImmediate() && scheduleInfoForm.minAgentVersionImmediateUpdateInstall) {
                    this.minVersionToInstallUpdatesImmediate.set(scheduleInfoForm.minAgentVersionImmediateUpdateInstall);
                }

                this.initializeForm(scheduleInfoForm);
            });
    }

    private handleFormValueChanges() {
        this.form.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                debounceTime(200),
                distinctUntilChanged()
            )
            .subscribe(value => {
                if (this.form.valid) {
                    this.updateStoreState(value);
                }
            });
    }

    private initializeForm(scheduleInfoForm: ScheduleInfoStore) {
        this.form.setValue({
            name: scheduleInfoForm.name,
            description: scheduleInfoForm.description,
            hasMicrosoftUpdates: scheduleInfoForm.hasMicrosoftUpdates,
            releaseTagName: scheduleInfoForm.releaseTagName
        }, { emitEvent: false });
        this.form.updateValueAndValidity();
    }

    // TODO: Fix typing
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private updateStoreState(value: any) {
        this.scheduleStore.updateScheduleInfoFormState({
            name: value.name,
            description: value.description,
            isScheduleInfoFormValid: !!value.name,
            isReadOnly: this.isReadOnly,
            hasMicrosoftUpdates: value.hasMicrosoftUpdates,
            minAgentVersionImmediateUpdateInstall: this.minVersionToInstallUpdatesImmediate(),
            releaseTagName: value.releaseTagName
        });
    }

    private sortByReleaseTagOrder(data: ReleaseTagId[], releaseTagOrder: string[]): ReleaseTagId[] {
        return data.sort((a, b) => {
            const aIndex = releaseTagOrder.indexOf(a.releaseTagName.toLowerCase());
            const bIndex = releaseTagOrder.indexOf(b.releaseTagName.toLowerCase());

            if (aIndex !== -1 && bIndex !== -1) {
                return aIndex - bIndex;
            } else if (aIndex !== -1) {
                return 1;
            } else if (bIndex !== -1) {
                return -1;
            }

            return a.releaseTagName.localeCompare(b.releaseTagName);
        });
    }
}

