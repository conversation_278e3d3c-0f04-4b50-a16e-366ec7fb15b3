import { ComponentFixture, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { UpdateUserAuthenticatorRequest } from '@app/core/requests/update-user-authenticator.request';
import { TwoFactorAuthenticationService } from '@app/core/services/two-factor-authentication.service';
import { ApiResult } from '@app/shared/models/api-service/api.result';
import { ApiService } from '@app/shared/services/api.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { UpdateUserAuthenticatorComponent } from './update-user-authenticator.component';

describe('UpdateUserAuthenticatorComponent', () => {
    let component: UpdateUserAuthenticatorComponent;
    let fixture: ComponentFixture<UpdateUserAuthenticatorComponent>;
    let mockTwoFactorAuthenticationService: jasmine.SpyObj<TwoFactorAuthenticationService>;
    let mockNotificationService: jasmine.SpyObj<NotificationService>;

    const apiResult: ApiResult = { message: 'success' };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [UpdateUserAuthenticatorComponent],
            providers: [FormBuilder,
                provideMock(NgbActiveModal),
                provideMock(TwoFactorAuthenticationService),
                provideMock(NotificationService),
                provideMock(ApiService)
            ]
        });
        mockTwoFactorAuthenticationService = TestBed.inject(TwoFactorAuthenticationService) as jasmine.SpyObj<TwoFactorAuthenticationService>;
        mockTwoFactorAuthenticationService.updateUserAuthenticator.and.returnValue(of(apiResult));
        mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;

        fixture = TestBed.createComponent(UpdateUserAuthenticatorComponent);
        component = fixture.componentInstance;
    });

    describe('ngOnInit', () => {

        it('should fill the form', () => {
            const name = '1';
            component.userAuthenticatorName.set('1');
            fixture.detectChanges();
            expect(component.form().controls.name.value).toEqual(name);
        });

    });

    describe('form submit', () => {

        it('should submit the form', fakeAsync(() => {
            const credentialId = '1';

            const request: UpdateUserAuthenticatorRequest = {
                name: 'updatedName'
            };

            component.userAuthenticatorId.set(credentialId);
            fixture.detectChanges();

            component.form().controls.name.setValue(request.name);
            fixture.detectChanges();

            component.submitForm();

            flush();
            expect(mockTwoFactorAuthenticationService.updateUserAuthenticator).toHaveBeenCalledOnceWith(credentialId, request);
            expect(mockNotificationService.notify).toHaveBeenCalledOnceWith(apiResult.message);
            expect(component.isSubmitted()).toBeTrue();
        }));

        it('should not submit the form', fakeAsync(() => {
            const credentialId = '1';

            component.userAuthenticatorId.set(credentialId);
            fixture.detectChanges();

            component.form().controls.name.setValue(null);
            fixture.detectChanges();

            component.submitForm();

            flush();

            expect(mockTwoFactorAuthenticationService.updateUserAuthenticator).not.toHaveBeenCalled();
            expect(mockNotificationService.notify).not.toHaveBeenCalled();
            expect(component.isSubmitted()).toBeTrue();
        }));

    });
});
