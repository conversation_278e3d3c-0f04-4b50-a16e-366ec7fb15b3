import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { AdaptiveCloudUsageSummaryAccount } from '@app/shared/models/adaptivecloud/adaptivecloud-usage-summary-account.mode';
import { AdaptiveCloudUsageSummary } from '@app/shared/models/adaptivecloud/adaptivecloud-usage-summary.model';
import { UserContext } from '@app/shared/models/user-context.model';
import { AdaptiveCloudUsageSharedService } from '@app/shared/services/adaptivecloud-usage-shared.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { AdaptiveCloudUsageSummaryComponent } from './adaptivecloud-usage-summary.component';

describe('AdaptiveCloudUsageSummaryComponent', () => {
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockAdaptiveCloudUsageService: jasmine.SpyObj<AdaptiveCloudUsageSharedService>;
    let fixture: ComponentFixture<AdaptiveCloudUsageSummaryComponent>;
    let component: AdaptiveCloudUsageSummaryComponent;

    const row: AdaptiveCloudUsageSummaryAccount = {
        accountId: '2',
        accountName: 'something',
        accountTotal: 5,
        vCpu: {
            quantity: 4,
            totalCost: 24,
            unitCost: 6,
            unitDescription: 'gruyere'
        },
        ram: {
            quantity: 7,
            totalCost: 7,
            unitCost: 1,
            unitDescription: 'gouda'
        },
        ipAddress: {
            quantity: 0,
            totalCost: 0,
            unitCost: 3,
            unitDescription: 'mozzarella'
        },
        networkBytes: {
            quantity: 4,
            totalCost: 8,
            unitCost: 2,
            unitDescription: 'cheddar'
        },
        primaryStorage: {
            quantity: 4,
            totalCost: 28,
            unitCost: 7,
            unitDescription: 'parmesano'
        },
        secondaryStorage: {
            quantity: 4,
            totalCost: 32,
            unitCost: 8,
            unitDescription: 'goat cheese'
        },
        licensing: {
            licenses: [],
            totalCost: 5,
        },
        virtualMachineCount: 0
    };

    const data: AdaptiveCloudUsageSummary = {
        accounts: [
            row
        ],
        total: row
    };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AdaptiveCloudUsageSummaryComponent
            ],
            providers: [
                provideMock(UserContextService),
                provideMock(AdaptiveCloudUsageSharedService)
            ]
        });

        mockAdaptiveCloudUsageService = TestBed.inject(AdaptiveCloudUsageSharedService) as jasmine.SpyObj<AdaptiveCloudUsageSharedService>;
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockAdaptiveCloudUsageService.getUsageSummary.and.returnValue(of({ data, message: 'success', totalCount: 1 }));
        mockUserContextService.currentUser = { organizationId: 15 } as UserContext;
        fixture = TestBed.createComponent(AdaptiveCloudUsageSummaryComponent);
        component = fixture.componentInstance;
        fixture.componentRef.setInput('period', '202101');
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should call getUsageSummary', () => {
            expect(mockAdaptiveCloudUsageService.getUsageSummary).toHaveBeenCalledOnceWith(15, '202101');
        });
    });

    describe('Component Interaction', () => {

        let dataTableDebugElement: DebugElement;
        let dataTable: HTMLElement;

        beforeEach(() => {
            dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            dataTable = dataTableDebugElement.nativeElement;
            fixture.detectChanges();
        });

        it('should have the same amount of rows as data', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(1);
        });

        it('should call the showAccountDetails function on click', () => {
            spyOn(component, 'showAccountDetails');
            const rows = dataTable.querySelectorAll<HTMLElement>('.fa-magnifying-glass-plus');
            const button = rows[0];
            button.click();
            expect(component.showAccountDetails).toHaveBeenCalled();
        });
    });
});
