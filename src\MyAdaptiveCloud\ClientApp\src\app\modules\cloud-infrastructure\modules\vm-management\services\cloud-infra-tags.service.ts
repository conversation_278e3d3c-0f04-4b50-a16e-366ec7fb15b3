import { inject, Injectable } from '@angular/core';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { map, Observable } from 'rxjs';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '../models/vm.constants';
import { CreateTagResponse } from '../responses/create-tag-response';

@Injectable({
    providedIn: 'root'
})
export class CloudInfrastructureTagsService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    createTags(vmId: string, tags: { key: string; value: string }[]): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createTags,
            resourceids: vmId,
            'tags[0].key': tags[0].key,
            'tags[0].value': tags[0].value,
            resourcetype: 'UserVm'
        };

        return this.cloudInfraApiService.get<CreateTagResponse>(params).pipe(map(response => response.createtagsresponse?.jobid));
    }

    deleteTags(vmId: string, tags: { key: string; value: string }[]): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.deleteTags,
            resourceids: vmId,
            'tags[0].key': tags[0].key,
            'tags[0].value': tags[0].value,
            resourcetype: 'UserVm'
        };

        return this.cloudInfraApiService.get<CreateTagResponse>(params).pipe(map(response => response.createtagsresponse?.jobid));
    }

}
