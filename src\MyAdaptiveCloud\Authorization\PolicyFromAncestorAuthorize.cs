using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
	public class PolicyFromAncestorAuthorizeFilter : BaseAsyncAuthorizationFilter
	{
		private readonly IIdentityService _identityService;
		private readonly IEntityAuthorizationService _entityAuthorizationService;

		public PolicyFromAncestorAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
			Perms[] perms, int distance, string name) : base(perms, distance, name)
		{
			_entityAuthorizationService = entityAuthorizationService;
			_identityService = identityService;
		}

		/// <summary>
		/// Grants authorization to a user from a descendant organization of Organization A to access the policy of Organization A.
		/// </summary>
		public override async Task OnAuthorizationAsync(AuthorizationFilterContext context)
		{
			int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
			if (userId == 0)
				context.Result = new UnauthorizedResult();
			else
			{
				string policyIdValue = AuthorizeFilterHelpers.GetEntityValue(context, _name);
				string organizationIdValue = AuthorizeFilterHelpers.GetEntityValue(context, "organizationId");
				_ = int.TryParse(policyIdValue, out int policyId);
				_ = int.TryParse(organizationIdValue, out int organizationId);

				var policyOrganizationId = await _entityAuthorizationService.GetPolicyOrganizationId(policyId);

				if (policyOrganizationId.HasValue)
				{
					// A user wants to see a policy from an organization that is ancestor of the user's organization
					var policyOrganizationIsAnAncestor = _entityAuthorizationService.IsOrganizationWithinOrganizationHierarchy(policyOrganizationId.Value, organizationId);
					var policyOrganizationIsADescendant = _entityAuthorizationService.IsOrganizationWithinOrganizationHierarchy(organizationId, policyOrganizationId.Value);

					if (!policyOrganizationIsAnAncestor && !policyOrganizationIsADescendant)
						context.Result = new ForbidResult(); //the user organization is not a descendant of the organization the policy belongs to
				}
				else
					context.Result = new BadRequestResult();
			}
		}
	}

	public class PolicyFromAncestorAuthorizeAttribute : BaseAuthorizeAttribute
	{
		public PolicyFromAncestorAuthorizeAttribute(params Perms[] perms) : base(typeof(PolicyFromAncestorAuthorizeFilter), perms)
		{
			Name = "policyId";
		}
	}
}