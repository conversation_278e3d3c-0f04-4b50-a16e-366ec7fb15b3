import { ChangeDetectorRef, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { CloudInfraZoneService } from '@app/modules/cloud-infrastructure/services/cloud-infra-zone.service';
import { getMockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { ConfirmationDialogComponent } from '@app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { VmInstance } from '@app/shared/models/cloud-infra/vm-instance.model';
import { VmStateEnum } from '@app/shared/models/cloud-infra/vm-state.enum';
import { FeatureFlag } from '@app/shared/models/feature-flag.enum';
import { UserContext } from '@app/shared/models/user-context.model';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal, NgbDropdownMenu, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { VmActionsService } from '../../services/vm-actions.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { VmManagementService } from '../../services/vm-management.service';
import { VmMediaService } from '../../services/vm-media-service';
import { AttachIsoComponent } from '../attach-iso/attach-iso.component';
import { DestroyVmComponent } from '../destroy-vm/destroy-vm.component';
import { MigrateHostComponent } from '../migrate-host/migrate-host.component';
import { RebootVmComponent } from '../reboot-vm/reboot-vm.component';
import { ReinstallVmComponent } from '../reinstall-vm/reinstall-vm.component';
import { ResetPasswordComponent } from '../reset-password/reset-password.component';
import { ResetSSHKeyPairComponent } from '../reset-ssh-key-pair/reset-ssh-key-pair.component';
import { SnapshotVmComponent } from '../snapshot-vm/snapshot-vm.component';
import { SnapshotVolumeComponent } from '../snapshot-volume/snapshot-volume.component';
import { StartVmComponent } from '../start-vm/start-vm.component';
import { StopVmComponent } from '../stop-vm/stop-vm.component';
import { VmListComponent } from './vm-list.component';

describe('VmListComponent', () => {
    let fixture: ComponentFixture<VmListComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let mockModalService: jasmine.SpyObj<ModalService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockVmManagementPermissionService: jasmine.SpyObj<VmManagementPermissionService>;
    let mockVmMediaService: jasmine.SpyObj<VmMediaService>;
    let mockZoneService: jasmine.SpyObj<CloudInfraZoneService>;
    let mockCloudInfrastructureQueueService: jasmine.SpyObj<CloudInfrastructureJobQueueService>;

    let vmRunningWithoutIso: VmInstance;
    let vmRunning: VmInstance;
    let vmStopped: VmInstance;
    let vmDestroyed: VmInstance;

    let data: VmInstance[];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [VmListComponent],
            providers: [
                provideMock(VmManagementService),
                provideMock(Router),
                provideMock(UserContextService),
                provideMock(VmManagementPermissionService),
                provideMock(NotificationService),
                provideMock(ModalService),
                provideMock(NgbActiveModal),
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraZoneService),
                provideMock(CloudInfrastructureApiService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: getMockZoneDomainAccountStore(),
                },
                provideMock(VmMediaService),
                provideMock(ActivatedRoute),
                ChangeDetectorRef,
                DestroyVmComponent,
                StopVmComponent,
                RebootVmComponent,
                StartVmComponent,
                ResetSSHKeyPairComponent,
                SnapshotVmComponent,
                ConfirmationDialogComponent,
                AttachIsoComponent,
                MigrateHostComponent,
                SnapshotVolumeComponent,
                ReinstallVmComponent,
                ResetPasswordComponent,
                VmActionsService
            ]
        });

        vmRunning = {
            affinitygroup: [],
            id: 'vm-001',
            name: '1-Running',
            displayname: 'web-server-01',
            account: 'account',
            domainid: 'id',
            domain: 'example.com',
            state: VmStateEnum.Running,
            zoneid: 'zone-101',
            zonename: 'Zone A',
            hostid: 'host-202',
            isoid: 'iso-404',
            nic: [
                {
                    id: 'nic-1',
                    networkid: 'network-101',
                    networkname: 'VLAN-101',
                    ipaddress: '***********',
                    macaddress: '00:1A:2B:3C:4D:5E',
                    isdefault: true,
                    type: 'public',
                    traffictype: 'guest',
                    netmask: '',
                    gateway: '',
                    isolationuri: '',
                    broadcasturi: '',
                    secondaryip: [],
                    extradhcpoption: []
                }
            ],
            osdisplayname: 'Linux',
            passwordenabled: true,
            created: new Date(2025, 10, 10).toISOString(),
            cpunumber: 2,
            memory: 4096,
            cpuused: '50%',
            memorykbs: 2048000,
            templatename: 'Ubuntu 20.04',
            isdynamicallyscalable: true,
            keypairs: [{ key: 'ssh-key', value: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...' }],
            tags: [{ key: 'environment', value: 'production' }],
            vgpu: ''
        };

        vmStopped = {
            affinitygroup: [],
            id: 'vm-003',
            name: '3-Stopped',
            displayname: 'Web Server 02',
            account: 'account',
            domainid: 'id',
            domain: 'example.com',
            state: VmStateEnum.Stopped,
            zoneid: 'zone-101',
            zonename: 'Zone A',
            hostid: 'host-202',
            isoid: 'iso-404',
            nic: [
                {
                    id: 'nic-1',
                    networkid: 'network-101',
                    networkname: 'VLAN-101',
                    ipaddress: '***********0',
                    macaddress: '00:1A:2B:3C:4D:5E',
                    isdefault: true,
                    type: 'public',
                    traffictype: 'guest',
                    netmask: '',
                    gateway: '',
                    isolationuri: '',
                    broadcasturi: '',
                    secondaryip: [],
                    extradhcpoption: []
                }
            ],
            osdisplayname: 'Linux',
            passwordenabled: true,
            created: new Date(2025, 10, 10).toISOString(),
            cpunumber: 2,
            memory: 4096,
            cpuused: '50%',
            memorykbs: 2048000,
            templatename: 'Ubuntu 20.04',
            isdynamicallyscalable: true,
            keypairs: [{ key: 'ssh-key', value: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...' }],
            tags: [{ key: 'environment', value: 'production' }],
            vgpu: ''
        };

        vmDestroyed = {
            affinitygroup: [],
            id: 'vm-004',
            name: '2-destroyed',
            displayname: 'Web Server 02',
            account: 'account',
            domainid: 'id',
            domain: 'example.com',
            state: VmStateEnum.Destroyed,
            zoneid: 'zone-101',
            zonename: 'Zone A',
            hostid: 'host-202',
            isoid: 'iso-404',
            nic: [
                {
                    id: 'nic-1',
                    networkid: 'network-101',
                    networkname: 'VLAN-101',
                    ipaddress: '***********0',
                    macaddress: '00:1A:2B:3C:4D:5E',
                    isdefault: true,
                    type: 'public',
                    traffictype: 'guest',
                    netmask: '',
                    gateway: '',
                    isolationuri: '',
                    broadcasturi: '',
                    secondaryip: [],
                    extradhcpoption: []
                }],
            osdisplayname: 'Linux',
            passwordenabled: true,
            created: new Date(2025, 10, 10).toISOString(),
            cpunumber: 2,
            memory: 4096,
            cpuused: '50%',
            memorykbs: 2048000,
            templatename: 'Ubuntu 20.04',
            isdynamicallyscalable: true,
            keypairs: [{ key: 'ssh-key', value: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...' }],
            tags: [{ key: 'environment', value: 'production' }],
            vgpu: ''
        };

        vmRunningWithoutIso = {
            affinitygroup: [],
            id: 'vm-001',
            name: '4-running-without-iso',
            displayname: 'web-server-01',
            account: 'account',
            domainid: 'id',
            domain: 'example.com',
            state: VmStateEnum.Running,
            zoneid: 'zone-101',
            zonename: 'Zone A',
            hostid: 'host-202',
            nic: [
                {
                    id: 'nic-1',
                    networkid: 'network-101',
                    networkname: 'VLAN-101',
                    ipaddress: '***********',
                    macaddress: '00:1A:2B:3C:4D:5E',
                    isdefault: true,
                    type: 'public',
                    traffictype: 'guest',
                    netmask: '',
                    gateway: '',
                    isolationuri: '',
                    broadcasturi: '',
                    secondaryip: [],
                    extradhcpoption: []
                }
            ],
            osdisplayname: 'Linux',
            passwordenabled: true,
            created: new Date(2025, 10, 10).toISOString(),
            cpunumber: 2,
            memory: 4096,
            cpuused: '50%',
            memorykbs: 2048000,
            templatename: 'Ubuntu 20.04',
            isdynamicallyscalable: true,
            keypairs: [{ key: 'ssh-key', value: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...' }],
            tags: [{ key: 'environment', value: 'production' }],
            vgpu: ''
        };

        data = [vmRunning, vmDestroyed, vmStopped, vmRunningWithoutIso];

        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.getVirtualMachineList.and.returnValue(of(data));
        mockVmManagementService.getVirtualMachineListByIds.and.returnValue(of(data));

        mockVmManagementService.getConsoleUrl.and.returnValue(of(''));

        mockVmMediaService = TestBed.inject(VmMediaService) as jasmine.SpyObj<VmMediaService>;
        mockVmMediaService.detachIso.and.returnValue(of('job-id'));

        mockZoneService = TestBed.inject(CloudInfraZoneService) as jasmine.SpyObj<CloudInfraZoneService>;
        mockZoneService.getZones.and.returnValue(of([]));

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            organizationId: 1,
            featureFlags: [FeatureFlag.FeatureFlagVirtualMachines],
            cloudInfraUserContext: {
                accountName: 'account',
                domainId: 'domainId',
            } as CloudInfraUserContext
        } as UserContext;

        mockModalService = TestBed.inject(ModalService) as jasmine.SpyObj<ModalService>;

        mockCloudInfrastructureQueueService = TestBed.inject(CloudInfrastructureJobQueueService) as jasmine.SpyObj<CloudInfrastructureJobQueueService>;
        mockCloudInfrastructureQueueService.updates$ = of();
        mockCloudInfrastructureQueueService.idsAndStatusLoading$ = of();

        mockVmManagementPermissionService = TestBed.inject(VmManagementPermissionService) as jasmine.SpyObj<VmManagementPermissionService>;
        mockVmManagementPermissionService.canStopVirtualMachine.and.returnValue(true);
        mockVmManagementPermissionService.canViewVirtualMachineList.and.returnValue(true);

        fixture = TestBed.createComponent(VmListComponent);
    });

    describe('Initialization', () => {

        it('should load VM list with domain only when context includes a mapped domain', () => {
            mockUserContextService.currentUser.cloudInfraUserContext.hasMappedDomain = true;
            fixture.detectChanges();
            expect(mockVmManagementService.getVirtualMachineList).toHaveBeenCalledOnceWith('domainId', null);
        });

        it('should load VM list with domain and account when context does not include a mapped domain', () => {
            mockUserContextService.currentUser.cloudInfraUserContext.hasMappedDomain = false;
            fixture.detectChanges();
            expect(mockVmManagementService.getVirtualMachineList).toHaveBeenCalledOnceWith('domainId', 'account');
        });

        it('should have the same amount of rows as data', () => {
            fixture.detectChanges();
            const dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
            const dataTable = dataTableDebugElement.nativeElement;
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toEqual(data.length);
        });
    });

    describe('Actions', () => {

        describe('Stop VM', () => {

            it('should disable stopping the virtual machine on a non running VM', () => {
                mockVmManagementPermissionService.canStopVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[2].query(By.css('.fa-solid.fa-circle-xmark'))).toBeNull();
            });

            it('should enable stopping the virtual machine on running VMs', () => {
                const modalRef: NgbModalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(StopVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canStopVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                (rows[0].query(By.css('.fa-solid.fa-circle-stop')).nativeElement as HTMLElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as StopVmComponent).virtualMachineId).toBe(data[0].id);
            });

            it('should disable stopping the virtual machine when the user does not have permission to call the endpoint', () => {
                mockVmManagementPermissionService.canStopVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[0].query(By.css('.fa-solid.fa-circle-stop'))).toBeNull();
            });

        });

        describe('Destroy VM', () => {

            it('should disable destroying the virtual machine on destroyed VMs', () => {
                mockVmManagementPermissionService.canDestroyVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[1].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.destroy-vm'))).toBeNull();
            });

            it('should enable destroying the virtual machine on stopped VMs', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(DestroyVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canDestroyVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.destroy-vm')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as DestroyVmComponent).inputData().virtualMachineId).toBe(data[2].id);
            });

            it('should disable destroying the virtual machine when the user does not have permission to call the endpoint', () => {
                mockVmManagementPermissionService.canDestroyVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.destroy-vm'))).toBeNull();
            });

        });

        describe('Reboot VM', () => {

            it('should disable rebooting the virtual machine on a non running VM ', () => {
                mockVmManagementPermissionService.canRebootVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[1].query(By.css('.fa-solid.fa-arrow-rotate-left'))).toBeNull();
            });

            it('should enable rebooting the virtual machine on running VMs ', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(RebootVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canRebootVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                (rows[0].query(By.css('.fa-solid.fa-arrow-rotate-left')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as RebootVmComponent).virtualMachineId).toBe(data[0].id);
            });

            it('should disable rebooting the virtual machine when the user does not have permission to call the endpoint', () => {
                mockVmManagementPermissionService.canRebootVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[1].query(By.css('.fa-solid.fa-arrow-rotate-left'))).toBeNull();
            });

        });

        describe('Reset SSH Key Pair', () => {

            it('should enable the action when the user has permissions and the VM is stopped', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ResetSSHKeyPairComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canResetSSHKeyPairForVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.reset-ssh-pair')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as ResetSSHKeyPairComponent).inputData().virtualMachineId).toBe(data[2].id);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canResetSSHKeyPairForVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reset-ssh-pair'))).toBeNull();
                fixture.detectChanges();
            });

            it('should disable the action when the VM is not stopped', () => {
                mockVmManagementPermissionService.canResetSSHKeyPairForVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[1].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reset-ssh-pair'))).toBeNull();
            });

        });

        describe('Snapshot VM', () => {

            it('should enable the action when the user has permissions', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(SnapshotVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canSnapshotVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.snapshot-vm')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as SnapshotVmComponent).virtualMachineId()).toBe(data[0].id);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canSnapshotVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.snapshot-vm'))).toBeNull();
            });

        });

        describe('Snapshot Volume', () => {

            it('should enable the action when the user has permissions', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(SnapshotVolumeComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canSnapshotVolume.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.snapshot-volume')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as SnapshotVolumeComponent).inputData().virtualMachineId).toBe(data[0].id);
                expect((modalRef.componentInstance as SnapshotVolumeComponent).inputData().account).toBe('account');
                expect((modalRef.componentInstance as SnapshotVolumeComponent).inputData().domainId).toBe('id');
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canSnapshotVolume.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.snapshot-volume'))).toBeNull();
            });

        });

        describe('Migrate VM host', () => {

            it('should enable the action when the user has permissions', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(MigrateHostComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canMigrateVirtualMachineHost.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.migrate-host')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as MigrateHostComponent).virtualMachineId).toBe(data[0].id);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canMigrateVirtualMachineHost.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.migrate-host'))).toBeNull();
            });

        });

        describe('Expunge VM', () => {

            it('should enable the action when the user has permissions and the VM is in destroyed state', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;

                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canExpungeVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                (rows[1].query(By.css('.expunge-vm')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Please confirm that you want to expunge this VM', title: 'Expunge VM', showCancelButton: true });
            });

            it('should disable the action when the user has permissions but the VM is not destroyed', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;

                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canExpungeVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.expunge-vm'))).toBeNull();
            });

            it('should disable the action when the user does not have permission', () => {
                mockVmManagementPermissionService.canExpungeVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[1].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.expunge-vm'))).toBeNull();
            });

        });

        describe('Recover VM', () => {

            it('should enable the action when the user has permissions and the VM is in destroyed state', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;

                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canRecoverVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[1].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.recover-vm')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledOnceWith({ content: 'Please confirm that you want to recover this VM', title: 'Recover VM', showCancelButton: true });
            });

            it('should disable the action when the user has permissions but the VM is not destroyed', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;

                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canRecoverVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.recover-vm'))).toBeNull();
            });

            it('should disable the action when the user does not have permission', () => {
                mockVmManagementPermissionService.canRecoverVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[1].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.recover-vm'))).toBeNull();
            });

        });

        describe('Attach ISO', () => {

            it('should enable the action when the user has permissions and the VM is in destroyed state', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(AttachIsoComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canAttachIso.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                (rows[3].query(By.css('.fa-solid.fa-compact-disc')).nativeElement as HTMLElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as AttachIsoComponent).inputData().virtualMachineId).toBe(data[0].id);
                expect((modalRef.componentInstance as AttachIsoComponent).inputData().virtualMachineZoneId).toBe(data[0].zoneid);
            });

            it('should disable the action when the user does not have permission', () => {
                mockVmManagementPermissionService.canAttachIso.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[3].query(By.css('.fa-solid.fa-compact-disc'))).toBeNull();
            });

            it('should disable the action when the user has permission but the VM is not running', () => {
                mockVmManagementPermissionService.canAttachIso.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[3].query(By.css('.fa-solid.fa-compact-disc'))).toBeNull();
            });

        });

        describe('Eject ISO', () => {

            it('should enable the action when the user has permissions and the VM is in running state and it has an ISO attached', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;

                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canEjectIso.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                (rows[0].query(By.css('.fa-solid.fa-eject')).nativeElement as HTMLElement).click();
                fixture.detectChanges();
                expect(mockModalService.openConfirmationDialog).toHaveBeenCalledTimes(1);
            });

            it('should disable the action when the user has permissions and the VM is in running state and it has no ISO attached', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;

                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canEjectIso.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[3].query(By.css('.fa-solid.fa-eject'))).toBeNull();
            });

            it('should disable the action when the user does not have permissions', () => {
                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ConfirmationDialogComponent)
                } as NgbModalRef;

                mockModalService.openConfirmationDialog.and.returnValue(modalRef);

                mockVmManagementPermissionService.canEjectIso.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                expect(rows[0].query(By.css('.fa-solid.fa-eject'))).toBeNull();
            });

        });

        describe('View Console', () => {

            it('should enable the action when the user has permissions and the VM is in destroyed state', () => {
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                (rows[0].query(By.css('.remote-control')).nativeElement as HTMLElement).click();
                fixture.detectChanges();
                expect(mockVmManagementService.getConsoleUrl).toHaveBeenCalledOnceWith(data[0].id);
            });

        });

        describe('Reinstall VM', () => {

            it('should enable the action when the user has permissions and the VM is stopped', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ReinstallVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.reinstall-vm')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as ReinstallVmComponent).inputData().virtualMachineId).toBe(data[2].id);
            });

            it('should enable the action when the user has permissions and the VM is running', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ReinstallVmComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.reinstall-vm')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as ReinstallVmComponent).inputData().virtualMachineId).toBe(data[0].id);
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reinstall-vm'))).toBeNull();
            });

            it('should disable the action when the VM is destroyed', () => {
                mockVmManagementPermissionService.canReinstallVirtualMachine.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[1].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reinstall-vm'))).toBeNull();
            });

        });

        describe('Reset Password', () => {

            it('should enable the action when the user has permissions and the VM is stopped', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ResetPasswordComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canResetVirtualMachinePassword.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                (actions.query(By.css('.reset-password')).nativeElement as HTMLButtonElement).click();
                fixture.detectChanges();
                expect(mockModalService.openModalComponent).toHaveBeenCalledTimes(1);
                expect((modalRef.componentInstance as ResetPasswordComponent).virtualMachineId).toBe(data[2].id);
            });

            it('should enable the action when the VM is not password enabled', () => {
                vmStopped.passwordenabled = false;
                mockVmManagementPermissionService.canResetVirtualMachinePassword.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reset-password'))).toBeNull();
            });

            it('should disable the action when the user has permissions and the VM is running', () => {

                const modalRef = {
                    closed: of(true),
                    componentInstance: TestBed.inject(ResetPasswordComponent)
                } as NgbModalRef;

                mockModalService.openModalComponent.and.returnValue(modalRef);

                mockVmManagementPermissionService.canResetVirtualMachinePassword.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[0].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reset-password'))).toBeNull();
            });

            it('should disable the action when the user does not have permissions', () => {
                mockVmManagementPermissionService.canResetVirtualMachinePassword.and.returnValue(false);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[2].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reset-password'))).toBeNull();
            });

            it('should disable the action when the VM is destroyed', () => {
                mockVmManagementPermissionService.canResetVirtualMachinePassword.and.returnValue(true);
                fixture.detectChanges();
                const dataTableDebugElement: DebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
                const rows: DebugElement[] = dataTableDebugElement.queryAll(By.css('.datatable-row-wrapper'));
                const actions = rows[1].query(By.directive(NgbDropdownMenu));
                expect(actions.query(By.css('.reset-password'))).toBeNull();
            });

        });

    });

});
