<div class="content-sub-heading">
    <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class="table bootstrap no-detail-row" />
    </div>
</div>

<ng-template #prefixesSentTemplate let-row="row">
    @if (toItem(row); as row) {
        <span>{{ row.prefixesSent }} ({{row.prefixesSentSimulated}} Simulated)</span>
    }
</ng-template>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
        <app-table-action (clickHandler)="viewDetails(row)" [icon]="'icon-viewDetails'" [title]="'View Details'" />
    }
</ng-template>
