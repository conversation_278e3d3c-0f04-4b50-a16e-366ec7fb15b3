<div class="modal-header">
    <h4 class="modal-title">Upload MSI</h4>
</div>
<div class="modal-body">
    <form class="form-horizontal" [formGroup]="form" novalidate>
        <div class="mb-3 row">
            <label class="col-3 col-form-label">File<span class="required-asterisk">*</span></label>
            <div class="col-9">
                <label role="button" class="btn btn-primary">Choose
                    file
                    <input type="file" accept=".msi" multiple="false" class="d-none" (change)="selectFile($event)">
                </label>
                <span class="ms-2">{{ fileSelected()?.name }}</span>
            </div>
        </div>
        <div class="mb-3 row">
            <label class="col-3 col-form-label">Major Version<span class="required-asterisk">*</span></label>
            <div class="col-9">
                <input class="form-control" formControlName="majorVersion" placeholder="Major Version" type="number"
                    step="1" min="1" />
            </div>
        </div>
        <div class="mb-3 row">
            <label class="col-3 col-form-label">Minor Version<span class="required-asterisk">*</span></label>
            <div class="col-9">
                <input class="form-control" formControlName="minorVersion" placeholder="Minor Version" type="number"
                    step="1" min="1" />
            </div>
        </div>
        <div class="mb-3 row">
            <label class="col-3 col-form-label">Build<span class="required-asterisk">*</span></label>
            <div class="col-9">
                <input class="form-control" formControlName="build" placeholder="Build" type="number" step="1"
                    min="1" />
            </div>
        </div>
        <div class="mb-3 row">
            <div class="col-3">
                <label class="col-3 col-form-label">Service<span class="required-asterisk">*</span></label>
            </div>
            <div class="col-9">
                <ng-select placeholder="Agent or Watchdog" formControlName="serviceType" [clearable]="false"
                    [searchable]="false">
                    <ng-option [value]="serviceType.Agent">Agent</ng-option>
                    <ng-option [value]="serviceType.Watchdog">Watchdog</ng-option>
                </ng-select>
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    @if (isUploading()) {
    {{ uploadProgressMessage() }}
    }
    <button class="btn btn-outline-secondary ms-2" (click)="activeModal.close(true)">Cancel</button>
    <button class="btn btn-primary ms-2" [disabled]="!form?.valid || !fileSelected() || isUploading()"
        (click)="upload()">Upload</button>
</div>