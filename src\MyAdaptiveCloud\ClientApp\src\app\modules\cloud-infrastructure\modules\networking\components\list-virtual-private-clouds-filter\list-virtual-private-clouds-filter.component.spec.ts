import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { VirtualPrivateCloudListFilters } from '../../requests/virtual-private-cloud-list.filter';
import { ListVirtualPrivateCloudsFilterComponent } from './list-virtual-private-clouds-filter.component';

describe('ListVirtualPrivateCloudsFilterComponent', () => {
    let component: ListVirtualPrivateCloudsFilterComponent;
    let fixture: ComponentFixture<ListVirtualPrivateCloudsFilterComponent>;

    const mockZones = [{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }];

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ListVirtualPrivateCloudsFilterComponent],
        })
            .compileComponents();

        fixture = TestBed.createComponent(ListVirtualPrivateCloudsFilterComponent);
        component = fixture.componentInstance;
        fixture.componentRef.setInput('filters', new VirtualPrivateCloudListFilters());
        fixture.componentRef.setInput('zones', mockZones);

        fixture.detectChanges();
    });

    it('should set the Zone filter options correctly in the view', () => {
        const groupElement = fixture.debugElement.query(By.css('#zone-group'));
        const inputs = groupElement.queryAll(By.css('.form-check-input'));
        const labels = groupElement.queryAll(By.css('.form-check-label'));
        const groupSectionTitle = groupElement.query(By.css('h6')).nativeElement.innerText;
        const zoneGroup = component.filterGroupOptions.find(group => group.key === 'zoneId');

        expect(inputs.length).toEqual(mockZones.length);
        expect(groupSectionTitle).toEqual(zoneGroup.groupName);

        inputs.forEach((input, index) => {
            const radioInput = (input.nativeElement as HTMLInputElement).id;
            expect(radioInput).toEqual(mockZones[index].id);
        });

        labels.forEach((label, index) => {
            const labelText = (label.nativeElement as HTMLLabelElement).innerText;
            expect(labelText).toEqual(mockZones[index].name);
        });
    });

});
