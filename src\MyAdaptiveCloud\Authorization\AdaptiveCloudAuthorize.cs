using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class AdaptiveCloudAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;

        public AdaptiveCloudAuthorizeFilter(IUserContextService userContextService,
            IIdentityService identityService,
            IEntityAuthorizationService entityAuthorizationService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public override async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // Fetch acId
            string acIdStr = AuthorizeFilterHelpers.GetEntityValue(context, _name);

            int? adaptiveCloudOrganizationId;
            if (!string.IsNullOrEmpty(acIdStr))
            {
                adaptiveCloudOrganizationId = await _entityAuthorizationService.GetAdaptiveCloudOrganizationId(acIdStr);
            }
            else
            {
                adaptiveCloudOrganizationId = Constants.RootOrganizationId;
            }

            if (adaptiveCloudOrganizationId.HasValue)
            {
                if (!_userContextService.HasPermission(userId, adaptiveCloudOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, adaptiveCloudOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
                return;
            }

            return;
        }
    }

    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
    public class AdaptiveCloudAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public AdaptiveCloudAuthorizeAttribute(params Perms[] perms) : base(typeof(AdaptiveCloudAuthorizeFilter), perms)
        {
            Name = "acId";
        }
    }
}