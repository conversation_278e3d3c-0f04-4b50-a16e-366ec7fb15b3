###############################################################################
# Spec file for myadaptivecloud, myadaptivecloudlogs databases
################################################################################
#
%define name myadaptivecloud
%define username myac
%define usergroup %{username}
%define homedir /home/<USER>
%define installdir %{homedir}/%{name}
%define publishdir src/MyAdaptiveCloud.Data/sql

Summary: MyAdaptiveCloud portal
Name: %{name}-database
Version: 1.0
Release: %{_release}
License: IP Pathways Commercial
URL: http://www.ippathways.com
Group: System
Packager: <PERSON>
Requires(pre): shadow-utils
Requires: bash
Requires: mariadb-server >= 10.5
Buildroot: %{_top}/rpmbuild/
#BuildRoot: $WORKSPACE/rpmbuild/

# Build with the following syntax:
# rpmbuild --target noarch -bb whitelabel.spec

%description
MyAdaptiveCloud and MyAdaptiveCloudLogs Liquibase Database scripts

%prep
################################################################################
# Create the build tree and copy the files from the development directories    #
# into the build tree.                                                         #
################################################################################
echo "BUILDROOT = $RPM_BUILD_ROOT"
echo "BUILDROOT = %{RPM_BUILD_ROOT}"
echo "TOP = %{_top}"
echo "RPMDIR = %{_rpmdir}"
#rm -rf %{_rpmdir}/
#mkdir -p %{_rpmdir}
mkdir -p $RPM_BUILD_ROOT%{installdir}/
mkdir -p $RPM_BUILD_ROOT/RPMS/noarch $RPM_BUILD_ROOT/SOURCES $RPM_BUILD_ROOT/SPECS $RPM_BUILD_ROOT/SRPMS

# Copy the publish output into the rpm build env
cp -r $WORKSPACE/%{publishdir}/* $RPM_BUILD_ROOT%{installdir}/
exit

%files
%defattr(-, %{username}, %{usergroup})
#%attr(0744, root, root) %{installdir}/hosts/*
%{installdir}
%config %{installdir}/myadaptivecloud/liquibase.properties
%config %{installdir}/myadaptivecloudlogs/liquibase.properties

# Run during installation, prior to installing the files
%pre
# Create the user and group, if necessary
# Since we're creating a system user, it won't create a home directory or copy /etc/skel
# However, we need the home directory, but not /etc/skel, so we can go ahead and create it and ensure proper permissions during this
mkdir -p %{homedir}
getent group %{usergroup} >/dev/null || groupadd -r %{usergroup}
getent passwd %{username} >/dev/null || \
    useradd -r -g %{usergroup} -d %{homedir} -s /sbin/nologin \
    -c "MyAdaptiveCloud user for running the service" %{username}
chown -R %{username}:%{usergroup} %{homedir}

# Run after installation of files
%post
cp -p %{homedir}/liquibase.local.properties %{installdir}/myadaptivecloud/liquibase.local.properties
cp -p %{homedir}/liquibase.local.properties %{installdir}/myadaptivecloudlogs/liquibase.local.properties
cd %{installdir}/myadaptivecloud; liquibase update-sql --contexts=main

# Cleanup after rpm build process
%clean
rm -rf $RPM_BUILD_ROOT

# List changes
%changelog
