import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { of } from 'rxjs';
import { VmManagementService } from '../../services/vm-management.service';
import { ResetPasswordComponent } from './reset-password.component';

describe('ResetPasswordComponent', () => {

    let component: ResetPasswordComponent;
    let fixture: ComponentFixture<ResetPasswordComponent>;
    let mockVmManagementService: jasmine.SpyObj<VmManagementService>;
    let activeModal: jasmine.SpyObj<NgbActiveModal>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ResetPasswordComponent],
            providers: [
                provideMock(NgbActiveModal),
                provideMock(VmManagementService),
                FormBuilder
            ]
        })
            .compileComponents();

        fixture = TestBed.createComponent(ResetPasswordComponent);
        component = fixture.componentInstance;
        mockVmManagementService = TestBed.inject(VmManagementService) as jasmine.SpyObj<VmManagementService>;
        mockVmManagementService.resetVirtualMachinePassword.and.returnValue(of('jobId1'));

        activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;

        component.virtualMachineId = 'test-id';
    });

    describe('Submit', () => {

        it('should close modal on cancel', () => {

            fixture.detectChanges();
            const cancelButton = fixture.debugElement.query(By.css('.btn.btn-outline-secondary')).nativeElement as HTMLButtonElement;
            cancelButton.click();
            fixture.detectChanges();

            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

        it('should submit the form', () => {

            fixture.detectChanges();

            const submit = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement as HTMLButtonElement;
            submit.click();
            fixture.detectChanges();

            expect(mockVmManagementService.resetVirtualMachinePassword).toHaveBeenCalledOnceWith('test-id');
            expect(activeModal.close).toHaveBeenCalledTimes(1);
        });

    });

});

