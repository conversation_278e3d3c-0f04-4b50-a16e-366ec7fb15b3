import { Routes } from '@angular/router';
import { AllowIfMenuItemIsPresentGuard } from '@app/shared/guards/allow-if-menu-item-is-present.guard';
import { AdaptiveCloudModuleGuard } from './guards/adaptivecloud-module.guard';
import { CloudInfrastructureProvisioningGuard } from './guards/cloud-infrastructure-provisioning.guard';

export const routes: Routes = [
    {
        path: 'AcToCwMappingList',
        loadComponent: () => import('./components/ac-cw-mapping-list/ac-cw-mapping-list.component').then(m => m.AcToCwMappingListComponent),
        canActivate: [AllowIfMenuItemIsPresentGuard]
    },
    {
        path: 'AcCwProductList',
        loadComponent: () => import('./components/ac-cw-product-list/ac-cw-product-list.component').then(m => m.AcCwProductListComponent),
        canActivate: [AllowIfMenuItemIsPresentGuard]
    },
    {
        path: 'AcCwVmList',
        loadComponent: () => import('./components/ac-cw-vm-list/ac-cw-vm-list.component').then(m => m.AcCwVmListComponent),
        canActivate: [AllowIfMenuItemIsPresentGuard]
    },
    {
        path: 'AcUsageRunLogs',
        loadComponent: () => import('./components/adaptivecloud-usage-logs/adaptivecloud-usage-logs.component').then(m => m.AdaptiveCloudUsageLogsComponent),
        canActivate: [AllowIfMenuItemIsPresentGuard]
    },
    {
        path: 'AcUsageDetails/:id',
        loadComponent: () => import('./components/acusage-runlog-details/acusage-runlog-details.component').then(m => m.AcusageRunlogDetailsComponent),
        canActivate: [AdaptiveCloudModuleGuard]
    },
    {
        path: 'usage/adaptivecloud',
        loadComponent: () => import('./components/adaptivecloud-usage/adaptivecloud-usage.component').then(m => m.AdaptiveCloudUsageComponent),
        canActivate: [AllowIfMenuItemIsPresentGuard]
    },
    {
        path: 'usage/adaptivecloudoverall',
        loadComponent: () => import('./components/adaptivecloud-usage-overall/adaptivecloud-usage-overall.component').then(m => m.AdaptiveCloudUsageOverallComponent),
        canActivate: [AllowIfMenuItemIsPresentGuard]
    },
    {
        path: 'usage/network',
        loadComponent: () => import('./components/ac-cw-mapping-list/ac-cw-mapping-list.component').then(m => m.AcToCwMappingListComponent),
        canActivate: [AllowIfMenuItemIsPresentGuard]
    },
    {
        path: 'manage',
        loadChildren: () => import('../provisioning/provisioning.routes').then(m => m.routes),
        canActivate: [CloudInfrastructureProvisioningGuard]
    },
];
