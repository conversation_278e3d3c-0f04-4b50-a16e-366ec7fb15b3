using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class InvoiceAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public InvoiceAuthorizeFilter(IUserContextService userContextService, IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _entityAuthorizationService = entityAuthorizationService;
            _identityService = identityService;
        }

        public override async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int invoiceId);

            int? organizationId = await _entityAuthorizationService.GetInvoiceOrganizationId(invoiceId);
            if (organizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, (int)organizationId, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, (int)organizationId);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class InvoiceAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public InvoiceAuthorizeAttribute(params Perms[] perms) : base(typeof(InvoiceAuthorizeFilter), perms)
        {
            Name = "invoiceId";
        }
    }
}