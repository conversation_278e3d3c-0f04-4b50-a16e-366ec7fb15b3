import { ChangeDetectionStrategy, Component, DestroyRef, OnInit, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LoadingService } from '@app/shared/services/loading.service';
import { interval } from 'rxjs';
import { debounceTime, delayWhen } from 'rxjs/operators';

@Component({
    selector: 'app-loading',
    imports: [],
    templateUrl: './loading.component.html',
    styleUrl: './loading.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingComponent implements OnInit {
    private readonly loadingService = inject(LoadingService);
    private readonly destroyRef = inject(DestroyRef);

    protected readonly show = signal<boolean>(false);

    public ngOnInit(): void {
        this.loadingService.isRequestInProgress$
            .pipe(
                debounceTime(500),
                delayWhen(value => (value ? interval(0) : interval(200))),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(res => {
                this.show.set(res);
            });
    }

}
