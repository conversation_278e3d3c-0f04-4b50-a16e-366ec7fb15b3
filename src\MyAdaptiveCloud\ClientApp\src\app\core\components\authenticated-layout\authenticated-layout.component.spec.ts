import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { AuthenticatedLayoutComponent } from './authenticated-layout.component';

describe('AuthenticatedLayoutComponent', () => {
    let component: AuthenticatedLayoutComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AuthenticatedLayoutComponent
            ],
            providers: [
                provideMock(Router),
                provideMock(UserContextService)
            ]
        });

        component = TestBed.createComponent(AuthenticatedLayoutComponent).componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
