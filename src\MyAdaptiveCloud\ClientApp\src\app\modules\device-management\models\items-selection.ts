import { DeviceFolderTreeNode } from './device-folder-tree-node';
import { DeviceTreeNode } from './device-tree-node';

export class ItemsSelection {
    _devices: DeviceTreeNode[];
    _folders: DeviceFolderTreeNode[];

    get devices() {
        return this._devices;
    }

    get folders() {
        return this._folders;
    }

    constructor(selectedDevices?: DeviceTreeNode[], selectedFolders?: DeviceFolderTreeNode[]) {
        this._devices = selectedDevices ?? [];
        this._folders = selectedFolders ?? [];
    }

    removeFolder(folder: DeviceFolderTreeNode) {
        if (folder.isOrganizationFolder()) {
            this._folders = this.folders.filter(f => f.getUniqueStringId() !== folder.getUniqueStringId() || !f.isOrganizationFolder());
        } else {
            this._folders = this.folders.filter(f => f.getUniqueStringId() !== folder.getUniqueStringId() || f.isOrganizationFolder());
        }
    }

    removeDevice(device: DeviceTreeNode) {
        this._devices = this.devices.filter(d => d.agentId !== device.agentId);
    }

    getOrganization(organizationFolderId: number) {
        return this.folders.find(f => f.isOrganizationFolder() && f.getId() === organizationFolderId);
    }

    getFolder(folderId: number) {
        return this.folders.find(f => !f.isOrganizationFolder() && f.getId() === folderId);
    }

    getDevice(deviceId: number) {
        return this.devices.find(d => d.agentId === deviceId);
    }

    /**
     * Filters devices and folders avoiding duplicates
     */
    calculateFoldersAndDevicesToAdd(foldersAndDevicesToAdd: ItemsSelection): (DeviceFolderTreeNode | DeviceTreeNode)[] {
        const devices = this.calculateDevicesToAdd(foldersAndDevicesToAdd.devices);
        const folders = this.calculateFoldersToAdd(foldersAndDevicesToAdd.folders);
        return [...devices, ...folders];
    }

    /**
     * Adds devices and folders avoiding duplicates
     */
    add(foldersAndDevicesToAdd: ItemsSelection): (DeviceFolderTreeNode | DeviceTreeNode)[] {
        this.addDevices(foldersAndDevicesToAdd.devices);
        this.addFolders(foldersAndDevicesToAdd.folders);
        return [...this.devices, ...this.folders];
    }

    private calculateFoldersToAdd(folders: DeviceFolderTreeNode[]) {
        return folders.filter(folderToAdd => this._folders.every(folder => folder.getUniqueStringId() !== folderToAdd.getUniqueStringId() || folder.isOrganizationFolder() !== folderToAdd.isOrganizationFolder()));
    }

    /**
     * Adds a device if it doesn't already exist
     */
    addDevice(device: DeviceTreeNode) {
        const devicesToAdd = this.calculateDevicesToAdd([device]);
        this._devices.push(...devicesToAdd);
    }

    /**
     * Adds a folder if it doesn't already exist
     */
    addFolder(folder: DeviceFolderTreeNode) {
        const foldersToAdd = this.calculateFoldersToAdd([folder]);
        this._folders.push(...foldersToAdd);
    }

    /**
     * Add folders avoiding duplicates
     */
    addFolders(folders: DeviceFolderTreeNode[]) {
        const foldersToAdd = this.calculateFoldersToAdd(folders);
        this._folders.push(...foldersToAdd);
    }

    private calculateDevicesToAdd(devices: DeviceTreeNode[]) {
        return devices.filter(d => this._devices.every(dev => dev.agentId !== d.agentId));
    }

    /**
     * Add devices avoiding duplicates
     */
    addDevices(devices: DeviceTreeNode[]) {
        const devicesToAdd = this.calculateDevicesToAdd(devices);
        this._devices.push(...devicesToAdd);
    }

    setFoldersAndDevices(devices: DeviceTreeNode[], folders: DeviceFolderTreeNode[]) {
        this.setDevices(devices);
        this.setFolders(folders);
    }

    setDevices(devices: DeviceTreeNode[]) {
        this._devices = devices;
    }

    setFolders(folders: DeviceFolderTreeNode[]) {
        this._folders = folders;
    }

    /**
     * Returns the parent folder of one of the items. This method is useful when is guaranteed that the items are selected from the same parent
     */
    getParentFolder(): DeviceFolderTreeNode {
        return this.getOne().parent();
    }

    hasFolders(): boolean {
        return this.folders.length > 0;
    }

    hasDevices(): boolean {
        return this.devices.length > 0;
    }

    totalCount() {
        return this.devices.length + this.folders.length;
    }

    isEmpty(): boolean {
        return this.totalCount() === 0;
    }

    isMultipleSelection() {
        return this.totalCount() > 1;
    }

    /**
     * Returns any selected item
     */
    getOne(): DeviceFolderTreeNode | DeviceTreeNode {
        return this.devices[0] ?? this.folders[0];
    }

    /**
     * Returns any selected folder or any selected device folder
     */
    getAFolder(): DeviceFolderTreeNode {
        return this.folders[0] ?? this.devices[0].parent();
    }

    deselectAll() {
        this.devices.forEach(d => {
            d.isSelected.set(false);
            d.isSelectedForAction.set(false);
        });
        this.folders.forEach(f => {
            f.isSelected.set(false);
            f.isSelectedForAction.set(false);
        });
    }
}
