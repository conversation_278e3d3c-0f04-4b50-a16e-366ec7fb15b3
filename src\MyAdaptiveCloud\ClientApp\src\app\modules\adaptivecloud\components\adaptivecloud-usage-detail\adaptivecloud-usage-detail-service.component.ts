import { Cur<PERSON>cyPipe, DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, OnInit, TemplateRef, viewChild } from '@angular/core';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { AdaptiveCloudUsageDetailsService } from '../../models/adaptive-cloud-usage-details-service.model';

@Component({
    selector: 'app-adaptivecloud-usage-detail-service',
    imports: [NgxDatatableModule, DecimalPipe, CurrencyPipe],
    templateUrl: './adaptivecloud-usage-detail-service.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdaptiveCloudUsageDetailServiceComponent extends BaseListClientComponent<AdaptiveCloudUsageDetailsService> implements OnInit {

    readonly currencyCellTemplate = viewChild<TemplateRef<never>>('currencyCellTemplate');
    readonly summaryTemplate = viewChild<TemplateRef<never>>('summaryTemplate');
    readonly quantityCellTemplate = viewChild<TemplateRef<never>>('quantityCellTemplate');

    readonly data = input.required<AdaptiveCloudUsageDetailsService[]>();
    readonly descriptionLabel = input.required<string>();

    ngOnInit(): void {

        const columns: TableColumn[] = [
            {
                name: 'Type',
                prop: 'type',
                sortable: false,
                summaryTemplate: this.summaryTemplate(),
                resizeable: true,
                canAutoResize: true
            },
            {
                name: 'Description',
                prop: 'unitDescription',
                summaryTemplate: this.summaryTemplate(),
                sortable: false,
                resizeable: true,
                canAutoResize: true,
                width: 400
            },
            {
                name: 'Quantity',
                prop: 'quantity',
                sortable: false,
                cellTemplate: this.quantityCellTemplate(),
                summaryTemplate: this.summaryTemplate(),
                resizeable: true,
                canAutoResize: true,
                width: 100
            },
            {
                name: 'Price Each',
                prop: 'unitPrice',
                sortable: false,
                cellTemplate: this.currencyCellTemplate(),
                summaryTemplate: this.summaryTemplate(),
                resizeable: true,
                canAutoResize: true,
                width: 100
            },
            {
                name: 'Extended Price',
                prop: 'cost',
                sortable: false,
                cellTemplate: this.currencyCellTemplate(),
                summaryTemplate: this.summaryTemplate(),
                resizeable: true,
                canAutoResize: true,
                width: 120
            }
        ];

        super.initialize(() => of({ data: this.data(), message: '' }), columns);

        const table = this.table();
        table.summaryRow = true;
        table.summaryPosition = 'bottom';
        table.footerHeight = 0;
    }

    public getTotal(): number {
        let total = 0;
        this.data().forEach(row => {
            total += row.cost;
        });
        return total;
    }
}
