import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ActivatedRoute, ActivatedRouteSnapshot, Router, convertToParamMap } from '@angular/router';
import { OrganizationFoldersService } from '@app/modules/device-management/services/organization-folders.service';
import { UNASSIGNED_DEVICES_FOLDER_ID } from '@app/shared/constants/shared-folder-devices-constants';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { Organization } from '@app/shared/models/organization.model';
import { UserContext } from '@app/shared/models/user-context.model';
import { DeviceManagementRoutingService } from '@app/shared/services/device-management-routing.service';
import { ModalService } from '@app/shared/services/modal.service';
import { NotificationService } from '@app/shared/services/notification.service';
import { OrganizationSharedService } from '@app/shared/services/organization-shared.service';
import { PermissionService } from '@app/shared/services/permission.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { provideComponentStore } from '@ngrx/component-store';
import { of } from 'rxjs';
import { Device } from '../../models/device';
import { DeviceFolderTreeNode } from '../../models/device-folder-tree-node';
import { DeviceFolderTreeNodeDto } from '../../models/device-folder-tree-node-dto';
import { OrganizationFolderTreeNode } from '../../models/organization-folder-tree-node';
import { OrganizationFolder } from '../../models/organization-folder.model';
import { UnassignedDevicesFolderTreeNode } from '../../models/unassigned-devices-folder-tree-node';
import { DeviceFolderService } from '../../services/device-folder.service';
import { DevicesService } from '../../services/devices.service';
import { FoldersTreeStore } from '../../store/folders-tree.store';
import { OrganizationThresholdsService } from '../device-thresholds/services/device-thresholds.service';
import { DeviceFolderTreeComponent } from './folder-tree.component';

describe('DeviceFolderTreeComponent', () => {

    let component: DeviceFolderTreeComponent;
    let fixture: ComponentFixture<DeviceFolderTreeComponent>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockDevicesService: jasmine.SpyObj<DevicesService>;
    let mockDeviceFolderService: jasmine.SpyObj<DeviceFolderService>;
    let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;
    let mockOrganizationService: jasmine.SpyObj<OrganizationSharedService>;
    let mockOrganizationFoldersService: jasmine.SpyObj<OrganizationFoldersService>;
    let mockFoldersTreeStore: jasmine.SpyObj<FoldersTreeStore>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                DeviceFolderTreeComponent
            ],
            providers: [
                provideMock(DeviceFolderService),
                provideMock(DevicesService),
                provideMock(OrganizationSharedService),
                provideMock(OrganizationFoldersService),
                provideMock(UserContextService),
                provideMock(NotificationService),
                provideMock(ModalService),
                provideMock(NgbModal),
                provideMock(ActivatedRoute),
                provideMock(OrganizationThresholdsService),
                provideComponentStore(FoldersTreeStore),
                provideMock(PermissionService),
                provideMock(Router),
                provideMock(DeviceManagementRoutingService),
                ChangeDetectorRef
            ]
        }).compileComponents();

        const organization: Organization = {
            organizationId: 1,
            name: 'Root',
            allowSubOrg: true,
            parentOrganizationId: null,
            parentOrganizationName: null,
            allowWhiteLabel: false,
            isPartner: false,
            organizationParentFullPath: 'Root'
        };

        const organizationFolder: OrganizationFolder = {
            organizationId: 1,
            name: 'Root',
            parentOrganizationId: null,
            isPartner: false,
            deviceCount: 0,
            deviceCountCurrentFolder: 0,
            hasSubfolders: true
        };

        const deviceChildrenFolders: DeviceFolderTreeNodeDto[] = [
            {
                folderId: 4,
                name: 'D',
                description: 'D desc',
                deviceCount: 0,
                hasSubfolders: false,
                parentFolderId: 1,
                deviceCountCurrentFolder: 0
            },
            {
                folderId: 5,
                name: 'E',
                description: 'E desc',
                deviceCount: 0,
                hasSubfolders: false,
                parentFolderId: 1,
                deviceCountCurrentFolder: 0
            }
        ];

        const deviceRootFolders: DeviceFolderTreeNodeDto[] = [
            {
                folderId: 1,
                name: 'A',
                description: 'A desc',
                deviceCount: 0,
                hasSubfolders: true,
                parentFolderId: null,
                deviceCountCurrentFolder: 0,
            },
            {
                folderId: 2,
                name: 'B',
                description: 'B desc',
                deviceCount: 0,
                hasSubfolders: false,
                parentFolderId: null,
                deviceCountCurrentFolder: 0,
            },
            {
                folderId: 3,
                name: 'C',
                description: 'C desc',
                deviceCount: 0,
                hasSubfolders: true,
                parentFolderId: null,
                deviceCountCurrentFolder: 0,
            }
        ];

        const childrenOrganizationFolders: OrganizationFolder[] = [
            {
                organizationId: 101,
                name: 'subOrg-101',
                parentOrganizationId: organization.organizationId,
                hasSubfolders: true,
                isPartner: false,
                deviceCount: 0,
                deviceCountCurrentFolder: 0
            } as OrganizationFolder,
            {
                organizationId: 102,
                name: 'subOrg-102',
                parentOrganizationId: organization.organizationId,
                hasSubfolders: false,
                isPartner: false,
                deviceCount: 0,
                deviceCountCurrentFolder: 0
            } as OrganizationFolder
        ];

        const unassignedDevices: Device[] = [];
        Array.from(Array(20)).forEach((v, index) => unassignedDevices.push(({
            agentId: index + 1,
            orgId: organization.organizationId,
            hostname: `device${index + 1}`,
            description: `device ${index + 1}`,
            name: `device ${index + 1}`,
            folderId: UNASSIGNED_DEVICES_FOLDER_ID
        })));
        const unassignedDevicesFolder = new UnassignedDevicesFolderTreeNode(organization.organizationId, unassignedDevices);

        const getOrganizationResult: ApiDataResult<Organization> = {
            data: organization,
            message: 'success'
        };

        const getOrganizationFolderResult: ApiDataResult<OrganizationFolder> = {
            data: organizationFolder,
            message: 'success'
        };

        const getDevicesWithoutFoldersResult: ApiDatasetResult<Device[]> = {
            data: unassignedDevices,
            totalCount: unassignedDevices.length
        };

        const getOrganizationFoldersResult: ApiDataResult<DeviceFolderTreeNodeDto[]> = {
            data: deviceRootFolders,
            message: 'OK'
        };

        const getChildrenOrganizationFoldersResult: ApiDatasetResult<OrganizationFolder[]> = {
            data: childrenOrganizationFolders,
            totalCount: childrenOrganizationFolders.length
        };

        const subFolders = deviceRootFolders.map(f => new DeviceFolderTreeNode(f, 1));
        const suborganizationsOfTreeRoot = childrenOrganizationFolders.map(o => new OrganizationFolderTreeNode(o, [], 1));
        const rootFolder = new OrganizationFolderTreeNode(
            {
                name: organization.name,
                organizationId: organization.organizationId,
                parentOrganizationId: null,
                deviceCount: unassignedDevices.length,
                deviceCountCurrentFolder: 0
            },
            [unassignedDevicesFolder as DeviceFolderTreeNode].concat(suborganizationsOfTreeRoot).concat(subFolders), 0, null, true
        );

        mockActivatedRoute = TestBed.inject(ActivatedRoute) as jasmine.SpyObj<ActivatedRoute>;
        mockActivatedRoute.snapshot = {
            paramMap: convertToParamMap({})
        } as ActivatedRouteSnapshot;

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = {
            userId: 1,
            organizationId: 50
        } as UserContext;

        mockOrganizationService = TestBed.inject(OrganizationSharedService) as jasmine.SpyObj<OrganizationSharedService>;
        mockOrganizationService.getOrganizationById.and.returnValue(of(getOrganizationResult));

        mockOrganizationFoldersService = TestBed.inject(OrganizationFoldersService) as jasmine.SpyObj<OrganizationFoldersService>;
        mockOrganizationFoldersService.getOrganizationFolderById.and.returnValue(of(getOrganizationFolderResult));
        mockOrganizationFoldersService.getChildrenOrganizationFolders.and.returnValue(of(getChildrenOrganizationFoldersResult));

        mockDevicesService = TestBed.inject(DevicesService) as jasmine.SpyObj<DevicesService>;
        mockDevicesService.getDevicesWithoutFolder.and.returnValue(of(getDevicesWithoutFoldersResult));

        mockDeviceFolderService = TestBed.inject(DeviceFolderService) as jasmine.SpyObj<DeviceFolderService>;
        mockDeviceFolderService.getOrganizationFolders.and.returnValue(of(getOrganizationFoldersResult));
        mockDeviceFolderService.getChildrenFolders.and.returnValue(of({ data: deviceChildrenFolders, message: 'OK' }));
        mockDeviceFolderService.getDevicesByFolder.and.returnValue(of());

        mockFoldersTreeStore = TestBed.inject(FoldersTreeStore) as jasmine.SpyObj<FoldersTreeStore>;

        fixture = TestBed.createComponent(DeviceFolderTreeComponent);
        component = fixture.componentInstance;
        fixture.componentRef.setInput('folder', rootFolder);
        mockFoldersTreeStore.setAllowMultiSelect(false);
        mockFoldersTreeStore.setRootFolder(rootFolder);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should show devices count', () => {
        fixture.componentRef.setInput('showCounts', true);
        fixture.detectChanges();
        const count = fixture.debugElement.query(By.css('span[data-testid="device-count"]'));

        expect(count).toBeDefined();
        expect(count.nativeElement.title).toBe('Total devices in Root: 0\nTotal devices in Root and SubOrgs: 20');
    });

    it('should NOT show devices count', () => {
        fixture.componentRef.setInput('showCounts', false);
        fixture.detectChanges();
        const count = fixture.debugElement.query(By.css('span[data-testid="device-count"]'));

        expect(count).toBeNull();
    });

    it('should show discovered devices folder', () => {
        fixture.componentRef.setInput('showDiscoveredDevicesFolders', true);
        fixture.detectChanges();
        const folders = fixture.debugElement.queryAll(By.css('span[data-testid="folder-name"]'));

        expect(folders.map(f => (f.nativeElement as HTMLElement).title)).toContain('Unmonitored New Devices ');
    });

    it('should NOT show discovered devices folder', () => {
        fixture.componentRef.setInput('showDiscoveredDevicesFolders', false);
        fixture.detectChanges();
        const folders = fixture.debugElement.queryAll(By.css('span[data-testid="folder-name"]'));

        expect(folders.map(f => (f.nativeElement as HTMLElement).title)).not.toContain('Unmonitored New Devices ');
    });

    it('should render cdk drag and drop component', () => {
        fixture.componentRef.setInput('allowDragAndDrop', true);
        fixture.detectChanges();
        const dragAndDropComponent = fixture.debugElement.query(By.css('div[data-testid="drag-and-drop"]'));

        expect(dragAndDropComponent).toBeDefined();
    });

    it('should NOT render cdk drag and drop component', () => {
        fixture.componentRef.setInput('allowDragAndDrop', false);
        fixture.detectChanges();
        const dragAndDropComponent = fixture.debugElement.query(By.css('div[data-testid="drag-and-drop"]'));

        expect(dragAndDropComponent).toBeNull();
    });

    it('should get children and should not toggle folder when toggled folder has subfolders', () => {
        const toggleFolderSpy = spyOn(component.foldersTreeStore, 'toggleFolder').and.callThrough();
        const getSubfoldersSpy = spyOn(component.foldersTreeStore, 'getSubfolders$').and.callThrough();
        const toggleFolderAndUpdateStateSpy = spyOn(component.foldersTreeStore, 'toggleFolderAndUpdateState').and.callThrough();

        const arrow = fixture.debugElement.queryAll(By.css('.toggle-arrow'))[0].nativeElement;
        arrow.click();

        fixture.detectChanges();
        expect(toggleFolderSpy).toHaveBeenCalled();
        expect(getSubfoldersSpy).toHaveBeenCalled();
        expect(toggleFolderAndUpdateStateSpy).toHaveBeenCalled();
    });

    it('should toggle folder and should get children when folder has subfolders', () => {
        const toggleFolderSpy = spyOn(component.foldersTreeStore, 'toggleFolder').and.callThrough();
        const getSubfoldersSpy = spyOn(component.foldersTreeStore, 'getSubfolders$').and.callThrough();
        const toggleFolderAndUpdateStateSpy = spyOn(component.foldersTreeStore, 'toggleFolderAndUpdateState').and.callThrough();

        const arrow = fixture.debugElement.queryAll(By.css('.toggle-arrow'))[0].nativeElement;
        arrow.click();

        fixture.detectChanges();
        expect(toggleFolderSpy).toHaveBeenCalled();
        expect(getSubfoldersSpy).toHaveBeenCalled();
        expect(toggleFolderAndUpdateStateSpy).toHaveBeenCalled();
    });

    function getFolderElement() {
        return fixture.debugElement.query(By.css('.fill-content')).nativeElement as HTMLElement;
    }

    it('should apply "org-selected" class after clicking folder (single-select mode)', () => {
        fixture.detectChanges();

        fixture.componentRef.setInput('folder', {
            folderId: () => 4,
            description: () => 'D desc',
            deviceCount: () => 0,
            hasSubfolders: () => false,
            parentFolderId: () => 1,
            deviceCountCurrentFolder: () => 0,
            level: () => 2,
            getUniqueStringId: () => 'F4',
            isUnassignedDevicesFolder: () => false,
            isExpanded: () => true,
            isSelected: () => true,
            isRootOrganization: () => false,
            isOrganizationFolder: () => false,
            isPartnerOrganization: () => false,
            name: () => 'D',
            hasAnyDevice: () => false,
            deviceCountOnlySelf: () => 0,
            isDeviceFolder: () => true,
            getSubFolders: () => [],
        });
        fixture.detectChanges();

        const folderEl = getFolderElement();

        folderEl.click();
        fixture.detectChanges();

        expect(folderEl.classList).toContain('org-selected');
    });

    it('should apply "org-selected" class after clicking folder in multi-select mode', () => {
        fixture.detectChanges();

        mockFoldersTreeStore.setAllowMultiSelect(true);
        fixture.componentRef.setInput('folder', {
            folderId: () => 4,
            description: () => 'D desc',
            deviceCount: () => 0,
            hasSubfolders: () => false,
            parentFolderId: () => 1,
            deviceCountCurrentFolder: () => 0,
            level: () => 2,
            getUniqueStringId: () => 'F4',
            isUnassignedDevicesFolder: () => false,
            isExpanded: () => true,
            isSelected: () => true,
            isRootOrganization: () => false,
            isOrganizationFolder: () => false,
            isPartnerOrganization: () => false,
            name: () => 'D',
            hasAnyDevice: () => false,
            deviceCountOnlySelf: () => 0,
            isDeviceFolder: () => true,
            getSubFolders: () => [],
        });
        fixture.detectChanges();

        const folderEl = getFolderElement();

        folderEl.click();
        fixture.detectChanges();

        expect(folderEl.classList).toContain('org-selected');
    });

    it('should apply "org-selected bg-tree-secondary" when clicking non-root folder with level > 1', () => {

        fixture.componentRef.setInput('folder', {
            folderId: () => 4,
            description: () => 'D desc',
            deviceCount: () => 0,
            hasSubfolders: () => false,
            parentFolderId: () => 1,
            deviceCountCurrentFolder: () => 0,
            level: () => 2,
            getUniqueStringId: () => 'F4',
            isUnassignedDevicesFolder: () => false,
            isExpanded: () => true,
            isSelected: () => true,
            isRootOrganization: () => false,
            isOrganizationFolder: () => false,
            isPartnerOrganization: () => false,
            name: () => 'D',
            hasAnyDevice: () => false,
            deviceCountOnlySelf: () => 0,
            isDeviceFolder: () => true,
            getSubFolders: () => [],
        });

        fixture.detectChanges();

        const folderEl = getFolderElement();

        folderEl.click();
        fixture.detectChanges();

        expect(folderEl.classList).toContain('org-selected');
        expect(folderEl.classList).toContain('bg-tree-secondary');
    });

    it('should apply default "text-secondary" when folder is not selected', () => {
        fixture.componentRef.setInput('folder', {
            folderId: () => -1,
            description: () => 'U desc',
            deviceCount: () => 0,
            hasSubfolders: () => false,
            parentFolderId: () => 1,
            deviceCountCurrentFolder: () => 0,
            level: () => 2,
            getUniqueStringId: () => 'U_O1_-1',
            isUnassignedDevicesFolder: () => true,
            isExpanded: () => true,
            isSelected: () => false,
            isRootOrganization: () => false,
            isOrganizationFolder: () => false,
            name: () => 'U',
            isPartnerOrganization: () => false,
            hasAnyDevice: () => false,
            deviceCountOnlySelf: () => 0,
            isDeviceFolder: () => true,
            getSubFolders: () => [],
        });
        fixture.detectChanges();

        const folderEl = getFolderElement();

        expect(folderEl.classList).toContain('text-secondary');
    });

    it('should apply "org-selected bg-tree-secondary" when clicking unassigned devices folder level > 1', () => {
        fixture.componentRef.setInput('folder', {
            folderId: () => -1,
            description: () => 'U desc',
            deviceCount: () => 0,
            hasSubfolders: () => false,
            parentFolderId: () => 1,
            deviceCountCurrentFolder: () => 0,
            level: () => 2,
            getUniqueStringId: () => 'U_O1_-1',
            isUnassignedDevicesFolder: () => true,
            isExpanded: () => true,
            isSelected: () => true,
            isRootOrganization: () => false,
            isOrganizationFolder: () => false,
            name: () => 'U',
            isPartnerOrganization: () => false,
            hasAnyDevice: () => false,
            deviceCountOnlySelf: () => 0,
            isDeviceFolder: () => true,
            getSubFolders: () => [],
        });

        fixture.detectChanges();

        const folderEl = getFolderElement();

        folderEl.click();
        fixture.detectChanges();

        expect(folderEl.classList).toContain('org-selected');
        expect(folderEl.classList).toContain('bg-tree-secondary');
    });

    it('should apply "org-selected" when clicking unassigned devices folder level 1', () => {
        fixture.componentRef.setInput('folder', {
            folderId: () => -1,
            description: () => 'U desc',
            deviceCount: () => 0,
            hasSubfolders: () => false,
            parentFolderId: () => 1,
            deviceCountCurrentFolder: () => 0,
            level: () => 1,
            getUniqueStringId: () => 'U_O1_-1',
            isUnassignedDevicesFolder: () => true,
            isExpanded: () => true,
            isSelected: () => true,
            isRootOrganization: () => false,
            isOrganizationFolder: () => false,
            name: () => 'U',
            isPartnerOrganization: () => false,
            hasAnyDevice: () => false,
            deviceCountOnlySelf: () => 0,
            isDeviceFolder: () => true,
            getSubFolders: () => [],
        });

        fixture.detectChanges();

        const folderEl = getFolderElement();

        folderEl.click();
        fixture.detectChanges();

        expect(folderEl.classList).toContain('org-selected');
    });
});
