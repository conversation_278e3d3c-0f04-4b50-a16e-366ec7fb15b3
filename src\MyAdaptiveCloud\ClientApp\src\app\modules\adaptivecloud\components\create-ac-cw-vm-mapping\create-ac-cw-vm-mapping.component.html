<div class="modal-header">
  <h4 class="modal-title">Map to ConnectWise License SKU</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
  <form class="form-horizontal" name="createmapping" #createmapping="ngForm" novalidate>
    <div class="mb-3 row">
      <div class="col-xl-12">
        <label class="form-label">ConnectWise Product</label>
        <i class="ms-1 far fa-question-circle"
          [ngbPopover]="'For a ConnectWise product to show in this list, it first needs to be added in the Product Mappings area as a computelicense type product'"
          popoverTitle="Help" triggers="hover" container="body"></i>
        <ng-select placeholder="Select ConnectWise product" [items]="Products$ | async" bindLabel="cwProductName"
          [(ngModel)]="selectedProduct" name="productName" [disabled]="!!mapping?.id" #CWProduct="ngModel"
          [class]="{ 'is-invalid': createmapping.submitted && CWProduct.invalid }" required />
      </div>
    </div>
    <div class="mb-3 row">
      <div class="col-xl-12">
        <label class="form-label">Type of mapping</label>
        <i class="ms-1 far fa-question-circle"
          [ngbPopover]="'Select what type of mapping to create. Virtual Machine is the most specific, and Os Category is the least.'"
          popoverTitle="Help" triggers="hover" container="body"></i>
        <ng-select placeholder="Select type of mapping" autocomplete="false" autocorrect="false" spellcheck="false"
          [items]="MappingTypes" bindLabel="name" [(ngModel)]="selectedType" name="mappingType"
          (change)="onChangeMappingType()" />
      </div>
    </div>
    <div class="mb-3 row">
      @switch (selectedType?.name) {
      @case ('VirtualMachine') {
      <div class="col-xl-12">
        <label class="form-label">Virtual Machine</label>
        <i class="ms-1 far fa-question-circle"
          [ngbPopover]="'This mapping will match ONLY the VM selected here. This should probably be a high priority, such as 1, so that other mappings wont override it.'"
          popoverTitle="Help" triggers="hover" container="body"></i>
        <ng-select placeholder="Select virtual machine" [items]="VMs$ | async" bindLabel="name" groupBy="acctdom"
          [(ngModel)]="selectedTarget" [searchFn]="vmCustomSearchFn" name="vm" />
      </div>
      }
      @case ('OsCategory') {
      <div class="col-xl-12">
        <label class="form-label">OsCategory</label>
        <i class="ms-1 far fa-question-circle"
          [ngbPopover]="'This mapping will match all VMs that have a matching Os Category to the one selected here. This should probably be a lower priority, such as 10, as it may match a broad spectrum of VMs.'"
          popoverTitle="Help" triggers="hover" container="body"></i>
        <ng-select placeholder="Select OsCategory" [items]="OsCats$ | async" bindLabel="name"
          [(ngModel)]="selectedTarget" name="oscat" />
      </div>
      }
      @case ('OsType') {
      <div class="col-xl-12">
        <label class="form-label">OsType</label>
        <i class="ms-1 far fa-question-circle"
          [ngbPopover]="'This mapping will match all VMs that have a matching Os Type to the one selected here. This should be a medium priority, such as 5, as it is more specific than Os Category, but less specific than Virtual Machine'"
          popoverTitle="Help" triggers="hover" container="body"></i>
        <ng-select placeholder="Select OsType" [items]="OsTypes$ | async" bindLabel="description" groupBy="OsCategory"
          [(ngModel)]="selectedTarget" name="ostype" />
      </div>
      }
      @case ('Template') {
      <div class="col-xl-12">
        <label class="form-label">Template</label>
        <ng-select placeholder="Select Template" [items]="VMs$ | async" bindLabel="name" groupBy="account"
          [(ngModel)]="selectedTarget" name="vm" />
      </div>
      }
      }
    </div>
    @if (selectedType) {
    <div>
      <div class="mb-3 row">
        <div class="col-12">
          <label class="form-label">Priority</label>
          <i class="ms-1 far fa-question-circle"
            [ngbPopover]="'Only one mapping will apply per CW product. This priority will determine which one gets used when a particular VM matches multiple mappings. 1 is highest, 10 is lowest.'"
            popoverTitle="Help" triggers="hover" container="body"></i>
          <ng-select placeholder="Select OsType" [items]="[ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 ]"
            [(ngModel)]="selectedPriority" name="priority" />
        </div>
      </div>
      <div class="mb-3 row">
        <div class="col-12">
          <label class="form-label">Value / Adjuster</label>
          <i class="ms-1 far fa-question-circle"
            [ngbPopover]="'This field can either be a numeric value, or a field or function applied to fields to calculate a value. Adjustments to this field will apply the next billing cycle.'"
            popoverTitle="Help" triggers="hover" container="body"></i>
        </div>
        <div class="col-xl-12 mt-2">
          <label class="form-label">
            <input type="radio" [(ngModel)]="isFormula" name="valueType" [value]="false">
            <span class="mleft-5">Static Value</span>
          </label>
          <label class="form-label ml-10">
            <input type="radio" [(ngModel)]="isFormula" name="valueType" [value]="true">
            <span class="mleft-5">Formula/Field</span>
          </label>
        </div>
      </div>
      @if (!isFormula) {
      <div class="mb-3 row">
        <label class="col-auto col-form-label">Numeric Value</label>
        <div class="col-auto mt-1">
          <input type="text" [(ngModel)]="selectedValue" name="value">
        </div>
      </div>
      } @else {
      <div class="mb-3 row">
        <label class="col-3 col-form-label">Formula/Field</label>
        <div class="col-9 mt-1">
          <ng-select placeholder="Select formula" [items]="[ 'SQL-License', 'Average vCPUs' ]"
            [(ngModel)]="selectedValue" name="value" />
        </div>
      </div>
      }
      <div class="mb-3">
        <div class="input-group">
          <input class="form-control" placeholder="yyyy-mm-dd" [(ngModel)]="startDate"
            [ngModelOptions]="{standalone: true}" ngbDatepicker #sd="ngbDatepicker">
          <div>
            <button class="btn btn-outline-secondary calendar" (click)="sd.toggle()" type="button">
              <i class="fas fa-calendar-alt"></i>
            </button>
          </div>
        </div>
      </div>
      <div class="mb-3">
        <div class="input-group">
          <input class="form-control" placeholder="yyyy-mm-dd" [(ngModel)]="endDate"
            [ngModelOptions]="{standalone: true}" ngbDatepicker #ed="ngbDatepicker">
          <div>
            <button class="btn btn-outline-secondary calendar" (click)="ed.toggle()" type="button">
              <i class="fas fa-calendar-alt"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    }
  </form>
</div>
<div class="modal-footer">
  <button class="btn btn-outline-secondary" (click)="activeModal.dismiss()">Cancel</button>
  <app-btn-submit (submitClickEvent)="submitForm(createmapping)" [disabled]="createmapping.form?.invalid"
    [btnClasses]="'ms-2 btn-primary'">Save</app-btn-submit>
</div>
