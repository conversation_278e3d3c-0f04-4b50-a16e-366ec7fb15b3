﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class NotificationAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IUserContextService _userContextService;
        private readonly IIdentityService _identityService;

        public NotificationAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService, IUserContextService userContextService,
            Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _userContextService = userContextService;
            _identityService = identityService;
            _entityAuthorizationService = entityAuthorizationService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            int.TryParse(val, out int notificationId);

            var notificationOrganizationId = await _entityAuthorizationService.GetNotificationOrganizationId(notificationId);
            if (notificationOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, notificationOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, notificationOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class NotificationAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public NotificationAuthorizeAttribute(params Perms[] perms) : base(typeof(NotificationAuthorizeFilter), perms)
        {
            Name = "notificationId";
        }
    }
}