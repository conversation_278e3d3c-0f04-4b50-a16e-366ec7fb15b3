import { FormControl } from '@angular/forms';

export interface NetworkDetailForm {
  cidr: FormControl<string>;
  networkdomain: FormControl<string>;
  zonename: FormControl<string>;
  domain: FormControl<string>;
  account: FormControl<string>;
  type: FormControl<string>;
  id: FormControl<string>;
  networkofferingname: FormControl<string>;
  restartrequired: FormControl<string>;
  redundantrouter: FormControl<string>;
}
