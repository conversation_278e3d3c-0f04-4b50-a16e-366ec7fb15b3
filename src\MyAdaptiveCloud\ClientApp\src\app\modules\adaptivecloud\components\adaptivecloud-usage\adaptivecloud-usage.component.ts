import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, inject, OnInit, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BackComponent } from '@app/shared/components/back/back.component';
import { AdaptiveCloudUsageSummaryAccount } from '@app/shared/models/adaptivecloud/adaptivecloud-usage-summary-account.mode';
import { ElementSelector } from '@app/shared/models/element-selector.model';
import { ReportType } from '@app/shared/models/report-type.enum';
import { PeriodPipe } from '@app/shared/pipes/period.pipe';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { distinctUntilChanged, filter, map, Observable, tap } from 'rxjs';
import { UsagePeriodForm } from '../../forms/usage-periods.form';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AdaptiveCloudUsageDetailComponent } from '../adaptivecloud-usage-detail/adaptivecloud-usage-detail.component';
import { AdaptiveCloudUsageReportModalComponent } from '../adaptivecloud-usage-report-modal/adaptivecloud-usage-report-modal.component';
import { AdaptiveCloudUsageSummaryComponent } from '../adaptivecloud-usage-summary/adaptivecloud-usage-summary.component';

@Component({
    selector: 'app-adaptivecloud-usage',
    imports: [ReactiveFormsModule, BackComponent, AdaptiveCloudUsageSummaryComponent, AdaptiveCloudUsageDetailComponent, NgSelectModule, PeriodPipe, AsyncPipe],
    templateUrl: './adaptivecloud-usage.component.html',
    styleUrl: './adaptivecloud-usage.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdaptiveCloudUsageComponent implements OnInit {
    private readonly usageService = inject(AdaptiveCloudUsageService);
    private readonly userContextService = inject(UserContextService);
    private readonly modalService = inject(ModalService);
    private readonly formBuilder = inject(FormBuilder);
    private readonly changeDetectorRef = inject(ChangeDetectorRef);
    private readonly destroyRef = inject(DestroyRef);

    readonly summary = viewChild(AdaptiveCloudUsageSummaryComponent);
    readonly details = viewChild(AdaptiveCloudUsageDetailComponent);

    protected readonly showDetailView = signal<boolean>(false);
    protected readonly showViews = signal<boolean>(false);
    protected readonly accounts = signal<ElementSelector[]>([]);
    protected periods$: Observable<string[]>;
    protected readonly selectedAccountId = signal<string>(null);
    protected readonly selectedAccountName = signal<string>(null);
    protected form: FormGroup<UsagePeriodForm>;

    public ngOnInit(): void {
        this.form = this.formBuilder.group<UsagePeriodForm>({
            period: this.formBuilder.control<string>('', Validators.required)
        });

        this.form.controls.period.valueChanges.pipe(
            filter(period => !!period),
            distinctUntilChanged(),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(() => {
            // Force change detection to regenerate children components with the new period
            if (this.showViews()) {
                this.showViews.set(false);
                this.changeDetectorRef.detectChanges();
            }
            this.showViews.set(true);
        });

        this.periods$ = this.usageService.getUsagePeriods(this.userContextService.currentUser.organizationId)
            .pipe(
                map(res => res.data),
                tap(res => {
                    this.form.controls.period.setValue(res[0]);
                })
            );
    }

    protected onShowDetails(account: AdaptiveCloudUsageSummaryAccount): void {
        this.selectedAccountId.set(account.accountId);
        this.selectedAccountName.set(account.accountName);
        this.showDetailView.update(s => !s);
    }

    protected onShowSummary(): void {
        this.selectedAccountId.set(null);
        this.selectedAccountName.set(null);
        this.showDetailView.update(s => !s);
    }

    protected onSummaryDataLoaded(accounts: ElementSelector[]) {
        this.accounts.set(accounts);
    }

    protected openGenerateReportDialog(): void {
        const modalRef = this.modalService.openModalComponent(AdaptiveCloudUsageReportModalComponent);
        (modalRef.componentInstance as AdaptiveCloudUsageReportModalComponent).period = this.form.controls.period.value;
        (modalRef.componentInstance as AdaptiveCloudUsageReportModalComponent).accounts = this.accounts();
        (modalRef.componentInstance as AdaptiveCloudUsageReportModalComponent).selectedAccounts = this.selectedAccountId() ?
            [{ id: this.selectedAccountId(), name: this.selectedAccountName() }] : this.accounts();
        (modalRef.componentInstance as AdaptiveCloudUsageReportModalComponent).reportType = !this.showDetailView() ? ReportType.Summary : ReportType.Detail;
    }

}
