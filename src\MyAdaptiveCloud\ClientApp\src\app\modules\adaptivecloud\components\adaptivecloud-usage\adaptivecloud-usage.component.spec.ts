import { ComponentFixture, discardPeriodicTasks, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { AdaptiveCloudUsageSummary } from '@app/shared/models/adaptivecloud/adaptivecloud-usage-summary.model';
import { UserContext } from '@app/shared/models/user-context.model';
import { AdaptiveCloudUsageSharedService } from '@app/shared/services/adaptivecloud-usage-shared.service';
import { ModalService } from '@app/shared/services/modal.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { selectOption } from '@app/shared/test-helper/testng-select';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { AdaptiveCloudUsageDetails } from '../../models/adaptive-cloud-usage-details.model';
import { AdaptiveCloudUsageService } from '../../services/adaptivecloud-usage.service';
import { AdaptiveCloudUsageSummaryComponent } from '../adaptivecloud-usage-summary/adaptivecloud-usage-summary.component';
import { AdaptiveCloudUsageComponent } from './adaptivecloud-usage.component';

describe('AdaptiveCloudUsageComponent', () => {
    let fixture: ComponentFixture<AdaptiveCloudUsageComponent>;
    let mockUsageService: jasmine.SpyObj<AdaptiveCloudUsageService>;
    let mockUsageSharedService: jasmine.SpyObj<AdaptiveCloudUsageSharedService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                AdaptiveCloudUsageComponent
            ],
            providers: [
                provideMock(UserContextService),
                provideMock(AdaptiveCloudUsageService),
                provideMock(AdaptiveCloudUsageSharedService),
                provideMock(ModalService)
            ]
        });

        mockUsageService = TestBed.inject(AdaptiveCloudUsageService) as jasmine.SpyObj<AdaptiveCloudUsageService>;
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUsageSharedService = TestBed.inject(AdaptiveCloudUsageSharedService) as jasmine.SpyObj<AdaptiveCloudUsageSharedService>;

        mockUserContextService.currentUser = {
            organizationId: 1
        } as UserContext;

        mockUsageService.getUsagePeriods.and.returnValue(of({
            data: ['202202', '202201'], message: ''
        }));

        const data: AdaptiveCloudUsageSummary = {
            accounts: [
                {
                    accountId: '1',
                    accountName: 'Account 1',
                    vCpu: {
                        quantity: 1,
                        totalCost: 1,
                        unitCost: 1,
                        unitDescription: ''
                    },
                    ram: {
                        quantity: 1,
                        totalCost: 1,
                        unitCost: 1,
                        unitDescription: ''
                    },
                    primaryStorage: {
                        quantity: 1,
                        totalCost: 1,
                        unitCost: 1,
                        unitDescription: ''
                    },
                    ipAddress: {
                        quantity: 1,
                        totalCost: 1,
                        unitCost: 1,
                        unitDescription: ''
                    },
                    networkBytes: {
                        quantity: 1,
                        totalCost: 1,
                        unitCost: 1,
                        unitDescription: ''
                    },
                    secondaryStorage: {
                        quantity: 1,
                        totalCost: 1,
                        unitCost: 1,
                        unitDescription: ''
                    },
                    licensing: {
                        totalCost: 1,
                        licenses: []
                    },
                    virtualMachineCount: 1,
                    accountTotal: 1
                }
            ],
            total: {
                accountId: '1',
                accountName: 'Account 1',
                vCpu: {
                    quantity: 1,
                    totalCost: 1,
                    unitCost: 1,
                    unitDescription: ''
                },
                ram: {
                    quantity: 1,
                    totalCost: 1,
                    unitCost: 1,
                    unitDescription: ''
                },
                primaryStorage: {
                    quantity: 1,
                    totalCost: 1,
                    unitCost: 1,
                    unitDescription: ''
                },
                ipAddress: {
                    quantity: 1,
                    totalCost: 1,
                    unitCost: 1,
                    unitDescription: ''
                },
                networkBytes: {
                    quantity: 1,
                    totalCost: 1,
                    unitCost: 1,
                    unitDescription: ''
                },
                secondaryStorage: {
                    quantity: 1,
                    totalCost: 1,
                    unitCost: 1,
                    unitDescription: ''
                },
                licensing: {
                    totalCost: 1,
                    licenses: []
                },
                virtualMachineCount: 1,
                accountTotal: 1
            }
        };

        mockUsageSharedService.getUsageSummary.and.returnValue(of({ data, message: '', totalCount: 1 }));

        mockUsageService.getUsageDetails.and.returnValue(of({
            data: {} as AdaptiveCloudUsageDetails,
            message: '',
            totalCount: 0
        }));

        fixture = TestBed.createComponent(AdaptiveCloudUsageComponent);
        fixture.detectChanges();
    });

    describe('Initialization', () => {

        it('should load summary by default', () => {
            expect(mockUsageService.getUsagePeriods).toHaveBeenCalledTimes(1);
            expect(mockUsageSharedService.getUsageSummary).toHaveBeenCalledOnceWith(1, '202202');
            const summary = fixture.debugElement.query(By.directive(AdaptiveCloudUsageSummaryComponent));
            expect(summary.nativeNode.querySelectorAll('datatable-row-wrapper').length).toEqual(1);
        });

        it('should refresh summary with new selected period', fakeAsync(() => {
            selectOption(fixture, 'ng-select', 1, true, 0);
            expect(mockUsageSharedService.getUsageSummary).toHaveBeenCalledWith(1, '202201');
            flush();
            discardPeriodicTasks();
        }));

        it('should load details when selecting the first row action in the summary', () => {
            const summary = fixture.debugElement.query(By.directive(AdaptiveCloudUsageSummaryComponent));
            const summaryGrid = summary.query(By.directive(DatatableComponent));
            const action = summaryGrid.query(By.directive(TableActionComponent));
            action.query(By.css('span')).nativeElement.click();
            fixture.detectChanges();
            expect(mockUsageService.getUsageDetails).toHaveBeenCalledOnceWith({ accountId: '1', organizationId: 1, period: '202202' });
        });

    });
});
