﻿using AutoMapper;
using MyAdaptiveCloud.Api.ViewModel.Organization;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Policies;
using MyAdaptiveCloud.Services.AutoMapper.Extensions;
using MyAdaptiveCloud.Services.DTOs.Organization;

namespace MyAdaptiveCloud.Api.AutoMapper.Organizations
{
    public class OrganizationMapperProfile : Profile
    {
        public OrganizationMapperProfile()
        {
            CreateMap<Requests.Organizations.CreateOrganizationRequest, Services.Requests.Organizations.CreateOrganizationRequest>();
            CreateMap<Requests.Organizations.EditOrganizationRequest, Services.Requests.Organizations.EditOrganizationRequest>();
            CreateMap<Requests.Organizations.EditWhiteLabelRequest, Services.Requests.Organizations.EditWhiteLabelRequest>();
            CreateMap<Requests.Organizations.OrganizationListRequest, Services.Requests.Organizations.OrganizationListRequest>();
            CreateMap<Requests.Organizations.OrganizationRequest, Services.Requests.Organizations.OrganizationRequest>();
            CreateMap<Requests.Organizations.DeletedOrganizationsRequest, Services.Requests.Organizations.DeletedOrganizationsRequest>();
            CreateMap<Requests.Organizations.UpdateOrganizationPolicyRequest, Services.Requests.Organizations.UpdateOrganizationPolicyRequest>();
            CreateMap<Requests.Organizations.OrganizationWithApplicationListRequest, Services.Requests.Organizations.OrganizationWithApplicationListRequest>();

            CreateMap<Organization, OrganizationDTO>()
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore())
                .ForMember(d => d.ParentOrganization, opt => opt.Ignore());

            CreateMap<OrganizationHierarchy, OrganizationDTO>()
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore())
                .ForMember(d => d.ParentOrganization, opt => opt.Ignore());

            CreateMap<OrganizationAncestry, OrganizationAncestryDTO>()
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore());


            CreateMap<Organization, OrganizationViewModel>().IgnoreViewModelUserActions()
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore());

            CreateMap<OrganizationDTO, OrganizationViewModel>().IgnoreViewModelUserActions()
                .ForMember(d => d.ParentOrganizationName, opt => opt.Ignore());

            CreateMap<Tuple<Organization, OrganizationPolicyConfiguration>, OrganizationFolderDTO>()
                .ForMember(d => d.OrganizationId, opt => opt.MapFrom(s => s.Item1.OrganizationId))
                .ForMember(d => d.ParentOrganizationId, opt => opt.MapFrom(s => s.Item1.ParentOrganizationId))
                .ForMember(d => d.Name, opt => opt.MapFrom(s => s.Item1.Name))
                .ForMember(d => d.IsPartner, opt => opt.MapFrom(s => s.Item1.IsPartner))
                .ForMember(d => d.PolicyId, opt => opt.MapFrom(s => s.Item2 != null ? s.Item2.PolicyId : null))
                .ForMember(d => d.InheritsPolicy, opt => opt.MapFrom(s => s.Item2 == null))
                .ForMember(dest => dest.PolicyInheritedFrom, opt => opt.Ignore())
                .ForMember(dest => dest.HasSubfolders, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCount, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCountCurrentFolder, opt => opt.Ignore());

            CreateMap<Tuple<OrganizationHierarchy, OrganizationPolicyConfiguration>, OrganizationFolderDTO>()
                .ForMember(d => d.OrganizationId, opt => opt.MapFrom(s => s.Item1.OrganizationId))
                .ForMember(d => d.ParentOrganizationId, opt => opt.MapFrom(s => s.Item1.ParentOrganizationId))
                .ForMember(d => d.Name, opt => opt.MapFrom(s => s.Item1.Name))
                .ForMember(d => d.IsPartner, opt => opt.MapFrom(s => s.Item1.IsPartner))
                .ForMember(d => d.PolicyId, opt => opt.MapFrom(s => s.Item2 != null ? s.Item2.PolicyId : null))
                .ForMember(d => d.InheritsPolicy, opt => opt.MapFrom(s => s.Item2 == null))
                .ForMember(dest => dest.PolicyInheritedFrom, opt => opt.Ignore())
                .ForMember(dest => dest.HasSubfolders, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCount, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCountCurrentFolder, opt => opt.Ignore());

            CreateMap<OrganizationHierarchy, OrganizationViewModel>().IgnoreViewModelUserActions()
                .ForMember(d => d.OrganizationParentFullPath, opt => opt.Ignore());

            CreateMap<OrganizationDTO, Organization>()
                .ForMember(dest => dest.ParentOrganization, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationMappings, opt => opt.Ignore())
                .ForMember(dest => dest.DataProtectionCredentials, opt => opt.Ignore());

            CreateMap<Organization, OrganizationFolderTreeNodeDTO>()
                .ForMember(dest => dest.HasSubfolders, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCount, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCountCurrentFolder, opt => opt.Ignore());

            CreateMap<OrganizationHierarchy, OrganizationFolderTreeNodeDTO>()
                .ForMember(dest => dest.HasSubfolders, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCount, opt => opt.Ignore())
                .ForMember(dest => dest.DeviceCountCurrentFolder, opt => opt.Ignore());
        }
    }
}