<div ngbAccordion [closeOthers]="true">
    @for (nic of nics(); track nic.id) {
        <div ngbAccordionItem [collapsed]="false">
            <div ngbAccordionHeader>
                <button ngbAccordionButton>{{ nic.networkname }}</button>
            </div>
            <div ngbAccordionCollapse>
                <div ngbAccordionBody>
                    <div class="row">
                        <div class="col-6">
                            <p><strong>Type:</strong> {{ nic.type }}</p>
                            <p><strong>Traffic Type:</strong> {{ nic.traffictype }}</p>
                            <p><strong>Network Name:</strong> {{ nic.networkname }}</p>
                            <p><strong>Netmask:</strong> {{ nic.netmask }}</p>
                            <p><strong>IP Address:</strong> {{ nic.ipaddress }}</p>
                            <p><strong>MAC Address:</strong> {{ nic.macaddress }}</p>
                            <p><strong>ID:</strong> {{ nic.id }}</p>
                            <p><strong>Network ID:</strong> {{ nic.networkid }}</p>
                        </div>
                        <div class="col-6">
                            <p><strong>Isolation URI:</strong> {{ nic.isolationuri }}</p>
                            <p><strong>Broadcast URI:</strong> {{ nic.broadcasturi }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
