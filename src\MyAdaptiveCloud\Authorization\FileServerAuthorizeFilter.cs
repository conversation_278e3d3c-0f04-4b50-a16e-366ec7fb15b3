﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Authorization
{
    public class FileServerAuthorizeFilter : BaseAsyncAuthorizationFilter
    {
        private readonly IUserContextService _userContextService;
        private readonly IEntityAuthorizationService _entityAuthorizationService;
        private readonly IIdentityService _identityService;

        public FileServerAuthorizeFilter(IEntityAuthorizationService entityAuthorizationService, IIdentityService identityService,
            IUserContextService userContextService, Perms[] perms, int distance, string name) : base(perms, distance, name)
        {
            _entityAuthorizationService = entityAuthorizationService;
            _userContextService = userContextService;
            _identityService = identityService;
        }

        public async override Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            int userId = _identityService.PersonIdFromPrincipal(context.HttpContext.User);
            if (userId == 0)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            string val = AuthorizeFilterHelpers.GetEntityValue(context, _name);
            _ = int.TryParse(val, out int fileServerId);

            var fileServerOrganizationId = await _entityAuthorizationService.GetFileServerOrganizationId(fileServerId);
            if (fileServerOrganizationId.HasValue)
            {
                if (_perms != null && !_userContextService.HasPermission(userId, fileServerOrganizationId.Value, _distance, _perms))
                {
                    context.Result = new ForbidResult();
                }
                else
                {
                    AuthorizeFilterHelpers.SetOrganizationId(context, fileServerOrganizationId.Value);
                }
            }
            else
            {
                context.Result = new BadRequestResult();
            }
        }
    }

    public class FileServerAuthorizeAttribute : BaseAuthorizeAttribute
    {
        public FileServerAuthorizeAttribute(params Perms[] perms) : base(typeof(FileServerAuthorizeFilter), perms)
        {
            Name = "fileServerId";
        }
    }
}
