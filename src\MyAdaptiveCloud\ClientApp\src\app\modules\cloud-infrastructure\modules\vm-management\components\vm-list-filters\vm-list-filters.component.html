<form [formGroup]="filters().form">
    <div id="status-group">
        <h6 class="card-title mb-3 fw-normal h5">{{ filters().filterGroupOptions[1].groupName }}</h6>
        <div class="card-body">
            @for (option of filters().filterGroupOptions[1].options; track option.value) {
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio"
                        [formControlName]="filters().filterGroupOptions[1].key" id="{{option.value}}"
                        value="{{option.value}}">
                    <label class="form-check-label" for="{{option.value}}">
                        {{option.label}}
                    </label>
                </div>
            }
        </div>
    </div>

    <hr>
    <div id="zone-group">
        <h6 class="card-title mb-3 fw-normal h5">{{ filters().filterGroupOptions[0].groupName }}</h6>
        <div class="card-body">
            @for (zone of zoneOptions(); track zone.id) {
                <div class="form-check mb-2">
                    <input class="form-check-input" type="radio"
                        [formControlName]="filters().filterGroupOptions[0].key" id="{{zone.id}}" value="{{zone.id}}">
                    <label class="form-check-label" for="{{zone.id}}">
                        {{zone.label}}
                    </label>
                </div>
            }
        </div>
        <hr>
    </div>
</form>
