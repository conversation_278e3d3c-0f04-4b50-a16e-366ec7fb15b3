import { Network } from './network';

export interface Vpc {
    account: string;
    cidr: string;
    created: string;
    displaytext: string;
    distributedvpcrouter: boolean;
    domain: string;
    domainid: string;
    fordisplay: boolean;
    hasannotations: boolean;
    id: string;
    name: string;
    network: Network[];
    networkdomain: string;
    publicmtu: number;
    redundantvpcrouter: boolean;
    regionlevelvpc: boolean;
    restartrequired: boolean;
    service: VpcService[];
    state: 'Enabled' | 'Inactive';
    tags: string[];
    vpcofferingid: string;
    vpcofferingname: string;
    zoneid: string;
    zonename: string;
}

export interface VpcService {
    name: string;
    provider: VpcProvider[];
}

export interface VpcProvider {
    name: string;
}
